{"name": "@opentelemetry/instrumentation-pg", "version": "0.51.1", "description": "OpenTelemetry instrumentation for `pg` and `pg-pool` database client for PostgreSQL", "main": "build/src/index.js", "types": "build/src/index.d.ts", "repository": "open-telemetry/opentelemetry-js-contrib", "scripts": {"clean": "rimraf build/*", "compile": "tsc -p .", "lint": "eslint . --ext .ts", "lint:fix": "eslint . --ext .ts --fix", "lint:readme": "node ../../../scripts/lint-readme.js", "prewatch": "npm run precompile", "prepublishOnly": "npm run compile", "tdd": "npm run test -- --watch-extensions ts --watch", "test": "nyc mocha 'test/**/*.test.ts'", "test-all-versions": "tav", "test-all-versions:local": "cross-env RUN_POSTGRES_TESTS_LOCAL=true npm run test-all-versions", "test:debug": "mocha --inspect-brk --no-timeouts 'test/**/*.test.ts'", "test:local": "cross-env RUN_POSTGRES_TESTS_LOCAL=true npm run test", "version:update": "node ../../../scripts/version-update.js", "watch": "tsc -w"}, "keywords": ["instrumentation", "nodejs", "opentelemetry", "pg", "plugin", "postgres", "postgresql", "profiling", "tracing"], "author": "OpenTelemetry Authors", "license": "Apache-2.0", "engines": {"node": ">=14"}, "files": ["build/src/**/*.js", "build/src/**/*.js.map", "build/src/**/*.d.ts"], "publishConfig": {"access": "public"}, "peerDependencies": {"@opentelemetry/api": "^1.3.0"}, "devDependencies": {"@opentelemetry/api": "^1.3.0", "@opentelemetry/context-async-hooks": "^1.8.0", "@opentelemetry/contrib-test-utils": "^0.45.1", "@opentelemetry/sdk-trace-base": "^1.8.0", "@opentelemetry/sdk-trace-node": "^1.8.0", "@types/mocha": "7.0.2", "@types/node": "18.18.14", "@types/sinon": "10.0.20", "cross-env": "7.0.3", "nyc": "15.1.0", "pg": "8.7.1", "pg-pool": "3.4.1", "rimraf": "5.0.10", "safe-stable-stringify": "^2.4.1", "sinon": "15.2.0", "test-all-versions": "6.1.0", "typescript": "4.4.4"}, "dependencies": {"@opentelemetry/core": "^1.26.0", "@opentelemetry/instrumentation": "^0.57.1", "@opentelemetry/semantic-conventions": "^1.27.0", "@opentelemetry/sql-common": "^0.40.1", "@types/pg": "8.6.1", "@types/pg-pool": "2.0.6"}, "homepage": "https://github.com/open-telemetry/opentelemetry-js-contrib/tree/main/plugins/node/opentelemetry-instrumentation-pg#readme", "gitHead": "1eb77007669bae87fe5664d68ba6533b95275d52"}