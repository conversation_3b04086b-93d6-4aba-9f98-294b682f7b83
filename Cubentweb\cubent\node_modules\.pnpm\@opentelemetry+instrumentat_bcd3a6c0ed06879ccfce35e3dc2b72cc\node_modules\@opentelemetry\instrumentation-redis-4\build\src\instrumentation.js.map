{"version": 3, "file": "instrumentation.js", "sourceRoot": "", "sources": ["../../src/instrumentation.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,4CAM4B;AAC5B,oEAKwC;AACxC,mCAA8C;AAC9C,8DAA2E;AAE3E,kBAAkB;AAClB,uCAA0D;AAC1D,8EAA4E;AAG5E,MAAM,eAAe,GAAG,MAAM,CAC5B,gDAAgD,CACjD,CAAC;AACF,MAAM,qBAAqB,GAAG,MAAM,CAClC,2DAA2D,CAC5D,CAAC;AAQF,MAAM,cAAc,GAA+B;IACjD,iBAAiB,EAAE,KAAK;CACzB,CAAC;AAEF,MAAa,oBAAqB,SAAQ,qCAA+C;IAGvF,YAAY,SAAqC,EAAE;QACjD,KAAK,CAAC,sBAAY,EAAE,yBAAe,kCAAO,cAAc,GAAK,MAAM,EAAG,CAAC;IACzE,CAAC;IAEQ,SAAS,CAAC,SAAqC,EAAE;QACxD,KAAK,CAAC,SAAS,iCAAM,cAAc,GAAK,MAAM,EAAG,CAAC;IACpD,CAAC;IAES,IAAI;QACZ,+EAA+E;QAC/E,kDAAkD;QAClD,mCAAmC;QACnC,OAAO;YACL,IAAI,CAAC,uCAAuC,CAAC,eAAe,CAAC;YAC7D,IAAI,CAAC,uCAAuC,CAAC,oBAAoB,CAAC;SACnE,CAAC;IACJ,CAAC;IAEO,uCAAuC,CAC7C,eAAuB;QAEvB,MAAM,mBAAmB,GAAG,IAAI,+CAA6B,CAC3D,GAAG,eAAe,wBAAwB,EAC1C,CAAC,QAAQ,CAAC,EACV,CAAC,aAAkB,EAAE,aAAsB,EAAE,EAAE;YAC7C,MAAM,yBAAyB,GAC7B,aAAa,CAAC,yBAAyB,CAAC;YAC1C,IAAI,CAAC,yBAAyB,EAAE;gBAC9B,IAAI,CAAC,KAAK,CAAC,KAAK,CACd,4EAA4E,CAC7E,CAAC;gBACF,OAAO,aAAa,CAAC;aACtB;YAED,mGAAmG;YACnG,0EAA0E;YAC1E,MAAM,eAAe,GAAG,CAAA,aAAa,aAAb,aAAa,uBAAb,aAAa,CAAE,UAAU,CAAC,MAAM,CAAC;gBACvD,CAAC,CAAC,oBAAoB;gBACtB,CAAC,CAAC,gBAAgB,CAAC;YACrB,2EAA2E;YAC3E,4DAA4D;YAC5D,IAAI,IAAA,2BAAS,EAAC,aAAa,aAAb,aAAa,uBAAb,aAAa,CAAG,eAAe,CAAC,CAAC,EAAE;gBAC/C,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,eAAe,CAAC,CAAC;aAC9C;YACD,IAAI,CAAC,KAAK,CACR,aAAa,EACb,eAAe,EACf,IAAI,CAAC,2BAA2B,CAAC,yBAAyB,CAAC,CAC5D,CAAC;YAEF,OAAO,aAAa,CAAC;QACvB,CAAC,EACD,CAAC,aAAkB,EAAE,EAAE;YACrB,IAAI,IAAA,2BAAS,EAAC,aAAa,aAAb,aAAa,uBAAb,aAAa,CAAE,kBAAkB,CAAC,EAAE;gBAChD,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,oBAAoB,CAAC,CAAC;aACnD;YACD,IAAI,IAAA,2BAAS,EAAC,aAAa,aAAb,aAAa,uBAAb,aAAa,CAAE,cAAc,CAAC,EAAE;gBAC5C,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,gBAAgB,CAAC,CAAC;aAC/C;QACH,CAAC,CACF,CAAC;QAEF,MAAM,oBAAoB,GAAG,IAAI,+CAA6B,CAC5D,GAAG,eAAe,mCAAmC,EACrD,CAAC,QAAQ,CAAC,EACV,CAAC,aAAkB,EAAE,EAAE;;YACrB,MAAM,gCAAgC,GACpC,MAAA,aAAa,aAAb,aAAa,uBAAb,aAAa,CAAE,OAAO,0CAAE,SAAS,CAAC;YAEpC,IAAI,IAAA,2BAAS,EAAC,gCAAgC,aAAhC,gCAAgC,uBAAhC,gCAAgC,CAAE,IAAI,CAAC,EAAE;gBACrD,IAAI,CAAC,OAAO,CAAC,gCAAgC,EAAE,MAAM,CAAC,CAAC;aACxD;YACD,IAAI,CAAC,KAAK,CACR,gCAAgC,EAChC,MAAM,EACN,IAAI,CAAC,0BAA0B,EAAE,CAClC,CAAC;YAEF,IAAI,IAAA,2BAAS,EAAC,gCAAgC,aAAhC,gCAAgC,uBAAhC,gCAAgC,CAAE,UAAU,CAAC,EAAE;gBAC3D,IAAI,CAAC,OAAO,CAAC,gCAAgC,EAAE,YAAY,CAAC,CAAC;aAC9D;YACD,IAAI,CAAC,KAAK,CACR,gCAAgC,EAChC,YAAY,EACZ,IAAI,CAAC,gCAAgC,EAAE,CACxC,CAAC;YAEF,OAAO,aAAa,CAAC;QACvB,CAAC,EACD,CAAC,aAAkB,EAAE,EAAE;;YACrB,MAAM,gCAAgC,GACpC,MAAA,aAAa,aAAb,aAAa,uBAAb,aAAa,CAAE,OAAO,0CAAE,SAAS,CAAC;YACpC,IAAI,IAAA,2BAAS,EAAC,gCAAgC,aAAhC,gCAAgC,uBAAhC,gCAAgC,CAAE,IAAI,CAAC,EAAE;gBACrD,IAAI,CAAC,OAAO,CAAC,gCAAgC,EAAE,MAAM,CAAC,CAAC;aACxD;YACD,IAAI,IAAA,2BAAS,EAAC,gCAAgC,aAAhC,gCAAgC,uBAAhC,gCAAgC,CAAE,UAAU,CAAC,EAAE;gBAC3D,IAAI,CAAC,OAAO,CAAC,gCAAgC,EAAE,YAAY,CAAC,CAAC;aAC9D;QACH,CAAC,CACF,CAAC;QAEF,MAAM,iBAAiB,GAAG,IAAI,+CAA6B,CACzD,GAAG,eAAe,2BAA2B,EAC7C,CAAC,QAAQ,CAAC,EACV,CAAC,aAAkB,EAAE,EAAE;;YACrB,MAAM,oBAAoB,GAAG,MAAA,aAAa,aAAb,aAAa,uBAAb,aAAa,CAAE,OAAO,0CAAE,SAAS,CAAC;YAE/D,+DAA+D;YAC/D,iEAAiE;YACjE,+DAA+D;YAC/D,gEAAgE;YAChE,+BAA+B;YAC/B,IAAI,oBAAoB,aAApB,oBAAoB,uBAApB,oBAAoB,CAAE,KAAK,EAAE;gBAC/B,IAAI,IAAA,2BAAS,EAAC,oBAAoB,aAApB,oBAAoB,uBAApB,oBAAoB,CAAE,KAAK,CAAC,EAAE;oBAC1C,IAAI,CAAC,OAAO,CAAC,oBAAoB,EAAE,OAAO,CAAC,CAAC;iBAC7C;gBACD,IAAI,CAAC,KAAK,CACR,oBAAoB,EACpB,OAAO,EACP,IAAI,CAAC,yBAAyB,EAAE,CACjC,CAAC;aACH;YACD,IAAI,oBAAoB,aAApB,oBAAoB,uBAApB,oBAAoB,CAAE,KAAK,EAAE;gBAC/B,IAAI,IAAA,2BAAS,EAAC,oBAAoB,aAApB,oBAAoB,uBAApB,oBAAoB,CAAE,KAAK,CAAC,EAAE;oBAC1C,IAAI,CAAC,OAAO,CAAC,oBAAoB,EAAE,OAAO,CAAC,CAAC;iBAC7C;gBACD,IAAI,CAAC,KAAK,CACR,oBAAoB,EACpB,OAAO,EACP,IAAI,CAAC,yBAAyB,EAAE,CACjC,CAAC;aACH;YAED,IAAI,IAAA,2BAAS,EAAC,oBAAoB,aAApB,oBAAoB,uBAApB,oBAAoB,CAAE,WAAW,CAAC,EAAE;gBAChD,IAAI,CAAC,OAAO,CAAC,oBAAoB,EAAE,aAAa,CAAC,CAAC;aACnD;YACD,IAAI,CAAC,KAAK,CACR,oBAAoB,EACpB,aAAa,EACb,IAAI,CAAC,+BAA+B,EAAE,CACvC,CAAC;YAEF,IAAI,CAAC,KAAK,CACR,oBAAoB,EACpB,SAAS,EACT,IAAI,CAAC,wBAAwB,EAAE,CAChC,CAAC;YAEF,OAAO,aAAa,CAAC;QACvB,CAAC,EACD,CAAC,aAAkB,EAAE,EAAE;;YACrB,MAAM,oBAAoB,GAAG,MAAA,aAAa,aAAb,aAAa,uBAAb,aAAa,CAAE,OAAO,0CAAE,SAAS,CAAC;YAC/D,IAAI,IAAA,2BAAS,EAAC,oBAAoB,aAApB,oBAAoB,uBAApB,oBAAoB,CAAE,KAAK,CAAC,EAAE;gBAC1C,IAAI,CAAC,OAAO,CAAC,oBAAoB,EAAE,OAAO,CAAC,CAAC;aAC7C;YACD,IAAI,IAAA,2BAAS,EAAC,oBAAoB,aAApB,oBAAoB,uBAApB,oBAAoB,CAAE,KAAK,CAAC,EAAE;gBAC1C,IAAI,CAAC,OAAO,CAAC,oBAAoB,EAAE,OAAO,CAAC,CAAC;aAC7C;YACD,IAAI,IAAA,2BAAS,EAAC,oBAAoB,aAApB,oBAAoB,uBAApB,oBAAoB,CAAE,WAAW,CAAC,EAAE;gBAChD,IAAI,CAAC,OAAO,CAAC,oBAAoB,EAAE,aAAa,CAAC,CAAC;aACnD;QACH,CAAC,CACF,CAAC;QAEF,OAAO,IAAI,qDAAmC,CAC5C,eAAe,EACf,CAAC,QAAQ,CAAC,EACV,CAAC,aAAkB,EAAE,EAAE;YACrB,OAAO,aAAa,CAAC;QACvB,CAAC,EACD,GAAG,EAAE,GAAE,CAAC,EACR,CAAC,mBAAmB,EAAE,oBAAoB,EAAE,iBAAiB,CAAC,CAC/D,CAAC;IACJ,CAAC;IAED,wEAAwE;IACxE,yDAAyD;IACjD,2BAA2B,CAAC,yBAAmC;QACrE,MAAM,MAAM,GAAG,IAAI,CAAC;QACpB,OAAO,SAAS,8BAA8B,CAAC,QAAkB;YAC/D,OAAO,SAAS,uBAAuB,CAAY,MAAW;;gBAC5D,IAAI,CAAA,MAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,SAAS,0CAAE,IAAI,MAAK,aAAa,EAAE;oBAC7C,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;iBACxC;gBAED,MAAM,YAAY,GAAG,MAAM,CAAC,QAAQ,CAAC;gBACrC,MAAM,CAAC,QAAQ,GAAG,UAEhB,OAAY,EACZ,IAA4B;oBAE5B,MAAM,qBAAqB,GAAG,yBAAyB,CACrD,OAAO,EACP,IAAI,CACL,CAAC,IAAI,CAAC;oBACP,OAAO,MAAM,CAAC,mBAAmB,CAC/B,YAAY,EACZ,IAAI,EACJ,SAAS,EACT,qBAAqB,CACtB,CAAC;gBACJ,CAAC,CAAC;gBACF,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;YACzC,CAAC,CAAC;QACJ,CAAC,CAAC;IACJ,CAAC;IAEO,0BAA0B;QAChC,MAAM,MAAM,GAAG,IAAI,CAAC;QACpB,OAAO,SAAS,gBAAgB,CAAC,QAAkB;YACjD,OAAO,SAAS,SAAS;gBACvB,MAAM,OAAO,GAAG,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;gBAChD,IAAI,OAAO,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI,CAAA,KAAK,UAAU,EAAE;oBACvC,MAAM,CAAC,KAAK,CAAC,KAAK,CAChB,mEAAmE,CACpE,CAAC;oBACF,OAAO,OAAO,CAAC;iBAChB;gBAED,OAAO,OAAO;qBACX,IAAI,CAAC,CAAC,QAAmB,EAAE,EAAE;oBAC5B,MAAM,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC;oBACxC,MAAM,CAAC,yBAAyB,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;oBACtD,OAAO,QAAQ,CAAC;gBAClB,CAAC,CAAC;qBACD,KAAK,CAAC,CAAC,GAAU,EAAE,EAAE;oBACpB,MAAM,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC;oBACxC,IAAI,CAAC,SAAS,EAAE;wBACd,MAAM,CAAC,KAAK,CAAC,KAAK,CAChB,uDAAuD,CACxD,CAAC;qBACH;yBAAM;wBACL,MAAM,OAAO,GACX,GAAG,CAAC,WAAW,CAAC,IAAI,KAAK,iBAAiB;4BACxC,CAAC,CAAE,GAAuB,CAAC,OAAO;4BAClC,CAAC,CAAC,IAAI,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;wBAC5C,MAAM,CAAC,yBAAyB,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;qBACtD;oBACD,OAAO,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;gBAC7B,CAAC,CAAC,CAAC;YACP,CAAC,CAAC;QACJ,CAAC,CAAC;IACJ,CAAC;IAEO,gCAAgC;QACtC,MAAM,MAAM,GAAG,IAAI,CAAC;QACpB,OAAO,SAAS,iBAAiB,CAAC,QAAkB;YAClD,OAAO,SAAS,eAAe,CAAY,IAA4B;gBACrE,OAAO,MAAM,CAAC,mBAAmB,CAAC,QAAQ,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;YACrE,CAAC,CAAC;QACJ,CAAC,CAAC;IACJ,CAAC;IAEO,yBAAyB;QAC/B,OAAO,SAAS,iBAAiB,CAAC,QAAkB;YAClD,OAAO,SAAS,UAAU;gBACxB,MAAM,QAAQ,GAAG,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;gBACjD,QAAQ,CAAC,qBAAqB,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;gBAC/C,OAAO,QAAQ,CAAC;YAClB,CAAC,CAAC;QACJ,CAAC,CAAC;IACJ,CAAC;IAEO,+BAA+B;QACrC,MAAM,MAAM,GAAG,IAAI,CAAC;QACpB,OAAO,SAAS,kBAAkB,CAAC,QAAkB;YACnD,OAAO,SAAS,gBAAgB,CAE9B,IAA4B;gBAE5B,OAAO,MAAM,CAAC,mBAAmB,CAAC,QAAQ,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;YACrE,CAAC,CAAC;QACJ,CAAC,CAAC;IACJ,CAAC;IAEO,wBAAwB;QAC9B,MAAM,MAAM,GAAG,IAAI,CAAC;QACpB,OAAO,SAAS,cAAc,CAAC,QAAkB;YAC/C,OAAO,SAAS,cAAc;gBAC5B,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;gBAE7B,MAAM,UAAU,GAAG,IAAA,2BAAmB,EAAC,MAAM,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;gBAE9D,MAAM,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,SAAS,CAClC,GAAG,oBAAoB,CAAC,SAAS,UAAU,EAC3C;oBACE,IAAI,EAAE,cAAQ,CAAC,MAAM;oBACrB,UAAU;iBACX,CACF,CAAC;gBAEF,MAAM,GAAG,GAAG,aAAO,CAAC,IAAI,CAAC,WAAK,CAAC,OAAO,CAAC,aAAO,CAAC,MAAM,EAAE,EAAE,IAAI,CAAC,EAAE,GAAG,EAAE;oBACnE,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;gBAC9B,CAAC,CAAC,CAAC;gBAEH,OAAO,GAAG;qBACP,IAAI,CAAC,CAAC,MAAe,EAAE,EAAE;oBACxB,IAAI,CAAC,GAAG,EAAE,CAAC;oBACX,OAAO,MAAM,CAAC;gBAChB,CAAC,CAAC;qBACD,KAAK,CAAC,CAAC,KAAY,EAAE,EAAE;oBACtB,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;oBAC5B,IAAI,CAAC,SAAS,CAAC;wBACb,IAAI,EAAE,oBAAc,CAAC,KAAK;wBAC1B,OAAO,EAAE,KAAK,CAAC,OAAO;qBACvB,CAAC,CAAC;oBACH,IAAI,CAAC,GAAG,EAAE,CAAC;oBACX,OAAO,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;gBAC/B,CAAC,CAAC,CAAC;YACP,CAAC,CAAC;QACJ,CAAC,CAAC;IACJ,CAAC;IAEO,mBAAmB,CACzB,YAAsB,EACtB,QAAa,EACb,aAAyB,EACzB,qBAA6C;QAE7C,MAAM,eAAe,GAAG,WAAK,CAAC,OAAO,CAAC,aAAO,CAAC,MAAM,EAAE,CAAC,KAAK,SAAS,CAAC;QACtE,IAAI,eAAe,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC,iBAAiB,EAAE;YACzD,OAAO,YAAY,CAAC,KAAK,CAAC,QAAQ,EAAE,aAAa,CAAC,CAAC;SACpD;QAED,MAAM,aAAa,GAAG,QAAQ,CAAC,OAAO,IAAI,QAAQ,CAAC,qBAAqB,CAAC,CAAC;QAE1E,MAAM,WAAW,GAAG,qBAAqB,CAAC,CAAC,CAAW,CAAC,CAAC,sEAAsE;QAC9H,MAAM,WAAW,GAAG,qBAAqB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QAEnD,MAAM,qBAAqB,GACzB,IAAI,CAAC,SAAS,EAAE,CAAC,qBAAqB,IAAI,2CAA4B,CAAC;QAEzE,MAAM,UAAU,GAAG,IAAA,2BAAmB,EAAC,IAAI,CAAC,KAAK,EAAE,aAAa,CAAC,CAAC;QAElE,IAAI;YACF,MAAM,WAAW,GAAG,qBAAqB,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;YACpE,IAAI,WAAW,IAAI,IAAI,EAAE;gBACvB,UAAU,CAAC,4CAAqB,CAAC,GAAG,WAAW,CAAC;aACjD;SACF;QAAC,OAAO,CAAC,EAAE;YACV,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,0CAA0C,EAAE,CAAC,EAAE;gBAC9D,WAAW;aACZ,CAAC,CAAC;SACJ;QAED,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,CAChC,GAAG,oBAAoB,CAAC,SAAS,IAAI,WAAW,EAAE,EAClD;YACE,IAAI,EAAE,cAAQ,CAAC,MAAM;YACrB,UAAU;SACX,CACF,CAAC;QAEF,MAAM,GAAG,GAAG,aAAO,CAAC,IAAI,CAAC,WAAK,CAAC,OAAO,CAAC,aAAO,CAAC,MAAM,EAAE,EAAE,IAAI,CAAC,EAAE,GAAG,EAAE;YACnE,OAAO,YAAY,CAAC,KAAK,CAAC,QAAQ,EAAE,aAAa,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;QACH,IAAI,OAAO,CAAA,GAAG,aAAH,GAAG,uBAAH,GAAG,CAAE,IAAI,CAAA,KAAK,UAAU,EAAE;YACnC,GAAG,CAAC,IAAI,CACN,CAAC,QAAiB,EAAE,EAAE;gBACpB,IAAI,CAAC,oBAAoB,CACvB,IAAI,EACJ,WAAW,EACX,WAAW,EACX,QAAQ,EACR,SAAS,CACV,CAAC;YACJ,CAAC,EACD,CAAC,GAAQ,EAAE,EAAE;gBACX,IAAI,CAAC,oBAAoB,CAAC,IAAI,EAAE,WAAW,EAAE,WAAW,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC;YACvE,CAAC,CACF,CAAC;SACH;aAAM;YACL,MAAM,uBAAuB,GAAG,GAE/B,CAAC;YACF,uBAAuB,CAAC,eAAe,CAAC;gBACtC,uBAAuB,CAAC,eAAe,CAAC,IAAI,EAAE,CAAC;YACjD,uBAAuB,CAAC,eAAe,CAAE,CAAC,IAAI,CAAC;gBAC7C,IAAI;gBACJ,WAAW;gBACX,WAAW;aACZ,CAAC,CAAC;SACJ;QACD,OAAO,GAAG,CAAC;IACb,CAAC;IAEO,yBAAyB,CAC/B,SAAkC,EAClC,OAAkB;QAElB,IAAI,CAAC,SAAS,EAAE;YACd,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CACrB,uDAAuD,CACxD,CAAC;SACH;QACD,IAAI,OAAO,CAAC,MAAM,KAAK,SAAS,CAAC,MAAM,EAAE;YACvC,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CACrB,kEAAkE,CACnE,CAAC;SACH;QACD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACzC,MAAM,EAAE,IAAI,EAAE,WAAW,EAAE,WAAW,EAAE,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;YACxD,MAAM,cAAc,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;YAClC,MAAM,CAAC,GAAG,EAAE,GAAG,CAAC,GACd,cAAc,YAAY,KAAK;gBAC7B,CAAC,CAAC,CAAC,IAAI,EAAE,cAAc,CAAC;gBACxB,CAAC,CAAC,CAAC,cAAc,EAAE,SAAS,CAAC,CAAC;YAClC,IAAI,CAAC,oBAAoB,CAAC,IAAI,EAAE,WAAW,EAAE,WAAW,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;SACrE;IACH,CAAC;IAEO,oBAAoB,CAC1B,IAAU,EACV,WAAmB,EACnB,WAAmC,EACnC,QAAiB,EACjB,KAAwB;QAExB,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QAC1C,IAAI,CAAC,KAAK,IAAI,YAAY,EAAE;YAC1B,IAAI;gBACF,YAAY,CAAC,IAAI,EAAE,WAAW,EAAE,WAAW,EAAE,QAAQ,CAAC,CAAC;aACxD;YAAC,OAAO,GAAG,EAAE;gBACZ,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,iCAAiC,EAAE,GAAG,CAAC,CAAC;aAC1D;SACF;QACD,IAAI,KAAK,EAAE;YACT,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;YAC5B,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,oBAAc,CAAC,KAAK,EAAE,OAAO,EAAE,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,OAAO,EAAE,CAAC,CAAC;SACzE;QACD,IAAI,CAAC,GAAG,EAAE,CAAC;IACb,CAAC;;AAlbH,oDAmbC;AAlbiB,8BAAS,GAAG,OAAO,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  trace,\n  context,\n  SpanKind,\n  Span,\n  SpanStatusCode,\n} from '@opentelemetry/api';\nimport {\n  isWrapped,\n  InstrumentationBase,\n  InstrumentationNodeModuleDefinition,\n  InstrumentationNodeModuleFile,\n} from '@opentelemetry/instrumentation';\nimport { getClientAttributes } from './utils';\nimport { defaultDbStatementSerializer } from '@opentelemetry/redis-common';\nimport { RedisInstrumentationConfig } from './types';\n/** @knipignore */\nimport { PACKAGE_NAME, PACKAGE_VERSION } from './version';\nimport { SEMATTRS_DB_STATEMENT } from '@opentelemetry/semantic-conventions';\nimport type { MultiErrorReply } from './internal-types';\n\nconst OTEL_OPEN_SPANS = Symbol(\n  'opentelemetry.instrumentation.redis.open_spans'\n);\nconst MULTI_COMMAND_OPTIONS = Symbol(\n  'opentelemetry.instrumentation.redis.multi_command_options'\n);\n\ninterface MutliCommandInfo {\n  span: Span;\n  commandName: string;\n  commandArgs: Array<string | Buffer>;\n}\n\nconst DEFAULT_CONFIG: RedisInstrumentationConfig = {\n  requireParentSpan: false,\n};\n\nexport class RedisInstrumentation extends InstrumentationBase<RedisInstrumentationConfig> {\n  static readonly COMPONENT = 'redis';\n\n  constructor(config: RedisInstrumentationConfig = {}) {\n    super(PACKAGE_NAME, PACKAGE_VERSION, { ...DEFAULT_CONFIG, ...config });\n  }\n\n  override setConfig(config: RedisInstrumentationConfig = {}) {\n    super.setConfig({ ...DEFAULT_CONFIG, ...config });\n  }\n\n  protected init() {\n    // @node-redis/client is a new package introduced and consumed by 'redis 4.0.x'\n    // on redis@4.1.0 it was changed to @redis/client.\n    // we will instrument both packages\n    return [\n      this._getInstrumentationNodeModuleDefinition('@redis/client'),\n      this._getInstrumentationNodeModuleDefinition('@node-redis/client'),\n    ];\n  }\n\n  private _getInstrumentationNodeModuleDefinition(\n    basePackageName: string\n  ): InstrumentationNodeModuleDefinition {\n    const commanderModuleFile = new InstrumentationNodeModuleFile(\n      `${basePackageName}/dist/lib/commander.js`,\n      ['^1.0.0'],\n      (moduleExports: any, moduleVersion?: string) => {\n        const transformCommandArguments =\n          moduleExports.transformCommandArguments;\n        if (!transformCommandArguments) {\n          this._diag.error(\n            'internal instrumentation error, missing transformCommandArguments function'\n          );\n          return moduleExports;\n        }\n\n        // function name and signature changed in redis 4.1.0 from 'extendWithCommands' to 'attachCommands'\n        // the matching internal package names starts with 1.0.x (for redis 4.0.x)\n        const functionToPatch = moduleVersion?.startsWith('1.0.')\n          ? 'extendWithCommands'\n          : 'attachCommands';\n        // this is the function that extend a redis client with a list of commands.\n        // the function patches the commandExecutor to record a span\n        if (isWrapped(moduleExports?.[functionToPatch])) {\n          this._unwrap(moduleExports, functionToPatch);\n        }\n        this._wrap(\n          moduleExports,\n          functionToPatch,\n          this._getPatchExtendWithCommands(transformCommandArguments)\n        );\n\n        return moduleExports;\n      },\n      (moduleExports: any) => {\n        if (isWrapped(moduleExports?.extendWithCommands)) {\n          this._unwrap(moduleExports, 'extendWithCommands');\n        }\n        if (isWrapped(moduleExports?.attachCommands)) {\n          this._unwrap(moduleExports, 'attachCommands');\n        }\n      }\n    );\n\n    const multiCommanderModule = new InstrumentationNodeModuleFile(\n      `${basePackageName}/dist/lib/client/multi-command.js`,\n      ['^1.0.0'],\n      (moduleExports: any) => {\n        const redisClientMultiCommandPrototype =\n          moduleExports?.default?.prototype;\n\n        if (isWrapped(redisClientMultiCommandPrototype?.exec)) {\n          this._unwrap(redisClientMultiCommandPrototype, 'exec');\n        }\n        this._wrap(\n          redisClientMultiCommandPrototype,\n          'exec',\n          this._getPatchMultiCommandsExec()\n        );\n\n        if (isWrapped(redisClientMultiCommandPrototype?.addCommand)) {\n          this._unwrap(redisClientMultiCommandPrototype, 'addCommand');\n        }\n        this._wrap(\n          redisClientMultiCommandPrototype,\n          'addCommand',\n          this._getPatchMultiCommandsAddCommand()\n        );\n\n        return moduleExports;\n      },\n      (moduleExports: any) => {\n        const redisClientMultiCommandPrototype =\n          moduleExports?.default?.prototype;\n        if (isWrapped(redisClientMultiCommandPrototype?.exec)) {\n          this._unwrap(redisClientMultiCommandPrototype, 'exec');\n        }\n        if (isWrapped(redisClientMultiCommandPrototype?.addCommand)) {\n          this._unwrap(redisClientMultiCommandPrototype, 'addCommand');\n        }\n      }\n    );\n\n    const clientIndexModule = new InstrumentationNodeModuleFile(\n      `${basePackageName}/dist/lib/client/index.js`,\n      ['^1.0.0'],\n      (moduleExports: any) => {\n        const redisClientPrototype = moduleExports?.default?.prototype;\n\n        // In some @redis/client versions 'multi' is a method. In later\n        // versions, as of https://github.com/redis/node-redis/pull/2324,\n        // 'MULTI' is a method and 'multi' is a property defined in the\n        // constructor that points to 'MULTI', and therefore it will not\n        // be defined on the prototype.\n        if (redisClientPrototype?.multi) {\n          if (isWrapped(redisClientPrototype?.multi)) {\n            this._unwrap(redisClientPrototype, 'multi');\n          }\n          this._wrap(\n            redisClientPrototype,\n            'multi',\n            this._getPatchRedisClientMulti()\n          );\n        }\n        if (redisClientPrototype?.MULTI) {\n          if (isWrapped(redisClientPrototype?.MULTI)) {\n            this._unwrap(redisClientPrototype, 'MULTI');\n          }\n          this._wrap(\n            redisClientPrototype,\n            'MULTI',\n            this._getPatchRedisClientMulti()\n          );\n        }\n\n        if (isWrapped(redisClientPrototype?.sendCommand)) {\n          this._unwrap(redisClientPrototype, 'sendCommand');\n        }\n        this._wrap(\n          redisClientPrototype,\n          'sendCommand',\n          this._getPatchRedisClientSendCommand()\n        );\n\n        this._wrap(\n          redisClientPrototype,\n          'connect',\n          this._getPatchedClientConnect()\n        );\n\n        return moduleExports;\n      },\n      (moduleExports: any) => {\n        const redisClientPrototype = moduleExports?.default?.prototype;\n        if (isWrapped(redisClientPrototype?.multi)) {\n          this._unwrap(redisClientPrototype, 'multi');\n        }\n        if (isWrapped(redisClientPrototype?.MULTI)) {\n          this._unwrap(redisClientPrototype, 'MULTI');\n        }\n        if (isWrapped(redisClientPrototype?.sendCommand)) {\n          this._unwrap(redisClientPrototype, 'sendCommand');\n        }\n      }\n    );\n\n    return new InstrumentationNodeModuleDefinition(\n      basePackageName,\n      ['^1.0.0'],\n      (moduleExports: any) => {\n        return moduleExports;\n      },\n      () => {},\n      [commanderModuleFile, multiCommanderModule, clientIndexModule]\n    );\n  }\n\n  // serves both for redis 4.0.x where function name is extendWithCommands\n  // and redis ^4.1.0 where function name is attachCommands\n  private _getPatchExtendWithCommands(transformCommandArguments: Function) {\n    const plugin = this;\n    return function extendWithCommandsPatchWrapper(original: Function) {\n      return function extendWithCommandsPatch(this: any, config: any) {\n        if (config?.BaseClass?.name !== 'RedisClient') {\n          return original.apply(this, arguments);\n        }\n\n        const origExecutor = config.executor;\n        config.executor = function (\n          this: any,\n          command: any,\n          args: Array<string | Buffer>\n        ) {\n          const redisCommandArguments = transformCommandArguments(\n            command,\n            args\n          ).args;\n          return plugin._traceClientCommand(\n            origExecutor,\n            this,\n            arguments,\n            redisCommandArguments\n          );\n        };\n        return original.apply(this, arguments);\n      };\n    };\n  }\n\n  private _getPatchMultiCommandsExec() {\n    const plugin = this;\n    return function execPatchWrapper(original: Function) {\n      return function execPatch(this: any) {\n        const execRes = original.apply(this, arguments);\n        if (typeof execRes?.then !== 'function') {\n          plugin._diag.error(\n            'got non promise result when patching RedisClientMultiCommand.exec'\n          );\n          return execRes;\n        }\n\n        return execRes\n          .then((redisRes: unknown[]) => {\n            const openSpans = this[OTEL_OPEN_SPANS];\n            plugin._endSpansWithRedisReplies(openSpans, redisRes);\n            return redisRes;\n          })\n          .catch((err: Error) => {\n            const openSpans = this[OTEL_OPEN_SPANS];\n            if (!openSpans) {\n              plugin._diag.error(\n                'cannot find open spans to end for redis multi command'\n              );\n            } else {\n              const replies =\n                err.constructor.name === 'MultiErrorReply'\n                  ? (err as MultiErrorReply).replies\n                  : new Array(openSpans.length).fill(err);\n              plugin._endSpansWithRedisReplies(openSpans, replies);\n            }\n            return Promise.reject(err);\n          });\n      };\n    };\n  }\n\n  private _getPatchMultiCommandsAddCommand() {\n    const plugin = this;\n    return function addCommandWrapper(original: Function) {\n      return function addCommandPatch(this: any, args: Array<string | Buffer>) {\n        return plugin._traceClientCommand(original, this, arguments, args);\n      };\n    };\n  }\n\n  private _getPatchRedisClientMulti() {\n    return function multiPatchWrapper(original: Function) {\n      return function multiPatch(this: any) {\n        const multiRes = original.apply(this, arguments);\n        multiRes[MULTI_COMMAND_OPTIONS] = this.options;\n        return multiRes;\n      };\n    };\n  }\n\n  private _getPatchRedisClientSendCommand() {\n    const plugin = this;\n    return function sendCommandWrapper(original: Function) {\n      return function sendCommandPatch(\n        this: any,\n        args: Array<string | Buffer>\n      ) {\n        return plugin._traceClientCommand(original, this, arguments, args);\n      };\n    };\n  }\n\n  private _getPatchedClientConnect() {\n    const plugin = this;\n    return function connectWrapper(original: Function) {\n      return function patchedConnect(this: any): Promise<void> {\n        const options = this.options;\n\n        const attributes = getClientAttributes(plugin._diag, options);\n\n        const span = plugin.tracer.startSpan(\n          `${RedisInstrumentation.COMPONENT}-connect`,\n          {\n            kind: SpanKind.CLIENT,\n            attributes,\n          }\n        );\n\n        const res = context.with(trace.setSpan(context.active(), span), () => {\n          return original.apply(this);\n        });\n\n        return res\n          .then((result: unknown) => {\n            span.end();\n            return result;\n          })\n          .catch((error: Error) => {\n            span.recordException(error);\n            span.setStatus({\n              code: SpanStatusCode.ERROR,\n              message: error.message,\n            });\n            span.end();\n            return Promise.reject(error);\n          });\n      };\n    };\n  }\n\n  private _traceClientCommand(\n    origFunction: Function,\n    origThis: any,\n    origArguments: IArguments,\n    redisCommandArguments: Array<string | Buffer>\n  ) {\n    const hasNoParentSpan = trace.getSpan(context.active()) === undefined;\n    if (hasNoParentSpan && this.getConfig().requireParentSpan) {\n      return origFunction.apply(origThis, origArguments);\n    }\n\n    const clientOptions = origThis.options || origThis[MULTI_COMMAND_OPTIONS];\n\n    const commandName = redisCommandArguments[0] as string; // types also allows it to be a Buffer, but in practice it only string\n    const commandArgs = redisCommandArguments.slice(1);\n\n    const dbStatementSerializer =\n      this.getConfig().dbStatementSerializer || defaultDbStatementSerializer;\n\n    const attributes = getClientAttributes(this._diag, clientOptions);\n\n    try {\n      const dbStatement = dbStatementSerializer(commandName, commandArgs);\n      if (dbStatement != null) {\n        attributes[SEMATTRS_DB_STATEMENT] = dbStatement;\n      }\n    } catch (e) {\n      this._diag.error('dbStatementSerializer throw an exception', e, {\n        commandName,\n      });\n    }\n\n    const span = this.tracer.startSpan(\n      `${RedisInstrumentation.COMPONENT}-${commandName}`,\n      {\n        kind: SpanKind.CLIENT,\n        attributes,\n      }\n    );\n\n    const res = context.with(trace.setSpan(context.active(), span), () => {\n      return origFunction.apply(origThis, origArguments);\n    });\n    if (typeof res?.then === 'function') {\n      res.then(\n        (redisRes: unknown) => {\n          this._endSpanWithResponse(\n            span,\n            commandName,\n            commandArgs,\n            redisRes,\n            undefined\n          );\n        },\n        (err: any) => {\n          this._endSpanWithResponse(span, commandName, commandArgs, null, err);\n        }\n      );\n    } else {\n      const redisClientMultiCommand = res as {\n        [OTEL_OPEN_SPANS]?: Array<MutliCommandInfo>;\n      };\n      redisClientMultiCommand[OTEL_OPEN_SPANS] =\n        redisClientMultiCommand[OTEL_OPEN_SPANS] || [];\n      redisClientMultiCommand[OTEL_OPEN_SPANS]!.push({\n        span,\n        commandName,\n        commandArgs,\n      });\n    }\n    return res;\n  }\n\n  private _endSpansWithRedisReplies(\n    openSpans: Array<MutliCommandInfo>,\n    replies: unknown[]\n  ) {\n    if (!openSpans) {\n      return this._diag.error(\n        'cannot find open spans to end for redis multi command'\n      );\n    }\n    if (replies.length !== openSpans.length) {\n      return this._diag.error(\n        'number of multi command spans does not match response from redis'\n      );\n    }\n    for (let i = 0; i < openSpans.length; i++) {\n      const { span, commandName, commandArgs } = openSpans[i];\n      const currCommandRes = replies[i];\n      const [res, err] =\n        currCommandRes instanceof Error\n          ? [null, currCommandRes]\n          : [currCommandRes, undefined];\n      this._endSpanWithResponse(span, commandName, commandArgs, res, err);\n    }\n  }\n\n  private _endSpanWithResponse(\n    span: Span,\n    commandName: string,\n    commandArgs: Array<string | Buffer>,\n    response: unknown,\n    error: Error | undefined\n  ) {\n    const { responseHook } = this.getConfig();\n    if (!error && responseHook) {\n      try {\n        responseHook(span, commandName, commandArgs, response);\n      } catch (err) {\n        this._diag.error('responseHook throw an exception', err);\n      }\n    }\n    if (error) {\n      span.recordException(error);\n      span.setStatus({ code: SpanStatusCode.ERROR, message: error?.message });\n    }\n    span.end();\n  }\n}\n"]}