{"name": "esast-util-from-estree", "version": "2.0.0", "description": "esast utility to transform from estree", "license": "MIT", "keywords": ["esast", "esast-util", "util", "utility", "recma", "esast", "estree", "javascript", "ecmascript", "tree", "ast", "transform"], "repository": "syntax-tree/esast-util-from-estree", "bugs": "https://github.com/syntax-tree/esast-util-from-estree/issues", "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}, "author": "<PERSON> <<EMAIL>> (https://wooorm.com)", "contributors": ["<PERSON> <<EMAIL>> (https://wooorm.com)"], "sideEffects": false, "type": "module", "exports": "./index.js", "files": ["lib/", "index.js", "index.d.ts"], "dependencies": {"@types/estree-jsx": "^1.0.0", "devlop": "^1.0.0", "estree-util-visit": "^2.0.0", "unist-util-position-from-estree": "^2.0.0"}, "devDependencies": {"@types/node": "^20.0.0", "acorn": "^8.0.0", "acorn-jsx": "^5.0.0", "c8": "^8.0.0", "prettier": "^3.0.0", "remark-cli": "^11.0.0", "remark-preset-wooorm": "^9.0.0", "type-coverage": "^2.0.0", "typescript": "^5.0.0", "xo": "^0.55.0"}, "scripts": {"prepack": "npm run build && npm run format", "build": "tsc --build --clean && tsc --build && type-coverage", "format": "remark . -qfo && prettier . -w --log-level warn && xo --fix", "test-api": "node --conditions development test.js", "test-coverage": "c8 --100 --reporter lcov npm run test-api", "test": "npm run build && npm run format && npm run test-coverage"}, "prettier": {"bracketSpacing": false, "semi": false, "singleQuote": true, "tabWidth": 2, "trailingComma": "none", "useTabs": false}, "remarkConfig": {"plugins": ["remark-preset-wooorm"]}, "typeCoverage": {"atLeast": 100, "detail": true, "ignoreCatch": true, "strict": true}, "xo": {"prettier": true, "rules": {"unicorn/prefer-at": "off", "unicorn/prefer-code-point": "off"}}}