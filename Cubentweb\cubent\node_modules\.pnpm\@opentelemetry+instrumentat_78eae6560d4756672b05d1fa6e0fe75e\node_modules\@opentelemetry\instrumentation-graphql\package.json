{"name": "@opentelemetry/instrumentation-graphql", "version": "0.47.1", "description": "OpenTelemetry instrumentation for `graphql` gql query language and runtime for GraphQL", "main": "build/src/index.js", "types": "build/src/index.d.ts", "repository": "open-telemetry/opentelemetry-js-contrib", "scripts": {"clean": "rimraf build/*", "compile": "tsc -p .", "lint": "eslint . --ext .ts", "lint:fix": "eslint . --ext .ts --fix", "lint:readme": "node ../../../scripts/lint-readme.js", "prewatch": "npm run precompile", "prepublishOnly": "npm run compile", "test": "nyc mocha 'test/**/*.test.ts'", "test-all-versions": "tav", "tdd": "npm run test -- --watch-extensions ts --watch", "version:update": "node ../../../scripts/version-update.js", "watch": "tsc -w"}, "keywords": ["graphql", "metrics", "nodejs", "opentelemetry", "stats", "tracing"], "author": "OpenTelemetry Authors", "license": "Apache-2.0", "engines": {"node": ">=14"}, "files": ["build/src/**/*.js", "build/src/**/*.js.map", "build/src/**/*.d.ts"], "publishConfig": {"access": "public"}, "peerDependencies": {"@opentelemetry/api": "^1.3.0"}, "devDependencies": {"@opentelemetry/api": "^1.3.0", "@opentelemetry/sdk-trace-base": "^1.8.0", "@opentelemetry/semantic-conventions": "^1.27.0", "@types/mocha": "8.2.3", "@types/node": "18.18.14", "graphql": "^16.5.0", "nyc": "15.1.0", "rimraf": "5.0.10", "test-all-versions": "6.1.0", "typescript": "4.4.4"}, "dependencies": {"@opentelemetry/instrumentation": "^0.57.1"}, "homepage": "https://github.com/open-telemetry/opentelemetry-js-contrib/tree/main/plugins/node/opentelemetry-instrumentation-graphql#readme", "gitHead": "1eb77007669bae87fe5664d68ba6533b95275d52"}