// This file was generated by basehub. Do not edit directly. Read more: https://basehub.com/docs/api-reference/basehub-sdk

/* eslint-disable */
/* eslint-disable eslint-comments/no-restricted-disable */
/* tslint:disable */

import "./chunk-YSQDPG26.js";

// ../../node_modules/.pnpm/basehub@8.2.7_@babel+runtim_3aebfa8e064bb601162c04844d38dd02/node_modules/basehub/src/next/toolbar/server-toolbar.tsx
import * as React from "react";
import { draftMode } from "next/headers";
import { revalidateTag } from "next/cache";
import {
  getStuffFromEnv,
  resolvedRef
} from "../index";
var LazyClientConditionalRenderer = React.lazy(
  () => import("./client-conditional-renderer-KQINRCBN.js").then((mod) => ({
    default: mod.ClientConditionalRenderer
  }))
);
var ServerToolbar = async ({
  ...basehubProps
}) => {
  const { isForcedDraft } = getStuffFromEnv(basehubProps);
  const enableDraftMode_unbound = async (basehubProps2, { bshbPreviewToken }) => {
    "use server";
    try {
      const { headers, url } = getStuffFromEnv(basehubProps2);
      const appApiEndpoint = getBaseHubAppApiEndpoint(
        url,
        "/api/nextjs/preview-auth"
      );
      const res = await fetch(appApiEndpoint, {
        cache: "no-store",
        method: "POST",
        headers: {
          "content-type": "application/json",
          "x-basehub-token": headers["x-basehub-token"]
        },
        body: JSON.stringify({ bshbPreview: bshbPreviewToken })
      });
      const responseIsJson = res.headers.get("content-type")?.includes("json");
      if (!responseIsJson) {
        return { status: 400, response: { error: "Bad request" } };
      }
      const response = await res.json();
      if (res.status === 200)
        (await draftMode()).enable();
      return { status: res.status, response };
    } catch (error) {
      return { status: 500, response: { error: "Something went wrong" } };
    }
  };
  const getLatestBranches_unbound = async (basehubProps2, { bshbPreviewToken }) => {
    "use server";
    try {
      const { headers, url, isForcedDraft: isForcedDraft2 } = getStuffFromEnv(basehubProps2);
      if ((await draftMode()).isEnabled === false && !isForcedDraft2 && !bshbPreviewToken) {
        return { status: 403, response: { error: "Unauthorized" } };
      }
      const appApiEndpoint = getBaseHubAppApiEndpoint(
        url,
        "/api/nextjs/latest-branches"
      );
      const res = await fetch(appApiEndpoint, {
        cache: "no-store",
        method: "GET",
        headers: {
          "content-type": "application/json",
          "x-basehub-token": headers["x-basehub-token"],
          ...bshbPreviewToken && {
            "x-basehub-preview-token": bshbPreviewToken
          },
          ...isForcedDraft2 && {
            "x-basehub-forced-draft": "true"
          }
        }
      });
      const responseIsJson = res.headers.get("content-type")?.includes("json");
      if (!responseIsJson) {
        return { status: 400, response: { error: "Bad request" } };
      }
      const response = await res.json();
      return { status: res.status, response };
    } catch (error) {
      return { status: 500, response: { error: "Something went wrong" } };
    }
  };
  const disableDraftMode = async () => {
    "use server";
    (await draftMode()).disable();
  };
  const revalidateTags_unbound = async (basehubProps2, {
    bshbPreviewToken,
    ref
  }) => {
    "use server";
    const { headers, url } = getStuffFromEnv(basehubProps2);
    const appApiEndpoint = getBaseHubAppApiEndpoint(
      url,
      "/api/nextjs/pending-tags"
    );
    if (!bshbPreviewToken) {
      return { success: false, error: "Unauthorized" };
    }
    const res = await fetch(appApiEndpoint, {
      cache: "no-store",
      method: "GET",
      headers: {
        "content-type": "application/json",
        "x-basehub-token": headers["x-basehub-token"],
        "x-basehub-ref": ref || headers["x-basehub-ref"],
        "x-basehub-preview-token": bshbPreviewToken,
        "x-basehub-sdk-build-id": headers["x-basehub-sdk-build-id"]
      }
    });
    if (res.status !== 200) {
      return {
        success: false,
        message: `Received status ${res.status} from server`
      };
    }
    const response = await res.json();
    try {
      const { tags } = response;
      if (!tags || !Array.isArray(tags) || tags.length === 0) {
        return { success: true, message: "No tags to revalidate" };
      }
      await Promise.all(
        tags.map(async (_tag) => {
          const tag = _tag.startsWith("basehub-") ? _tag : `basehub-${_tag}`;
          await revalidateTag(tag);
        })
      );
      return { success: true, message: `Revalidated ${tags.length} tags` };
    } catch (error) {
      console.log(response);
      console.error(error);
      return {
        success: false,
        message: "Something went wrong while revalidating tags"
      };
    }
  };
  const enableDraftMode = enableDraftMode_unbound.bind(null, basehubProps);
  const getLatestBranches = getLatestBranches_unbound.bind(null, basehubProps);
  const revalidateTags = revalidateTags_unbound.bind(null, basehubProps);
  return /* @__PURE__ */ React.createElement(
    LazyClientConditionalRenderer,
    {
      draft: (await draftMode()).isEnabled,
      isForcedDraft,
      enableDraftMode,
      disableDraftMode,
      revalidateTags,
      getLatestBranches,
      resolvedRef
    }
  );
};
function getBaseHubAppApiEndpoint(url, pathname) {
  let origin;
  switch (true) {
    case url.origin.includes("api.basehub.com"):
      origin = "https://basehub.com" + pathname + url.search + url.hash;
      break;
    case url.origin.includes("api.bshb.dev"):
      origin = "https://basehub.dev" + pathname + url.search + url.hash;
      break;
    case url.origin.includes("localhost:3001"):
      origin = "http://localhost:3000" + pathname + url.search + url.hash;
      break;
    default:
      origin = url.origin + pathname + url.search + url.hash;
  }
  return origin;
}
export {
  ServerToolbar as Toolbar
};
