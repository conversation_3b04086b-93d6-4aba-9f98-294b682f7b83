{"version": 3, "file": "amqplib.js", "sourceRoot": "", "sources": ["../../src/amqplib.ts"], "names": [], "mappings": ";;;AAAA;;;;;;;;;;;;;;GAcG;AACH,4CAW4B;AAC5B,8CAI6B;AAC7B,oEAMwC;AACxC,8EAS6C;AAQ7C,mCAIiB;AACjB,mCAciB;AACjB,kBAAkB;AAClB,uCAA0D;AAE1D,MAAM,iBAAiB,GAAG,CAAC,YAAY,CAAC,CAAC;AAEzC,MAAa,sBAAuB,SAAQ,qCAAiD;IAC3F,YAAY,SAAuC,EAAE;QACnD,KAAK,CAAC,sBAAY,EAAE,yBAAe,kCAAO,sBAAc,GAAK,MAAM,EAAG,CAAC;IACzE,CAAC;IAEQ,SAAS,CAAC,SAAuC,EAAE;QAC1D,KAAK,CAAC,SAAS,iCAAM,sBAAc,GAAK,MAAM,EAAG,CAAC;IACpD,CAAC;IAES,IAAI;QACZ,MAAM,sBAAsB,GAAG,IAAI,+CAA6B,CAC9D,8BAA8B,EAC9B,iBAAiB,EACjB,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,EACjC,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,CACpC,CAAC;QAEF,MAAM,uBAAuB,GAAG,IAAI,+CAA6B,CAC/D,+BAA+B,EAC/B,iBAAiB,EACjB,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,EACjC,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,CACpC,CAAC;QAEF,MAAM,iBAAiB,GAAG,IAAI,+CAA6B,CACzD,wBAAwB,EACxB,iBAAiB,EACjB,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,EAC5B,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAC/B,CAAC;QAEF,MAAM,MAAM,GAAG,IAAI,qDAAmC,CACpD,SAAS,EACT,iBAAiB,EACjB,SAAS,EACT,SAAS,EACT,CAAC,sBAAsB,EAAE,iBAAiB,EAAE,uBAAuB,CAAC,CACrE,CAAC;QACF,OAAO,MAAM,CAAC;IAChB,CAAC;IAEO,YAAY,CAAC,aAAkB;QACrC,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC;QACnD,IAAI,CAAC,IAAA,2BAAS,EAAC,aAAa,CAAC,OAAO,CAAC,EAAE;YACrC,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE,SAAS,EAAE,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;SACvE;QACD,OAAO,aAAa,CAAC;IACvB,CAAC;IAEO,cAAc,CAAC,aAAkB;QACvC,IAAI,IAAA,2BAAS,EAAC,aAAa,CAAC,OAAO,CAAC,EAAE;YACpC,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,SAAS,CAAC,CAAC;SACxC;QACD,OAAO,aAAa,CAAC;IACvB,CAAC;IAEO,iBAAiB,CACvB,aAAkB,EAClB,aAAiC;QAEjC,IAAI,CAAC,IAAA,2BAAS,EAAC,aAAa,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE;YACvD,IAAI,CAAC,KAAK,CACR,aAAa,CAAC,OAAO,CAAC,SAAS,EAC/B,SAAS,EACT,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,EAAE,aAAa,CAAC,CAC/C,CAAC;SACH;QACD,IAAI,CAAC,IAAA,2BAAS,EAAC,aAAa,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE;YACvD,IAAI,CAAC,KAAK,CACR,aAAa,CAAC,OAAO,CAAC,SAAS,EAC/B,SAAS,EACT,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,EAAE,aAAa,CAAC,CAC/C,CAAC;SACH;QACD,IAAI,CAAC,IAAA,2BAAS,EAAC,aAAa,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE;YACnD,IAAI,CAAC,KAAK,CACR,aAAa,CAAC,OAAO,CAAC,SAAS,EAC/B,KAAK,EACL,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,oBAAY,CAAC,GAAG,CAAC,CACrD,CAAC;SACH;QACD,IAAI,CAAC,IAAA,2BAAS,EAAC,aAAa,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE;YACpD,IAAI,CAAC,KAAK,CACR,aAAa,CAAC,OAAO,CAAC,SAAS,EAC/B,MAAM,EACN,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,oBAAY,CAAC,IAAI,CAAC,CACrD,CAAC;SACH;QACD,IAAI,CAAC,IAAA,2BAAS,EAAC,aAAa,CAAC,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE;YACtD,IAAI,CAAC,KAAK,CACR,aAAa,CAAC,OAAO,CAAC,SAAS,EAC/B,QAAQ,EACR,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,oBAAY,CAAC,MAAM,CAAC,CACvD,CAAC;SACH;QACD,IAAI,CAAC,IAAA,2BAAS,EAAC,aAAa,CAAC,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE;YACtD,IAAI,CAAC,KAAK,CACR,aAAa,CAAC,OAAO,CAAC,SAAS,EAC/B,QAAQ,EACR,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,oBAAY,CAAC,MAAM,CAAC,CAC3D,CAAC;SACH;QACD,IAAI,CAAC,IAAA,2BAAS,EAAC,aAAa,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE;YACvD,IAAI,CAAC,KAAK,CACR,aAAa,CAAC,OAAO,CAAC,SAAS,EAC/B,SAAS,EACT,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,oBAAY,CAAC,OAAO,CAAC,CAC3D,CAAC;SACH;QACD,IAAI,CAAC,IAAA,2BAAS,EAAC,aAAa,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE;YACpD,IAAI,CAAC,KAAK,CACR,aAAa,CAAC,OAAO,CAAC,SAAS,EAC/B,MAAM,EACN,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,CACpC,CAAC;SACH;QACD,IAAI,CAAC,IAAA,2BAAS,EAAC,aAAa,CAAC,cAAc,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE;YAC9D,IAAI,CAAC,KAAK,CACR,aAAa,CAAC,cAAc,CAAC,SAAS,EACtC,SAAS,EACT,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,IAAI,EAAE,aAAa,CAAC,CACxD,CAAC;SACH;QACD,OAAO,aAAa,CAAC;IACvB,CAAC;IAEO,mBAAmB,CAAC,aAAkB;QAC5C,IAAI,IAAA,2BAAS,EAAC,aAAa,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE;YACtD,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,OAAO,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;SAC1D;QACD,IAAI,IAAA,2BAAS,EAAC,aAAa,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE;YACtD,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,OAAO,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;SAC1D;QACD,IAAI,IAAA,2BAAS,EAAC,aAAa,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE;YAClD,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,OAAO,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;SACtD;QACD,IAAI,IAAA,2BAAS,EAAC,aAAa,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE;YACnD,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,OAAO,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;SACvD;QACD,IAAI,IAAA,2BAAS,EAAC,aAAa,CAAC,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE;YACrD,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,OAAO,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;SACzD;QACD,IAAI,IAAA,2BAAS,EAAC,aAAa,CAAC,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE;YACrD,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,OAAO,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;SACzD;QACD,IAAI,IAAA,2BAAS,EAAC,aAAa,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE;YACtD,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,OAAO,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;SAC1D;QACD,IAAI,IAAA,2BAAS,EAAC,aAAa,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE;YACnD,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,OAAO,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;SACvD;QACD,IAAI,IAAA,2BAAS,EAAC,aAAa,CAAC,cAAc,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE;YAC7D,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,cAAc,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;SACjE;QACD,OAAO,aAAa,CAAC;IACvB,CAAC;IAEO,eAAe,CACrB,QAIe;QAEf,OAAO,SAAS,cAAc,CAE5B,GAA6B,EAC7B,aAAkB,EAClB,YAAsB;YAEtB,OAAO,QAAQ,CAAC,IAAI,CAClB,IAAI,EACJ,GAAG,EACH,aAAa,EACb,UAAyB,GAAG,EAAE,IAAgB;gBAC5C,IAAI,GAAG,IAAI,IAAI,EAAE;oBACf,MAAM,aAAa,GAAG,IAAA,sCAA8B,EAAC,GAAG,CAAC,CAAC;oBAC1D,8FAA8F;oBAC9F,uEAAuE;oBACvE,2FAA2F;oBAC3F,wBAAwB;oBACxB,8CAA8C;oBAC9C,MAAM,gBAAgB,GAAG,IAAA,yCAAiC,EACxD,IAAW,CACZ,CAAC;oBACD,IAAY,CAAC,6BAAqB,CAAC,mCAC/B,aAAa,GACb,gBAAgB,CACpB,CAAC;iBACH;gBACD,YAAY,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;YACtC,CAAC,CACF,CAAC;QACJ,CAAC,CAAC;IACJ,CAAC;IAEO,mBAAmB,CAAC,QAAkB;QAC5C,MAAM,IAAI,GAAG,IAAI,CAAC;QAClB,OAAO,SAAS,IAAI,CAElB,SAAiB;YAEjB,IAAI,SAAS,KAAK,OAAO,EAAE;gBACzB,IAAI,CAAC,oBAAoB,CACvB,IAAI,EACJ,IAAI,EACJ,oBAAY,CAAC,aAAa,EAC1B,SAAS,CACV,CAAC;gBACF,MAAM,WAAW,GAAG,IAAI,CAAC,qCAA6B,CAAC,CAAC;gBACxD,IAAI,WAAW,EAAE;oBACf,aAAa,CAAC,WAAW,CAAC,CAAC;iBAC5B;gBACD,IAAI,CAAC,qCAA6B,CAAC,GAAG,SAAS,CAAC;aACjD;iBAAM,IAAI,SAAS,KAAK,OAAO,EAAE;gBAChC,IAAI,CAAC,oBAAoB,CACvB,IAAI,EACJ,IAAI,EACJ,oBAAY,CAAC,YAAY,EACzB,SAAS,CACV,CAAC;aACH;YACD,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;QACzC,CAAC,CAAC;IACJ,CAAC;IAEO,cAAc,CACpB,UAAmB,EACnB,YAA0B,EAC1B,QAAkB;QAElB,MAAM,IAAI,GAAG,IAAI,CAAC;QAClB,OAAO,SAAS,MAAM,CAEpB,cAAwB;YAExB,IAAI,CAAC,oBAAoB,CAAC,IAAI,EAAE,UAAU,EAAE,YAAY,EAAE,cAAc,CAAC,CAAC;YAC1E,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;QACzC,CAAC,CAAC;IACJ,CAAC;IAEO,WAAW,CACjB,UAAmB,EACnB,YAA0B,EAC1B,QAAkB;QAElB,MAAM,IAAI,GAAG,IAAI,CAAC;QAClB,OAAO,SAAS,GAAG,CAEjB,OAAgB,EAChB,gBAA0B,EAC1B,OAAiB;;YAEjB,MAAM,OAAO,GAAG,IAAI,CAAC;YACrB,+EAA+E;YAC/E,MAAM,eAAe,GACnB,YAAY,KAAK,oBAAY,CAAC,MAAM,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,OAAO,CAAC;YAEpE,MAAM,aAAa,GACjB,MAAA,OAAO,CAAC,+BAAuB,CAAC,mCAAI,EAAE,CAAC;YACzC,MAAM,QAAQ,GAAG,aAAa,CAAC,SAAS,CACtC,UAAU,CAAC,EAAE,CAAC,UAAU,CAAC,GAAG,KAAK,OAAO,CACzC,CAAC;YACF,IAAI,QAAQ,GAAG,CAAC,EAAE;gBAChB,kCAAkC;gBAClC,+EAA+E;gBAC/E,IAAI,CAAC,eAAe,CAClB,OAAO,EACP,UAAU,EACV,YAAY,EACZ,eAAe,CAChB,CAAC;aACH;iBAAM,IAAI,YAAY,KAAK,oBAAY,CAAC,MAAM,IAAI,gBAAgB,EAAE;gBACnE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,QAAQ,EAAE,CAAC,EAAE,EAAE;oBAClC,IAAI,CAAC,eAAe,CAClB,aAAa,CAAC,CAAC,CAAC,CAAC,GAAG,EACpB,UAAU,EACV,YAAY,EACZ,eAAe,CAChB,CAAC;iBACH;gBACD,aAAa,CAAC,MAAM,CAAC,CAAC,EAAE,QAAQ,GAAG,CAAC,CAAC,CAAC;aACvC;iBAAM;gBACL,IAAI,CAAC,eAAe,CAClB,OAAO,EACP,UAAU,EACV,YAAY,EACZ,eAAe,CAChB,CAAC;gBACF,aAAa,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;aACnC;YACD,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;QACzC,CAAC,CAAC;IACJ,CAAC;IAEO,eAAe,CACrB,aAAiC,EACjC,QAAkB;QAElB,MAAM,IAAI,GAAG,IAAI,CAAC;QAClB,OAAO,SAAS,OAAO,CAErB,KAAa,EACb,SAA+C,EAC/C,OAAyB;YAEzB,MAAM,OAAO,GAAG,IAAI,CAAC;YACrB,IACE,CAAC,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,EAAE,+BAAuB,CAAC,EACvE;gBACA,MAAM,EAAE,gBAAgB,EAAE,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;gBAC9C,IAAI,gBAAgB,EAAE;oBACpB,MAAM,KAAK,GAAG,WAAW,CAAC,GAAG,EAAE;wBAC7B,IAAI,CAAC,4BAA4B,CAAC,OAAO,CAAC,CAAC;oBAC7C,CAAC,EAAE,gBAAgB,CAAC,CAAC;oBACrB,KAAK,CAAC,KAAK,EAAE,CAAC;oBACd,OAAO,CAAC,qCAA6B,CAAC,GAAG,KAAK,CAAC;iBAChD;gBACD,OAAO,CAAC,+BAAuB,CAAC,GAAG,EAAE,CAAC;aACvC;YAED,MAAM,gBAAgB,GAAG,UAEvB,GAAkC;;gBAElC,wEAAwE;gBACxE,gDAAgD;gBAChD,uEAAuE;gBACvE,IAAI,CAAC,GAAG,EAAE;oBACR,OAAO,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;iBAClC;gBAED,MAAM,OAAO,GAAG,MAAA,GAAG,CAAC,UAAU,CAAC,OAAO,mCAAI,EAAE,CAAC;gBAC7C,IAAI,aAAa,GAAwB,iBAAW,CAAC,OAAO,CAC1D,kBAAY,EACZ,OAAO,CACR,CAAC;gBACF,MAAM,QAAQ,GAAG,MAAA,GAAG,CAAC,MAAM,0CAAE,QAAQ,CAAC;gBACtC,IAAI,KAAyB,CAAC;gBAC9B,IAAI,IAAI,CAAC,OAAO,CAAC,kBAAkB,EAAE;oBACnC,MAAM,iBAAiB,GAAG,aAAa;wBACrC,CAAC,CAAC,MAAA,WAAK,CAAC,OAAO,CAAC,aAAa,CAAC,0CAAE,WAAW,EAAE;wBAC7C,CAAC,CAAC,SAAS,CAAC;oBACd,aAAa,GAAG,SAAS,CAAC;oBAC1B,IAAI,iBAAiB,EAAE;wBACrB,KAAK,GAAG;4BACN;gCACE,OAAO,EAAE,iBAAiB;6BAC3B;yBACF,CAAC;qBACH;iBACF;gBACD,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,CAChC,GAAG,KAAK,UAAU,EAClB;oBACE,IAAI,EAAE,cAAQ,CAAC,QAAQ;oBACvB,UAAU,kCACL,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,UAAU,0CAAG,6BAAqB,CAAC,KAC/C,CAAC,qDAA8B,CAAC,EAAE,QAAQ,EAC1C,CAAC,0DAAmC,CAAC,EACnC,2DAAoC,EACtC,CAAC,8DAAuC,CAAC,EAAE,MAAA,GAAG,CAAC,MAAM,0CAAE,UAAU,EACjE,CAAC,mDAA4B,CAAC,EAAE,uDAAgC,EAChE,CAAC,oDAA6B,CAAC,EAAE,GAAG,aAAH,GAAG,uBAAH,GAAG,CAAE,UAAU,CAAC,SAAS,EAC1D,CAAC,yDAAkC,CAAC,EAClC,GAAG,aAAH,GAAG,uBAAH,GAAG,CAAE,UAAU,CAAC,aAAa,GAChC;oBACD,KAAK;iBACN,EACD,aAAa,CACd,CAAC;gBAEF,MAAM,EAAE,WAAW,EAAE,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;gBACzC,IAAI,WAAW,EAAE;oBACf,IAAA,wCAAsB,EACpB,GAAG,EAAE,CAAC,WAAW,CAAC,IAAI,EAAE,EAAE,aAAa,EAAE,GAAG,EAAE,CAAC,EAC/C,CAAC,CAAC,EAAE;wBACF,IAAI,CAAC,EAAE;4BACL,UAAI,CAAC,KAAK,CAAC,6CAA6C,EAAE,CAAC,CAAC,CAAC;yBAC9D;oBACH,CAAC,EACD,IAAI,CACL,CAAC;iBACH;gBAED,IAAI,CAAC,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,KAAK,CAAA,EAAE;oBACnB,0EAA0E;oBAC1E,OAAO,CAAC,+BAAuB,CAAE,CAAC,IAAI,CAAC;wBACrC,GAAG;wBACH,aAAa,EAAE,IAAA,aAAM,GAAE;qBACxB,CAAC,CAAC;oBAEH,6EAA6E;oBAC7E,GAAG,CAAC,2BAAmB,CAAC,GAAG,IAAI,CAAC;iBACjC;gBACD,MAAM,UAAU,GAAY,aAAa;oBACvC,CAAC,CAAC,aAAa;oBACf,CAAC,CAAC,kBAAY,CAAC;gBACjB,aAAO,CAAC,IAAI,CAAC,WAAK,CAAC,OAAO,CAAC,UAAU,EAAE,IAAI,CAAC,EAAE,GAAG,EAAE;oBACjD,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;gBAC5B,CAAC,CAAC,CAAC;gBAEH,IAAI,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,KAAK,EAAE;oBAClB,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,GAAG,EAAE,KAAK,EAAE,oBAAY,CAAC,OAAO,CAAC,CAAC;oBAChE,IAAI,CAAC,GAAG,EAAE,CAAC;iBACZ;YACH,CAAC,CAAC;YACF,SAAS,CAAC,CAAC,CAAC,GAAG,gBAAgB,CAAC;YAChC,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;QACzC,CAAC,CAAC;IACJ,CAAC;IAEO,wBAAwB,CAC9B,aAAiC,EACjC,QAAkB;QAElB,MAAM,IAAI,GAAG,IAAI,CAAC;QAClB,OAAO,SAAS,gBAAgB,CAE9B,QAAgB,EAChB,UAAkB,EAClB,OAAe,EACf,OAAyB,EACzB,QAAgD;YAEhD,MAAM,OAAO,GAAG,IAAI,CAAC;YACrB,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,GAAG,IAAI,CAAC,iBAAiB,CACtD,IAAI,EACJ,QAAQ,EACR,UAAU,EACV,OAAO,EACP,OAAO,CACR,CAAC;YAEF,MAAM,EAAE,WAAW,EAAE,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;YACzC,IAAI,WAAW,EAAE;gBACf,IAAA,wCAAsB,EACpB,GAAG,EAAE,CACH,WAAW,CAAC,IAAI,EAAE;oBAChB,aAAa;oBACb,QAAQ;oBACR,UAAU;oBACV,OAAO;oBACP,OAAO,EAAE,eAAe;oBACxB,gBAAgB,EAAE,IAAI;iBACvB,CAAC,EACJ,CAAC,CAAC,EAAE;oBACF,IAAI,CAAC,EAAE;wBACL,UAAI,CAAC,KAAK,CAAC,4CAA4C,EAAE,CAAC,CAAC,CAAC;qBAC7D;gBACH,CAAC,EACD,IAAI,CACL,CAAC;aACH;YAED,MAAM,gBAAgB,GAAG,UAEvB,GAAQ,EACR,EAAiB;gBAEjB,IAAI;oBACF,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,IAAI,CAAC,IAAI,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC;iBAC/B;wBAAS;oBACR,MAAM,EAAE,kBAAkB,EAAE,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;oBAChD,IAAI,kBAAkB,EAAE;wBACtB,IAAA,wCAAsB,EACpB,GAAG,EAAE,CACH,kBAAkB,CAAC,IAAI,EAAE;4BACvB,aAAa;4BACb,QAAQ;4BACR,UAAU;4BACV,OAAO;4BACP,OAAO;4BACP,gBAAgB,EAAE,IAAI;4BACtB,YAAY,EAAE,GAAG;yBAClB,CAAC,EACJ,CAAC,CAAC,EAAE;4BACF,IAAI,CAAC,EAAE;gCACL,UAAI,CAAC,KAAK,CACR,mDAAmD,EACnD,CAAC,CACF,CAAC;6BACH;wBACH,CAAC,EACD,IAAI,CACL,CAAC;qBACH;oBAED,IAAI,GAAG,EAAE;wBACP,IAAI,CAAC,SAAS,CAAC;4BACb,IAAI,EAAE,oBAAc,CAAC,KAAK;4BAC1B,OAAO,EAAE,uCAAuC;yBACjD,CAAC,CAAC;qBACJ;oBACD,IAAI,CAAC,GAAG,EAAE,CAAC;iBACZ;YACH,CAAC,CAAC;YAEF,4HAA4H;YAC5H,qCAAqC;YACrC,MAAM,aAAa,GAAG,IAAA,iCAAyB,EAAC,aAAO,CAAC,MAAM,EAAE,CAAC,CAAC;YAClE,MAAM,aAAa,GAAG,CAAC,GAAG,SAAS,CAAC,CAAC;YACrC,aAAa,CAAC,CAAC,CAAC,GAAG,eAAe,CAAC;YACnC,aAAa,CAAC,CAAC,CAAC,GAAG,aAAO,CAAC,IAAI,CAC7B,IAAA,mCAA2B,EAAC,WAAK,CAAC,OAAO,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC,EAC/D,gBAAgB,CACjB,CAAC;YACF,OAAO,aAAO,CAAC,IAAI,CAAC,aAAa,EAAE,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,aAAa,CAAC,CAAC,CAAC;QAC5E,CAAC,CAAC;IACJ,CAAC;IAEO,eAAe,CACrB,aAAiC,EACjC,QAAkB;QAElB,MAAM,IAAI,GAAG,IAAI,CAAC;QAClB,OAAO,SAAS,OAAO,CAErB,QAAgB,EAChB,UAAkB,EAClB,OAAe,EACf,OAAyB;YAEzB,IAAI,IAAA,+BAAuB,EAAC,aAAO,CAAC,MAAM,EAAE,CAAC,EAAE;gBAC7C,oBAAoB;gBACpB,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;aACxC;iBAAM;gBACL,MAAM,OAAO,GAAG,IAAI,CAAC;gBACrB,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,GAAG,IAAI,CAAC,iBAAiB,CACtD,IAAI,EACJ,QAAQ,EACR,UAAU,EACV,OAAO,EACP,OAAO,CACR,CAAC;gBAEF,MAAM,EAAE,WAAW,EAAE,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;gBACzC,IAAI,WAAW,EAAE;oBACf,IAAA,wCAAsB,EACpB,GAAG,EAAE,CACH,WAAW,CAAC,IAAI,EAAE;wBAChB,aAAa;wBACb,QAAQ;wBACR,UAAU;wBACV,OAAO;wBACP,OAAO,EAAE,eAAe;wBACxB,gBAAgB,EAAE,KAAK;qBACxB,CAAC,EACJ,CAAC,CAAC,EAAE;wBACF,IAAI,CAAC,EAAE;4BACL,UAAI,CAAC,KAAK,CAAC,4CAA4C,EAAE,CAAC,CAAC,CAAC;yBAC7D;oBACH,CAAC,EACD,IAAI,CACL,CAAC;iBACH;gBAED,gFAAgF;gBAChF,+FAA+F;gBAC/F,MAAM,aAAa,GAAG,CAAC,GAAG,SAAS,CAAC,CAAC;gBACrC,aAAa,CAAC,CAAC,CAAC,GAAG,eAAe,CAAC;gBACnC,MAAM,WAAW,GAAG,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,aAAoB,CAAC,CAAC;gBAC/D,IAAI,CAAC,GAAG,EAAE,CAAC;gBACX,OAAO,WAAW,CAAC;aACpB;QACH,CAAC,CAAC;IACJ,CAAC;IAEO,iBAAiB,CACvB,IAAU,EACV,QAAgB,EAChB,UAAkB,EAClB,OAAsC,EACtC,OAAyB;;QAEzB,MAAM,kBAAkB,GAAG,IAAA,yBAAiB,EAAC,QAAQ,CAAC,CAAC;QAEvD,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,WAAW,kBAAkB,EAAE,EAAE;YAClE,IAAI,EAAE,cAAQ,CAAC,QAAQ;YACvB,UAAU,kCACL,OAAO,CAAC,UAAU,CAAC,6BAAqB,CAAC,KAC5C,CAAC,qDAA8B,CAAC,EAAE,QAAQ,EAC1C,CAAC,0DAAmC,CAAC,EACnC,2DAAoC,EACtC,CAAC,8DAAuC,CAAC,EAAE,UAAU,EACrD,CAAC,oDAA6B,CAAC,EAAE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,SAAS,EACnD,CAAC,yDAAkC,CAAC,EAAE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,aAAa,GAC7D;SACF,CAAC,CAAC;QACH,MAAM,eAAe,GAAG,OAAO,aAAP,OAAO,cAAP,OAAO,GAAI,EAAE,CAAC;QACtC,eAAe,CAAC,OAAO,GAAG,MAAA,eAAe,CAAC,OAAO,mCAAI,EAAE,CAAC;QAExD,iBAAW,CAAC,MAAM,CAChB,WAAK,CAAC,OAAO,CAAC,aAAO,CAAC,MAAM,EAAE,EAAE,IAAI,CAAC,EACrC,eAAe,CAAC,OAAO,CACxB,CAAC;QAEF,OAAO,EAAE,IAAI,EAAE,eAAe,EAAE,CAAC;IACnC,CAAC;IAEO,eAAe,CACrB,OAA+B,EAC/B,UAA0B,EAC1B,SAAuB,EACvB,OAA4B;QAE5B,MAAM,UAAU,GAAqB,OAAO,CAAC,2BAAmB,CAAC,CAAC;QAClE,IAAI,CAAC,UAAU;YAAE,OAAO;QACxB,IAAI,UAAU,KAAK,KAAK,EAAE;YACxB,UAAU,CAAC,SAAS,CAAC;gBACnB,IAAI,EAAE,oBAAc,CAAC,KAAK;gBAC1B,OAAO,EACL,SAAS,KAAK,oBAAY,CAAC,aAAa;oBACxC,SAAS,KAAK,oBAAY,CAAC,YAAY;oBACrC,CAAC,CAAC,GAAG,SAAS,qBACV,OAAO,KAAK,IAAI;wBACd,CAAC,CAAC,eAAe;wBACjB,CAAC,CAAC,OAAO,KAAK,KAAK;4BACnB,CAAC,CAAC,kBAAkB;4BACpB,CAAC,CAAC,EACN,EAAE;oBACJ,CAAC,CAAC,SAAS;aAChB,CAAC,CAAC;SACJ;QACD,IAAI,CAAC,kBAAkB,CAAC,UAAU,EAAE,OAAO,EAAE,UAAU,EAAE,SAAS,CAAC,CAAC;QACpE,UAAU,CAAC,GAAG,EAAE,CAAC;QACjB,OAAO,CAAC,2BAAmB,CAAC,GAAG,SAAS,CAAC;IAC3C,CAAC;IAEO,oBAAoB,CAC1B,OAAsC,EACtC,UAAmB,EACnB,SAAuB,EACvB,OAA4B;;QAE5B,MAAM,aAAa,GACjB,MAAA,OAAO,CAAC,+BAAuB,CAAC,mCAAI,EAAE,CAAC;QACzC,aAAa,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE;YACjC,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,GAAG,EAAE,UAAU,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;QACvE,CAAC,CAAC,CAAC;QACH,OAAO,CAAC,+BAAuB,CAAC,GAAG,EAAE,CAAC;IACxC,CAAC;IAEO,kBAAkB,CACxB,IAAU,EACV,GAAmB,EACnB,QAAwB,EACxB,YAA0B;QAE1B,MAAM,EAAE,cAAc,EAAE,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QAC5C,IAAI,CAAC,cAAc;YAAE,OAAO;QAE5B,IAAA,wCAAsB,EACpB,GAAG,EAAE,CAAC,cAAc,CAAC,IAAI,EAAE,EAAE,GAAG,EAAE,QAAQ,EAAE,YAAY,EAAE,CAAC,EAC3D,CAAC,CAAC,EAAE;YACF,IAAI,CAAC,EAAE;gBACL,UAAI,CAAC,KAAK,CAAC,gDAAgD,EAAE,CAAC,CAAC,CAAC;aACjE;QACH,CAAC,EACD,IAAI,CACL,CAAC;IACJ,CAAC;IAEO,4BAA4B,CAAC,OAAsC;;QACzE,MAAM,WAAW,GAAG,IAAA,aAAM,GAAE,CAAC;QAC7B,MAAM,aAAa,GAAG,MAAA,OAAO,CAAC,+BAAuB,CAAC,mCAAI,EAAE,CAAC;QAC7D,IAAI,CAAS,CAAC;QACd,MAAM,EAAE,gBAAgB,EAAE,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QAC9C,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,aAAa,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACzC,MAAM,WAAW,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC;YACrC,MAAM,eAAe,GAAG,IAAA,qBAAc,EACpC,WAAW,CAAC,aAAa,EACzB,WAAW,CACZ,CAAC;YACF,IAAI,IAAA,2BAAoB,EAAC,eAAe,CAAC,GAAG,gBAAiB,EAAE;gBAC7D,MAAM;aACP;YACD,IAAI,CAAC,eAAe,CAClB,WAAW,CAAC,GAAG,EACf,IAAI,EACJ,oBAAY,CAAC,sBAAsB,EACnC,IAAI,CACL,CAAC;SACH;QACD,aAAa,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC7B,CAAC;CACF;AA9qBD,wDA8qBC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport {\n  context,\n  diag,\n  propagation,\n  trace,\n  Span,\n  SpanKind,\n  SpanStatusCode,\n  ROOT_CONTEXT,\n  Link,\n  Context,\n} from '@opentelemetry/api';\nimport {\n  hrTime,\n  hrTimeDuration,\n  hrTimeToMilliseconds,\n} from '@opentelemetry/core';\nimport {\n  InstrumentationBase,\n  InstrumentationNodeModuleDefinition,\n  InstrumentationNodeModuleFile,\n  isWrapped,\n  safeExecuteInTheMiddle,\n} from '@opentelemetry/instrumentation';\nimport {\n  SEMATTRS_MESSAGING_DESTINATION,\n  SEMATTRS_MESSAGING_DESTINATION_KIND,\n  MESSAGINGDESTINATIONKINDVALUES_TOPIC,\n  SEMATTRS_MESSAGING_RABBITMQ_ROUTING_KEY,\n  SEMATTRS_MESSAGING_OPERATION,\n  MESSAGINGOPERATIONVALUES_PROCESS,\n  SEMATTRS_MESSAGING_MESSAGE_ID,\n  SEMATTRS_MESSAGING_CONVERSATION_ID,\n} from '@opentelemetry/semantic-conventions';\nimport type {\n  Connection,\n  ConsumeMessage,\n  Message,\n  Options,\n  Replies,\n} from 'amqplib';\nimport {\n  AmqplibInstrumentationConfig,\n  DEFAULT_CONFIG,\n  EndOperation,\n} from './types';\nimport {\n  CHANNEL_CONSUME_TIMEOUT_TIMER,\n  CHANNEL_SPANS_NOT_ENDED,\n  CONNECTION_ATTRIBUTES,\n  getConnectionAttributesFromServer,\n  getConnectionAttributesFromUrl,\n  InstrumentationConsumeChannel,\n  InstrumentationMessage,\n  InstrumentationPublishChannel,\n  isConfirmChannelTracing,\n  markConfirmChannelTracing,\n  MESSAGE_STORED_SPAN,\n  normalizeExchange,\n  unmarkConfirmChannelTracing,\n} from './utils';\n/** @knipignore */\nimport { PACKAGE_NAME, PACKAGE_VERSION } from './version';\n\nconst supportedVersions = ['>=0.5.5 <1'];\n\nexport class AmqplibInstrumentation extends InstrumentationBase<AmqplibInstrumentationConfig> {\n  constructor(config: AmqplibInstrumentationConfig = {}) {\n    super(PACKAGE_NAME, PACKAGE_VERSION, { ...DEFAULT_CONFIG, ...config });\n  }\n\n  override setConfig(config: AmqplibInstrumentationConfig = {}) {\n    super.setConfig({ ...DEFAULT_CONFIG, ...config });\n  }\n\n  protected init() {\n    const channelModelModuleFile = new InstrumentationNodeModuleFile(\n      'amqplib/lib/channel_model.js',\n      supportedVersions,\n      this.patchChannelModel.bind(this),\n      this.unpatchChannelModel.bind(this)\n    );\n\n    const callbackModelModuleFile = new InstrumentationNodeModuleFile(\n      'amqplib/lib/callback_model.js',\n      supportedVersions,\n      this.patchChannelModel.bind(this),\n      this.unpatchChannelModel.bind(this)\n    );\n\n    const connectModuleFile = new InstrumentationNodeModuleFile(\n      'amqplib/lib/connect.js',\n      supportedVersions,\n      this.patchConnect.bind(this),\n      this.unpatchConnect.bind(this)\n    );\n\n    const module = new InstrumentationNodeModuleDefinition(\n      'amqplib',\n      supportedVersions,\n      undefined,\n      undefined,\n      [channelModelModuleFile, connectModuleFile, callbackModelModuleFile]\n    );\n    return module;\n  }\n\n  private patchConnect(moduleExports: any) {\n    moduleExports = this.unpatchConnect(moduleExports);\n    if (!isWrapped(moduleExports.connect)) {\n      this._wrap(moduleExports, 'connect', this.getConnectPatch.bind(this));\n    }\n    return moduleExports;\n  }\n\n  private unpatchConnect(moduleExports: any) {\n    if (isWrapped(moduleExports.connect)) {\n      this._unwrap(moduleExports, 'connect');\n    }\n    return moduleExports;\n  }\n\n  private patchChannelModel(\n    moduleExports: any,\n    moduleVersion: string | undefined\n  ) {\n    if (!isWrapped(moduleExports.Channel.prototype.publish)) {\n      this._wrap(\n        moduleExports.Channel.prototype,\n        'publish',\n        this.getPublishPatch.bind(this, moduleVersion)\n      );\n    }\n    if (!isWrapped(moduleExports.Channel.prototype.consume)) {\n      this._wrap(\n        moduleExports.Channel.prototype,\n        'consume',\n        this.getConsumePatch.bind(this, moduleVersion)\n      );\n    }\n    if (!isWrapped(moduleExports.Channel.prototype.ack)) {\n      this._wrap(\n        moduleExports.Channel.prototype,\n        'ack',\n        this.getAckPatch.bind(this, false, EndOperation.Ack)\n      );\n    }\n    if (!isWrapped(moduleExports.Channel.prototype.nack)) {\n      this._wrap(\n        moduleExports.Channel.prototype,\n        'nack',\n        this.getAckPatch.bind(this, true, EndOperation.Nack)\n      );\n    }\n    if (!isWrapped(moduleExports.Channel.prototype.reject)) {\n      this._wrap(\n        moduleExports.Channel.prototype,\n        'reject',\n        this.getAckPatch.bind(this, true, EndOperation.Reject)\n      );\n    }\n    if (!isWrapped(moduleExports.Channel.prototype.ackAll)) {\n      this._wrap(\n        moduleExports.Channel.prototype,\n        'ackAll',\n        this.getAckAllPatch.bind(this, false, EndOperation.AckAll)\n      );\n    }\n    if (!isWrapped(moduleExports.Channel.prototype.nackAll)) {\n      this._wrap(\n        moduleExports.Channel.prototype,\n        'nackAll',\n        this.getAckAllPatch.bind(this, true, EndOperation.NackAll)\n      );\n    }\n    if (!isWrapped(moduleExports.Channel.prototype.emit)) {\n      this._wrap(\n        moduleExports.Channel.prototype,\n        'emit',\n        this.getChannelEmitPatch.bind(this)\n      );\n    }\n    if (!isWrapped(moduleExports.ConfirmChannel.prototype.publish)) {\n      this._wrap(\n        moduleExports.ConfirmChannel.prototype,\n        'publish',\n        this.getConfirmedPublishPatch.bind(this, moduleVersion)\n      );\n    }\n    return moduleExports;\n  }\n\n  private unpatchChannelModel(moduleExports: any) {\n    if (isWrapped(moduleExports.Channel.prototype.publish)) {\n      this._unwrap(moduleExports.Channel.prototype, 'publish');\n    }\n    if (isWrapped(moduleExports.Channel.prototype.consume)) {\n      this._unwrap(moduleExports.Channel.prototype, 'consume');\n    }\n    if (isWrapped(moduleExports.Channel.prototype.ack)) {\n      this._unwrap(moduleExports.Channel.prototype, 'ack');\n    }\n    if (isWrapped(moduleExports.Channel.prototype.nack)) {\n      this._unwrap(moduleExports.Channel.prototype, 'nack');\n    }\n    if (isWrapped(moduleExports.Channel.prototype.reject)) {\n      this._unwrap(moduleExports.Channel.prototype, 'reject');\n    }\n    if (isWrapped(moduleExports.Channel.prototype.ackAll)) {\n      this._unwrap(moduleExports.Channel.prototype, 'ackAll');\n    }\n    if (isWrapped(moduleExports.Channel.prototype.nackAll)) {\n      this._unwrap(moduleExports.Channel.prototype, 'nackAll');\n    }\n    if (isWrapped(moduleExports.Channel.prototype.emit)) {\n      this._unwrap(moduleExports.Channel.prototype, 'emit');\n    }\n    if (isWrapped(moduleExports.ConfirmChannel.prototype.publish)) {\n      this._unwrap(moduleExports.ConfirmChannel.prototype, 'publish');\n    }\n    return moduleExports;\n  }\n\n  private getConnectPatch(\n    original: (\n      url: string | Options.Connect,\n      socketOptions: any,\n      openCallback: (err: any, connection: Connection) => void\n    ) => Connection\n  ) {\n    return function patchedConnect(\n      this: unknown,\n      url: string | Options.Connect,\n      socketOptions: any,\n      openCallback: Function\n    ) {\n      return original.call(\n        this,\n        url,\n        socketOptions,\n        function (this: unknown, err, conn: Connection) {\n          if (err == null) {\n            const urlAttributes = getConnectionAttributesFromUrl(url);\n            // the type of conn in @types/amqplib is amqp.Connection, but in practice the library send the\n            // `serverProperties` on the `conn` and not in a property `connection`.\n            // I don't have capacity to debug it currently but it should probably be fixed in @types or\n            // in the package itself\n            // currently setting as any to calm typescript\n            const serverAttributes = getConnectionAttributesFromServer(\n              conn as any\n            );\n            (conn as any)[CONNECTION_ATTRIBUTES] = {\n              ...urlAttributes,\n              ...serverAttributes,\n            };\n          }\n          openCallback.apply(this, arguments);\n        }\n      );\n    };\n  }\n\n  private getChannelEmitPatch(original: Function) {\n    const self = this;\n    return function emit(\n      this: InstrumentationConsumeChannel,\n      eventName: string\n    ) {\n      if (eventName === 'close') {\n        self.endAllSpansOnChannel(\n          this,\n          true,\n          EndOperation.ChannelClosed,\n          undefined\n        );\n        const activeTimer = this[CHANNEL_CONSUME_TIMEOUT_TIMER];\n        if (activeTimer) {\n          clearInterval(activeTimer);\n        }\n        this[CHANNEL_CONSUME_TIMEOUT_TIMER] = undefined;\n      } else if (eventName === 'error') {\n        self.endAllSpansOnChannel(\n          this,\n          true,\n          EndOperation.ChannelError,\n          undefined\n        );\n      }\n      return original.apply(this, arguments);\n    };\n  }\n\n  private getAckAllPatch(\n    isRejected: boolean,\n    endOperation: EndOperation,\n    original: Function\n  ) {\n    const self = this;\n    return function ackAll(\n      this: InstrumentationConsumeChannel,\n      requeueOrEmpty?: boolean\n    ): void {\n      self.endAllSpansOnChannel(this, isRejected, endOperation, requeueOrEmpty);\n      return original.apply(this, arguments);\n    };\n  }\n\n  private getAckPatch(\n    isRejected: boolean,\n    endOperation: EndOperation,\n    original: Function\n  ) {\n    const self = this;\n    return function ack(\n      this: InstrumentationConsumeChannel,\n      message: Message,\n      allUpToOrRequeue?: boolean,\n      requeue?: boolean\n    ): void {\n      const channel = this;\n      // we use this patch in reject function as well, but it has different signature\n      const requeueResolved =\n        endOperation === EndOperation.Reject ? allUpToOrRequeue : requeue;\n\n      const spansNotEnded: { msg: Message }[] =\n        channel[CHANNEL_SPANS_NOT_ENDED] ?? [];\n      const msgIndex = spansNotEnded.findIndex(\n        msgDetails => msgDetails.msg === message\n      );\n      if (msgIndex < 0) {\n        // should not happen in happy flow\n        // but possible if user is calling the api function ack twice with same message\n        self.endConsumerSpan(\n          message,\n          isRejected,\n          endOperation,\n          requeueResolved\n        );\n      } else if (endOperation !== EndOperation.Reject && allUpToOrRequeue) {\n        for (let i = 0; i <= msgIndex; i++) {\n          self.endConsumerSpan(\n            spansNotEnded[i].msg,\n            isRejected,\n            endOperation,\n            requeueResolved\n          );\n        }\n        spansNotEnded.splice(0, msgIndex + 1);\n      } else {\n        self.endConsumerSpan(\n          message,\n          isRejected,\n          endOperation,\n          requeueResolved\n        );\n        spansNotEnded.splice(msgIndex, 1);\n      }\n      return original.apply(this, arguments);\n    };\n  }\n\n  private getConsumePatch(\n    moduleVersion: string | undefined,\n    original: Function\n  ) {\n    const self = this;\n    return function consume(\n      this: InstrumentationConsumeChannel,\n      queue: string,\n      onMessage: (msg: ConsumeMessage | null) => void,\n      options?: Options.Consume\n    ): Promise<Replies.Consume> {\n      const channel = this;\n      if (\n        !Object.prototype.hasOwnProperty.call(channel, CHANNEL_SPANS_NOT_ENDED)\n      ) {\n        const { consumeTimeoutMs } = self.getConfig();\n        if (consumeTimeoutMs) {\n          const timer = setInterval(() => {\n            self.checkConsumeTimeoutOnChannel(channel);\n          }, consumeTimeoutMs);\n          timer.unref();\n          channel[CHANNEL_CONSUME_TIMEOUT_TIMER] = timer;\n        }\n        channel[CHANNEL_SPANS_NOT_ENDED] = [];\n      }\n\n      const patchedOnMessage = function (\n        this: unknown,\n        msg: InstrumentationMessage | null\n      ) {\n        // msg is expected to be null for signaling consumer cancel notification\n        // https://www.rabbitmq.com/consumer-cancel.html\n        // in this case, we do not start a span, as this is not a real message.\n        if (!msg) {\n          return onMessage.call(this, msg);\n        }\n\n        const headers = msg.properties.headers ?? {};\n        let parentContext: Context | undefined = propagation.extract(\n          ROOT_CONTEXT,\n          headers\n        );\n        const exchange = msg.fields?.exchange;\n        let links: Link[] | undefined;\n        if (self._config.useLinksForConsume) {\n          const parentSpanContext = parentContext\n            ? trace.getSpan(parentContext)?.spanContext()\n            : undefined;\n          parentContext = undefined;\n          if (parentSpanContext) {\n            links = [\n              {\n                context: parentSpanContext,\n              },\n            ];\n          }\n        }\n        const span = self.tracer.startSpan(\n          `${queue} process`,\n          {\n            kind: SpanKind.CONSUMER,\n            attributes: {\n              ...channel?.connection?.[CONNECTION_ATTRIBUTES],\n              [SEMATTRS_MESSAGING_DESTINATION]: exchange,\n              [SEMATTRS_MESSAGING_DESTINATION_KIND]:\n                MESSAGINGDESTINATIONKINDVALUES_TOPIC,\n              [SEMATTRS_MESSAGING_RABBITMQ_ROUTING_KEY]: msg.fields?.routingKey,\n              [SEMATTRS_MESSAGING_OPERATION]: MESSAGINGOPERATIONVALUES_PROCESS,\n              [SEMATTRS_MESSAGING_MESSAGE_ID]: msg?.properties.messageId,\n              [SEMATTRS_MESSAGING_CONVERSATION_ID]:\n                msg?.properties.correlationId,\n            },\n            links,\n          },\n          parentContext\n        );\n\n        const { consumeHook } = self.getConfig();\n        if (consumeHook) {\n          safeExecuteInTheMiddle(\n            () => consumeHook(span, { moduleVersion, msg }),\n            e => {\n              if (e) {\n                diag.error('amqplib instrumentation: consumerHook error', e);\n              }\n            },\n            true\n          );\n        }\n\n        if (!options?.noAck) {\n          // store the message on the channel so we can close the span on ackAll etc\n          channel[CHANNEL_SPANS_NOT_ENDED]!.push({\n            msg,\n            timeOfConsume: hrTime(),\n          });\n\n          // store the span on the message, so we can end it when user call 'ack' on it\n          msg[MESSAGE_STORED_SPAN] = span;\n        }\n        const setContext: Context = parentContext\n          ? parentContext\n          : ROOT_CONTEXT;\n        context.with(trace.setSpan(setContext, span), () => {\n          onMessage.call(this, msg);\n        });\n\n        if (options?.noAck) {\n          self.callConsumeEndHook(span, msg, false, EndOperation.AutoAck);\n          span.end();\n        }\n      };\n      arguments[1] = patchedOnMessage;\n      return original.apply(this, arguments);\n    };\n  }\n\n  private getConfirmedPublishPatch(\n    moduleVersion: string | undefined,\n    original: Function\n  ) {\n    const self = this;\n    return function confirmedPublish(\n      this: InstrumentationConsumeChannel,\n      exchange: string,\n      routingKey: string,\n      content: Buffer,\n      options?: Options.Publish,\n      callback?: (err: any, ok: Replies.Empty) => void\n    ): boolean {\n      const channel = this;\n      const { span, modifiedOptions } = self.createPublishSpan(\n        self,\n        exchange,\n        routingKey,\n        channel,\n        options\n      );\n\n      const { publishHook } = self.getConfig();\n      if (publishHook) {\n        safeExecuteInTheMiddle(\n          () =>\n            publishHook(span, {\n              moduleVersion,\n              exchange,\n              routingKey,\n              content,\n              options: modifiedOptions,\n              isConfirmChannel: true,\n            }),\n          e => {\n            if (e) {\n              diag.error('amqplib instrumentation: publishHook error', e);\n            }\n          },\n          true\n        );\n      }\n\n      const patchedOnConfirm = function (\n        this: unknown,\n        err: any,\n        ok: Replies.Empty\n      ) {\n        try {\n          callback?.call(this, err, ok);\n        } finally {\n          const { publishConfirmHook } = self.getConfig();\n          if (publishConfirmHook) {\n            safeExecuteInTheMiddle(\n              () =>\n                publishConfirmHook(span, {\n                  moduleVersion,\n                  exchange,\n                  routingKey,\n                  content,\n                  options,\n                  isConfirmChannel: true,\n                  confirmError: err,\n                }),\n              e => {\n                if (e) {\n                  diag.error(\n                    'amqplib instrumentation: publishConfirmHook error',\n                    e\n                  );\n                }\n              },\n              true\n            );\n          }\n\n          if (err) {\n            span.setStatus({\n              code: SpanStatusCode.ERROR,\n              message: \"message confirmation has been nack'ed\",\n            });\n          }\n          span.end();\n        }\n      };\n\n      // calling confirm channel publish function is storing the message in queue and registering the callback for broker confirm.\n      // span ends in the patched callback.\n      const markedContext = markConfirmChannelTracing(context.active());\n      const argumentsCopy = [...arguments];\n      argumentsCopy[3] = modifiedOptions;\n      argumentsCopy[4] = context.bind(\n        unmarkConfirmChannelTracing(trace.setSpan(markedContext, span)),\n        patchedOnConfirm\n      );\n      return context.with(markedContext, original.bind(this, ...argumentsCopy));\n    };\n  }\n\n  private getPublishPatch(\n    moduleVersion: string | undefined,\n    original: Function\n  ) {\n    const self = this;\n    return function publish(\n      this: InstrumentationPublishChannel,\n      exchange: string,\n      routingKey: string,\n      content: Buffer,\n      options?: Options.Publish\n    ): boolean {\n      if (isConfirmChannelTracing(context.active())) {\n        // work already done\n        return original.apply(this, arguments);\n      } else {\n        const channel = this;\n        const { span, modifiedOptions } = self.createPublishSpan(\n          self,\n          exchange,\n          routingKey,\n          channel,\n          options\n        );\n\n        const { publishHook } = self.getConfig();\n        if (publishHook) {\n          safeExecuteInTheMiddle(\n            () =>\n              publishHook(span, {\n                moduleVersion,\n                exchange,\n                routingKey,\n                content,\n                options: modifiedOptions,\n                isConfirmChannel: false,\n              }),\n            e => {\n              if (e) {\n                diag.error('amqplib instrumentation: publishHook error', e);\n              }\n            },\n            true\n          );\n        }\n\n        // calling normal channel publish function is only storing the message in queue.\n        // it does not send it and waits for an ack, so the span duration is expected to be very short.\n        const argumentsCopy = [...arguments];\n        argumentsCopy[3] = modifiedOptions;\n        const originalRes = original.apply(this, argumentsCopy as any);\n        span.end();\n        return originalRes;\n      }\n    };\n  }\n\n  private createPublishSpan(\n    self: this,\n    exchange: string,\n    routingKey: string,\n    channel: InstrumentationPublishChannel,\n    options?: Options.Publish\n  ) {\n    const normalizedExchange = normalizeExchange(exchange);\n\n    const span = self.tracer.startSpan(`publish ${normalizedExchange}`, {\n      kind: SpanKind.PRODUCER,\n      attributes: {\n        ...channel.connection[CONNECTION_ATTRIBUTES],\n        [SEMATTRS_MESSAGING_DESTINATION]: exchange,\n        [SEMATTRS_MESSAGING_DESTINATION_KIND]:\n          MESSAGINGDESTINATIONKINDVALUES_TOPIC,\n        [SEMATTRS_MESSAGING_RABBITMQ_ROUTING_KEY]: routingKey,\n        [SEMATTRS_MESSAGING_MESSAGE_ID]: options?.messageId,\n        [SEMATTRS_MESSAGING_CONVERSATION_ID]: options?.correlationId,\n      },\n    });\n    const modifiedOptions = options ?? {};\n    modifiedOptions.headers = modifiedOptions.headers ?? {};\n\n    propagation.inject(\n      trace.setSpan(context.active(), span),\n      modifiedOptions.headers\n    );\n\n    return { span, modifiedOptions };\n  }\n\n  private endConsumerSpan(\n    message: InstrumentationMessage,\n    isRejected: boolean | null,\n    operation: EndOperation,\n    requeue: boolean | undefined\n  ) {\n    const storedSpan: Span | undefined = message[MESSAGE_STORED_SPAN];\n    if (!storedSpan) return;\n    if (isRejected !== false) {\n      storedSpan.setStatus({\n        code: SpanStatusCode.ERROR,\n        message:\n          operation !== EndOperation.ChannelClosed &&\n          operation !== EndOperation.ChannelError\n            ? `${operation} called on message${\n                requeue === true\n                  ? ' with requeue'\n                  : requeue === false\n                  ? ' without requeue'\n                  : ''\n              }`\n            : operation,\n      });\n    }\n    this.callConsumeEndHook(storedSpan, message, isRejected, operation);\n    storedSpan.end();\n    message[MESSAGE_STORED_SPAN] = undefined;\n  }\n\n  private endAllSpansOnChannel(\n    channel: InstrumentationConsumeChannel,\n    isRejected: boolean,\n    operation: EndOperation,\n    requeue: boolean | undefined\n  ) {\n    const spansNotEnded: { msg: Message }[] =\n      channel[CHANNEL_SPANS_NOT_ENDED] ?? [];\n    spansNotEnded.forEach(msgDetails => {\n      this.endConsumerSpan(msgDetails.msg, isRejected, operation, requeue);\n    });\n    channel[CHANNEL_SPANS_NOT_ENDED] = [];\n  }\n\n  private callConsumeEndHook(\n    span: Span,\n    msg: ConsumeMessage,\n    rejected: boolean | null,\n    endOperation: EndOperation\n  ) {\n    const { consumeEndHook } = this.getConfig();\n    if (!consumeEndHook) return;\n\n    safeExecuteInTheMiddle(\n      () => consumeEndHook(span, { msg, rejected, endOperation }),\n      e => {\n        if (e) {\n          diag.error('amqplib instrumentation: consumerEndHook error', e);\n        }\n      },\n      true\n    );\n  }\n\n  private checkConsumeTimeoutOnChannel(channel: InstrumentationConsumeChannel) {\n    const currentTime = hrTime();\n    const spansNotEnded = channel[CHANNEL_SPANS_NOT_ENDED] ?? [];\n    let i: number;\n    const { consumeTimeoutMs } = this.getConfig();\n    for (i = 0; i < spansNotEnded.length; i++) {\n      const currMessage = spansNotEnded[i];\n      const timeFromConsume = hrTimeDuration(\n        currMessage.timeOfConsume,\n        currentTime\n      );\n      if (hrTimeToMilliseconds(timeFromConsume) < consumeTimeoutMs!) {\n        break;\n      }\n      this.endConsumerSpan(\n        currMessage.msg,\n        null,\n        EndOperation.InstrumentationTimeout,\n        true\n      );\n    }\n    spansNotEnded.splice(0, i);\n  }\n}\n"]}