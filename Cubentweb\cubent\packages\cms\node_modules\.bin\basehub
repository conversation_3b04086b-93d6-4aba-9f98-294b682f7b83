#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/basehub@8.2.7_@babel+runtim_3aebfa8e064bb601162c04844d38dd02/node_modules/basehub/dist/node_modules:/mnt/c/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/basehub@8.2.7_@babel+runtim_3aebfa8e064bb601162c04844d38dd02/node_modules/basehub/node_modules:/mnt/c/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/basehub@8.2.7_@babel+runtim_3aebfa8e064bb601162c04844d38dd02/node_modules:/mnt/c/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/basehub@8.2.7_@babel+runtim_3aebfa8e064bb601162c04844d38dd02/node_modules/basehub/dist/node_modules:/mnt/c/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/basehub@8.2.7_@babel+runtim_3aebfa8e064bb601162c04844d38dd02/node_modules/basehub/node_modules:/mnt/c/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/basehub@8.2.7_@babel+runtim_3aebfa8e064bb601162c04844d38dd02/node_modules:/mnt/c/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../basehub/dist/bin.cjs" "$@"
else
  exec node  "$basedir/../basehub/dist/bin.cjs" "$@"
fi
