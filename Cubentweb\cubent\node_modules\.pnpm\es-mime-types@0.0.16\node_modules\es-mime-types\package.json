{"name": "es-mime-types", "description": "mime-types rewrite in TypeScript with ESM and CommonJS targets", "version": "0.0.16", "repository": "https://github.com/talentlessguy/es-mime-types.git", "keywords": ["http", "esm", "es", "vary", "nodejs", "mime", "mime-types"], "engines": {"node": ">=12.x"}, "files": ["dist", "src"], "author": "talentlessguy <<EMAIL>>", "license": "MIT", "type": "module", "main": "dist/index.cjs", "module": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}, "./package.json": "./package.json", "./": "./"}, "devDependencies": {"@types/mime-db": "^1.43.0", "@types/node": "^14.11.1", "rollup": "^2.27.1", "rollup-plugin-typescript2": "^0.27.2", "typescript": "^4.0.3"}, "dependencies": {"mime-db": "^1.44.0"}, "scripts": {"prepare": "pnpm build", "build": "rollup -c"}}