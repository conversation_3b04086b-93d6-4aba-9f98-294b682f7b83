// This file was generated by basehub. Do not edit directly. Read more: https://basehub.com/docs/api-reference/basehub-sdk

/* eslint-disable */
/* eslint-disable eslint-comments/no-restricted-disable */
/* tslint:disable */
// @ts-nocheck

import type { QueryResults } from "./server-pump";
export type ResponseCache = {
    data: QueryResults<[]>[number] | null;
    spaceID: string;
    pusherData: {
        channel_key: string;
        app_key: string;
        cluster: string;
    };
    newPumpToken: string;
    errors: {
        message: string;
        path?: string[];
    }[] | null;
    responseHash: string;
};
export type PumpState = {
    data: QueryResults<[]> | null[];
    responseHashes: string[];
    errors: Array<ResponseCache["errors"]>;
    pusherData: ResponseCache["pusherData"];
    spaceID: string;
};
