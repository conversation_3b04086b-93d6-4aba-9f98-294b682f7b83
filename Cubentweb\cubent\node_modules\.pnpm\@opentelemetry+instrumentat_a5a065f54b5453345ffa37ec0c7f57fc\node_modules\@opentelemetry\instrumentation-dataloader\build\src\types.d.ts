import { InstrumentationConfig } from '@opentelemetry/instrumentation';
export interface DataloaderInstrumentationConfig extends InstrumentationConfig {
    /**
     * Whether the instrumentation requires a parent span, if set to true
     * and there is no parent span, no additional spans are created
     * (default: true)
     */
    requireParentSpan?: boolean;
}
//# sourceMappingURL=types.d.ts.map