{"version": 3, "file": "HostDetector.js", "sourceRoot": "", "sources": ["../../../../../src/detectors/platform/node/HostDetector.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAKH,yDAAsD;AAEtD;;;GAGG;AACH,MAAM,YAAY;IAChB,MAAM,CAAC,OAAiC;QACtC,OAAO,OAAO,CAAC,OAAO,CAAC,mCAAgB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC;IAC3D,CAAC;CACF;AAEY,QAAA,YAAY,GAAG,IAAI,YAAY,EAAE,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Detector } from '../../../types';\nimport { ResourceDetectionConfig } from '../../../config';\nimport { IResource } from '../../../IResource';\nimport { hostDetectorSync } from './HostDetectorSync';\n\n/**\n * HostDetector detects the resources related to the host current process is\n * running on. Currently only non-cloud-based attributes are included.\n */\nclass HostDetector implements Detector {\n  detect(_config?: ResourceDetectionConfig): Promise<IResource> {\n    return Promise.resolve(hostDetectorSync.detect(_config));\n  }\n}\n\nexport const hostDetector = new HostDetector();\n"]}