{"version": 3, "file": "embla-carousel-react.esm.js", "sources": ["../src/components/useEmblaCarousel.ts"], "sourcesContent": ["import { useRef, useEffect, useState, useCallback } from 'react'\nimport {\n  areOptionsEqual,\n  arePluginsEqual,\n  canUseDOM\n} from 'embla-carousel-reactive-utils'\nimport EmblaCarousel, {\n  EmblaCarouselType,\n  EmblaOptionsType,\n  EmblaPluginType\n} from 'embla-carousel'\n\nexport type EmblaViewportRefType = <ViewportElement extends HTMLElement>(\n  instance: ViewportElement | null\n) => void\n\nexport type UseEmblaCarouselType = [\n  EmblaViewportRefType,\n  EmblaCarouselType | undefined\n]\n\nfunction useEmblaCarousel(\n  options: EmblaOptionsType = {},\n  plugins: EmblaPluginType[] = []\n): UseEmblaCarouselType {\n  const storedOptions = useRef(options)\n  const storedPlugins = useRef(plugins)\n  const [emblaApi, setEmblaApi] = useState<EmblaCarouselType>()\n  const [viewport, setViewport] = useState<HTMLElement>()\n\n  const reInit = useCallback(() => {\n    if (emblaApi) emblaApi.reInit(storedOptions.current, storedPlugins.current)\n  }, [emblaApi])\n\n  useEffect(() => {\n    if (areOptionsEqual(storedOptions.current, options)) return\n    storedOptions.current = options\n    reInit()\n  }, [options, reInit])\n\n  useEffect(() => {\n    if (arePluginsEqual(storedPlugins.current, plugins)) return\n    storedPlugins.current = plugins\n    reInit()\n  }, [plugins, reInit])\n\n  useEffect(() => {\n    if (canUseDOM() && viewport) {\n      EmblaCarousel.globalOptions = useEmblaCarousel.globalOptions\n      const newEmblaApi = EmblaCarousel(\n        viewport,\n        storedOptions.current,\n        storedPlugins.current\n      )\n      setEmblaApi(newEmblaApi)\n      return () => newEmblaApi.destroy()\n    } else {\n      setEmblaApi(undefined)\n    }\n  }, [viewport, setEmblaApi])\n\n  return [<EmblaViewportRefType>setViewport, emblaApi]\n}\n\ndeclare namespace useEmblaCarousel {\n  let globalOptions: EmblaOptionsType | undefined\n}\n\nuseEmblaCarousel.globalOptions = undefined\n\nexport default useEmblaCarousel\n"], "names": ["useEmblaCarousel", "options", "plugins", "storedOptions", "useRef", "storedPlugins", "emblaApi", "setEmblaApi", "useState", "viewport", "setViewport", "reInit", "useCallback", "current", "useEffect", "areOptionsEqual", "arePluginsEqual", "canUseDOM", "EmblaCarousel", "globalOptions", "newEmblaApi", "destroy", "undefined"], "mappings": ";;;;AAqBA,SAASA,gBAAgBA,CACvBC,OAAA,GAA4B,EAAE,EAC9BC,UAA6B,EAAE,EAAA;AAE/B,EAAA,MAAMC,aAAa,GAAGC,MAAM,CAACH,OAAO,CAAC;AACrC,EAAA,MAAMI,aAAa,GAAGD,MAAM,CAACF,OAAO,CAAC;EACrC,MAAM,CAACI,QAAQ,EAAEC,WAAW,CAAC,GAAGC,QAAQ,EAAqB;EAC7D,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGF,QAAQ,EAAe;AAEvD,EAAA,MAAMG,MAAM,GAAGC,WAAW,CAAC,MAAK;AAC9B,IAAA,IAAIN,QAAQ,EAAEA,QAAQ,CAACK,MAAM,CAACR,aAAa,CAACU,OAAO,EAAER,aAAa,CAACQ,OAAO,CAAC;AAC7E,GAAC,EAAE,CAACP,QAAQ,CAAC,CAAC;AAEdQ,EAAAA,SAAS,CAAC,MAAK;IACb,IAAIC,eAAe,CAACZ,aAAa,CAACU,OAAO,EAAEZ,OAAO,CAAC,EAAE;IACrDE,aAAa,CAACU,OAAO,GAAGZ,OAAO;AAC/BU,IAAAA,MAAM,EAAE;AACV,GAAC,EAAE,CAACV,OAAO,EAAEU,MAAM,CAAC,CAAC;AAErBG,EAAAA,SAAS,CAAC,MAAK;IACb,IAAIE,eAAe,CAACX,aAAa,CAACQ,OAAO,EAAEX,OAAO,CAAC,EAAE;IACrDG,aAAa,CAACQ,OAAO,GAAGX,OAAO;AAC/BS,IAAAA,MAAM,EAAE;AACV,GAAC,EAAE,CAACT,OAAO,EAAES,MAAM,CAAC,CAAC;AAErBG,EAAAA,SAAS,CAAC,MAAK;AACb,IAAA,IAAIG,SAAS,EAAE,IAAIR,QAAQ,EAAE;AAC3BS,MAAAA,aAAa,CAACC,aAAa,GAAGnB,gBAAgB,CAACmB,aAAa;AAC5D,MAAA,MAAMC,WAAW,GAAGF,aAAa,CAC/BT,QAAQ,EACRN,aAAa,CAACU,OAAO,EACrBR,aAAa,CAACQ,OAAO,CACtB;MACDN,WAAW,CAACa,WAAW,CAAC;AACxB,MAAA,OAAO,MAAMA,WAAW,CAACC,OAAO,EAAE;AACpC,KAAC,MAAM;MACLd,WAAW,CAACe,SAAS,CAAC;AACxB;AACF,GAAC,EAAE,CAACb,QAAQ,EAAEF,WAAW,CAAC,CAAC;AAE3B,EAAA,OAAO,CAAuBG,WAAW,EAAEJ,QAAQ,CAAC;AACtD;AAMAN,gBAAgB,CAACmB,aAAa,GAAGG,SAAS;;;;"}