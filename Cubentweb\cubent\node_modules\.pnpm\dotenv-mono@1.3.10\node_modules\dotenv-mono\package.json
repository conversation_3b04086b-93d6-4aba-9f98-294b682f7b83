{"name": "dotenv-mono", "version": "1.3.10", "main": "./dist/index.js", "module": "./dist/index.js", "types": "./dist/index.d.ts", "description": "This package permit to have a centralized dotenv on a monorepo. It also includes some extra features such as manipulation and saving of changes to the dotenv file, a default centralized file, and a file loader with ordering and priorities.", "author": "<PERSON> <<EMAIL>>", "exports": {".": {"default": "./dist/index.js", "require": "./dist/index.js", "types": "./dist/index.d.ts"}, "./load": "./dist/load.js", "./package.json": "./package.json"}, "files": ["dist"], "bugs": {"url": "https://github.com/marcocesarato/dotenv-mono/issues"}, "homepage": "https://github.com/marcocesarato/dotenv-mono", "license": "GPL-3.0-or-later", "scripts": {"release": "standard-version", "lint": "eslint --ext .js,.ts .", "lint:fix": "eslint --ext .js,.ts . --fix", "format": "prettier --write .", "build": "tsc", "tsc": "tsc", "prepare": "tsc", "test": "jest", "coverage": "jest --coverage && start coverage/index.html"}, "dependencies": {"dotenv": "^16.0.3", "dotenv-expand": "^10.0.0"}, "devDependencies": {"@types/jest": "^29.4.0", "@types/mock-fs": "^4.13.1", "@types/node": "^18.14.0", "@typescript-eslint/eslint-plugin": "^5.52.0", "@typescript-eslint/parser": "^5.52.0", "eslint": "^8.34.0", "eslint-config-prettier": "^8.6.0", "eslint-plugin-jest": "^27.2.1", "eslint-plugin-jest-extended": "^2.0.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-simple-import-sort": "^10.0.0", "husky": "^8.0.3", "jest": "^29.4.3", "jest-extended": "^3.2.4", "lint-staged": "^13.1.2", "mock-fs": "^5.2.0", "prettier": "^2.8.4", "standard-version": "^9.5.0", "ts-jest": "^29.0.5", "typescript": "^4.9.5"}, "publishConfig": {"access": "public"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{js,jsx,ts,tsx}": "eslint --fix", "*.{json,md}": "prettier --write"}, "standard-version": {"scripts": {"postchangelog": "prettier -w CHANGELOG.md"}}, "repository": {"type": "git", "url": "git+https://github.com/marcocesarato/dotenv-mono.git"}, "keywords": ["monorepo", "dotenv", "dotenv-expand", "expand", "vars", "variables", "load", "loader", "mono", "one", "single", "centralized", "repo", "changes", "edit", "priorities", "env", "environemnt", "local", "test", "production", "development", "prod", "dev", "apps", "packages", "nextjs", "next.js", "react", "angular", ".env", ".env.local", ".env.development", ".env.production", "turbo", "turborepo", "storybook", "shared", "share", "default", "defaults", "cli", "console", "preload"]}