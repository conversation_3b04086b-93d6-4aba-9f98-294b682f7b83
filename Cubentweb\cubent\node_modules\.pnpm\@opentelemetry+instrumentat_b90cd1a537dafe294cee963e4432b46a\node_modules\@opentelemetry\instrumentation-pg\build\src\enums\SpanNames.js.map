{"version": 3, "file": "SpanNames.js", "sourceRoot": "", "sources": ["../../../src/enums/SpanNames.ts"], "names": [], "mappings": ";;;AAAA;;;;;;;;;;;;;;GAcG;AACH,kDAAkD;AAClD,IAAY,SAIX;AAJD,WAAY,SAAS;IACnB,sCAAyB,CAAA;IACzB,mCAAsB,CAAA;IACtB,6CAAgC,CAAA;AAClC,CAAC,EAJW,SAAS,GAAT,iBAAS,KAAT,iBAAS,QAIpB", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n// Contains span names produced by instrumentation\nexport enum SpanNames {\n  QUERY_PREFIX = 'pg.query',\n  CONNECT = 'pg.connect',\n  POOL_CONNECT = 'pg-pool.connect',\n}\n"]}