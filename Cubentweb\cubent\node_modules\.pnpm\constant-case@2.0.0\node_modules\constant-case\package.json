{"name": "constant-case", "version": "2.0.0", "description": "Constant case a string", "main": "constant-case.js", "typings": "constant-case.d.ts", "files": ["constant-case.js", "constant-case.d.ts", "LICENSE"], "scripts": {"lint": "standard", "test-std": "mocha -- -R spec --bail", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- -R spec --bail", "test": "npm run lint && npm run test-cov"}, "repository": {"type": "git", "url": "git://github.com/blakeembrey/constant-case.git"}, "keywords": ["constant", "case", "upper"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blakeembrey.me"}, "license": "MIT", "bugs": {"url": "https://github.com/blakeembrey/constant-case/issues"}, "homepage": "https://github.com/blakeembrey/constant-case", "devDependencies": {"istanbul": "^0.4.3", "mocha": "^2.2.1", "standard": "^7.1.2"}, "dependencies": {"snake-case": "^2.1.0", "upper-case": "^1.1.1"}}