{"version": 3, "sources": ["../src/index.ts", "../src/providers/anthropic.ts", "../src/providers/bedrock.ts", "../src/providers/chutes.ts", "../src/providers/deepseek.ts", "../src/providers/gemini.ts", "../src/providers/glama.ts", "../src/providers/groq.ts", "../src/providers/lite-llm.ts", "../src/providers/lm-studio.ts", "../src/providers/mistral.ts", "../src/providers/openai.ts", "../src/providers/openrouter.ts", "../src/providers/requesty.ts", "../src/providers/unbound.ts", "../src/providers/vertex.ts", "../src/providers/vscode-llm.ts", "../src/providers/xai.ts", "../src/codebase-index.ts", "../src/cloud.ts", "../src/experiment.ts", "../src/global-settings.ts", "../src/type-fu.ts", "../src/provider-settings.ts", "../src/model.ts", "../src/history.ts", "../src/telemetry.ts", "../src/message.ts", "../src/mode.ts", "../src/tool.ts", "../src/vscode.ts", "../src/ipc.ts", "../src/terminal.ts", "../src/user-management.ts"], "sourcesContent": ["export * from \"./providers/index.js\"\r\n\r\nexport * from \"./api.js\"\r\nexport * from \"./codebase-index.js\"\r\nexport * from \"./cloud.js\"\r\nexport * from \"./experiment.js\"\r\nexport * from \"./global-settings.js\"\r\nexport * from \"./history.js\"\r\nexport * from \"./ipc.js\"\r\nexport * from \"./message.js\"\r\nexport * from \"./mode.js\"\r\nexport * from \"./model.js\"\r\nexport * from \"./provider-settings.js\"\r\nexport * from \"./telemetry.js\"\r\nexport * from \"./terminal.js\"\r\nexport * from \"./tool.js\"\r\nexport * from \"./type-fu.js\"\r\nexport * from \"./user-management.js\"\r\nexport * from \"./vscode.js\"\r\n", "import type { ModelInfo } from \"../model.js\"\r\n\r\n// https://docs.anthropic.com/en/docs/about-claude/models\r\n\r\nexport type AnthropicModelId = keyof typeof anthropicModels\r\nexport const anthropicDefaultModelId: AnthropicModelId = \"claude-sonnet-4-20250514\"\r\n\r\nexport const anthropicModels = {\r\n\t\"claude-sonnet-4-20250514\": {\r\n\t\tmaxTokens: 64_000, // Overridden to 8k if `enableReasoningEffort` is false.\r\n\t\tcontextWindow: 200_000,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsComputerUse: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 3.0, // $3 per million input tokens\r\n\t\toutputPrice: 15.0, // $15 per million output tokens\r\n\t\tcacheWritesPrice: 3.75, // $3.75 per million tokens\r\n\t\tcacheReadsPrice: 0.3, // $0.30 per million tokens\r\n\t\tsupportsReasoningBudget: true,\r\n\t},\r\n\t\"claude-opus-4-20250514\": {\r\n\t\tmaxTokens: 32_000, // Overridden to 8k if `enableReasoningEffort` is false.\r\n\t\tcontextWindow: 200_000,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsComputerUse: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 15.0, // $15 per million input tokens\r\n\t\toutputPrice: 75.0, // $75 per million output tokens\r\n\t\tcacheWritesPrice: 18.75, // $18.75 per million tokens\r\n\t\tcacheReadsPrice: 1.5, // $1.50 per million tokens\r\n\t\tsupportsReasoningBudget: true,\r\n\t},\r\n\t\"claude-3-7-sonnet-20250219:thinking\": {\r\n\t\tmaxTokens: 128_000, // Unlocked by passing `beta` flag to the model. Otherwise, it's 64k.\r\n\t\tcontextWindow: 200_000,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsComputerUse: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 3.0, // $3 per million input tokens\r\n\t\toutputPrice: 15.0, // $15 per million output tokens\r\n\t\tcacheWritesPrice: 3.75, // $3.75 per million tokens\r\n\t\tcacheReadsPrice: 0.3, // $0.30 per million tokens\r\n\t\tsupportsReasoningBudget: true,\r\n\t\trequiredReasoningBudget: true,\r\n\t},\r\n\t\"claude-3-7-sonnet-20250219\": {\r\n\t\tmaxTokens: 8192, // Since we already have a `:thinking` virtual model we aren't setting `supportsReasoningBudget: true` here.\r\n\t\tcontextWindow: 200_000,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsComputerUse: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 3.0, // $3 per million input tokens\r\n\t\toutputPrice: 15.0, // $15 per million output tokens\r\n\t\tcacheWritesPrice: 3.75, // $3.75 per million tokens\r\n\t\tcacheReadsPrice: 0.3, // $0.30 per million tokens\r\n\t},\r\n\t\"claude-3-5-sonnet-20241022\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 200_000,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsComputerUse: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 3.0, // $3 per million input tokens\r\n\t\toutputPrice: 15.0, // $15 per million output tokens\r\n\t\tcacheWritesPrice: 3.75, // $3.75 per million tokens\r\n\t\tcacheReadsPrice: 0.3, // $0.30 per million tokens\r\n\t},\r\n\t\"claude-3-5-haiku-20241022\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 200_000,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 1.0,\r\n\t\toutputPrice: 5.0,\r\n\t\tcacheWritesPrice: 1.25,\r\n\t\tcacheReadsPrice: 0.1,\r\n\t},\r\n\t\"claude-3-opus-20240229\": {\r\n\t\tmaxTokens: 4096,\r\n\t\tcontextWindow: 200_000,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 15.0,\r\n\t\toutputPrice: 75.0,\r\n\t\tcacheWritesPrice: 18.75,\r\n\t\tcacheReadsPrice: 1.5,\r\n\t},\r\n\t\"claude-3-haiku-20240307\": {\r\n\t\tmaxTokens: 4096,\r\n\t\tcontextWindow: 200_000,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 0.25,\r\n\t\toutputPrice: 1.25,\r\n\t\tcacheWritesPrice: 0.3,\r\n\t\tcacheReadsPrice: 0.03,\r\n\t},\r\n} as const satisfies Record<string, ModelInfo>\r\n\r\nexport const ANTHROPIC_DEFAULT_MAX_TOKENS = 8192\r\n", "import type { ModelInfo } from \"../model.js\"\r\n\r\n// https://docs.aws.amazon.com/bedrock/latest/userguide/conversation-inference.html\r\n\r\nexport type BedrockModelId = keyof typeof bedrockModels\r\n\r\nexport const bedrockDefaultModelId: BedrockModelId = \"anthropic.claude-sonnet-4-20250514-v1:0\"\r\n\r\nexport const bedrockDefaultPromptRouterModelId: BedrockModelId = \"anthropic.claude-3-sonnet-20240229-v1:0\"\r\n\r\n// March, 12 2025 - updated prices to match US-West-2 list price shown at\r\n// https://aws.amazon.com/bedrock/pricing, including older models that are part\r\n// of the default prompt routers AWS enabled for GA of the promot router\r\n// feature.\r\nexport const bedrockModels = {\r\n\t\"amazon.nova-pro-v1:0\": {\r\n\t\tmaxTokens: 5000,\r\n\t\tcontextWindow: 300_000,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsComputerUse: false,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 0.8,\r\n\t\toutputPrice: 3.2,\r\n\t\tcacheWritesPrice: 0.8, // per million tokens\r\n\t\tcacheReadsPrice: 0.2, // per million tokens\r\n\t\tminTokensPerCachePoint: 1,\r\n\t\tmaxCachePoints: 1,\r\n\t\tcachableFields: [\"system\"],\r\n\t},\r\n\t\"amazon.nova-pro-latency-optimized-v1:0\": {\r\n\t\tmaxTokens: 5000,\r\n\t\tcontextWindow: 300_000,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsComputerUse: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 1.0,\r\n\t\toutputPrice: 4.0,\r\n\t\tcacheWritesPrice: 1.0, // per million tokens\r\n\t\tcacheReadsPrice: 0.25, // per million tokens\r\n\t\tdescription: \"Amazon Nova Pro with latency optimized inference\",\r\n\t},\r\n\t\"amazon.nova-lite-v1:0\": {\r\n\t\tmaxTokens: 5000,\r\n\t\tcontextWindow: 300_000,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsComputerUse: false,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 0.06,\r\n\t\toutputPrice: 0.24,\r\n\t\tcacheWritesPrice: 0.06, // per million tokens\r\n\t\tcacheReadsPrice: 0.015, // per million tokens\r\n\t\tminTokensPerCachePoint: 1,\r\n\t\tmaxCachePoints: 1,\r\n\t\tcachableFields: [\"system\"],\r\n\t},\r\n\t\"amazon.nova-micro-v1:0\": {\r\n\t\tmaxTokens: 5000,\r\n\t\tcontextWindow: 128_000,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsComputerUse: false,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 0.035,\r\n\t\toutputPrice: 0.14,\r\n\t\tcacheWritesPrice: 0.035, // per million tokens\r\n\t\tcacheReadsPrice: 0.00875, // per million tokens\r\n\t\tminTokensPerCachePoint: 1,\r\n\t\tmaxCachePoints: 1,\r\n\t\tcachableFields: [\"system\"],\r\n\t},\r\n\t\"anthropic.claude-sonnet-4-20250514-v1:0\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 200_000,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsComputerUse: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 3.0,\r\n\t\toutputPrice: 15.0,\r\n\t\tcacheWritesPrice: 3.75,\r\n\t\tcacheReadsPrice: 0.3,\r\n\t\tminTokensPerCachePoint: 1024,\r\n\t\tmaxCachePoints: 4,\r\n\t\tcachableFields: [\"system\", \"messages\", \"tools\"],\r\n\t},\r\n\t\"anthropic.claude-opus-4-20250514-v1:0\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 200_000,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsComputerUse: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 15.0,\r\n\t\toutputPrice: 75.0,\r\n\t\tcacheWritesPrice: 18.75,\r\n\t\tcacheReadsPrice: 1.5,\r\n\t\tminTokensPerCachePoint: 1024,\r\n\t\tmaxCachePoints: 4,\r\n\t\tcachableFields: [\"system\", \"messages\", \"tools\"],\r\n\t},\r\n\t\"anthropic.claude-3-7-sonnet-20250219-v1:0\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 200_000,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsComputerUse: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 3.0,\r\n\t\toutputPrice: 15.0,\r\n\t\tcacheWritesPrice: 3.75,\r\n\t\tcacheReadsPrice: 0.3,\r\n\t\tminTokensPerCachePoint: 1024,\r\n\t\tmaxCachePoints: 4,\r\n\t\tcachableFields: [\"system\", \"messages\", \"tools\"],\r\n\t},\r\n\t\"anthropic.claude-3-5-sonnet-20241022-v2:0\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 200_000,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsComputerUse: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 3.0,\r\n\t\toutputPrice: 15.0,\r\n\t\tcacheWritesPrice: 3.75,\r\n\t\tcacheReadsPrice: 0.3,\r\n\t\tminTokensPerCachePoint: 1024,\r\n\t\tmaxCachePoints: 4,\r\n\t\tcachableFields: [\"system\", \"messages\", \"tools\"],\r\n\t},\r\n\t\"anthropic.claude-3-5-haiku-20241022-v1:0\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 200_000,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 0.8,\r\n\t\toutputPrice: 4.0,\r\n\t\tcacheWritesPrice: 1.0,\r\n\t\tcacheReadsPrice: 0.08,\r\n\t\tminTokensPerCachePoint: 2048,\r\n\t\tmaxCachePoints: 4,\r\n\t\tcachableFields: [\"system\", \"messages\", \"tools\"],\r\n\t},\r\n\t\"anthropic.claude-3-5-sonnet-20240620-v1:0\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 200_000,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 3.0,\r\n\t\toutputPrice: 15.0,\r\n\t},\r\n\t\"anthropic.claude-3-opus-20240229-v1:0\": {\r\n\t\tmaxTokens: 4096,\r\n\t\tcontextWindow: 200_000,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 15.0,\r\n\t\toutputPrice: 75.0,\r\n\t},\r\n\t\"anthropic.claude-3-sonnet-20240229-v1:0\": {\r\n\t\tmaxTokens: 4096,\r\n\t\tcontextWindow: 200_000,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 3.0,\r\n\t\toutputPrice: 15.0,\r\n\t},\r\n\t\"anthropic.claude-3-haiku-20240307-v1:0\": {\r\n\t\tmaxTokens: 4096,\r\n\t\tcontextWindow: 200_000,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0.25,\r\n\t\toutputPrice: 1.25,\r\n\t},\r\n\t\"anthropic.claude-2-1-v1:0\": {\r\n\t\tmaxTokens: 4096,\r\n\t\tcontextWindow: 100_000,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 8.0,\r\n\t\toutputPrice: 24.0,\r\n\t\tdescription: \"Claude 2.1\",\r\n\t},\r\n\t\"anthropic.claude-2-0-v1:0\": {\r\n\t\tmaxTokens: 4096,\r\n\t\tcontextWindow: 100_000,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 8.0,\r\n\t\toutputPrice: 24.0,\r\n\t\tdescription: \"Claude 2.0\",\r\n\t},\r\n\t\"anthropic.claude-instant-v1:0\": {\r\n\t\tmaxTokens: 4096,\r\n\t\tcontextWindow: 100_000,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0.8,\r\n\t\toutputPrice: 2.4,\r\n\t\tdescription: \"Claude Instant\",\r\n\t},\r\n\t\"deepseek.r1-v1:0\": {\r\n\t\tmaxTokens: 32_768,\r\n\t\tcontextWindow: 128_000,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 1.35,\r\n\t\toutputPrice: 5.4,\r\n\t},\r\n\t\"meta.llama3-3-70b-instruct-v1:0\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 128_000,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsComputerUse: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0.72,\r\n\t\toutputPrice: 0.72,\r\n\t\tdescription: \"Llama 3.3 Instruct (70B)\",\r\n\t},\r\n\t\"meta.llama3-2-90b-instruct-v1:0\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 128_000,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsComputerUse: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0.72,\r\n\t\toutputPrice: 0.72,\r\n\t\tdescription: \"Llama 3.2 Instruct (90B)\",\r\n\t},\r\n\t\"meta.llama3-2-11b-instruct-v1:0\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 128_000,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsComputerUse: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0.16,\r\n\t\toutputPrice: 0.16,\r\n\t\tdescription: \"Llama 3.2 Instruct (11B)\",\r\n\t},\r\n\t\"meta.llama3-2-3b-instruct-v1:0\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 128_000,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsComputerUse: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0.15,\r\n\t\toutputPrice: 0.15,\r\n\t\tdescription: \"Llama 3.2 Instruct (3B)\",\r\n\t},\r\n\t\"meta.llama3-2-1b-instruct-v1:0\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 128_000,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsComputerUse: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0.1,\r\n\t\toutputPrice: 0.1,\r\n\t\tdescription: \"Llama 3.2 Instruct (1B)\",\r\n\t},\r\n\t\"meta.llama3-1-405b-instruct-v1:0\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 128_000,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsComputerUse: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 2.4,\r\n\t\toutputPrice: 2.4,\r\n\t\tdescription: \"Llama 3.1 Instruct (405B)\",\r\n\t},\r\n\t\"meta.llama3-1-70b-instruct-v1:0\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 128_000,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsComputerUse: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0.72,\r\n\t\toutputPrice: 0.72,\r\n\t\tdescription: \"Llama 3.1 Instruct (70B)\",\r\n\t},\r\n\t\"meta.llama3-1-70b-instruct-latency-optimized-v1:0\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 128_000,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsComputerUse: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0.9,\r\n\t\toutputPrice: 0.9,\r\n\t\tdescription: \"Llama 3.1 Instruct (70B) (w/ latency optimized inference)\",\r\n\t},\r\n\t\"meta.llama3-1-8b-instruct-v1:0\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 8_000,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsComputerUse: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0.22,\r\n\t\toutputPrice: 0.22,\r\n\t\tdescription: \"Llama 3.1 Instruct (8B)\",\r\n\t},\r\n\t\"meta.llama3-70b-instruct-v1:0\": {\r\n\t\tmaxTokens: 2048,\r\n\t\tcontextWindow: 8_000,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsComputerUse: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 2.65,\r\n\t\toutputPrice: 3.5,\r\n\t},\r\n\t\"meta.llama3-8b-instruct-v1:0\": {\r\n\t\tmaxTokens: 2048,\r\n\t\tcontextWindow: 4_000,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsComputerUse: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0.3,\r\n\t\toutputPrice: 0.6,\r\n\t},\r\n\t\"amazon.titan-text-lite-v1:0\": {\r\n\t\tmaxTokens: 4096,\r\n\t\tcontextWindow: 8_000,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsComputerUse: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0.15,\r\n\t\toutputPrice: 0.2,\r\n\t\tdescription: \"Amazon Titan Text Lite\",\r\n\t},\r\n\t\"amazon.titan-text-express-v1:0\": {\r\n\t\tmaxTokens: 4096,\r\n\t\tcontextWindow: 8_000,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsComputerUse: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0.2,\r\n\t\toutputPrice: 0.6,\r\n\t\tdescription: \"Amazon Titan Text Express\",\r\n\t},\r\n\t\"amazon.titan-text-embeddings-v1:0\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 8_000,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsComputerUse: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0.1,\r\n\t\tdescription: \"Amazon Titan Text Embeddings\",\r\n\t},\r\n\t\"amazon.titan-text-embeddings-v2:0\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 8_000,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsComputerUse: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0.02,\r\n\t\tdescription: \"Amazon Titan Text Embeddings V2\",\r\n\t},\r\n} as const satisfies Record<string, ModelInfo>\r\n\r\nexport const BEDROCK_DEFAULT_TEMPERATURE = 0.3\r\n\r\nexport const BEDROCK_MAX_TOKENS = 4096\r\n\r\nexport const BEDROCK_REGION_INFO: Record<\r\n\tstring,\r\n\t{\r\n\t\tregionId: string\r\n\t\tdescription: string\r\n\t\tpattern?: string\r\n\t\tmultiRegion?: boolean\r\n\t}\r\n> = {\r\n\t/*\r\n\t * This JSON generated by AWS's AI assistant - Amazon Q on March 29, 2025\r\n\t *\r\n\t *  - Africa (Cape Town) region does not appear to support Amazon Bedrock at this time.\r\n\t *  - Some Asia Pacific regions, such as Asia Pacific (Hong Kong) and Asia Pacific (Jakarta), are not listed among the supported regions for Bedrock services.\r\n\t *  - Middle East regions, including Middle East (Bahrain) and Middle East (UAE), are not mentioned in the list of supported regions for Bedrock. [3]\r\n\t *  - China regions (Beijing and Ningxia) are not listed as supported for Amazon Bedrock.\r\n\t *  - Some newer or specialized AWS regions may not have Bedrock support yet.\r\n\t */\r\n\t\"us.\": { regionId: \"us-east-1\", description: \"US East (N. Virginia)\", pattern: \"us-\", multiRegion: true },\r\n\t\"use.\": { regionId: \"us-east-1\", description: \"US East (N. Virginia)\" },\r\n\t\"use1.\": { regionId: \"us-east-1\", description: \"US East (N. Virginia)\" },\r\n\t\"use2.\": { regionId: \"us-east-2\", description: \"US East (Ohio)\" },\r\n\t\"usw.\": { regionId: \"us-west-2\", description: \"US West (Oregon)\" },\r\n\t\"usw2.\": { regionId: \"us-west-2\", description: \"US West (Oregon)\" },\r\n\t\"ug.\": {\r\n\t\tregionId: \"us-gov-west-1\",\r\n\t\tdescription: \"AWS GovCloud (US-West)\",\r\n\t\tpattern: \"us-gov-\",\r\n\t\tmultiRegion: true,\r\n\t},\r\n\t\"uge1.\": { regionId: \"us-gov-east-1\", description: \"AWS GovCloud (US-East)\" },\r\n\t\"ugw1.\": { regionId: \"us-gov-west-1\", description: \"AWS GovCloud (US-West)\" },\r\n\t\"eu.\": { regionId: \"eu-west-1\", description: \"Europe (Ireland)\", pattern: \"eu-\", multiRegion: true },\r\n\t\"euw1.\": { regionId: \"eu-west-1\", description: \"Europe (Ireland)\" },\r\n\t\"euw2.\": { regionId: \"eu-west-2\", description: \"Europe (London)\" },\r\n\t\"euw3.\": { regionId: \"eu-west-3\", description: \"Europe (Paris)\" },\r\n\t\"euc1.\": { regionId: \"eu-central-1\", description: \"Europe (Frankfurt)\" },\r\n\t\"euc2.\": { regionId: \"eu-central-2\", description: \"Europe (Zurich)\" },\r\n\t\"eun1.\": { regionId: \"eu-north-1\", description: \"Europe (Stockholm)\" },\r\n\t\"eus1.\": { regionId: \"eu-south-1\", description: \"Europe (Milan)\" },\r\n\t\"eus2.\": { regionId: \"eu-south-2\", description: \"Europe (Spain)\" },\r\n\t\"ap.\": {\r\n\t\tregionId: \"ap-southeast-1\",\r\n\t\tdescription: \"Asia Pacific (Singapore)\",\r\n\t\tpattern: \"ap-\",\r\n\t\tmultiRegion: true,\r\n\t},\r\n\t\"ape1.\": { regionId: \"ap-east-1\", description: \"Asia Pacific (Hong Kong)\" },\r\n\t\"apne1.\": { regionId: \"ap-northeast-1\", description: \"Asia Pacific (Tokyo)\" },\r\n\t\"apne2.\": { regionId: \"ap-northeast-2\", description: \"Asia Pacific (Seoul)\" },\r\n\t\"apne3.\": { regionId: \"ap-northeast-3\", description: \"Asia Pacific (Osaka)\" },\r\n\t\"aps1.\": { regionId: \"ap-south-1\", description: \"Asia Pacific (Mumbai)\" },\r\n\t\"aps2.\": { regionId: \"ap-south-2\", description: \"Asia Pacific (Hyderabad)\" },\r\n\t\"apse1.\": { regionId: \"ap-southeast-1\", description: \"Asia Pacific (Singapore)\" },\r\n\t\"apse2.\": { regionId: \"ap-southeast-2\", description: \"Asia Pacific (Sydney)\" },\r\n\t\"ca.\": { regionId: \"ca-central-1\", description: \"Canada (Central)\", pattern: \"ca-\", multiRegion: true },\r\n\t\"cac1.\": { regionId: \"ca-central-1\", description: \"Canada (Central)\" },\r\n\t\"sa.\": { regionId: \"sa-east-1\", description: \"South America (São Paulo)\", pattern: \"sa-\", multiRegion: true },\r\n\t\"sae1.\": { regionId: \"sa-east-1\", description: \"South America (São Paulo)\" },\r\n\r\n\t// These are not official - they weren't generated by Amazon Q nor were\r\n\t// found in the AWS documentation but another cubent contributor found apac.\r\n\t// Was needed so I've added the pattern of the other geo zones.\r\n\t\"apac.\": { regionId: \"ap-southeast-1\", description: \"Default APAC region\", pattern: \"ap-\", multiRegion: true },\r\n\t\"emea.\": { regionId: \"eu-west-1\", description: \"Default EMEA region\", pattern: \"eu-\", multiRegion: true },\r\n\t\"amer.\": { regionId: \"us-east-1\", description: \"Default Americas region\", pattern: \"us-\", multiRegion: true },\r\n}\r\n\r\nexport const BEDROCK_REGIONS = Object.values(BEDROCK_REGION_INFO)\r\n\t// Extract all region IDs\r\n\t.map((info) => ({ value: info.regionId, label: info.regionId }))\r\n\t// Filter to unique region IDs (remove duplicates)\r\n\t.filter((region, index, self) => index === self.findIndex((r) => r.value === region.value))\r\n\t// Sort alphabetically by region ID\r\n\t.sort((a, b) => a.value.localeCompare(b.value))\r\n", "import type { ModelInfo } from \"../model.js\"\r\n\r\n// https://llm.chutes.ai/v1 (OpenAI compatible)\r\nexport type ChutesModelId =\r\n\t| \"deepseek-ai/DeepSeek-R1-0528\"\r\n\t| \"deepseek-ai/DeepSeek-R1\"\r\n\t| \"deepseek-ai/DeepSeek-V3\"\r\n\t| \"unsloth/Llama-3.3-70B-Instruct\"\r\n\t| \"chutesai/Llama-4-Scout-17B-16E-Instruct\"\r\n\t| \"unsloth/Mistral-Nemo-Instruct-2407\"\r\n\t| \"unsloth/gemma-3-12b-it\"\r\n\t| \"NousResearch/DeepHermes-3-Llama-3-8B-Preview\"\r\n\t| \"unsloth/gemma-3-4b-it\"\r\n\t| \"nvidia/Llama-3_3-Nemotron-Super-49B-v1\"\r\n\t| \"nvidia/Llama-3_1-Nemotron-Ultra-253B-v1\"\r\n\t| \"chutesai/Llama-4-Maverick-17B-128E-Instruct-FP8\"\r\n\t| \"deepseek-ai/DeepSeek-V3-Base\"\r\n\t| \"deepseek-ai/DeepSeek-R1-Zero\"\r\n\t| \"deepseek-ai/DeepSeek-V3-0324\"\r\n\t| \"Qwen/Qwen3-235B-A22B\"\r\n\t| \"Qwen/Qwen3-32B\"\r\n\t| \"Qwen/Qwen3-30B-A3B\"\r\n\t| \"Qwen/Qwen3-14B\"\r\n\t| \"Qwen/Qwen3-8B\"\r\n\t| \"microsoft/MAI-DS-R1-FP8\"\r\n\t| \"tngtech/DeepSeek-R1T-Chimera\"\r\n\r\nexport const chutesDefaultModelId: ChutesModelId = \"deepseek-ai/DeepSeek-R1-0528\"\r\n\r\nexport const chutesModels = {\r\n\t\"deepseek-ai/DeepSeek-R1-0528\": {\r\n\t\tmaxTokens: 32768,\r\n\t\tcontextWindow: 163840,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t\tdescription: \"DeepSeek R1 0528 model.\",\r\n\t},\r\n\t\"deepseek-ai/DeepSeek-R1\": {\r\n\t\tmaxTokens: 32768,\r\n\t\tcontextWindow: 163840,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t\tdescription: \"DeepSeek R1 model.\",\r\n\t},\r\n\t\"deepseek-ai/DeepSeek-V3\": {\r\n\t\tmaxTokens: 32768,\r\n\t\tcontextWindow: 163840,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t\tdescription: \"DeepSeek V3 model.\",\r\n\t},\r\n\t\"unsloth/Llama-3.3-70B-Instruct\": {\r\n\t\tmaxTokens: 32768, // From Groq\r\n\t\tcontextWindow: 131072, // From Groq\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t\tdescription: \"Unsloth Llama 3.3 70B Instruct model.\",\r\n\t},\r\n\t\"chutesai/Llama-4-Scout-17B-16E-Instruct\": {\r\n\t\tmaxTokens: 32768,\r\n\t\tcontextWindow: 512000,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t\tdescription: \"ChutesAI Llama 4 Scout 17B Instruct model, 512K context.\",\r\n\t},\r\n\t\"unsloth/Mistral-Nemo-Instruct-2407\": {\r\n\t\tmaxTokens: 32768,\r\n\t\tcontextWindow: 128000,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t\tdescription: \"Unsloth Mistral Nemo Instruct model.\",\r\n\t},\r\n\t\"unsloth/gemma-3-12b-it\": {\r\n\t\tmaxTokens: 32768,\r\n\t\tcontextWindow: 131072,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t\tdescription: \"Unsloth Gemma 3 12B IT model.\",\r\n\t},\r\n\t\"NousResearch/DeepHermes-3-Llama-3-8B-Preview\": {\r\n\t\tmaxTokens: 32768,\r\n\t\tcontextWindow: 131072,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t\tdescription: \"Nous DeepHermes 3 Llama 3 8B Preview model.\",\r\n\t},\r\n\t\"unsloth/gemma-3-4b-it\": {\r\n\t\tmaxTokens: 32768,\r\n\t\tcontextWindow: 131072,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t\tdescription: \"Unsloth Gemma 3 4B IT model.\",\r\n\t},\r\n\t\"nvidia/Llama-3_3-Nemotron-Super-49B-v1\": {\r\n\t\tmaxTokens: 32768,\r\n\t\tcontextWindow: 131072,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t\tdescription: \"Nvidia Llama 3.3 Nemotron Super 49B model.\",\r\n\t},\r\n\t\"nvidia/Llama-3_1-Nemotron-Ultra-253B-v1\": {\r\n\t\tmaxTokens: 32768,\r\n\t\tcontextWindow: 131072,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t\tdescription: \"Nvidia Llama 3.1 Nemotron Ultra 253B model.\",\r\n\t},\r\n\t\"chutesai/Llama-4-Maverick-17B-128E-Instruct-FP8\": {\r\n\t\tmaxTokens: 32768,\r\n\t\tcontextWindow: 256000,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t\tdescription: \"ChutesAI Llama 4 Maverick 17B Instruct FP8 model.\",\r\n\t},\r\n\t\"deepseek-ai/DeepSeek-V3-Base\": {\r\n\t\tmaxTokens: 32768,\r\n\t\tcontextWindow: 163840,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t\tdescription: \"DeepSeek V3 Base model.\",\r\n\t},\r\n\t\"deepseek-ai/DeepSeek-R1-Zero\": {\r\n\t\tmaxTokens: 32768,\r\n\t\tcontextWindow: 163840,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t\tdescription: \"DeepSeek R1 Zero model.\",\r\n\t},\r\n\t\"deepseek-ai/DeepSeek-V3-0324\": {\r\n\t\tmaxTokens: 32768,\r\n\t\tcontextWindow: 163840,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t\tdescription: \"DeepSeek V3 (0324) model.\",\r\n\t},\r\n\t\"Qwen/Qwen3-235B-A22B\": {\r\n\t\tmaxTokens: 32768,\r\n\t\tcontextWindow: 40960,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t\tdescription: \"Qwen3 235B A22B model.\",\r\n\t},\r\n\t\"Qwen/Qwen3-32B\": {\r\n\t\tmaxTokens: 32768,\r\n\t\tcontextWindow: 40960,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t\tdescription: \"Qwen3 32B model.\",\r\n\t},\r\n\t\"Qwen/Qwen3-30B-A3B\": {\r\n\t\tmaxTokens: 32768,\r\n\t\tcontextWindow: 40960,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t\tdescription: \"Qwen3 30B A3B model.\",\r\n\t},\r\n\t\"Qwen/Qwen3-14B\": {\r\n\t\tmaxTokens: 32768,\r\n\t\tcontextWindow: 40960,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t\tdescription: \"Qwen3 14B model.\",\r\n\t},\r\n\t\"Qwen/Qwen3-8B\": {\r\n\t\tmaxTokens: 32768,\r\n\t\tcontextWindow: 40960,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t\tdescription: \"Qwen3 8B model.\",\r\n\t},\r\n\t\"microsoft/MAI-DS-R1-FP8\": {\r\n\t\tmaxTokens: 32768,\r\n\t\tcontextWindow: 163840,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t\tdescription: \"Microsoft MAI-DS-R1 FP8 model.\",\r\n\t},\r\n\t\"tngtech/DeepSeek-R1T-Chimera\": {\r\n\t\tmaxTokens: 32768,\r\n\t\tcontextWindow: 163840,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t\tdescription: \"TNGTech DeepSeek R1T Chimera model.\",\r\n\t},\r\n} as const satisfies Record<string, ModelInfo>\r\n", "import type { ModelInfo } from \"../model.js\"\r\n\r\n// https://platform.deepseek.com/docs/api\r\nexport type DeepSeekModelId = keyof typeof deepSeekModels\r\n\r\nexport const deepSeekDefaultModelId: DeepSeekModelId = \"deepseek-chat\"\r\n\r\nexport const deepSeekModels = {\r\n\t\"deepseek-chat\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 64_000,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 0.27, // $0.27 per million tokens (cache miss)\r\n\t\toutputPrice: 1.1, // $1.10 per million tokens\r\n\t\tcacheWritesPrice: 0.27, // $0.27 per million tokens (cache miss)\r\n\t\tcacheReadsPrice: 0.07, // $0.07 per million tokens (cache hit).\r\n\t\tdescription: `DeepSeek-V3 achieves a significant breakthrough in inference speed over previous models. It tops the leaderboard among open-source models and rivals the most advanced closed-source models globally.`,\r\n\t},\r\n\t\"deepseek-reasoner\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 64_000,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 0.55, // $0.55 per million tokens (cache miss)\r\n\t\toutputPrice: 2.19, // $2.19 per million tokens\r\n\t\tcacheWritesPrice: 0.55, // $0.55 per million tokens (cache miss)\r\n\t\tcacheReadsPrice: 0.14, // $0.14 per million tokens (cache hit)\r\n\t\tdescription: `DeepSeek-R1 achieves performance comparable to OpenAI-o1 across math, code, and reasoning tasks. Supports Chain of Thought reasoning with up to 32K tokens.`,\r\n\t},\r\n} as const satisfies Record<string, ModelInfo>\r\n\r\nexport const DEEP_SEEK_DEFAULT_TEMPERATURE = 0.6\r\n", "import type { ModelInfo } from \"../model.js\"\r\n\r\n// https://ai.google.dev/gemini-api/docs/models/gemini\r\nexport type GeminiModelId = keyof typeof geminiModels\r\n\r\nexport const geminiDefaultModelId: GeminiModelId = \"gemini-2.0-flash-001\"\r\n\r\nexport const geminiModels = {\r\n\t\"gemini-2.5-flash-preview-04-17:thinking\": {\r\n\t\tmaxTokens: 65_535,\r\n\t\tcontextWindow: 1_048_576,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0.15,\r\n\t\toutputPrice: 3.5,\r\n\t\tmaxThinkingTokens: 24_576,\r\n\t\tsupportsReasoningBudget: true,\r\n\t\trequiredReasoningBudget: true,\r\n\t},\r\n\t\"gemini-2.5-flash-preview-04-17\": {\r\n\t\tmaxTokens: 65_535,\r\n\t\tcontextWindow: 1_048_576,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0.15,\r\n\t\toutputPrice: 0.6,\r\n\t},\r\n\t\"gemini-2.5-flash-preview-05-20:thinking\": {\r\n\t\tmaxTokens: 65_535,\r\n\t\tcontextWindow: 1_048_576,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 0.15,\r\n\t\toutputPrice: 3.5,\r\n\t\tcacheReadsPrice: 0.0375,\r\n\t\tcacheWritesPrice: 1.0,\r\n\t\tmaxThinkingTokens: 24_576,\r\n\t\tsupportsReasoningBudget: true,\r\n\t\trequiredReasoningBudget: true,\r\n\t},\r\n\t\"gemini-2.5-flash-preview-05-20\": {\r\n\t\tmaxTokens: 65_535,\r\n\t\tcontextWindow: 1_048_576,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 0.15,\r\n\t\toutputPrice: 0.6,\r\n\t\tcacheReadsPrice: 0.0375,\r\n\t\tcacheWritesPrice: 1.0,\r\n\t},\r\n\t\"gemini-2.5-pro-exp-03-25\": {\r\n\t\tmaxTokens: 65_535,\r\n\t\tcontextWindow: 1_048_576,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t},\r\n\t\"gemini-2.5-pro-preview-03-25\": {\r\n\t\tmaxTokens: 65_535,\r\n\t\tcontextWindow: 1_048_576,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 2.5, // This is the pricing for prompts above 200k tokens.\r\n\t\toutputPrice: 15,\r\n\t\tcacheReadsPrice: 0.625,\r\n\t\tcacheWritesPrice: 4.5,\r\n\t\ttiers: [\r\n\t\t\t{\r\n\t\t\t\tcontextWindow: 200_000,\r\n\t\t\t\tinputPrice: 1.25,\r\n\t\t\t\toutputPrice: 10,\r\n\t\t\t\tcacheReadsPrice: 0.31,\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tcontextWindow: Infinity,\r\n\t\t\t\tinputPrice: 2.5,\r\n\t\t\t\toutputPrice: 15,\r\n\t\t\t\tcacheReadsPrice: 0.625,\r\n\t\t\t},\r\n\t\t],\r\n\t},\r\n\t\"gemini-2.5-pro-preview-05-06\": {\r\n\t\tmaxTokens: 65_535,\r\n\t\tcontextWindow: 1_048_576,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 2.5, // This is the pricing for prompts above 200k tokens.\r\n\t\toutputPrice: 15,\r\n\t\tcacheReadsPrice: 0.625,\r\n\t\tcacheWritesPrice: 4.5,\r\n\t\ttiers: [\r\n\t\t\t{\r\n\t\t\t\tcontextWindow: 200_000,\r\n\t\t\t\tinputPrice: 1.25,\r\n\t\t\t\toutputPrice: 10,\r\n\t\t\t\tcacheReadsPrice: 0.31,\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tcontextWindow: Infinity,\r\n\t\t\t\tinputPrice: 2.5,\r\n\t\t\t\toutputPrice: 15,\r\n\t\t\t\tcacheReadsPrice: 0.625,\r\n\t\t\t},\r\n\t\t],\r\n\t},\r\n\t\"gemini-2.0-flash-001\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 1_048_576,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 0.1,\r\n\t\toutputPrice: 0.4,\r\n\t\tcacheReadsPrice: 0.025,\r\n\t\tcacheWritesPrice: 1.0,\r\n\t},\r\n\t\"gemini-2.0-flash-lite-preview-02-05\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 1_048_576,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t},\r\n\t\"gemini-2.0-pro-exp-02-05\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 2_097_152,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t},\r\n\t\"gemini-2.0-flash-thinking-exp-01-21\": {\r\n\t\tmaxTokens: 65_536,\r\n\t\tcontextWindow: 1_048_576,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t},\r\n\t\"gemini-2.0-flash-thinking-exp-1219\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 32_767,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t},\r\n\t\"gemini-2.0-flash-exp\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 1_048_576,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t},\r\n\t\"gemini-1.5-flash-002\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 1_048_576,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 0.15, // This is the pricing for prompts above 128k tokens.\r\n\t\toutputPrice: 0.6,\r\n\t\tcacheReadsPrice: 0.0375,\r\n\t\tcacheWritesPrice: 1.0,\r\n\t\ttiers: [\r\n\t\t\t{\r\n\t\t\t\tcontextWindow: 128_000,\r\n\t\t\t\tinputPrice: 0.075,\r\n\t\t\t\toutputPrice: 0.3,\r\n\t\t\t\tcacheReadsPrice: 0.01875,\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tcontextWindow: Infinity,\r\n\t\t\t\tinputPrice: 0.15,\r\n\t\t\t\toutputPrice: 0.6,\r\n\t\t\t\tcacheReadsPrice: 0.0375,\r\n\t\t\t},\r\n\t\t],\r\n\t},\r\n\t\"gemini-1.5-flash-exp-0827\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 1_048_576,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t},\r\n\t\"gemini-1.5-flash-8b-exp-0827\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 1_048_576,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t},\r\n\t\"gemini-1.5-pro-002\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 2_097_152,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t},\r\n\t\"gemini-1.5-pro-exp-0827\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 2_097_152,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t},\r\n\t\"gemini-exp-1206\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 2_097_152,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t},\r\n} as const satisfies Record<string, ModelInfo>\r\n", "import type { ModelInfo } from \"../model.js\"\r\n\r\n// https://glama.ai/models\r\nexport const glamaDefaultModelId = \"anthropic/claude-3-7-sonnet\"\r\n\r\nexport const glamaDefaultModelInfo: ModelInfo = {\r\n\tmaxTokens: 8192,\r\n\tcontextWindow: 200_000,\r\n\tsupportsImages: true,\r\n\tsupportsComputerUse: true,\r\n\tsupportsPromptCache: true,\r\n\tinputPrice: 3.0,\r\n\toutputPrice: 15.0,\r\n\tcacheWritesPrice: 3.75,\r\n\tcacheReadsPrice: 0.3,\r\n\tdescription:\r\n\t\t\"Claude 3.7 Sonnet is an advanced large language model with improved reasoning, coding, and problem-solving capabilities. It introduces a hybrid reasoning approach, allowing users to choose between rapid responses and extended, step-by-step processing for complex tasks. The model demonstrates notable improvements in coding, particularly in front-end development and full-stack updates, and excels in agentic workflows, where it can autonomously navigate multi-step processes. Claude 3.7 Sonnet maintains performance parity with its predecessor in standard mode while offering an extended reasoning mode for enhanced accuracy in math, coding, and instruction-following tasks. Read more at the [blog post here](https://www.anthropic.com/news/claude-3-7-sonnet)\",\r\n}\r\n\r\nexport const GLAMA_DEFAULT_TEMPERATURE = 0\r\n", "import type { ModelInfo } from \"../model.js\"\r\n\r\n// https://console.groq.com/docs/models\r\nexport type GroqModelId =\r\n\t| \"llama-3.1-8b-instant\"\r\n\t| \"llama-3.3-70b-versatile\"\r\n\t| \"meta-llama/llama-4-scout-17b-16e-instruct\"\r\n\t| \"meta-llama/llama-4-maverick-17b-128e-instruct\"\r\n\t| \"mistral-saba-24b\"\r\n\t| \"qwen-qwq-32b\"\r\n\t| \"deepseek-r1-distill-llama-70b\"\r\n\r\nexport const groqDefaultModelId: GroqModelId = \"llama-3.3-70b-versatile\" // Defaulting to Llama3 70B Versatile\r\n\r\nexport const groqModels = {\r\n\t// Models based on API response: https://api.groq.com/openai/v1/models\r\n\t\"llama-3.1-8b-instant\": {\r\n\t\tmaxTokens: 131072,\r\n\t\tcontextWindow: 131072,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t\tdescription: \"Meta Llama 3.1 8B Instant model, 128K context.\",\r\n\t},\r\n\t\"llama-3.3-70b-versatile\": {\r\n\t\tmaxTokens: 32768,\r\n\t\tcontextWindow: 131072,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t\tdescription: \"Meta Llama 3.3 70B Versatile model, 128K context.\",\r\n\t},\r\n\t\"meta-llama/llama-4-scout-17b-16e-instruct\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 131072,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t\tdescription: \"Meta Llama 4 Scout 17B Instruct model, 128K context.\",\r\n\t},\r\n\t\"meta-llama/llama-4-maverick-17b-128e-instruct\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 131072,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t\tdescription: \"Meta Llama 4 Maverick 17B Instruct model, 128K context.\",\r\n\t},\r\n\t\"mistral-saba-24b\": {\r\n\t\tmaxTokens: 32768,\r\n\t\tcontextWindow: 32768,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t\tdescription: \"Mistral Saba 24B model, 32K context.\",\r\n\t},\r\n\t\"qwen-qwq-32b\": {\r\n\t\tmaxTokens: 131072,\r\n\t\tcontextWindow: 131072,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t\tdescription: \"Alibaba Qwen QwQ 32B model, 128K context.\",\r\n\t},\r\n\t\"deepseek-r1-distill-llama-70b\": {\r\n\t\tmaxTokens: 131072,\r\n\t\tcontextWindow: 131072,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t\tdescription: \"DeepSeek R1 Distill Llama 70B model, 128K context.\",\r\n\t},\r\n} as const satisfies Record<string, ModelInfo>\r\n", "import type { ModelInfo } from \"../model.js\"\r\n\r\n// https://docs.litellm.ai/\r\nexport const litellmDefaultModelId = \"claude-3-7-sonnet-20250219\"\r\n\r\nexport const litellmDefaultModelInfo: ModelInfo = {\r\n\tmaxTokens: 8192,\r\n\tcontextWindow: 200_000,\r\n\tsupportsImages: true,\r\n\tsupportsComputerUse: true,\r\n\tsupportsPromptCache: true,\r\n\tinputPrice: 3.0,\r\n\toutputPrice: 15.0,\r\n\tcacheWritesPrice: 3.75,\r\n\tcacheReadsPrice: 0.3,\r\n}\r\n\r\nexport const LITELLM_COMPUTER_USE_MODELS = new Set([\r\n\t\"claude-3-5-sonnet-latest\",\r\n\t\"claude-opus-4-20250514\",\r\n\t\"claude-sonnet-4-20250514\",\r\n\t\"claude-3-7-sonnet-latest\",\r\n\t\"claude-3-7-sonnet-20250219\",\r\n\t\"claude-3-5-sonnet-20241022\",\r\n\t\"vertex_ai/claude-3-5-sonnet\",\r\n\t\"vertex_ai/claude-3-5-sonnet-v2\",\r\n\t\"vertex_ai/claude-3-5-sonnet-v2@20241022\",\r\n\t\"vertex_ai/claude-3-7-sonnet@20250219\",\r\n\t\"vertex_ai/claude-opus-4@20250514\",\r\n\t\"vertex_ai/claude-sonnet-4@20250514\",\r\n\t\"openrouter/anthropic/claude-3.5-sonnet\",\r\n\t\"openrouter/anthropic/claude-3.5-sonnet:beta\",\r\n\t\"openrouter/anthropic/claude-3.7-sonnet\",\r\n\t\"openrouter/anthropic/claude-3.7-sonnet:beta\",\r\n\t\"anthropic.claude-opus-4-20250514-v1:0\",\r\n\t\"anthropic.claude-sonnet-4-20250514-v1:0\",\r\n\t\"anthropic.claude-3-7-sonnet-20250219-v1:0\",\r\n\t\"anthropic.claude-3-5-sonnet-20241022-v2:0\",\r\n\t\"us.anthropic.claude-3-5-sonnet-20241022-v2:0\",\r\n\t\"us.anthropic.claude-3-7-sonnet-20250219-v1:0\",\r\n\t\"us.anthropic.claude-opus-4-20250514-v1:0\",\r\n\t\"us.anthropic.claude-sonnet-4-20250514-v1:0\",\r\n\t\"eu.anthropic.claude-3-5-sonnet-20241022-v2:0\",\r\n\t\"eu.anthropic.claude-3-7-sonnet-20250219-v1:0\",\r\n\t\"eu.anthropic.claude-opus-4-20250514-v1:0\",\r\n\t\"eu.anthropic.claude-sonnet-4-20250514-v1:0\",\r\n\t\"snowflake/claude-3-5-sonnet\",\r\n])\r\n", "export const LMSTUDIO_DEFAULT_TEMPERATURE = 0\r\n", "import type { ModelInfo } from \"../model.js\"\r\n\r\n// https://docs.mistral.ai/getting-started/models/models_overview/\r\nexport type MistralModelId = keyof typeof mistralModels\r\n\r\nexport const mistralDefaultModelId: MistralModelId = \"codestral-latest\"\r\n\r\nexport const mistralModels = {\r\n\t\"codestral-latest\": {\r\n\t\tmaxTokens: 256_000,\r\n\t\tcontextWindow: 256_000,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0.3,\r\n\t\toutputPrice: 0.9,\r\n\t},\r\n\t\"mistral-large-latest\": {\r\n\t\tmaxTokens: 131_000,\r\n\t\tcontextWindow: 131_000,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 2.0,\r\n\t\toutputPrice: 6.0,\r\n\t},\r\n\t\"ministral-8b-latest\": {\r\n\t\tmaxTokens: 131_000,\r\n\t\tcontextWindow: 131_000,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0.1,\r\n\t\toutputPrice: 0.1,\r\n\t},\r\n\t\"ministral-3b-latest\": {\r\n\t\tmaxTokens: 131_000,\r\n\t\tcontextWindow: 131_000,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0.04,\r\n\t\toutputPrice: 0.04,\r\n\t},\r\n\t\"mistral-small-latest\": {\r\n\t\tmaxTokens: 32_000,\r\n\t\tcontextWindow: 32_000,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0.2,\r\n\t\toutputPrice: 0.6,\r\n\t},\r\n\t\"pixtral-large-latest\": {\r\n\t\tmaxTokens: 131_000,\r\n\t\tcontextWindow: 131_000,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 2.0,\r\n\t\toutputPrice: 6.0,\r\n\t},\r\n} as const satisfies Record<string, ModelInfo>\r\n\r\nexport const MISTRAL_DEFAULT_TEMPERATURE = 0\r\n", "import type { ModelInfo } from \"../model.js\"\r\n\r\n// https://openai.com/api/pricing/\r\nexport type OpenAiNativeModelId = keyof typeof openAiNativeModels\r\n\r\nexport const openAiNativeDefaultModelId: OpenAiNativeModelId = \"gpt-4.1\"\r\n\r\nexport const openAiNativeModels = {\r\n\t\"gpt-4.1\": {\r\n\t\tmaxTokens: 32_768,\r\n\t\tcontextWindow: 1_047_576,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 2,\r\n\t\toutputPrice: 8,\r\n\t\tcacheReadsPrice: 0.5,\r\n\t},\r\n\t\"gpt-4.1-mini\": {\r\n\t\tmaxTokens: 32_768,\r\n\t\tcontextWindow: 1_047_576,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 0.4,\r\n\t\toutputPrice: 1.6,\r\n\t\tcacheReadsPrice: 0.1,\r\n\t},\r\n\t\"gpt-4.1-nano\": {\r\n\t\tmaxTokens: 32_768,\r\n\t\tcontextWindow: 1_047_576,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 0.1,\r\n\t\toutputPrice: 0.4,\r\n\t\tcacheReadsPrice: 0.025,\r\n\t},\r\n\to3: {\r\n\t\tmaxTokens: 100_000,\r\n\t\tcontextWindow: 200_000,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 10.0,\r\n\t\toutputPrice: 40.0,\r\n\t\tcacheReadsPrice: 2.5,\r\n\t\tsupportsReasoningEffort: true,\r\n\t\treasoningEffort: \"medium\",\r\n\t},\r\n\t\"o3-high\": {\r\n\t\tmaxTokens: 100_000,\r\n\t\tcontextWindow: 200_000,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 10.0,\r\n\t\toutputPrice: 40.0,\r\n\t\tcacheReadsPrice: 2.5,\r\n\t\treasoningEffort: \"high\",\r\n\t},\r\n\t\"o3-low\": {\r\n\t\tmaxTokens: 100_000,\r\n\t\tcontextWindow: 200_000,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 10.0,\r\n\t\toutputPrice: 40.0,\r\n\t\tcacheReadsPrice: 2.5,\r\n\t\treasoningEffort: \"low\",\r\n\t},\r\n\t\"o4-mini\": {\r\n\t\tmaxTokens: 100_000,\r\n\t\tcontextWindow: 200_000,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 1.1,\r\n\t\toutputPrice: 4.4,\r\n\t\tcacheReadsPrice: 0.275,\r\n\t\tsupportsReasoningEffort: true,\r\n\t\treasoningEffort: \"medium\",\r\n\t},\r\n\t\"o4-mini-high\": {\r\n\t\tmaxTokens: 100_000,\r\n\t\tcontextWindow: 200_000,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 1.1,\r\n\t\toutputPrice: 4.4,\r\n\t\tcacheReadsPrice: 0.275,\r\n\t\treasoningEffort: \"high\",\r\n\t},\r\n\t\"o4-mini-low\": {\r\n\t\tmaxTokens: 100_000,\r\n\t\tcontextWindow: 200_000,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 1.1,\r\n\t\toutputPrice: 4.4,\r\n\t\tcacheReadsPrice: 0.275,\r\n\t\treasoningEffort: \"low\",\r\n\t},\r\n\t\"o3-mini\": {\r\n\t\tmaxTokens: 100_000,\r\n\t\tcontextWindow: 200_000,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 1.1,\r\n\t\toutputPrice: 4.4,\r\n\t\tcacheReadsPrice: 0.55,\r\n\t\tsupportsReasoningEffort: true,\r\n\t\treasoningEffort: \"medium\",\r\n\t},\r\n\t\"o3-mini-high\": {\r\n\t\tmaxTokens: 100_000,\r\n\t\tcontextWindow: 200_000,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 1.1,\r\n\t\toutputPrice: 4.4,\r\n\t\tcacheReadsPrice: 0.55,\r\n\t\treasoningEffort: \"high\",\r\n\t},\r\n\t\"o3-mini-low\": {\r\n\t\tmaxTokens: 100_000,\r\n\t\tcontextWindow: 200_000,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 1.1,\r\n\t\toutputPrice: 4.4,\r\n\t\tcacheReadsPrice: 0.55,\r\n\t\treasoningEffort: \"low\",\r\n\t},\r\n\to1: {\r\n\t\tmaxTokens: 100_000,\r\n\t\tcontextWindow: 200_000,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 15,\r\n\t\toutputPrice: 60,\r\n\t\tcacheReadsPrice: 7.5,\r\n\t},\r\n\t\"o1-preview\": {\r\n\t\tmaxTokens: 32_768,\r\n\t\tcontextWindow: 128_000,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 15,\r\n\t\toutputPrice: 60,\r\n\t\tcacheReadsPrice: 7.5,\r\n\t},\r\n\t\"o1-mini\": {\r\n\t\tmaxTokens: 65_536,\r\n\t\tcontextWindow: 128_000,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 1.1,\r\n\t\toutputPrice: 4.4,\r\n\t\tcacheReadsPrice: 0.55,\r\n\t},\r\n\t\"gpt-4.5-preview\": {\r\n\t\tmaxTokens: 16_384,\r\n\t\tcontextWindow: 128_000,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 75,\r\n\t\toutputPrice: 150,\r\n\t\tcacheReadsPrice: 37.5,\r\n\t},\r\n\t\"gpt-4o\": {\r\n\t\tmaxTokens: 16_384,\r\n\t\tcontextWindow: 128_000,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 2.5,\r\n\t\toutputPrice: 10,\r\n\t\tcacheReadsPrice: 1.25,\r\n\t},\r\n\t\"gpt-4o-mini\": {\r\n\t\tmaxTokens: 16_384,\r\n\t\tcontextWindow: 128_000,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 0.15,\r\n\t\toutputPrice: 0.6,\r\n\t\tcacheReadsPrice: 0.075,\r\n\t},\r\n} as const satisfies Record<string, ModelInfo>\r\n\r\nexport const openAiModelInfoSaneDefaults: ModelInfo = {\r\n\tmaxTokens: -1,\r\n\tcontextWindow: 128_000,\r\n\tsupportsImages: true,\r\n\tsupportsPromptCache: false,\r\n\tinputPrice: 0,\r\n\toutputPrice: 0,\r\n}\r\n\r\n// https://learn.microsoft.com/en-us/azure/ai-services/openai/api-version-deprecation\r\n// https://learn.microsoft.com/en-us/azure/ai-services/openai/reference#api-specs\r\nexport const azureOpenAiDefaultApiVersion = \"2024-08-01-preview\"\r\n\r\nexport const OPENAI_NATIVE_DEFAULT_TEMPERATURE = 0\r\n\r\nexport const OPENAI_AZURE_AI_INFERENCE_PATH = \"/models/chat/completions\"\r\n", "import type { ModelInfo } from \"../model.js\"\r\n\r\n// https://openrouter.ai/models?order=newest&supported_parameters=tools\r\nexport const openRouterDefaultModelId = \"anthropic/claude-sonnet-4\"\r\n\r\nexport const openRouterDefaultModelInfo: ModelInfo = {\r\n\tmaxTokens: 8192,\r\n\tcontextWindow: 200_000,\r\n\tsupportsImages: true,\r\n\tsupportsComputerUse: true,\r\n\tsupportsPromptCache: true,\r\n\tinputPrice: 3.0,\r\n\toutputPrice: 15.0,\r\n\tcacheWritesPrice: 3.75,\r\n\tcacheReadsPrice: 0.3,\r\n\tdescription:\r\n\t\t\"Claude 3.7 Sonnet is an advanced large language model with improved reasoning, coding, and problem-solving capabilities. It introduces a hybrid reasoning approach, allowing users to choose between rapid responses and extended, step-by-step processing for complex tasks. The model demonstrates notable improvements in coding, particularly in front-end development and full-stack updates, and excels in agentic workflows, where it can autonomously navigate multi-step processes. Claude 3.7 Sonnet maintains performance parity with its predecessor in standard mode while offering an extended reasoning mode for enhanced accuracy in math, coding, and instruction-following tasks. Read more at the [blog post here](https://www.anthropic.com/news/claude-3-7-sonnet)\",\r\n}\r\n\r\nexport const OPENROUTER_DEFAULT_PROVIDER_NAME = \"[default]\"\r\n\r\nexport const OPEN_ROUTER_PROMPT_CACHING_MODELS = new Set([\r\n\t\"anthropic/claude-3-haiku\",\r\n\t\"anthropic/claude-3-haiku:beta\",\r\n\t\"anthropic/claude-3-opus\",\r\n\t\"anthropic/claude-3-opus:beta\",\r\n\t\"anthropic/claude-3-sonnet\",\r\n\t\"anthropic/claude-3-sonnet:beta\",\r\n\t\"anthropic/claude-3.5-haiku\",\r\n\t\"anthropic/claude-3.5-haiku-20241022\",\r\n\t\"anthropic/claude-3.5-haiku-20241022:beta\",\r\n\t\"anthropic/claude-3.5-haiku:beta\",\r\n\t\"anthropic/claude-3.5-sonnet\",\r\n\t\"anthropic/claude-3.5-sonnet-20240620\",\r\n\t\"anthropic/claude-3.5-sonnet-20240620:beta\",\r\n\t\"anthropic/claude-3.5-sonnet:beta\",\r\n\t\"anthropic/claude-3.7-sonnet\",\r\n\t\"anthropic/claude-3.7-sonnet:beta\",\r\n\t\"anthropic/claude-3.7-sonnet:thinking\",\r\n\t\"anthropic/claude-sonnet-4\",\r\n\t\"anthropic/claude-opus-4\",\r\n\t\"google/gemini-2.5-pro-preview\",\r\n\t\"google/gemini-2.5-flash-preview\",\r\n\t\"google/gemini-2.5-flash-preview:thinking\",\r\n\t\"google/gemini-2.5-flash-preview-05-20\",\r\n\t\"google/gemini-2.5-flash-preview-05-20:thinking\",\r\n\t\"google/gemini-2.0-flash-001\",\r\n\t\"google/gemini-flash-1.5\",\r\n\t\"google/gemini-flash-1.5-8b\",\r\n])\r\n\r\n// https://www.anthropic.com/news/3-5-models-and-computer-use\r\nexport const OPEN_ROUTER_COMPUTER_USE_MODELS = new Set([\r\n\t\"anthropic/claude-3.5-sonnet\",\r\n\t\"anthropic/claude-3.5-sonnet:beta\",\r\n\t\"anthropic/claude-3.7-sonnet\",\r\n\t\"anthropic/claude-3.7-sonnet:beta\",\r\n\t\"anthropic/claude-3.7-sonnet:thinking\",\r\n\t\"anthropic/claude-sonnet-4\",\r\n\t\"anthropic/claude-opus-4\",\r\n])\r\n\r\nexport const OPEN_ROUTER_REASONING_BUDGET_MODELS = new Set([\r\n\t\"anthropic/claude-3.7-sonnet:beta\",\r\n\t\"anthropic/claude-3.7-sonnet:thinking\",\r\n\t\"anthropic/claude-opus-4\",\r\n\t\"anthropic/claude-sonnet-4\",\r\n\t\"google/gemini-2.5-flash-preview-05-20\",\r\n\t\"google/gemini-2.5-flash-preview-05-20:thinking\",\r\n])\r\n\r\nexport const OPEN_ROUTER_REQUIRED_REASONING_BUDGET_MODELS = new Set([\r\n\t\"anthropic/claude-3.7-sonnet:thinking\",\r\n\t\"google/gemini-2.5-flash-preview-05-20:thinking\",\r\n])\r\n", "import type { ModelInfo } from \"../model.js\"\r\n\r\n// Requesty\r\n// https://requesty.ai/router-2\r\nexport const requestyDefaultModelId = \"coding/claude-4-sonnet\"\r\n\r\nexport const requestyDefaultModelInfo: ModelInfo = {\r\n\tmaxTokens: 8192,\r\n\tcontextWindow: 200_000,\r\n\tsupportsImages: true,\r\n\tsupportsComputerUse: true,\r\n\tsupportsPromptCache: true,\r\n\tinputPrice: 3.0,\r\n\toutputPrice: 15.0,\r\n\tcacheWritesPrice: 3.75,\r\n\tcacheReadsPrice: 0.3,\r\n\tdescription:\r\n\t\t\"The best coding model, optimized by Requesty, and automatically routed to the fastest provider. Claude 4 Sonnet is an advanced large language model with improved reasoning, coding, and problem-solving capabilities.\",\r\n}\r\n", "import type { ModelInfo } from \"../model.js\"\r\n\r\nexport const unboundDefaultModelId = \"anthropic/claude-3-7-sonnet-20250219\"\r\n\r\nexport const unboundDefaultModelInfo: ModelInfo = {\r\n\tmaxTokens: 8192,\r\n\tcontextWindow: 200_000,\r\n\tsupportsImages: true,\r\n\tsupportsPromptCache: true,\r\n\tinputPrice: 3.0,\r\n\toutputPrice: 15.0,\r\n\tcacheWritesPrice: 3.75,\r\n\tcacheReadsPrice: 0.3,\r\n}\r\n", "import type { ModelInfo } from \"../model.js\"\r\n\r\n// https://cloud.google.com/vertex-ai/generative-ai/docs/partner-models/use-claude\r\nexport type VertexModelId = keyof typeof vertexModels\r\n\r\nexport const vertexDefaultModelId: VertexModelId = \"claude-sonnet-4@20250514\"\r\n\r\nexport const vertexModels = {\r\n\t\"gemini-2.5-flash-preview-05-20:thinking\": {\r\n\t\tmaxTokens: 65_535,\r\n\t\tcontextWindow: 1_048_576,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 0.15,\r\n\t\toutputPrice: 3.5,\r\n\t\tmaxThinkingTokens: 24_576,\r\n\t\tsupportsReasoningBudget: true,\r\n\t\trequiredReasoningBudget: true,\r\n\t},\r\n\t\"gemini-2.5-flash-preview-05-20\": {\r\n\t\tmaxTokens: 65_535,\r\n\t\tcontextWindow: 1_048_576,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 0.15,\r\n\t\toutputPrice: 0.6,\r\n\t},\r\n\t\"gemini-2.5-flash-preview-04-17:thinking\": {\r\n\t\tmaxTokens: 65_535,\r\n\t\tcontextWindow: 1_048_576,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0.15,\r\n\t\toutputPrice: 3.5,\r\n\t\tmaxThinkingTokens: 24_576,\r\n\t\tsupportsReasoningBudget: true,\r\n\t\trequiredReasoningBudget: true,\r\n\t},\r\n\t\"gemini-2.5-flash-preview-04-17\": {\r\n\t\tmaxTokens: 65_535,\r\n\t\tcontextWindow: 1_048_576,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0.15,\r\n\t\toutputPrice: 0.6,\r\n\t},\r\n\t\"gemini-2.5-pro-preview-03-25\": {\r\n\t\tmaxTokens: 65_535,\r\n\t\tcontextWindow: 1_048_576,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 2.5,\r\n\t\toutputPrice: 15,\r\n\t},\r\n\t\"gemini-2.5-pro-preview-05-06\": {\r\n\t\tmaxTokens: 65_535,\r\n\t\tcontextWindow: 1_048_576,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 2.5,\r\n\t\toutputPrice: 15,\r\n\t},\r\n\t\"gemini-2.5-pro-exp-03-25\": {\r\n\t\tmaxTokens: 65_535,\r\n\t\tcontextWindow: 1_048_576,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t},\r\n\t\"gemini-2.0-pro-exp-02-05\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 2_097_152,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t},\r\n\t\"gemini-2.0-flash-001\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 1_048_576,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 0.15,\r\n\t\toutputPrice: 0.6,\r\n\t},\r\n\t\"gemini-2.0-flash-lite-001\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 1_048_576,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0.075,\r\n\t\toutputPrice: 0.3,\r\n\t},\r\n\t\"gemini-2.0-flash-thinking-exp-01-21\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 32_768,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t},\r\n\t\"gemini-1.5-flash-002\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 1_048_576,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 0.075,\r\n\t\toutputPrice: 0.3,\r\n\t},\r\n\t\"gemini-1.5-pro-002\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 2_097_152,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 1.25,\r\n\t\toutputPrice: 5,\r\n\t},\r\n\t\"claude-sonnet-4@20250514\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 200_000,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsComputerUse: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 3.0,\r\n\t\toutputPrice: 15.0,\r\n\t\tcacheWritesPrice: 3.75,\r\n\t\tcacheReadsPrice: 0.3,\r\n\t\tsupportsReasoningBudget: true,\r\n\t},\r\n\t\"claude-opus-4@20250514\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 200_000,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsComputerUse: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 15.0,\r\n\t\toutputPrice: 75.0,\r\n\t\tcacheWritesPrice: 18.75,\r\n\t\tcacheReadsPrice: 1.5,\r\n\t},\r\n\t\"claude-3-7-sonnet@20250219:thinking\": {\r\n\t\tmaxTokens: 64_000,\r\n\t\tcontextWindow: 200_000,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsComputerUse: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 3.0,\r\n\t\toutputPrice: 15.0,\r\n\t\tcacheWritesPrice: 3.75,\r\n\t\tcacheReadsPrice: 0.3,\r\n\t\tsupportsReasoningBudget: true,\r\n\t\trequiredReasoningBudget: true,\r\n\t},\r\n\t\"claude-3-7-sonnet@20250219\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 200_000,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsComputerUse: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 3.0,\r\n\t\toutputPrice: 15.0,\r\n\t\tcacheWritesPrice: 3.75,\r\n\t\tcacheReadsPrice: 0.3,\r\n\t},\r\n\t\"claude-3-5-sonnet-v2@20241022\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 200_000,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsComputerUse: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 3.0,\r\n\t\toutputPrice: 15.0,\r\n\t\tcacheWritesPrice: 3.75,\r\n\t\tcacheReadsPrice: 0.3,\r\n\t},\r\n\t\"claude-3-5-sonnet@20240620\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 200_000,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 3.0,\r\n\t\toutputPrice: 15.0,\r\n\t\tcacheWritesPrice: 3.75,\r\n\t\tcacheReadsPrice: 0.3,\r\n\t},\r\n\t\"claude-3-5-haiku@20241022\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 200_000,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 1.0,\r\n\t\toutputPrice: 5.0,\r\n\t\tcacheWritesPrice: 1.25,\r\n\t\tcacheReadsPrice: 0.1,\r\n\t},\r\n\t\"claude-3-opus@20240229\": {\r\n\t\tmaxTokens: 4096,\r\n\t\tcontextWindow: 200_000,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 15.0,\r\n\t\toutputPrice: 75.0,\r\n\t\tcacheWritesPrice: 18.75,\r\n\t\tcacheReadsPrice: 1.5,\r\n\t},\r\n\t\"claude-3-haiku@20240307\": {\r\n\t\tmaxTokens: 4096,\r\n\t\tcontextWindow: 200_000,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: true,\r\n\t\tinputPrice: 0.25,\r\n\t\toutputPrice: 1.25,\r\n\t\tcacheWritesPrice: 0.3,\r\n\t\tcacheReadsPrice: 0.03,\r\n\t},\r\n} as const satisfies Record<string, ModelInfo>\r\n\r\nexport const VERTEX_REGIONS = [\r\n\t{ value: \"us-east5\", label: \"us-east5\" },\r\n\t{ value: \"us-central1\", label: \"us-central1\" },\r\n\t{ value: \"europe-west1\", label: \"europe-west1\" },\r\n\t{ value: \"europe-west4\", label: \"europe-west4\" },\r\n\t{ value: \"asia-southeast1\", label: \"asia-southeast1\" },\r\n]\r\n", "import type { ModelInfo } from \"../model.js\"\r\n\r\nexport type VscodeLlmModelId = keyof typeof vscodeLlmModels\r\n\r\nexport const vscodeLlmDefaultModelId: VscodeLlmModelId = \"claude-3.5-sonnet\"\r\n\r\nexport const vscodeLlmModels = {\r\n\t\"gpt-3.5-turbo\": {\r\n\t\tcontextWindow: 12114,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t\tfamily: \"gpt-3.5-turbo\",\r\n\t\tversion: \"gpt-3.5-turbo-0613\",\r\n\t\tname: \"GPT 3.5 Turbo\",\r\n\t\tsupportsToolCalling: true,\r\n\t\tmaxInputTokens: 12114,\r\n\t},\r\n\t\"gpt-4o-mini\": {\r\n\t\tcontextWindow: 12115,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t\tfamily: \"gpt-4o-mini\",\r\n\t\tversion: \"gpt-4o-mini-2024-07-18\",\r\n\t\tname: \"GPT-4o mini\",\r\n\t\tsupportsToolCalling: true,\r\n\t\tmaxInputTokens: 12115,\r\n\t},\r\n\t\"gpt-4\": {\r\n\t\tcontextWindow: 28501,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t\tfamily: \"gpt-4\",\r\n\t\tversion: \"gpt-4-0613\",\r\n\t\tname: \"GPT 4\",\r\n\t\tsupportsToolCalling: true,\r\n\t\tmaxInputTokens: 28501,\r\n\t},\r\n\t\"gpt-4-0125-preview\": {\r\n\t\tcontextWindow: 63826,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t\tfamily: \"gpt-4-turbo\",\r\n\t\tversion: \"gpt-4-0125-preview\",\r\n\t\tname: \"GPT 4 Turbo\",\r\n\t\tsupportsToolCalling: true,\r\n\t\tmaxInputTokens: 63826,\r\n\t},\r\n\t\"gpt-4o\": {\r\n\t\tcontextWindow: 63827,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t\tfamily: \"gpt-4o\",\r\n\t\tversion: \"gpt-4o-2024-11-20\",\r\n\t\tname: \"GPT-4o\",\r\n\t\tsupportsToolCalling: true,\r\n\t\tmaxInputTokens: 63827,\r\n\t},\r\n\to1: {\r\n\t\tcontextWindow: 19827,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t\tfamily: \"o1-ga\",\r\n\t\tversion: \"o1-2024-12-17\",\r\n\t\tname: \"o1 (Preview)\",\r\n\t\tsupportsToolCalling: true,\r\n\t\tmaxInputTokens: 19827,\r\n\t},\r\n\t\"o3-mini\": {\r\n\t\tcontextWindow: 63827,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t\tfamily: \"o3-mini\",\r\n\t\tversion: \"o3-mini-2025-01-31\",\r\n\t\tname: \"o3-mini\",\r\n\t\tsupportsToolCalling: true,\r\n\t\tmaxInputTokens: 63827,\r\n\t},\r\n\t\"claude-3.5-sonnet\": {\r\n\t\tcontextWindow: 81638,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t\tfamily: \"claude-3.5-sonnet\",\r\n\t\tversion: \"claude-3.5-sonnet\",\r\n\t\tname: \"Claude 3.5 Sonnet\",\r\n\t\tsupportsToolCalling: true,\r\n\t\tmaxInputTokens: 81638,\r\n\t},\r\n\t\"gemini-2.0-flash-001\": {\r\n\t\tcontextWindow: 127827,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t\tfamily: \"gemini-2.0-flash\",\r\n\t\tversion: \"gemini-2.0-flash-001\",\r\n\t\tname: \"Gemini 2.0 Flash\",\r\n\t\tsupportsToolCalling: false,\r\n\t\tmaxInputTokens: 127827,\r\n\t},\r\n\t\"gemini-2.5-pro\": {\r\n\t\tcontextWindow: 63830,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t\tfamily: \"gemini-2.5-pro\",\r\n\t\tversion: \"gemini-2.5-pro-preview-03-25\",\r\n\t\tname: \"Gemini 2.5 Pro (Preview)\",\r\n\t\tsupportsToolCalling: true,\r\n\t\tmaxInputTokens: 63830,\r\n\t},\r\n\t\"o4-mini\": {\r\n\t\tcontextWindow: 111446,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t\tfamily: \"o4-mini\",\r\n\t\tversion: \"o4-mini-2025-04-16\",\r\n\t\tname: \"o4-mini (Preview)\",\r\n\t\tsupportsToolCalling: true,\r\n\t\tmaxInputTokens: 111446,\r\n\t},\r\n\t\"gpt-4.1\": {\r\n\t\tcontextWindow: 111446,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0,\r\n\t\toutputPrice: 0,\r\n\t\tfamily: \"gpt-4.1\",\r\n\t\tversion: \"gpt-4.1-2025-04-14\",\r\n\t\tname: \"GPT-4.1 (Preview)\",\r\n\t\tsupportsToolCalling: true,\r\n\t\tmaxInputTokens: 111446,\r\n\t},\r\n} as const satisfies Record<\r\n\tstring,\r\n\tModelInfo & {\r\n\t\tfamily: string\r\n\t\tversion: string\r\n\t\tname: string\r\n\t\tsupportsToolCalling: boolean\r\n\t\tmaxInputTokens: number\r\n\t}\r\n>\r\n", "import type { ModelInfo } from \"../model.js\"\r\n\r\n// https://docs.x.ai/docs/api-reference\r\nexport type XAIModelId = keyof typeof xaiModels\r\n\r\nexport const xaiDefaultModelId: XAIModelId = \"grok-3-mini\"\r\n\r\nexport const xaiModels = {\r\n\t\"grok-3-beta\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 131072,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 3.0,\r\n\t\toutputPrice: 15.0,\r\n\t\tdescription: \"xAI's Grok-3 beta model with 131K context window\",\r\n\t},\r\n\t\"grok-3-fast-beta\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 131072,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 5.0,\r\n\t\toutputPrice: 25.0,\r\n\t\tdescription: \"xAI's Grok-3 fast beta model with 131K context window\",\r\n\t},\r\n\t\"grok-3-mini-beta\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 131072,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0.3,\r\n\t\toutputPrice: 0.5,\r\n\t\tdescription: \"xAI's Grok-3 mini beta model with 131K context window\",\r\n\t\tsupportsReasoningEffort: true,\r\n\t},\r\n\t\"grok-3-mini-fast-beta\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 131072,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0.6,\r\n\t\toutputPrice: 4.0,\r\n\t\tdescription: \"xAI's Grok-3 mini fast beta model with 131K context window\",\r\n\t\tsupportsReasoningEffort: true,\r\n\t},\r\n\t\"grok-3\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 131072,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 3.0,\r\n\t\toutputPrice: 15.0,\r\n\t\tdescription: \"xAI's Grok-3 model with 131K context window\",\r\n\t},\r\n\t\"grok-3-fast\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 131072,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 5.0,\r\n\t\toutputPrice: 25.0,\r\n\t\tdescription: \"xAI's Grok-3 fast model with 131K context window\",\r\n\t},\r\n\t\"grok-3-mini\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 131072,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0.3,\r\n\t\toutputPrice: 0.5,\r\n\t\tdescription: \"xAI's Grok-3 mini model with 131K context window\",\r\n\t\tsupportsReasoningEffort: true,\r\n\t},\r\n\t\"grok-3-mini-fast\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 131072,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 0.6,\r\n\t\toutputPrice: 4.0,\r\n\t\tdescription: \"xAI's Grok-3 mini fast model with 131K context window\",\r\n\t\tsupportsReasoningEffort: true,\r\n\t},\r\n\t\"grok-2-latest\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 131072,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 2.0,\r\n\t\toutputPrice: 10.0,\r\n\t\tdescription: \"xAI's Grok-2 model - latest version with 131K context window\",\r\n\t},\r\n\t\"grok-2\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 131072,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 2.0,\r\n\t\toutputPrice: 10.0,\r\n\t\tdescription: \"xAI's Grok-2 model with 131K context window\",\r\n\t},\r\n\t\"grok-2-1212\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 131072,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 2.0,\r\n\t\toutputPrice: 10.0,\r\n\t\tdescription: \"xAI's Grok-2 model (version 1212) with 131K context window\",\r\n\t},\r\n\t\"grok-2-vision-latest\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 32768,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 2.0,\r\n\t\toutputPrice: 10.0,\r\n\t\tdescription: \"xAI's Grok-2 Vision model - latest version with image support and 32K context window\",\r\n\t},\r\n\t\"grok-2-vision\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 32768,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 2.0,\r\n\t\toutputPrice: 10.0,\r\n\t\tdescription: \"xAI's Grok-2 Vision model with image support and 32K context window\",\r\n\t},\r\n\t\"grok-2-vision-1212\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 32768,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 2.0,\r\n\t\toutputPrice: 10.0,\r\n\t\tdescription: \"xAI's Grok-2 Vision model (version 1212) with image support and 32K context window\",\r\n\t},\r\n\t\"grok-vision-beta\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 8192,\r\n\t\tsupportsImages: true,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 5.0,\r\n\t\toutputPrice: 15.0,\r\n\t\tdescription: \"xAI's Grok Vision Beta model with image support and 8K context window\",\r\n\t},\r\n\t\"grok-beta\": {\r\n\t\tmaxTokens: 8192,\r\n\t\tcontextWindow: 131072,\r\n\t\tsupportsImages: false,\r\n\t\tsupportsPromptCache: false,\r\n\t\tinputPrice: 5.0,\r\n\t\toutputPrice: 15.0,\r\n\t\tdescription: \"xAI's Grok Beta model (legacy) with 131K context window\",\r\n\t},\r\n} as const satisfies Record<string, ModelInfo>\r\n", "import { z } from \"zod\"\r\n\r\n/**\r\n * CodebaseIndexConfig\r\n */\r\n\r\nexport const codebaseIndexConfigSchema = z.object({\r\n\tcodebaseIndexEnabled: z.boolean().optional(),\r\n\tcodebaseIndexQdrantUrl: z.string().optional(),\r\n\tcodebaseIndexEmbedderProvider: z.enum([\"openai\", \"ollama\"]).optional(),\r\n\tcodebaseIndexEmbedderBaseUrl: z.string().optional(),\r\n\tcodebaseIndexEmbedderModelId: z.string().optional(),\r\n})\r\n\r\nexport type CodebaseIndexConfig = z.infer<typeof codebaseIndexConfigSchema>\r\n\r\n/**\r\n * CodebaseIndexModels\r\n */\r\n\r\nexport const codebaseIndexModelsSchema = z.object({\r\n\topenai: z.record(z.string(), z.object({ dimension: z.number() })).optional(),\r\n\tollama: z.record(z.string(), z.object({ dimension: z.number() })).optional(),\r\n})\r\n\r\nexport type CodebaseIndexModels = z.infer<typeof codebaseIndexModelsSchema>\r\n\r\n/**\r\n * CdebaseIndexProvider\r\n */\r\n\r\nexport const codebaseIndexProviderSchema = z.object({\r\n\tcodeIndexOpenAiKey: z.string().optional(),\r\n\tcodeIndexQdrantApiKey: z.string().optional(),\r\n})\r\n\r\nexport type CodebaseIndexProvider = z.infer<typeof codebaseIndexProviderSchema>\r\n", "import { z } from \"zod\"\r\n\r\nexport interface CloudUserInfo {\r\n\tname?: string\r\n\temail?: string\r\n\tpicture?: string\r\n}\r\n\r\n/**\r\n * Organization Allow List\r\n */\r\n\r\nexport const organizationAllowListSchema = z.object({\r\n\tallowAll: z.boolean(),\r\n\tproviders: z.record(\r\n\t\tz.object({\r\n\t\t\tallowAll: z.boolean(),\r\n\t\t\tmodels: z.array(z.string()).optional(),\r\n\t\t}),\r\n\t),\r\n})\r\n\r\nexport type OrganizationAllowList = z.infer<typeof organizationAllowListSchema>\r\n\r\nexport const ORGANIZATION_ALLOW_ALL: OrganizationAllowList = {\r\n\tallowAll: true,\r\n\tproviders: {},\r\n} as const\r\n\r\n/**\r\n * Organization Settings\r\n */\r\n\r\nexport const organizationSettingsSchema = z.object({\r\n\tversion: z.number(),\r\n\tdefaultSettings: z\r\n\t\t.object({\r\n\t\t\tenableCheckpoints: z.boolean().optional(),\r\n\t\t\tmaxOpenTabsContext: z.number().optional(),\r\n\t\t\tmaxWorkspaceFiles: z.number().optional(),\r\n\t\t\tshowRooIgnoredFiles: z.boolean().optional(),\r\n\t\t\tmaxReadFileLine: z.number().optional(),\r\n\t\t\tfuzzyMatchThreshold: z.number().optional(),\r\n\t\t})\r\n\t\t.optional(),\r\n\tcloudSettings: z\r\n\t\t.object({\r\n\t\t\trecordTaskMessages: z.boolean().optional(),\r\n\t\t})\r\n\t\t.optional(),\r\n\tallowList: organizationAllowListSchema,\r\n})\r\n\r\nexport type OrganizationSettings = z.infer<typeof organizationSettingsSchema>\r\n", "import { z } from \"zod\"\r\n\r\nimport type { Keys, Equals, AssertEqual } from \"./type-fu.js\"\r\n\r\n/**\r\n * ExperimentId\r\n */\r\n\r\nexport const experimentIds = [\"powerSteering\", \"concurrentFileReads\"] as const\r\n\r\nexport const experimentIdsSchema = z.enum(experimentIds)\r\n\r\nexport type ExperimentId = z.infer<typeof experimentIdsSchema>\r\n\r\n/**\r\n * Experiments\r\n */\r\n\r\nexport const experimentsSchema = z.object({\r\n\tpowerSteering: z.boolean(),\r\n\tconcurrentFileReads: z.boolean(),\r\n})\r\n\r\nexport type Experiments = z.infer<typeof experimentsSchema>\r\n\r\ntype _AssertExperiments = AssertEqual<Equals<ExperimentId, Keys<Experiments>>>\r\n", "import { z } from \"zod\"\r\n\r\nimport { type Keys, keysOf } from \"./type-fu.js\"\r\nimport {\r\n\ttype ProviderSettings,\r\n\tPROVIDER_SETTINGS_KEYS,\r\n\tproviderSettingsEntrySchema,\r\n\tproviderSettingsSchema,\r\n} from \"./provider-settings.js\"\r\nimport { historyItemSchema } from \"./history.js\"\r\nimport { codebaseIndexModelsSchema, codebaseIndexConfigSchema } from \"./codebase-index.js\"\r\nimport { experimentsSchema } from \"./experiment.js\"\r\nimport { telemetrySettingsSchema } from \"./telemetry.js\"\r\nimport { modeConfigSchema } from \"./mode.js\"\r\nimport { customModePromptsSchema, customSupportPromptsSchema } from \"./mode.js\"\r\nimport { languagesSchema } from \"./vscode.js\"\r\n\r\n/**\r\n * GlobalSettings\r\n */\r\n\r\nexport const globalSettingsSchema = z.object({\r\n\tcurrentApiConfigName: z.string().optional(),\r\n\tlistApiConfigMeta: z.array(providerSettingsEntrySchema).optional(),\r\n\tpinnedApiConfigs: z.record(z.string(), z.boolean()).optional(),\r\n\r\n\tlastShownAnnouncementId: z.string().optional(),\r\n\tcustomInstructions: z.string().optional(),\r\n\ttaskHistory: z.array(historyItemSchema).optional(),\r\n\r\n\tcondensingApiConfigId: z.string().optional(),\r\n\tcustomCondensingPrompt: z.string().optional(),\r\n\r\n\tautoApprovalEnabled: z.boolean().optional(),\r\n\talwaysAllowReadOnly: z.boolean().optional(),\r\n\talwaysAllowReadOnlyOutsideWorkspace: z.boolean().optional(),\r\n\talwaysAllowWrite: z.boolean().optional(),\r\n\talwaysAllowWriteOutsideWorkspace: z.boolean().optional(),\r\n\twriteDelayMs: z.number().optional(),\r\n\talwaysAllowBrowser: z.boolean().optional(),\r\n\talwaysApproveResubmit: z.boolean().optional(),\r\n\trequestDelaySeconds: z.number().optional(),\r\n\talwaysAllowMcp: z.boolean().optional(),\r\n\talwaysAllowModeSwitch: z.boolean().optional(),\r\n\talwaysAllowSubtasks: z.boolean().optional(),\r\n\talwaysAllowExecute: z.boolean().optional(),\r\n\tallowedCommands: z.array(z.string()).optional(),\r\n\tallowedMaxRequests: z.number().nullish(),\r\n\tautoCondenseContext: z.boolean().optional(),\r\n\tautoCondenseContextPercent: z.number().optional(),\r\n \tmaxConcurrentFileReads: z.number().optional(),\r\n\r\n\tbrowserToolEnabled: z.boolean().optional(),\r\n\tbrowserViewportSize: z.string().optional(),\r\n\tscreenshotQuality: z.number().optional(),\r\n\tremoteBrowserEnabled: z.boolean().optional(),\r\n\tremoteBrowserHost: z.string().optional(),\r\n\tcachedChromeHostUrl: z.string().optional(),\r\n\r\n\tenableCheckpoints: z.boolean().optional(),\r\n\r\n\tttsEnabled: z.boolean().optional(),\r\n\tttsSpeed: z.number().optional(),\r\n\tsoundEnabled: z.boolean().optional(),\r\n\tsoundVolume: z.number().optional(),\r\n\r\n\tmaxOpenTabsContext: z.number().optional(),\r\n\tmaxWorkspaceFiles: z.number().optional(),\r\n\tshowRooIgnoredFiles: z.boolean().optional(),\r\n\tmaxReadFileLine: z.number().optional(),\r\n\r\n\tterminalOutputLineLimit: z.number().optional(),\r\n\tterminalShellIntegrationTimeout: z.number().optional(),\r\n\tterminalShellIntegrationDisabled: z.boolean().optional(),\r\n\tterminalCommandDelay: z.number().optional(),\r\n\tterminalPowershellCounter: z.boolean().optional(),\r\n\tterminalZshClearEolMark: z.boolean().optional(),\r\n\tterminalZshOhMy: z.boolean().optional(),\r\n\tterminalZshP10k: z.boolean().optional(),\r\n\tterminalZdotdir: z.boolean().optional(),\r\n\tterminalCompressProgressBar: z.boolean().optional(),\r\n\r\n\trateLimitSeconds: z.number().optional(),\r\n\tdiffEnabled: z.boolean().optional(),\r\n\tfuzzyMatchThreshold: z.number().optional(),\r\n\texperiments: experimentsSchema.optional(),\r\n\r\n\tcodebaseIndexModels: codebaseIndexModelsSchema.optional(),\r\n\tcodebaseIndexConfig: codebaseIndexConfigSchema.optional(),\r\n\r\n\tlanguage: languagesSchema.optional(),\r\n\r\n\ttelemetrySetting: telemetrySettingsSchema.optional(),\r\n\r\n\tmcpEnabled: z.boolean().optional(),\r\n\tenableMcpServerCreation: z.boolean().optional(),\r\n\r\n\tmode: z.string().optional(),\r\n\tmodeApiConfigs: z.record(z.string(), z.string()).optional(),\r\n\tcustomModes: z.array(modeConfigSchema).optional(),\r\n\tcustomModePrompts: customModePromptsSchema.optional(),\r\n\tcustomSupportPrompts: customSupportPromptsSchema.optional(),\r\n\tenhancementApiConfigId: z.string().optional(),\r\n\thistoryPreviewCollapsed: z.boolean().optional(),\r\n})\r\n\r\nexport type GlobalSettings = z.infer<typeof globalSettingsSchema>\r\n\r\nexport const GLOBAL_SETTINGS_KEYS = keysOf<GlobalSettings>()([\r\n\t\"currentApiConfigName\",\r\n\t\"listApiConfigMeta\",\r\n\t\"pinnedApiConfigs\",\r\n\r\n\t\"lastShownAnnouncementId\",\r\n\t\"customInstructions\",\r\n\t\"taskHistory\",\r\n\r\n\t\"condensingApiConfigId\",\r\n\t\"customCondensingPrompt\",\r\n\r\n\t\"autoApprovalEnabled\",\r\n\t\"alwaysAllowReadOnly\",\r\n\t\"alwaysAllowReadOnlyOutsideWorkspace\",\r\n\t\"alwaysAllowWrite\",\r\n\t\"alwaysAllowWriteOutsideWorkspace\",\r\n\t\"writeDelayMs\",\r\n\t\"alwaysAllowBrowser\",\r\n\t\"alwaysApproveResubmit\",\r\n\t\"requestDelaySeconds\",\r\n\t\"alwaysAllowMcp\",\r\n\t\"alwaysAllowModeSwitch\",\r\n\t\"alwaysAllowSubtasks\",\r\n\t\"alwaysAllowExecute\",\r\n\t\"allowedCommands\",\r\n\t\"allowedMaxRequests\",\r\n\t\"autoCondenseContext\",\r\n\t\"autoCondenseContextPercent\",\r\n\t\"maxConcurrentFileReads\",\r\n\r\n\t\"browserToolEnabled\",\r\n\t\"browserViewportSize\",\r\n\t\"screenshotQuality\",\r\n\t\"remoteBrowserEnabled\",\r\n\t\"remoteBrowserHost\",\r\n\r\n\t\"enableCheckpoints\",\r\n\r\n\t\"ttsEnabled\",\r\n\t\"ttsSpeed\",\r\n\t\"soundEnabled\",\r\n\t\"soundVolume\",\r\n\r\n\t\"maxOpenTabsContext\",\r\n\t\"maxWorkspaceFiles\",\r\n\t\"showRooIgnoredFiles\",\r\n\t\"maxReadFileLine\",\r\n\r\n\t\"terminalOutputLineLimit\",\r\n\t\"terminalShellIntegrationTimeout\",\r\n\t\"terminalShellIntegrationDisabled\",\r\n\t\"terminalCommandDelay\",\r\n\t\"terminalPowershellCounter\",\r\n\t\"terminalZshClearEolMark\",\r\n\t\"terminalZshOhMy\",\r\n\t\"terminalZshP10k\",\r\n\t\"terminalZdotdir\",\r\n\t\"terminalCompressProgressBar\",\r\n\r\n\t\"rateLimitSeconds\",\r\n\t\"diffEnabled\",\r\n\t\"fuzzyMatchThreshold\",\r\n\t\"experiments\",\r\n\r\n\t\"codebaseIndexModels\",\r\n\t\"codebaseIndexConfig\",\r\n\r\n\t\"language\",\r\n\r\n\t\"telemetrySetting\",\r\n\t\"mcpEnabled\",\r\n\t\"enableMcpServerCreation\",\r\n\r\n\t\"mode\",\r\n\t\"modeApiConfigs\",\r\n\t\"customModes\",\r\n\t\"customModePrompts\",\r\n\t\"customSupportPrompts\",\r\n\t\"enhancementApiConfigId\",\r\n\t\"cachedChromeHostUrl\",\r\n\t\"historyPreviewCollapsed\",\r\n])\r\n\r\n/**\r\n * RooCodeSettings\r\n */\r\n\r\nexport const rooCodeSettingsSchema = providerSettingsSchema.merge(globalSettingsSchema)\r\n\r\nexport type RooCodeSettings = GlobalSettings & ProviderSettings\r\n\r\n/**\r\n * SecretState\r\n */\r\n\r\nexport type SecretState = Pick<\r\n\tProviderSettings,\r\n\t| \"apiKey\"\r\n\t| \"glamaApiKey\"\r\n\t| \"openRouterApiKey\"\r\n\t| \"awsAccessKey\"\r\n\t| \"awsSecretKey\"\r\n\t| \"awsSessionToken\"\r\n\t| \"openAiApiKey\"\r\n\t| \"geminiApiKey\"\r\n\t| \"openAiNativeApiKey\"\r\n\t| \"deepSeekApiKey\"\r\n\t| \"mistralApiKey\"\r\n\t| \"unboundApiKey\"\r\n\t| \"requestyApiKey\"\r\n\t| \"xaiApiKey\"\r\n\t| \"groqApiKey\"\r\n\t| \"chutesApiKey\"\r\n\t| \"litellmApiKey\"\r\n\t| \"codeIndexOpenAiKey\"\r\n\t| \"codeIndexQdrantApiKey\"\r\n>\r\n\r\nexport const SECRET_STATE_KEYS = keysOf<SecretState>()([\r\n\t\"apiKey\",\r\n\t\"glamaApiKey\",\r\n\t\"openRouterApiKey\",\r\n\t\"awsAccessKey\",\r\n\t\"awsSecretKey\",\r\n\t\"awsSessionToken\",\r\n\t\"openAiApiKey\",\r\n\t\"geminiApiKey\",\r\n\t\"openAiNativeApiKey\",\r\n\t\"deepSeekApiKey\",\r\n\t\"mistralApiKey\",\r\n\t\"unboundApiKey\",\r\n\t\"requestyApiKey\",\r\n\t\"xaiApiKey\",\r\n\t\"groqApiKey\",\r\n\t\"chutesApiKey\",\r\n\t\"litellmApiKey\",\r\n\t\"codeIndexOpenAiKey\",\r\n\t\"codeIndexQdrantApiKey\",\r\n])\r\n\r\nexport const isSecretStateKey = (key: string): key is Keys<SecretState> =>\r\n\tSECRET_STATE_KEYS.includes(key as Keys<SecretState>)\r\n\r\n/**\r\n * GlobalState\r\n */\r\n\r\nexport type GlobalState = Omit<RooCodeSettings, Keys<SecretState>>\r\n\r\nexport const GLOBAL_STATE_KEYS = [...GLOBAL_SETTINGS_KEYS, ...PROVIDER_SETTINGS_KEYS].filter(\r\n\t(key: Keys<RooCodeSettings>) => !SECRET_STATE_KEYS.includes(key as Keys<SecretState>),\r\n) as Keys<GlobalState>[]\r\n\r\nexport const isGlobalStateKey = (key: string): key is Keys<GlobalState> =>\r\n\tGLOBAL_STATE_KEYS.includes(key as Keys<GlobalState>)\r\n", "/**\r\n * TS\r\n */\r\n\r\nexport type Keys<T> = keyof T\r\n\r\nexport type Values<T> = T[keyof T]\r\n\r\nexport type Equals<X, Y> = (<T>() => T extends X ? 1 : 2) extends <T>() => T extends Y ? 1 : 2 ? true : false\r\n\r\nexport type AssertEqual<T extends true> = T\r\n\r\n/**\r\n * Creates a type-safe keys array that enforces ALL keys from type T are present.\r\n * Returns a compile-time error if any keys are missing or extra keys are provided.\r\n */\r\nexport function keysOf<T>() {\r\n\treturn <const U extends readonly (keyof T)[]>(\r\n\t\tkeys: keyof T extends U[number] ? (U[number] extends keyof T ? U : never) : never,\r\n\t): U => keys\r\n}\r\n", "import { z } from \"zod\"\n\nimport { keysOf } from \"./type-fu.js\"\nimport { reasoningEffortsSchema, modelInfoSchema } from \"./model.js\"\nimport { codebaseIndexProviderSchema } from \"./codebase-index.js\"\n\n/**\n * ProviderName\n */\n\nexport const providerNames = [\n\t\"anthropic\",\n\t\"glama\",\n\t\"openrouter\",\n\t\"bedrock\",\n\t\"vertex\",\n\t\"openai\",\n\t\"ollama\",\n\t\"vscode-lm\",\n\t\"lmstudio\",\n\t\"gemini\",\n\t\"openai-native\",\n\t\"mistral\",\n\t\"deepseek\",\n\t\"unbound\",\n\t\"requesty\",\n\t\"human-relay\",\n\t\"fake-ai\",\n\t\"xai\",\n\t\"groq\",\n\t\"chutes\",\n\t\"litellm\",\n] as const\n\nexport const providerNamesSchema = z.enum(providerNames)\n\nexport type ProviderName = z.infer<typeof providerNamesSchema>\n\n/**\n * ProviderSettingsEntry\n */\n\nexport const providerSettingsEntrySchema = z.object({\n\tid: z.string(),\n\tname: z.string(),\n\tapiProvider: providerNamesSchema.optional(),\n})\n\nexport type ProviderSettingsEntry = z.infer<typeof providerSettingsEntrySchema>\n\n/**\n * ProviderSettings\n */\n\nconst baseProviderSettingsSchema = z.object({\n\tincludeMaxTokens: z.boolean().optional(),\n\tdiffEnabled: z.boolean().optional(),\n\tfuzzyMatchThreshold: z.number().optional(),\n\tmodelTemperature: z.number().nullish(),\n\trateLimitSeconds: z.number().optional(),\n\n\t// Model reasoning.\n\tenableReasoningEffort: z.boolean().optional(),\n\treasoningEffort: reasoningEffortsSchema.optional(),\n\tmodelMaxTokens: z.number().optional(),\n\tmodelMaxThinkingTokens: z.number().optional(),\n})\n\n// Several of the providers share common model config properties.\nconst apiModelIdProviderModelSchema = baseProviderSettingsSchema.extend({\n\tapiModelId: z.string().optional(),\n})\n\nconst anthropicSchema = apiModelIdProviderModelSchema.extend({\n\tapiKey: z.string().optional(), // Keep for backward compatibility\n\tanthropicApiKey: z.string().optional(),\n\tanthropicBaseUrl: z.string().optional(),\n\tanthropicUseAuthToken: z.boolean().optional(),\n})\n\nconst glamaSchema = baseProviderSettingsSchema.extend({\n\tglamaModelId: z.string().optional(),\n\tglamaApiKey: z.string().optional(),\n})\n\nconst openRouterSchema = baseProviderSettingsSchema.extend({\n\topenRouterApiKey: z.string().optional(),\n\topenRouterModelId: z.string().optional(),\n\topenRouterBaseUrl: z.string().optional(),\n\topenRouterSpecificProvider: z.string().optional(),\n\topenRouterUseMiddleOutTransform: z.boolean().optional(),\n})\n\nconst bedrockSchema = apiModelIdProviderModelSchema.extend({\n\tawsAccessKey: z.string().optional(),\n\tawsSecretKey: z.string().optional(),\n\tawsSessionToken: z.string().optional(),\n\tawsRegion: z.string().optional(),\n\tawsUseCrossRegionInference: z.boolean().optional(),\n\tawsUsePromptCache: z.boolean().optional(),\n\tawsProfile: z.string().optional(),\n\tawsUseProfile: z.boolean().optional(),\n\tawsCustomArn: z.string().optional(),\n\tawsBedrockEndpointEnabled: z.boolean().optional(),\n\tawsBedrockEndpoint: z.string().optional(),\n})\n\nconst vertexSchema = apiModelIdProviderModelSchema.extend({\n\tvertexKeyFile: z.string().optional(),\n\tvertexJsonCredentials: z.string().optional(),\n\tvertexProjectId: z.string().optional(),\n\tvertexRegion: z.string().optional(),\n})\n\nconst openAiSchema = baseProviderSettingsSchema.extend({\n\topenAiBaseUrl: z.string().optional(),\n\topenAiApiKey: z.string().optional(),\n\topenAiLegacyFormat: z.boolean().optional(),\n\topenAiR1FormatEnabled: z.boolean().optional(),\n\topenAiModelId: z.string().optional(),\n\topenAiCustomModelInfo: modelInfoSchema.nullish(),\n\topenAiUseAzure: z.boolean().optional(),\n\tazureApiVersion: z.string().optional(),\n\topenAiStreamingEnabled: z.boolean().optional(),\n\topenAiHostHeader: z.string().optional(), // Keep temporarily for backward compatibility during migration.\n\topenAiHeaders: z.record(z.string(), z.string()).optional(),\n})\n\nconst ollamaSchema = baseProviderSettingsSchema.extend({\n\tollamaModelId: z.string().optional(),\n\tollamaBaseUrl: z.string().optional(),\n})\n\nconst vsCodeLmSchema = baseProviderSettingsSchema.extend({\n\tvsCodeLmModelSelector: z\n\t\t.object({\n\t\t\tvendor: z.string().optional(),\n\t\t\tfamily: z.string().optional(),\n\t\t\tversion: z.string().optional(),\n\t\t\tid: z.string().optional(),\n\t\t})\n\t\t.optional(),\n})\n\nconst lmStudioSchema = baseProviderSettingsSchema.extend({\n\tlmStudioModelId: z.string().optional(),\n\tlmStudioBaseUrl: z.string().optional(),\n\tlmStudioDraftModelId: z.string().optional(),\n\tlmStudioSpeculativeDecodingEnabled: z.boolean().optional(),\n})\n\nconst geminiSchema = apiModelIdProviderModelSchema.extend({\n\tgeminiApiKey: z.string().optional(),\n\tgoogleGeminiBaseUrl: z.string().optional(),\n})\n\nconst openAiNativeSchema = apiModelIdProviderModelSchema.extend({\n\topenAiNativeApiKey: z.string().optional(),\n\topenAiNativeBaseUrl: z.string().optional(),\n})\n\nconst mistralSchema = apiModelIdProviderModelSchema.extend({\n\tmistralApiKey: z.string().optional(),\n\tmistralCodestralUrl: z.string().optional(),\n})\n\nconst deepSeekSchema = apiModelIdProviderModelSchema.extend({\n\tdeepSeekBaseUrl: z.string().optional(),\n\tdeepSeekApiKey: z.string().optional(),\n})\n\nconst unboundSchema = baseProviderSettingsSchema.extend({\n\tunboundApiKey: z.string().optional(),\n\tunboundModelId: z.string().optional(),\n})\n\nconst requestySchema = baseProviderSettingsSchema.extend({\n\trequestyApiKey: z.string().optional(),\n\trequestyModelId: z.string().optional(),\n})\n\nconst humanRelaySchema = baseProviderSettingsSchema\n\nconst fakeAiSchema = baseProviderSettingsSchema.extend({\n\tfakeAi: z.unknown().optional(),\n})\n\nconst xaiSchema = apiModelIdProviderModelSchema.extend({\n\txaiApiKey: z.string().optional(),\n})\n\nconst groqSchema = apiModelIdProviderModelSchema.extend({\n\tgroqApiKey: z.string().optional(),\n})\n\nconst chutesSchema = apiModelIdProviderModelSchema.extend({\n\tchutesApiKey: z.string().optional(),\n})\n\nconst litellmSchema = baseProviderSettingsSchema.extend({\n\tlitellmBaseUrl: z.string().optional(),\n\tlitellmApiKey: z.string().optional(),\n\tlitellmModelId: z.string().optional(),\n})\n\nconst defaultSchema = z.object({\n\tapiProvider: z.undefined(),\n})\n\nexport const providerSettingsSchemaDiscriminated = z.discriminatedUnion(\"apiProvider\", [\n\tanthropicSchema.merge(z.object({ apiProvider: z.literal(\"anthropic\") })),\n\tglamaSchema.merge(z.object({ apiProvider: z.literal(\"glama\") })),\n\topenRouterSchema.merge(z.object({ apiProvider: z.literal(\"openrouter\") })),\n\tbedrockSchema.merge(z.object({ apiProvider: z.literal(\"bedrock\") })),\n\tvertexSchema.merge(z.object({ apiProvider: z.literal(\"vertex\") })),\n\topenAiSchema.merge(z.object({ apiProvider: z.literal(\"openai\") })),\n\tollamaSchema.merge(z.object({ apiProvider: z.literal(\"ollama\") })),\n\tvsCodeLmSchema.merge(z.object({ apiProvider: z.literal(\"vscode-lm\") })),\n\tlmStudioSchema.merge(z.object({ apiProvider: z.literal(\"lmstudio\") })),\n\tgeminiSchema.merge(z.object({ apiProvider: z.literal(\"gemini\") })),\n\topenAiNativeSchema.merge(z.object({ apiProvider: z.literal(\"openai-native\") })),\n\tmistralSchema.merge(z.object({ apiProvider: z.literal(\"mistral\") })),\n\tdeepSeekSchema.merge(z.object({ apiProvider: z.literal(\"deepseek\") })),\n\tunboundSchema.merge(z.object({ apiProvider: z.literal(\"unbound\") })),\n\trequestySchema.merge(z.object({ apiProvider: z.literal(\"requesty\") })),\n\thumanRelaySchema.merge(z.object({ apiProvider: z.literal(\"human-relay\") })),\n\tfakeAiSchema.merge(z.object({ apiProvider: z.literal(\"fake-ai\") })),\n\txaiSchema.merge(z.object({ apiProvider: z.literal(\"xai\") })),\n\tgroqSchema.merge(z.object({ apiProvider: z.literal(\"groq\") })),\n\tchutesSchema.merge(z.object({ apiProvider: z.literal(\"chutes\") })),\n\tlitellmSchema.merge(z.object({ apiProvider: z.literal(\"litellm\") })),\n\tdefaultSchema,\n])\n\nexport const providerSettingsSchema = z.object({\n\tapiProvider: providerNamesSchema.optional(),\n\t...anthropicSchema.shape,\n\t...glamaSchema.shape,\n\t...openRouterSchema.shape,\n\t...bedrockSchema.shape,\n\t...vertexSchema.shape,\n\t...openAiSchema.shape,\n\t...ollamaSchema.shape,\n\t...vsCodeLmSchema.shape,\n\t...lmStudioSchema.shape,\n\t...geminiSchema.shape,\n\t...openAiNativeSchema.shape,\n\t...mistralSchema.shape,\n\t...deepSeekSchema.shape,\n\t...unboundSchema.shape,\n\t...requestySchema.shape,\n\t...humanRelaySchema.shape,\n\t...fakeAiSchema.shape,\n\t...xaiSchema.shape,\n\t...groqSchema.shape,\n\t...chutesSchema.shape,\n\t...litellmSchema.shape,\n\t...codebaseIndexProviderSchema.shape,\n})\n\nexport type ProviderSettings = z.infer<typeof providerSettingsSchema>\n\nexport const PROVIDER_SETTINGS_KEYS = keysOf<ProviderSettings>()([\n\t\"apiProvider\",\n\t// Anthropic\n\t\"apiModelId\",\n\t\"apiKey\", // Keep for backward compatibility\n\t\"anthropicApiKey\",\n\t\"anthropicBaseUrl\",\n\t\"anthropicUseAuthToken\",\n\t// Glama\n\t\"glamaModelId\",\n\t\"glamaApiKey\",\n\t// OpenRouter\n\t\"openRouterApiKey\",\n\t\"openRouterModelId\",\n\t\"openRouterBaseUrl\",\n\t\"openRouterSpecificProvider\",\n\t\"openRouterUseMiddleOutTransform\",\n\t// Amazon Bedrock\n\t\"awsAccessKey\",\n\t\"awsSecretKey\",\n\t\"awsSessionToken\",\n\t\"awsRegion\",\n\t\"awsUseCrossRegionInference\",\n\t\"awsUsePromptCache\",\n\t\"awsProfile\",\n\t\"awsUseProfile\",\n\t\"awsCustomArn\",\n\t\"awsBedrockEndpointEnabled\",\n\t\"awsBedrockEndpoint\",\n\t// Google Vertex\n\t\"vertexKeyFile\",\n\t\"vertexJsonCredentials\",\n\t\"vertexProjectId\",\n\t\"vertexRegion\",\n\t// OpenAI\n\t\"openAiBaseUrl\",\n\t\"openAiApiKey\",\n\t\"openAiLegacyFormat\",\n\t\"openAiR1FormatEnabled\",\n\t\"openAiModelId\",\n\t\"openAiCustomModelInfo\",\n\t\"openAiUseAzure\",\n\t\"azureApiVersion\",\n\t\"openAiStreamingEnabled\",\n\t\"openAiHostHeader\", // Keep temporarily for backward compatibility during migration.\n\t\"openAiHeaders\",\n\t// Ollama\n\t\"ollamaModelId\",\n\t\"ollamaBaseUrl\",\n\t// VS Code LM\n\t\"vsCodeLmModelSelector\",\n\t\"lmStudioModelId\",\n\t\"lmStudioBaseUrl\",\n\t\"lmStudioDraftModelId\",\n\t\"lmStudioSpeculativeDecodingEnabled\",\n\t// Gemini\n\t\"geminiApiKey\",\n\t\"googleGeminiBaseUrl\",\n\t// OpenAI Native\n\t\"openAiNativeApiKey\",\n\t\"openAiNativeBaseUrl\",\n\t// Mistral\n\t\"mistralApiKey\",\n\t\"mistralCodestralUrl\",\n\t// DeepSeek\n\t\"deepSeekBaseUrl\",\n\t\"deepSeekApiKey\",\n\t// Unbound\n\t\"unboundApiKey\",\n\t\"unboundModelId\",\n\t// Requesty\n\t\"requestyApiKey\",\n\t\"requestyModelId\",\n\t// Code Index\n\t\"codeIndexOpenAiKey\",\n\t\"codeIndexQdrantApiKey\",\n\t// Reasoning\n\t\"enableReasoningEffort\",\n\t\"reasoningEffort\",\n\t\"modelMaxTokens\",\n\t\"modelMaxThinkingTokens\",\n\t// Generic\n\t\"includeMaxTokens\",\n\t\"diffEnabled\",\n\t\"fuzzyMatchThreshold\",\n\t\"modelTemperature\",\n\t\"rateLimitSeconds\",\n\t// Fake AI\n\t\"fakeAi\",\n\t// X.AI (Grok)\n\t\"xaiApiKey\",\n\t// Groq\n\t\"groqApiKey\",\n\t// Chutes AI\n\t\"chutesApiKey\",\n\t// LiteLLM\n\t\"litellmBaseUrl\",\n\t\"litellmApiKey\",\n\t\"litellmModelId\",\n])\n", "import { z } from \"zod\"\r\n\r\n/**\r\n * ReasoningEffort\r\n */\r\n\r\nexport const reasoningEfforts = [\"low\", \"medium\", \"high\"] as const\r\n\r\nexport const reasoningEffortsSchema = z.enum(reasoningEfforts)\r\n\r\nexport type ReasoningEffort = z.infer<typeof reasoningEffortsSchema>\r\n\r\n/**\r\n * ModelParameter\r\n */\r\n\r\nexport const modelParameters = [\"max_tokens\", \"temperature\", \"reasoning\", \"include_reasoning\"] as const\r\n\r\nexport const modelParametersSchema = z.enum(modelParameters)\r\n\r\nexport type ModelParameter = z.infer<typeof modelParametersSchema>\r\n\r\nexport const isModelParameter = (value: string): value is ModelParameter =>\r\n\tmodelParameters.includes(value as ModelParameter)\r\n\r\n/**\r\n * ModelInfo\r\n */\r\n\r\nexport const modelInfoSchema = z.object({\r\n\tmaxTokens: z.number().nullish(),\r\n\tmaxThinkingTokens: z.number().nullish(),\r\n\tcontextWindow: z.number(),\r\n\tsupportsImages: z.boolean().optional(),\r\n\tsupportsComputerUse: z.boolean().optional(),\r\n\tsupportsPromptCache: z.boolean(),\r\n\tsupportsReasoningBudget: z.boolean().optional(),\r\n\trequiredReasoningBudget: z.boolean().optional(),\r\n\tsupportsReasoningEffort: z.boolean().optional(),\r\n\tsupportedParameters: z.array(modelParametersSchema).optional(),\r\n\tinputPrice: z.number().optional(),\r\n\toutputPrice: z.number().optional(),\r\n\tcacheWritesPrice: z.number().optional(),\r\n\tcacheReadsPrice: z.number().optional(),\r\n\tdescription: z.string().optional(),\r\n\treasoningEffort: reasoningEffortsSchema.optional(),\r\n\tminTokensPerCachePoint: z.number().optional(),\r\n\tmaxCachePoints: z.number().optional(),\r\n\tcachableFields: z.array(z.string()).optional(),\r\n\ttiers: z\r\n\t\t.array(\r\n\t\t\tz.object({\r\n\t\t\t\tcontextWindow: z.number(),\r\n\t\t\t\tinputPrice: z.number().optional(),\r\n\t\t\t\toutputPrice: z.number().optional(),\r\n\t\t\t\tcacheWritesPrice: z.number().optional(),\r\n\t\t\t\tcacheReadsPrice: z.number().optional(),\r\n\t\t\t}),\r\n\t\t)\r\n\t\t.optional(),\r\n})\r\n\r\nexport type ModelInfo = z.infer<typeof modelInfoSchema>\r\n", "import { z } from \"zod\"\r\n\r\n/**\r\n * HistoryItem\r\n */\r\n\r\nexport const historyItemSchema = z.object({\r\n\tid: z.string(),\r\n\tnumber: z.number(),\r\n\tts: z.number(),\r\n\ttask: z.string(),\r\n\ttokensIn: z.number(),\r\n\ttokensOut: z.number(),\r\n\tcacheWrites: z.number().optional(),\r\n\tcacheReads: z.number().optional(),\r\n\ttotalCost: z.number(),\r\n\tsize: z.number().optional(),\r\n\tworkspace: z.string().optional(),\r\n\ttitle: z.string().optional(),\r\n\tpinned: z.boolean().optional(),\r\n})\r\n\r\nexport type HistoryItem = z.infer<typeof historyItemSchema>\r\n", "import { z } from \"zod\"\r\n\r\nimport { providerNames } from \"./provider-settings.js\"\r\nimport { clineMessageSchema } from \"./message.js\"\r\n\r\n/**\r\n * TelemetrySetting\r\n */\r\n\r\nexport const telemetrySettings = [\"unset\", \"enabled\", \"disabled\"] as const\r\n\r\nexport const telemetrySettingsSchema = z.enum(telemetrySettings)\r\n\r\nexport type TelemetrySetting = z.infer<typeof telemetrySettingsSchema>\r\n\r\n/**\r\n * TelemetryEventName\r\n */\r\n\r\nexport enum TelemetryEventName {\r\n\tTASK_CREATED = \"Task Created\",\r\n\tTASK_RESTARTED = \"Task Reopened\",\r\n\tTASK_COMPLETED = \"Task Completed\",\r\n\tTASK_MESSAGE = \"Task Message\",\r\n\tTASK_CONVERSATION_MESSAGE = \"Conversation Message\",\r\n\tLLM_COMPLETION = \"LLM Completion\",\r\n\tMODE_SWITCH = \"Mode Switched\",\r\n\tTOOL_USED = \"Tool Used\",\r\n\r\n\tCHECKPOINT_CREATED = \"Checkpoint Created\",\r\n\tCHECKPOINT_RESTORED = \"Checkpoint Restored\",\r\n\tCHECKPOINT_DIFFED = \"Checkpoint Diffed\",\r\n\r\n\tCONTEXT_CONDENSED = \"Context Condensed\",\r\n\tSLIDING_WINDOW_TRUNCATION = \"Sliding Window Truncation\",\r\n\r\n\tCODE_ACTION_USED = \"Code Action Used\",\r\n\tPROMPT_ENHANCED = \"Prompt Enhanced\",\r\n\r\n\tTITLE_BUTTON_CLICKED = \"Title Button Clicked\",\r\n\r\n\tAUTHENTICATION_INITIATED = \"Authentication Initiated\",\r\n\r\n\tSCHEMA_VALIDATION_ERROR = \"Schema Validation Error\",\r\n\tDIFF_APPLICATION_ERROR = \"Diff Application Error\",\r\n\tSHELL_INTEGRATION_ERROR = \"Shell Integration Error\",\r\n\tCONSECUTIVE_MISTAKE_ERROR = \"Consecutive Mistake Error\",\r\n}\r\n\r\n/**\r\n * TelemetryProperties\r\n */\r\n\r\nexport const appPropertiesSchema = z.object({\r\n\tappName: z.string(),\r\n\tappVersion: z.string(),\r\n\tvscodeVersion: z.string(),\r\n\tplatform: z.string(),\r\n\teditorName: z.string(),\r\n\tlanguage: z.string(),\r\n\tmode: z.string(),\r\n})\r\n\r\nexport const taskPropertiesSchema = z.object({\r\n\ttaskId: z.string().optional(),\r\n\tapiProvider: z.enum(providerNames).optional(),\r\n\tmodelId: z.string().optional(),\r\n\tdiffStrategy: z.string().optional(),\r\n\tisSubtask: z.boolean().optional(),\r\n})\r\n\r\nexport const telemetryPropertiesSchema = z.object({\r\n\t...appPropertiesSchema.shape,\r\n\t...taskPropertiesSchema.shape,\r\n})\r\n\r\nexport type TelemetryProperties = z.infer<typeof telemetryPropertiesSchema>\r\n\r\n/**\r\n * TelemetryEvent\r\n */\r\n\r\nexport type TelemetryEvent = {\r\n\tevent: TelemetryEventName\r\n\t// eslint-disable-next-line @typescript-eslint/no-explicit-any\r\n\tproperties?: Record<string, any>\r\n}\r\n\r\n/**\r\n * RooCodeTelemetryEvent\r\n */\r\n\r\nexport const rooCodeTelemetryEventSchema = z.discriminatedUnion(\"type\", [\r\n\tz.object({\r\n\t\ttype: z.enum([\r\n\t\t\tTelemetryEventName.TASK_CREATED,\r\n\t\t\tTelemetryEventName.TASK_RESTARTED,\r\n\t\t\tTelemetryEventName.TASK_COMPLETED,\r\n\t\t\tTelemetryEventName.TASK_CONVERSATION_MESSAGE,\r\n\t\t\tTelemetryEventName.MODE_SWITCH,\r\n\t\t\tTelemetryEventName.TOOL_USED,\r\n\t\t\tTelemetryEventName.CHECKPOINT_CREATED,\r\n\t\t\tTelemetryEventName.CHECKPOINT_RESTORED,\r\n\t\t\tTelemetryEventName.CHECKPOINT_DIFFED,\r\n\t\t\tTelemetryEventName.CODE_ACTION_USED,\r\n\t\t\tTelemetryEventName.PROMPT_ENHANCED,\r\n\t\t\tTelemetryEventName.TITLE_BUTTON_CLICKED,\r\n\t\t\tTelemetryEventName.AUTHENTICATION_INITIATED,\r\n\t\t\tTelemetryEventName.SCHEMA_VALIDATION_ERROR,\r\n\t\t\tTelemetryEventName.DIFF_APPLICATION_ERROR,\r\n\t\t\tTelemetryEventName.SHELL_INTEGRATION_ERROR,\r\n\t\t\tTelemetryEventName.CONSECUTIVE_MISTAKE_ERROR,\r\n\t\t\tTelemetryEventName.CONTEXT_CONDENSED,\r\n\t\t\tTelemetryEventName.SLIDING_WINDOW_TRUNCATION,\r\n\t\t]),\r\n\t\tproperties: telemetryPropertiesSchema,\r\n\t}),\r\n\tz.object({\r\n\t\ttype: z.literal(TelemetryEventName.TASK_MESSAGE),\r\n\t\tproperties: z.object({\r\n\t\t\t...telemetryPropertiesSchema.shape,\r\n\t\t\ttaskId: z.string(),\r\n\t\t\tmessage: clineMessageSchema,\r\n\t\t}),\r\n\t}),\r\n\tz.object({\r\n\t\ttype: z.literal(TelemetryEventName.LLM_COMPLETION),\r\n\t\tproperties: z.object({\r\n\t\t\t...telemetryPropertiesSchema.shape,\r\n\t\t\tinputTokens: z.number(),\r\n\t\t\toutputTokens: z.number(),\r\n\t\t\tcacheReadTokens: z.number().optional(),\r\n\t\t\tcacheWriteTokens: z.number().optional(),\r\n\t\t\tcost: z.number().optional(),\r\n\t\t}),\r\n\t}),\r\n])\r\n\r\nexport type RooCodeTelemetryEvent = z.infer<typeof rooCodeTelemetryEventSchema>\r\n\r\n/**\r\n * TelemetryEventSubscription\r\n */\r\n\r\nexport type TelemetryEventSubscription =\r\n\t| { type: \"include\"; events: TelemetryEventName[] }\r\n\t| { type: \"exclude\"; events: TelemetryEventName[] }\r\n\r\n/**\r\n * TelemetryPropertiesProvider\r\n */\r\n\r\nexport interface TelemetryPropertiesProvider {\r\n\tgetTelemetryProperties(): Promise<TelemetryProperties>\r\n}\r\n\r\n/**\r\n * TelemetryClient\r\n */\r\n\r\nexport interface TelemetryClient {\r\n\tsubscription?: TelemetryEventSubscription\r\n\r\n\tsetProvider(provider: TelemetryPropertiesProvider): void\r\n\tcapture(options: TelemetryEvent): Promise<void>\r\n\tupdateTelemetryState(didUserOptIn: boolean): void\r\n\tisTelemetryEnabled(): boolean\r\n\tshutdown(): Promise<void>\r\n}\r\n", "import { z } from \"zod\"\r\n\r\n/**\r\n * ClineAsk\r\n */\r\n\r\nexport const clineAsks = [\r\n\t\"followup\",\r\n\t\"command\",\r\n\t\"command_output\",\r\n\t\"completion_result\",\r\n\t\"tool\",\r\n\t\"api_req_failed\",\r\n\t\"resume_task\",\r\n\t\"resume_completed_task\",\r\n\t\"mistake_limit_reached\",\r\n\t\"browser_action_launch\",\r\n\t\"use_mcp_server\",\r\n\t\"auto_approval_max_req_reached\",\r\n] as const\r\n\r\nexport const clineAskSchema = z.enum(clineAsks)\r\n\r\nexport type ClineAsk = z.infer<typeof clineAskSchema>\r\n\r\n/**\r\n * ClineSay\r\n */\r\n\r\nexport const clineSays = [\r\n\t\"error\",\r\n\t\"api_req_started\",\r\n\t\"api_req_finished\",\r\n\t\"api_req_retried\",\r\n\t\"api_req_retry_delayed\",\r\n\t\"api_req_deleted\",\r\n\t\"text\",\r\n\t\"reasoning\",\r\n\t\"completion_result\",\r\n\t\"user_feedback\",\r\n\t\"user_feedback_diff\",\r\n\t\"command_output\",\r\n\t\"shell_integration_warning\",\r\n\t\"browser_action\",\r\n\t\"browser_action_result\",\r\n\t\"mcp_server_request_started\",\r\n\t\"mcp_server_response\",\r\n\t\"subtask_result\",\r\n\t\"checkpoint_saved\",\r\n\t\"rooignore_error\",\r\n\t\"diff_error\",\r\n\t\"condense_context\",\r\n\t\"condense_context_error\",\r\n\t\"codebase_search_result\",\r\n] as const\r\n\r\nexport const clineSaySchema = z.enum(clineSays)\r\n\r\nexport type ClineSay = z.infer<typeof clineSaySchema>\r\n\r\n/**\r\n * ToolProgressStatus\r\n */\r\n\r\nexport const toolProgressStatusSchema = z.object({\r\n\ticon: z.string().optional(),\r\n\ttext: z.string().optional(),\r\n})\r\n\r\nexport type ToolProgressStatus = z.infer<typeof toolProgressStatusSchema>\r\n\r\n/**\r\n * ContextCondense\r\n */\r\n\r\nexport const contextCondenseSchema = z.object({\r\n\tcost: z.number(),\r\n\tprevContextTokens: z.number(),\r\n\tnewContextTokens: z.number(),\r\n\tsummary: z.string(),\r\n})\r\n\r\nexport type ContextCondense = z.infer<typeof contextCondenseSchema>\r\n\r\n/**\r\n * ClineMessage\r\n */\r\n\r\nexport const clineMessageSchema = z.object({\r\n\tts: z.number(),\r\n\ttype: z.union([z.literal(\"ask\"), z.literal(\"say\")]),\r\n\task: clineAskSchema.optional(),\r\n\tsay: clineSaySchema.optional(),\r\n\ttext: z.string().optional(),\r\n\timages: z.array(z.string()).optional(),\r\n\tpartial: z.boolean().optional(),\r\n\treasoning: z.string().optional(),\r\n\tconversationHistoryIndex: z.number().optional(),\r\n\tcheckpoint: z.record(z.string(), z.unknown()).optional(),\r\n\tprogressStatus: toolProgressStatusSchema.optional(),\r\n\tcontextCondense: contextCondenseSchema.optional(),\r\n})\r\n\r\nexport type ClineMessage = z.infer<typeof clineMessageSchema>\r\n\r\n/**\r\n * TokenUsage\r\n */\r\n\r\nexport const tokenUsageSchema = z.object({\r\n\ttotalTokensIn: z.number(),\r\n\ttotalTokensOut: z.number(),\r\n\ttotalCacheWrites: z.number().optional(),\r\n\ttotalCacheReads: z.number().optional(),\r\n\ttotalCost: z.number(),\r\n\tcontextTokens: z.number(),\r\n})\r\n\r\nexport type TokenUsage = z.infer<typeof tokenUsageSchema>\r\n", "import { z } from \"zod\"\r\n\r\nimport { toolGroupsSchema } from \"./tool.js\"\r\n\r\n/**\r\n * GroupOptions\r\n */\r\n\r\nexport const groupOptionsSchema = z.object({\r\n\tfileRegex: z\r\n\t\t.string()\r\n\t\t.optional()\r\n\t\t.refine(\r\n\t\t\t(pattern) => {\r\n\t\t\t\tif (!pattern) {\r\n\t\t\t\t\treturn true // Optional, so empty is valid.\r\n\t\t\t\t}\r\n\r\n\t\t\t\ttry {\r\n\t\t\t\t\tnew RegExp(pattern)\r\n\t\t\t\t\treturn true\r\n\t\t\t\t} catch {\r\n\t\t\t\t\treturn false\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t{ message: \"Invalid regular expression pattern\" },\r\n\t\t),\r\n\tdescription: z.string().optional(),\r\n})\r\n\r\nexport type GroupOptions = z.infer<typeof groupOptionsSchema>\r\n\r\n/**\r\n * GroupEntry\r\n */\r\n\r\nexport const groupEntrySchema = z.union([toolGroupsSchema, z.tuple([toolGroupsSchema, groupOptionsSchema])])\r\n\r\nexport type GroupEntry = z.infer<typeof groupEntrySchema>\r\n\r\n/**\r\n * ModeConfig\r\n */\r\n\r\nconst groupEntryArraySchema = z.array(groupEntrySchema).refine(\r\n\t(groups) => {\r\n\t\tconst seen = new Set()\r\n\r\n\t\treturn groups.every((group) => {\r\n\t\t\t// For tuples, check the group name (first element).\r\n\t\t\tconst groupName = Array.isArray(group) ? group[0] : group\r\n\r\n\t\t\tif (seen.has(groupName)) {\r\n\t\t\t\treturn false\r\n\t\t\t}\r\n\r\n\t\t\tseen.add(groupName)\r\n\t\t\treturn true\r\n\t\t})\r\n\t},\r\n\t{ message: \"Duplicate groups are not allowed\" },\r\n)\r\n\r\nexport const modeConfigSchema = z.object({\r\n\tslug: z.string().regex(/^[a-zA-Z0-9-]+$/, \"Slug must contain only letters numbers and dashes\"),\r\n\tname: z.string().min(1, \"Name is required\"),\r\n\troleDefinition: z.string().min(1, \"Role definition is required\"),\r\n\twhenToUse: z.string().optional(),\r\n\tcustomInstructions: z.string().optional(),\r\n\tgroups: groupEntryArraySchema,\r\n\tsource: z.enum([\"global\", \"project\"]).optional(),\r\n})\r\n\r\nexport type ModeConfig = z.infer<typeof modeConfigSchema>\r\n\r\n/**\r\n * CustomModesSettings\r\n */\r\n\r\nexport const customModesSettingsSchema = z.object({\r\n\tcustomModes: z.array(modeConfigSchema).refine(\r\n\t\t(modes) => {\r\n\t\t\tconst slugs = new Set()\r\n\r\n\t\t\treturn modes.every((mode) => {\r\n\t\t\t\tif (slugs.has(mode.slug)) {\r\n\t\t\t\t\treturn false\r\n\t\t\t\t}\r\n\r\n\t\t\t\tslugs.add(mode.slug)\r\n\t\t\t\treturn true\r\n\t\t\t})\r\n\t\t},\r\n\t\t{\r\n\t\t\tmessage: \"Duplicate mode slugs are not allowed\",\r\n\t\t},\r\n\t),\r\n})\r\n\r\nexport type CustomModesSettings = z.infer<typeof customModesSettingsSchema>\r\n\r\n/**\r\n * PromptComponent\r\n */\r\n\r\nexport const promptComponentSchema = z.object({\r\n\troleDefinition: z.string().optional(),\r\n\twhenToUse: z.string().optional(),\r\n\tcustomInstructions: z.string().optional(),\r\n})\r\n\r\nexport type PromptComponent = z.infer<typeof promptComponentSchema>\r\n\r\n/**\r\n * CustomModePrompts\r\n */\r\n\r\nexport const customModePromptsSchema = z.record(z.string(), promptComponentSchema.optional())\r\n\r\nexport type CustomModePrompts = z.infer<typeof customModePromptsSchema>\r\n\r\n/**\r\n * CustomSupportPrompts\r\n */\r\n\r\nexport const customSupportPromptsSchema = z.record(z.string(), z.string().optional())\r\n\r\nexport type CustomSupportPrompts = z.infer<typeof customSupportPromptsSchema>\r\n", "import { z } from \"zod\"\r\n\r\n/**\r\n * ToolGroup\r\n */\r\n\r\nexport const toolGroups = [\"read\", \"edit\", \"browser\", \"command\", \"mcp\", \"modes\"] as const\r\n\r\nexport const toolGroupsSchema = z.enum(toolGroups)\r\n\r\nexport type ToolGroup = z.infer<typeof toolGroupsSchema>\r\n\r\n/**\r\n * ToolName\r\n */\r\n\r\nexport const toolNames = [\r\n\t\"execute_command\",\r\n\t\"read_file\",\r\n\t\"write_to_file\",\r\n\t\"apply_diff\",\r\n\t\"insert_content\",\r\n\t\"search_and_replace\",\r\n\t\"search_files\",\r\n\t\"list_files\",\r\n\t\"list_code_definition_names\",\r\n\t\"browser_action\",\r\n\t\"use_mcp_tool\",\r\n\t\"access_mcp_resource\",\r\n\t\"ask_followup_question\",\r\n\t\"attempt_completion\",\r\n\t\"switch_mode\",\r\n\t\"new_task\",\r\n\t\"fetch_instructions\",\r\n\t\"codebase_search\",\r\n] as const\r\n\r\nexport const toolNamesSchema = z.enum(toolNames)\r\n\r\nexport type ToolName = z.infer<typeof toolNamesSchema>\r\n\r\n/**\r\n * ToolUsage\r\n */\r\n\r\nexport const toolUsageSchema = z.record(\r\n\ttoolNamesSchema,\r\n\tz.object({\r\n\t\tattempts: z.number(),\r\n\t\tfailures: z.number(),\r\n\t}),\r\n)\r\n\r\nexport type ToolUsage = z.infer<typeof toolUsageSchema>\r\n", "import { z } from \"zod\"\r\n\r\n/**\r\n * CodeAction\r\n */\r\n\r\nexport const codeActionIds = [\"explainCode\", \"fixCode\", \"improveCode\", \"addToContext\", \"newTask\"] as const\r\n\r\nexport type CodeActionId = (typeof codeActionIds)[number]\r\n\r\nexport type CodeActionName = \"EXPLAIN\" | \"FIX\" | \"IMPROVE\" | \"ADD_TO_CONTEXT\" | \"NEW_TASK\"\r\n\r\n/**\r\n * TerminalAction\r\n */\r\n\r\nexport const terminalActionIds = [\"terminalAddToContext\", \"terminalFixCommand\", \"terminalExplainCommand\"] as const\r\n\r\nexport type TerminalActionId = (typeof terminalActionIds)[number]\r\n\r\nexport type TerminalActionName = \"ADD_TO_CONTEXT\" | \"FIX\" | \"EXPLAIN\"\r\n\r\nexport type TerminalActionPromptType = `TERMINAL_${TerminalActionName}`\r\n\r\n/**\r\n * Command\r\n */\r\n\r\nexport const commandIds = [\r\n\t\"activationCompleted\",\r\n\r\n\t\"plusButtonClicked\",\r\n\t\"promptsButtonClicked\",\r\n\t\"mcpButtonClicked\",\r\n\t\"historyButtonClicked\",\r\n\t\"popoutButtonClicked\",\r\n\t\"accountButtonClicked\",\r\n\t\"settingsButtonClicked\",\r\n\r\n\t\"openInNewTab\",\r\n\r\n\t\"showHumanRelayDialog\",\r\n\t\"registerHumanRelayCallback\",\r\n\t\"unregisterHumanRelayCallback\",\r\n\t\"handleHumanRelayResponse\",\r\n\r\n\t\"newTask\",\r\n\r\n\t\"setCustomStoragePath\",\r\n\r\n\t\"focusInput\",\r\n\t\"acceptInput\",\r\n] as const\r\n\r\nexport type CommandId = (typeof commandIds)[number]\r\n\r\n/**\r\n * Language\r\n */\r\n\r\nexport const languages = [\r\n\t\"ca\",\r\n\t\"de\",\r\n\t\"en\",\r\n\t\"es\",\r\n\t\"fr\",\r\n\t\"hi\",\r\n\t\"it\",\r\n\t\"ja\",\r\n\t\"ko\",\r\n\t\"nl\",\r\n\t\"pl\",\r\n\t\"pt-BR\",\r\n\t\"ru\",\r\n\t\"tr\",\r\n\t\"vi\",\r\n\t\"zh-CN\",\r\n\t\"zh-TW\",\r\n] as const\r\n\r\nexport const languagesSchema = z.enum(languages)\r\n\r\nexport type Language = z.infer<typeof languagesSchema>\r\n\r\nexport const isLanguage = (value: string): value is Language => languages.includes(value as Language)\r\n", "import { z } from \"zod\"\r\n\r\nimport { clineMessageSchema, tokenUsageSchema } from \"./message.js\"\r\nimport { toolNamesSchema, toolUsageSchema } from \"./tool.js\"\r\nimport { rooCodeSettingsSchema } from \"./global-settings.js\"\r\n\r\n/**\r\n * RooCodeEvent\r\n */\r\n\r\nexport enum RooCodeEventName {\r\n\tMessage = \"message\",\r\n\tTaskCreated = \"taskCreated\",\r\n\tTaskStarted = \"taskStarted\",\r\n\tTaskModeSwitched = \"taskModeSwitched\",\r\n\tTaskPaused = \"taskPaused\",\r\n\tTaskUnpaused = \"taskUnpaused\",\r\n\tTaskAskResponded = \"taskAskResponded\",\r\n\tTaskAborted = \"taskAborted\",\r\n\tTaskSpawned = \"taskSpawned\",\r\n\tTaskCompleted = \"taskCompleted\",\r\n\tTaskTokenUsageUpdated = \"taskTokenUsageUpdated\",\r\n\tTaskToolFailed = \"taskToolFailed\",\r\n}\r\n\r\nexport const rooCodeEventsSchema = z.object({\r\n\t[RooCodeEventName.Message]: z.tuple([\r\n\t\tz.object({\r\n\t\t\ttaskId: z.string(),\r\n\t\t\taction: z.union([z.literal(\"created\"), z.literal(\"updated\")]),\r\n\t\t\tmessage: clineMessageSchema,\r\n\t\t}),\r\n\t]),\r\n\t[RooCodeEventName.TaskCreated]: z.tuple([z.string()]),\r\n\t[RooCodeEventName.TaskStarted]: z.tuple([z.string()]),\r\n\t[RooCodeEventName.TaskModeSwitched]: z.tuple([z.string(), z.string()]),\r\n\t[RooCodeEventName.TaskPaused]: z.tuple([z.string()]),\r\n\t[RooCodeEventName.TaskUnpaused]: z.tuple([z.string()]),\r\n\t[RooCodeEventName.TaskAskResponded]: z.tuple([z.string()]),\r\n\t[RooCodeEventName.TaskAborted]: z.tuple([z.string()]),\r\n\t[RooCodeEventName.TaskSpawned]: z.tuple([z.string(), z.string()]),\r\n\t[RooCodeEventName.TaskCompleted]: z.tuple([z.string(), tokenUsageSchema, toolUsageSchema]),\r\n\t[RooCodeEventName.TaskTokenUsageUpdated]: z.tuple([z.string(), tokenUsageSchema]),\r\n\t[RooCodeEventName.TaskToolFailed]: z.tuple([z.string(), toolNamesSchema, z.string()]),\r\n})\r\n\r\nexport type RooCodeEvents = z.infer<typeof rooCodeEventsSchema>\r\n\r\n/**\r\n * Ack\r\n */\r\n\r\nexport const ackSchema = z.object({\r\n\tclientId: z.string(),\r\n\tpid: z.number(),\r\n\tppid: z.number(),\r\n})\r\n\r\nexport type Ack = z.infer<typeof ackSchema>\r\n\r\n/**\r\n * TaskCommand\r\n */\r\n\r\nexport enum TaskCommandName {\r\n\tStartNewTask = \"StartNewTask\",\r\n\tCancelTask = \"CancelTask\",\r\n\tCloseTask = \"CloseTask\",\r\n}\r\n\r\nexport const taskCommandSchema = z.discriminatedUnion(\"commandName\", [\r\n\tz.object({\r\n\t\tcommandName: z.literal(TaskCommandName.StartNewTask),\r\n\t\tdata: z.object({\r\n\t\t\tconfiguration: rooCodeSettingsSchema,\r\n\t\t\ttext: z.string(),\r\n\t\t\timages: z.array(z.string()).optional(),\r\n\t\t\tnewTab: z.boolean().optional(),\r\n\t\t}),\r\n\t}),\r\n\tz.object({\r\n\t\tcommandName: z.literal(TaskCommandName.CancelTask),\r\n\t\tdata: z.string(),\r\n\t}),\r\n\tz.object({\r\n\t\tcommandName: z.literal(TaskCommandName.CloseTask),\r\n\t\tdata: z.string(),\r\n\t}),\r\n])\r\n\r\nexport type TaskCommand = z.infer<typeof taskCommandSchema>\r\n\r\n/**\r\n * TaskEvent\r\n */\r\n\r\nexport const taskEventSchema = z.discriminatedUnion(\"eventName\", [\r\n\tz.object({\r\n\t\teventName: z.literal(RooCodeEventName.Message),\r\n\t\tpayload: rooCodeEventsSchema.shape[RooCodeEventName.Message],\r\n\t}),\r\n\tz.object({\r\n\t\teventName: z.literal(RooCodeEventName.TaskCreated),\r\n\t\tpayload: rooCodeEventsSchema.shape[RooCodeEventName.TaskCreated],\r\n\t}),\r\n\tz.object({\r\n\t\teventName: z.literal(RooCodeEventName.TaskStarted),\r\n\t\tpayload: rooCodeEventsSchema.shape[RooCodeEventName.TaskStarted],\r\n\t}),\r\n\tz.object({\r\n\t\teventName: z.literal(RooCodeEventName.TaskModeSwitched),\r\n\t\tpayload: rooCodeEventsSchema.shape[RooCodeEventName.TaskModeSwitched],\r\n\t}),\r\n\tz.object({\r\n\t\teventName: z.literal(RooCodeEventName.TaskPaused),\r\n\t\tpayload: rooCodeEventsSchema.shape[RooCodeEventName.TaskPaused],\r\n\t}),\r\n\tz.object({\r\n\t\teventName: z.literal(RooCodeEventName.TaskUnpaused),\r\n\t\tpayload: rooCodeEventsSchema.shape[RooCodeEventName.TaskUnpaused],\r\n\t}),\r\n\tz.object({\r\n\t\teventName: z.literal(RooCodeEventName.TaskAskResponded),\r\n\t\tpayload: rooCodeEventsSchema.shape[RooCodeEventName.TaskAskResponded],\r\n\t}),\r\n\tz.object({\r\n\t\teventName: z.literal(RooCodeEventName.TaskAborted),\r\n\t\tpayload: rooCodeEventsSchema.shape[RooCodeEventName.TaskAborted],\r\n\t}),\r\n\tz.object({\r\n\t\teventName: z.literal(RooCodeEventName.TaskSpawned),\r\n\t\tpayload: rooCodeEventsSchema.shape[RooCodeEventName.TaskSpawned],\r\n\t}),\r\n\tz.object({\r\n\t\teventName: z.literal(RooCodeEventName.TaskCompleted),\r\n\t\tpayload: rooCodeEventsSchema.shape[RooCodeEventName.TaskCompleted],\r\n\t}),\r\n\tz.object({\r\n\t\teventName: z.literal(RooCodeEventName.TaskTokenUsageUpdated),\r\n\t\tpayload: rooCodeEventsSchema.shape[RooCodeEventName.TaskTokenUsageUpdated],\r\n\t}),\r\n])\r\n\r\nexport type TaskEvent = z.infer<typeof taskEventSchema>\r\n\r\n/**\r\n * IpcMessage\r\n */\r\n\r\nexport enum IpcMessageType {\r\n\tConnect = \"Connect\",\r\n\tDisconnect = \"Disconnect\",\r\n\tAck = \"Ack\",\r\n\tTaskCommand = \"TaskCommand\",\r\n\tTaskEvent = \"TaskEvent\",\r\n}\r\n\r\nexport enum IpcOrigin {\r\n\tClient = \"client\",\r\n\tServer = \"server\",\r\n}\r\n\r\nexport const ipcMessageSchema = z.discriminatedUnion(\"type\", [\r\n\tz.object({\r\n\t\ttype: z.literal(IpcMessageType.Ack),\r\n\t\torigin: z.literal(IpcOrigin.Server),\r\n\t\tdata: ackSchema,\r\n\t}),\r\n\tz.object({\r\n\t\ttype: z.literal(IpcMessageType.TaskCommand),\r\n\t\torigin: z.literal(IpcOrigin.Client),\r\n\t\tclientId: z.string(),\r\n\t\tdata: taskCommandSchema,\r\n\t}),\r\n\tz.object({\r\n\t\ttype: z.literal(IpcMessageType.TaskEvent),\r\n\t\torigin: z.literal(IpcOrigin.Server),\r\n\t\trelayClientId: z.string().optional(),\r\n\t\tdata: taskEventSchema,\r\n\t}),\r\n])\r\n\r\nexport type IpcMessage = z.infer<typeof ipcMessageSchema>\r\n", "import { z } from \"zod\"\r\n\r\n/**\r\n * CommandExecutionStatus\r\n */\r\n\r\nexport const commandExecutionStatusSchema = z.discriminatedUnion(\"status\", [\r\n\tz.object({\r\n\t\texecutionId: z.string(),\r\n\t\tstatus: z.literal(\"started\"),\r\n\t\tpid: z.number().optional(),\r\n\t\tcommand: z.string(),\r\n\t}),\r\n\tz.object({\r\n\t\texecutionId: z.string(),\r\n\t\tstatus: z.literal(\"output\"),\r\n\t\toutput: z.string(),\r\n\t}),\r\n\tz.object({\r\n\t\texecutionId: z.string(),\r\n\t\tstatus: z.literal(\"exited\"),\r\n\t\texitCode: z.number().optional(),\r\n\t}),\r\n\tz.object({\r\n\t\texecutionId: z.string(),\r\n\t\tstatus: z.literal(\"fallback\"),\r\n\t}),\r\n])\r\n\r\nexport type CommandExecutionStatus = z.infer<typeof commandExecutionStatusSchema>\r\n", "import { z } from \"zod\"\r\n\r\n/**\r\n * User Subscription Types\r\n */\r\nexport enum SubscriptionTier {\r\n\tFREE_TRIAL = \"free_trial\",\r\n\tBASIC = \"basic\", \r\n\tPRO = \"pro\",\r\n\tENTERPRISE = \"enterprise\"\r\n}\r\n\r\nexport enum SubscriptionStatus {\r\n\tACTIVE = \"active\",\r\n\tTRIAL = \"trial\",\r\n\tEXPIRED = \"expired\",\r\n\tCANCELLED = \"cancelled\",\r\n\tSUSPENDED = \"suspended\"\r\n}\r\n\r\n/**\r\n * Usage Quotas and Limits\r\n */\r\nexport interface UsageQuotas {\r\n\t// Token limits per month\r\n\tmonthlyTokenLimit: number\r\n\t// Cost limits per month (in USD)\r\n\tmonthlyCostLimit: number\r\n\t// API requests per hour\r\n\thourlyRequestLimit: number\r\n\t// API requests per day\r\n\tdailyRequestLimit: number\r\n\t// Maximum context window size\r\n\tmaxContextWindow: number\r\n\t// Available models\r\n\tallowedModels: string[]\r\n\t// Advanced features\r\n\tcanUseReasoningModels: boolean\r\n\tcanUseCodebaseIndex: boolean\r\n\tcanUseCustomModes: boolean\r\n\tcanExportHistory: boolean\r\n}\r\n\r\n/**\r\n * Usage Metrics\r\n */\r\nexport interface UsageMetrics {\r\n\t// Current period usage\r\n\tcurrentMonthTokens: number\r\n\tcurrentMonthCost: number\r\n\tcurrentHourRequests: number\r\n\tcurrentDayRequests: number\r\n\t\r\n\t// Historical data\r\n\ttotalTokensUsed: number\r\n\ttotalCostAccrued: number\r\n\ttotalRequestsMade: number\r\n\t\r\n\t// Last reset timestamps\r\n\tlastMonthlyReset: Date\r\n\tlastHourlyReset: Date\r\n\tlastDailyReset: Date\r\n\t\r\n\t// Usage breakdown by model\r\n\tmodelUsage: Record<string, {\r\n\t\ttokens: number\r\n\t\tcost: number\r\n\t\trequests: number\r\n\t}>\r\n}\r\n\r\n/**\r\n * User Profile\r\n */\r\nexport interface UserProfile {\r\n\tid: string\r\n\temail: string\r\n\tname?: string\r\n\tpicture?: string\r\n\t\r\n\t// Subscription info\r\n\tsubscriptionTier: SubscriptionTier\r\n\tsubscriptionStatus: SubscriptionStatus\r\n\tsubscriptionStartDate: Date\r\n\tsubscriptionEndDate?: Date\r\n\t\r\n\t// Trial info\r\n\ttrialStartDate?: Date\r\n\ttrialEndDate?: Date\r\n\ttrialExtensions: number\r\n\t\r\n\t// Usage tracking\r\n\tquotas: UsageQuotas\r\n\tusage: UsageMetrics\r\n\t\r\n\t// Settings\r\n\tpreferences: UserPreferences\r\n\t\r\n\t// Metadata\r\n\tcreatedAt: Date\r\n\tupdatedAt: Date\r\n\tlastActiveAt: Date\r\n}\r\n\r\n/**\r\n * User Preferences\r\n */\r\nexport interface UserPreferences {\r\n\t// Notifications\r\n\tusageWarningsEnabled: boolean\r\n\ttrialExpiryNotifications: boolean\r\n\t\r\n\t// Usage tracking\r\n\tdetailedUsageTracking: boolean\r\n\tcostAlertsEnabled: boolean\r\n\tcostAlertThreshold: number // Percentage of monthly limit\r\n\t\r\n\t// Features\r\n\tautoUpgradeEnabled: boolean\r\n\tpreferredUpgradeTier: SubscriptionTier\r\n}\r\n\r\n/**\r\n * Trial Management\r\n */\r\nexport interface TrialInfo {\r\n\tisInTrial: boolean\r\n\tdaysRemaining: number\r\n\ttokensRemaining: number\r\n\tcostRemaining: number\r\n\tcanExtend: boolean\r\n\textensionsUsed: number\r\n\tmaxExtensions: number\r\n}\r\n\r\n/**\r\n * Usage Alert\r\n */\r\nexport interface UsageAlert {\r\n\tid: string\r\n\tuserId: string\r\n\ttype: 'token_limit' | 'cost_limit' | 'request_limit' | 'trial_expiry'\r\n\tseverity: 'info' | 'warning' | 'critical'\r\n\tmessage: string\r\n\tthreshold: number\r\n\tcurrentValue: number\r\n\tcreatedAt: Date\r\n\tacknowledged: boolean\r\n}\r\n\r\n/**\r\n * Subscription Plans Configuration\r\n */\r\nexport const SUBSCRIPTION_PLANS: Record<SubscriptionTier, UsageQuotas> = {\r\n\t[SubscriptionTier.FREE_TRIAL]: {\r\n\t\tmonthlyTokenLimit: 100_000, // 100K tokens\r\n\t\tmonthlyCostLimit: 10, // $10\r\n\t\thourlyRequestLimit: 50,\r\n\t\tdailyRequestLimit: 500,\r\n\t\tmaxContextWindow: 32_000,\r\n\t\tallowedModels: [\r\n\t\t\t\"Claude 3.5 Sonnet\",\r\n\t\t\t\"GPT-4o Mini\",\r\n\t\t\t\"Gemini 1.5 Flash\"\r\n\t\t],\r\n\t\tcanUseReasoningModels: false,\r\n\t\tcanUseCodebaseIndex: false,\r\n\t\tcanUseCustomModes: false,\r\n\t\tcanExportHistory: false\r\n\t},\r\n\t[SubscriptionTier.BASIC]: {\r\n\t\tmonthlyTokenLimit: 1_000_000, // 1M tokens\r\n\t\tmonthlyCostLimit: 50, // $50\r\n\t\thourlyRequestLimit: 200,\r\n\t\tdailyRequestLimit: 2_000,\r\n\t\tmaxContextWindow: 128_000,\r\n\t\tallowedModels: [\r\n\t\t\t\"Claude 3.5 Sonnet\",\r\n\t\t\t\"Claude Sonnet 4\",\r\n\t\t\t\"GPT-4o\",\r\n\t\t\t\"GPT-4o Mini\",\r\n\t\t\t\"Gemini 1.5 Pro\",\r\n\t\t\t\"Gemini 1.5 Flash\"\r\n\t\t],\r\n\t\tcanUseReasoningModels: false,\r\n\t\tcanUseCodebaseIndex: true,\r\n\t\tcanUseCustomModes: true,\r\n\t\tcanExportHistory: true\r\n\t},\r\n\t[SubscriptionTier.PRO]: {\r\n\t\tmonthlyTokenLimit: 5_000_000, // 5M tokens\r\n\t\tmonthlyCostLimit: 200, // $200\r\n\t\thourlyRequestLimit: 500,\r\n\t\tdailyRequestLimit: 5_000,\r\n\t\tmaxContextWindow: 200_000,\r\n\t\tallowedModels: [\r\n\t\t\t\"Claude 3.5 Sonnet\",\r\n\t\t\t\"Claude Sonnet 4\",\r\n\t\t\t\"Claude 3.7 Sonnet (Thinking)\",\r\n\t\t\t\"GPT-4o\",\r\n\t\t\t\"GPT-4o Mini\",\r\n\t\t\t\"o1-preview\",\r\n\t\t\t\"o1-mini\",\r\n\t\t\t\"Gemini 1.5 Pro\",\r\n\t\t\t\"Gemini 2.0 Pro\",\r\n\t\t\t\"DeepSeek V3\"\r\n\t\t],\r\n\t\tcanUseReasoningModels: true,\r\n\t\tcanUseCodebaseIndex: true,\r\n\t\tcanUseCustomModes: true,\r\n\t\tcanExportHistory: true\r\n\t},\r\n\t[SubscriptionTier.ENTERPRISE]: {\r\n\t\tmonthlyTokenLimit: 20_000_000, // 20M tokens\r\n\t\tmonthlyCostLimit: 1000, // $1000\r\n\t\thourlyRequestLimit: 2000,\r\n\t\tdailyRequestLimit: 20_000,\r\n\t\tmaxContextWindow: 1_000_000,\r\n\t\tallowedModels: [], // All models allowed\r\n\t\tcanUseReasoningModels: true,\r\n\t\tcanUseCodebaseIndex: true,\r\n\t\tcanUseCustomModes: true,\r\n\t\tcanExportHistory: true\r\n\t}\r\n}\r\n\r\n/**\r\n * Zod Schemas for Validation\r\n */\r\nexport const usageQuotasSchema = z.object({\r\n\tmonthlyTokenLimit: z.number().min(0),\r\n\tmonthlyCostLimit: z.number().min(0),\r\n\thourlyRequestLimit: z.number().min(0),\r\n\tdailyRequestLimit: z.number().min(0),\r\n\tmaxContextWindow: z.number().min(0),\r\n\tallowedModels: z.array(z.string()),\r\n\tcanUseReasoningModels: z.boolean(),\r\n\tcanUseCodebaseIndex: z.boolean(),\r\n\tcanUseCustomModes: z.boolean(),\r\n\tcanExportHistory: z.boolean()\r\n})\r\n\r\nexport const usageMetricsSchema = z.object({\r\n\tcurrentMonthTokens: z.number().min(0),\r\n\tcurrentMonthCost: z.number().min(0),\r\n\tcurrentHourRequests: z.number().min(0),\r\n\tcurrentDayRequests: z.number().min(0),\r\n\ttotalTokensUsed: z.number().min(0),\r\n\ttotalCostAccrued: z.number().min(0),\r\n\ttotalRequestsMade: z.number().min(0),\r\n\tlastMonthlyReset: z.date(),\r\n\tlastHourlyReset: z.date(),\r\n\tlastDailyReset: z.date(),\r\n\tmodelUsage: z.record(z.object({\r\n\t\ttokens: z.number().min(0),\r\n\t\tcost: z.number().min(0),\r\n\t\trequests: z.number().min(0)\r\n\t}))\r\n})\r\n\r\nexport const userPreferencesSchema = z.object({\r\n\tusageWarningsEnabled: z.boolean(),\r\n\ttrialExpiryNotifications: z.boolean(),\r\n\tdetailedUsageTracking: z.boolean(),\r\n\tcostAlertsEnabled: z.boolean(),\r\n\tcostAlertThreshold: z.number().min(0).max(100),\r\n\tautoUpgradeEnabled: z.boolean(),\r\n\tpreferredUpgradeTier: z.nativeEnum(SubscriptionTier)\r\n})\r\n\r\nexport const userProfileSchema = z.object({\r\n\tid: z.string(),\r\n\temail: z.string().email(),\r\n\tname: z.string().optional(),\r\n\tpicture: z.string().optional(),\r\n\tsubscriptionTier: z.nativeEnum(SubscriptionTier),\r\n\tsubscriptionStatus: z.nativeEnum(SubscriptionStatus),\r\n\tsubscriptionStartDate: z.date(),\r\n\tsubscriptionEndDate: z.date().optional(),\r\n\ttrialStartDate: z.date().optional(),\r\n\ttrialEndDate: z.date().optional(),\r\n\ttrialExtensions: z.number().min(0),\r\n\tquotas: usageQuotasSchema,\r\n\tusage: usageMetricsSchema,\r\n\tpreferences: userPreferencesSchema,\r\n\tcreatedAt: z.date(),\r\n\tupdatedAt: z.date(),\r\n\tlastActiveAt: z.date()\r\n})\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACKO,IAAM,0BAA4C;AAElD,IAAM,kBAAkB;AAAA,EAC9B,4BAA4B;AAAA,IAC3B,WAAW;AAAA;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,qBAAqB;AAAA,IACrB,YAAY;AAAA;AAAA,IACZ,aAAa;AAAA;AAAA,IACb,kBAAkB;AAAA;AAAA,IAClB,iBAAiB;AAAA;AAAA,IACjB,yBAAyB;AAAA,EAC1B;AAAA,EACA,0BAA0B;AAAA,IACzB,WAAW;AAAA;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,qBAAqB;AAAA,IACrB,YAAY;AAAA;AAAA,IACZ,aAAa;AAAA;AAAA,IACb,kBAAkB;AAAA;AAAA,IAClB,iBAAiB;AAAA;AAAA,IACjB,yBAAyB;AAAA,EAC1B;AAAA,EACA,uCAAuC;AAAA,IACtC,WAAW;AAAA;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,qBAAqB;AAAA,IACrB,YAAY;AAAA;AAAA,IACZ,aAAa;AAAA;AAAA,IACb,kBAAkB;AAAA;AAAA,IAClB,iBAAiB;AAAA;AAAA,IACjB,yBAAyB;AAAA,IACzB,yBAAyB;AAAA,EAC1B;AAAA,EACA,8BAA8B;AAAA,IAC7B,WAAW;AAAA;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,qBAAqB;AAAA,IACrB,YAAY;AAAA;AAAA,IACZ,aAAa;AAAA;AAAA,IACb,kBAAkB;AAAA;AAAA,IAClB,iBAAiB;AAAA;AAAA,EAClB;AAAA,EACA,8BAA8B;AAAA,IAC7B,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,qBAAqB;AAAA,IACrB,YAAY;AAAA;AAAA,IACZ,aAAa;AAAA;AAAA,IACb,kBAAkB;AAAA;AAAA,IAClB,iBAAiB;AAAA;AAAA,EAClB;AAAA,EACA,6BAA6B;AAAA,IAC5B,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,kBAAkB;AAAA,IAClB,iBAAiB;AAAA,EAClB;AAAA,EACA,0BAA0B;AAAA,IACzB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,kBAAkB;AAAA,IAClB,iBAAiB;AAAA,EAClB;AAAA,EACA,2BAA2B;AAAA,IAC1B,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,kBAAkB;AAAA,IAClB,iBAAiB;AAAA,EAClB;AACD;AAEO,IAAM,+BAA+B;;;AC7FrC,IAAM,wBAAwC;AAE9C,IAAM,oCAAoD;AAM1D,IAAM,gBAAgB;AAAA,EAC5B,wBAAwB;AAAA,IACvB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,kBAAkB;AAAA;AAAA,IAClB,iBAAiB;AAAA;AAAA,IACjB,wBAAwB;AAAA,IACxB,gBAAgB;AAAA,IAChB,gBAAgB,CAAC,QAAQ;AAAA,EAC1B;AAAA,EACA,0CAA0C;AAAA,IACzC,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,kBAAkB;AAAA;AAAA,IAClB,iBAAiB;AAAA;AAAA,IACjB,aAAa;AAAA,EACd;AAAA,EACA,yBAAyB;AAAA,IACxB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,kBAAkB;AAAA;AAAA,IAClB,iBAAiB;AAAA;AAAA,IACjB,wBAAwB;AAAA,IACxB,gBAAgB;AAAA,IAChB,gBAAgB,CAAC,QAAQ;AAAA,EAC1B;AAAA,EACA,0BAA0B;AAAA,IACzB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,kBAAkB;AAAA;AAAA,IAClB,iBAAiB;AAAA;AAAA,IACjB,wBAAwB;AAAA,IACxB,gBAAgB;AAAA,IAChB,gBAAgB,CAAC,QAAQ;AAAA,EAC1B;AAAA,EACA,2CAA2C;AAAA,IAC1C,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,kBAAkB;AAAA,IAClB,iBAAiB;AAAA,IACjB,wBAAwB;AAAA,IACxB,gBAAgB;AAAA,IAChB,gBAAgB,CAAC,UAAU,YAAY,OAAO;AAAA,EAC/C;AAAA,EACA,yCAAyC;AAAA,IACxC,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,kBAAkB;AAAA,IAClB,iBAAiB;AAAA,IACjB,wBAAwB;AAAA,IACxB,gBAAgB;AAAA,IAChB,gBAAgB,CAAC,UAAU,YAAY,OAAO;AAAA,EAC/C;AAAA,EACA,6CAA6C;AAAA,IAC5C,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,kBAAkB;AAAA,IAClB,iBAAiB;AAAA,IACjB,wBAAwB;AAAA,IACxB,gBAAgB;AAAA,IAChB,gBAAgB,CAAC,UAAU,YAAY,OAAO;AAAA,EAC/C;AAAA,EACA,6CAA6C;AAAA,IAC5C,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,kBAAkB;AAAA,IAClB,iBAAiB;AAAA,IACjB,wBAAwB;AAAA,IACxB,gBAAgB;AAAA,IAChB,gBAAgB,CAAC,UAAU,YAAY,OAAO;AAAA,EAC/C;AAAA,EACA,4CAA4C;AAAA,IAC3C,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,kBAAkB;AAAA,IAClB,iBAAiB;AAAA,IACjB,wBAAwB;AAAA,IACxB,gBAAgB;AAAA,IAChB,gBAAgB,CAAC,UAAU,YAAY,OAAO;AAAA,EAC/C;AAAA,EACA,6CAA6C;AAAA,IAC5C,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,EACd;AAAA,EACA,yCAAyC;AAAA,IACxC,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,EACd;AAAA,EACA,2CAA2C;AAAA,IAC1C,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,EACd;AAAA,EACA,0CAA0C;AAAA,IACzC,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,EACd;AAAA,EACA,6BAA6B;AAAA,IAC5B,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,6BAA6B;AAAA,IAC5B,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,iCAAiC;AAAA,IAChC,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,oBAAoB;AAAA,IACnB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,EACd;AAAA,EACA,mCAAmC;AAAA,IAClC,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,mCAAmC;AAAA,IAClC,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,mCAAmC;AAAA,IAClC,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,kCAAkC;AAAA,IACjC,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,kCAAkC;AAAA,IACjC,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,oCAAoC;AAAA,IACnC,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,mCAAmC;AAAA,IAClC,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,qDAAqD;AAAA,IACpD,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,kCAAkC;AAAA,IACjC,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,iCAAiC;AAAA,IAChC,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,EACd;AAAA,EACA,gCAAgC;AAAA,IAC/B,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,EACd;AAAA,EACA,+BAA+B;AAAA,IAC9B,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,kCAAkC;AAAA,IACjC,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,qCAAqC;AAAA,IACpC,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,EACd;AAAA,EACA,qCAAqC;AAAA,IACpC,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,EACd;AACD;AAEO,IAAM,8BAA8B;AAEpC,IAAM,qBAAqB;AAE3B,IAAM,sBAQT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUH,OAAO,EAAE,UAAU,aAAa,aAAa,yBAAyB,SAAS,OAAO,aAAa,KAAK;AAAA,EACxG,QAAQ,EAAE,UAAU,aAAa,aAAa,wBAAwB;AAAA,EACtE,SAAS,EAAE,UAAU,aAAa,aAAa,wBAAwB;AAAA,EACvE,SAAS,EAAE,UAAU,aAAa,aAAa,iBAAiB;AAAA,EAChE,QAAQ,EAAE,UAAU,aAAa,aAAa,mBAAmB;AAAA,EACjE,SAAS,EAAE,UAAU,aAAa,aAAa,mBAAmB;AAAA,EAClE,OAAO;AAAA,IACN,UAAU;AAAA,IACV,aAAa;AAAA,IACb,SAAS;AAAA,IACT,aAAa;AAAA,EACd;AAAA,EACA,SAAS,EAAE,UAAU,iBAAiB,aAAa,yBAAyB;AAAA,EAC5E,SAAS,EAAE,UAAU,iBAAiB,aAAa,yBAAyB;AAAA,EAC5E,OAAO,EAAE,UAAU,aAAa,aAAa,oBAAoB,SAAS,OAAO,aAAa,KAAK;AAAA,EACnG,SAAS,EAAE,UAAU,aAAa,aAAa,mBAAmB;AAAA,EAClE,SAAS,EAAE,UAAU,aAAa,aAAa,kBAAkB;AAAA,EACjE,SAAS,EAAE,UAAU,aAAa,aAAa,iBAAiB;AAAA,EAChE,SAAS,EAAE,UAAU,gBAAgB,aAAa,qBAAqB;AAAA,EACvE,SAAS,EAAE,UAAU,gBAAgB,aAAa,kBAAkB;AAAA,EACpE,SAAS,EAAE,UAAU,cAAc,aAAa,qBAAqB;AAAA,EACrE,SAAS,EAAE,UAAU,cAAc,aAAa,iBAAiB;AAAA,EACjE,SAAS,EAAE,UAAU,cAAc,aAAa,iBAAiB;AAAA,EACjE,OAAO;AAAA,IACN,UAAU;AAAA,IACV,aAAa;AAAA,IACb,SAAS;AAAA,IACT,aAAa;AAAA,EACd;AAAA,EACA,SAAS,EAAE,UAAU,aAAa,aAAa,2BAA2B;AAAA,EAC1E,UAAU,EAAE,UAAU,kBAAkB,aAAa,uBAAuB;AAAA,EAC5E,UAAU,EAAE,UAAU,kBAAkB,aAAa,uBAAuB;AAAA,EAC5E,UAAU,EAAE,UAAU,kBAAkB,aAAa,uBAAuB;AAAA,EAC5E,SAAS,EAAE,UAAU,cAAc,aAAa,wBAAwB;AAAA,EACxE,SAAS,EAAE,UAAU,cAAc,aAAa,2BAA2B;AAAA,EAC3E,UAAU,EAAE,UAAU,kBAAkB,aAAa,2BAA2B;AAAA,EAChF,UAAU,EAAE,UAAU,kBAAkB,aAAa,wBAAwB;AAAA,EAC7E,OAAO,EAAE,UAAU,gBAAgB,aAAa,oBAAoB,SAAS,OAAO,aAAa,KAAK;AAAA,EACtG,SAAS,EAAE,UAAU,gBAAgB,aAAa,mBAAmB;AAAA,EACrE,OAAO,EAAE,UAAU,aAAa,aAAa,gCAA6B,SAAS,OAAO,aAAa,KAAK;AAAA,EAC5G,SAAS,EAAE,UAAU,aAAa,aAAa,+BAA4B;AAAA;AAAA;AAAA;AAAA,EAK3E,SAAS,EAAE,UAAU,kBAAkB,aAAa,uBAAuB,SAAS,OAAO,aAAa,KAAK;AAAA,EAC7G,SAAS,EAAE,UAAU,aAAa,aAAa,uBAAuB,SAAS,OAAO,aAAa,KAAK;AAAA,EACxG,SAAS,EAAE,UAAU,aAAa,aAAa,2BAA2B,SAAS,OAAO,aAAa,KAAK;AAC7G;AAEO,IAAM,kBAAkB,OAAO,OAAO,mBAAmB,EAE9D,IAAI,CAAC,UAAU,EAAE,OAAO,KAAK,UAAU,OAAO,KAAK,SAAS,EAAE,EAE9D,OAAO,CAAC,QAAQ,OAAO,SAAS,UAAU,KAAK,UAAU,CAAC,MAAM,EAAE,UAAU,OAAO,KAAK,CAAC,EAEzF,KAAK,CAAC,GAAG,MAAM,EAAE,MAAM,cAAc,EAAE,KAAK,CAAC;;;ACpZxC,IAAM,uBAAsC;AAE5C,IAAM,eAAe;AAAA,EAC3B,gCAAgC;AAAA,IAC/B,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,2BAA2B;AAAA,IAC1B,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,2BAA2B;AAAA,IAC1B,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,kCAAkC;AAAA,IACjC,WAAW;AAAA;AAAA,IACX,eAAe;AAAA;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,2CAA2C;AAAA,IAC1C,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,sCAAsC;AAAA,IACrC,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,0BAA0B;AAAA,IACzB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,gDAAgD;AAAA,IAC/C,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,yBAAyB;AAAA,IACxB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,0CAA0C;AAAA,IACzC,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,2CAA2C;AAAA,IAC1C,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,mDAAmD;AAAA,IAClD,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,gCAAgC;AAAA,IAC/B,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,gCAAgC;AAAA,IAC/B,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,gCAAgC;AAAA,IAC/B,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,wBAAwB;AAAA,IACvB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,kBAAkB;AAAA,IACjB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,sBAAsB;AAAA,IACrB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,kBAAkB;AAAA,IACjB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,iBAAiB;AAAA,IAChB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,2BAA2B;AAAA,IAC1B,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,gCAAgC;AAAA,IAC/B,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AACD;;;AC/NO,IAAM,yBAA0C;AAEhD,IAAM,iBAAiB;AAAA,EAC7B,iBAAiB;AAAA,IAChB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA;AAAA,IACZ,aAAa;AAAA;AAAA,IACb,kBAAkB;AAAA;AAAA,IAClB,iBAAiB;AAAA;AAAA,IACjB,aAAa;AAAA,EACd;AAAA,EACA,qBAAqB;AAAA,IACpB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA;AAAA,IACZ,aAAa;AAAA;AAAA,IACb,kBAAkB;AAAA;AAAA,IAClB,iBAAiB;AAAA;AAAA,IACjB,aAAa;AAAA,EACd;AACD;AAEO,IAAM,gCAAgC;;;AC3BtC,IAAM,uBAAsC;AAE5C,IAAM,eAAe;AAAA,EAC3B,2CAA2C;AAAA,IAC1C,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,mBAAmB;AAAA,IACnB,yBAAyB;AAAA,IACzB,yBAAyB;AAAA,EAC1B;AAAA,EACA,kCAAkC;AAAA,IACjC,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,EACd;AAAA,EACA,2CAA2C;AAAA,IAC1C,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,kBAAkB;AAAA,IAClB,mBAAmB;AAAA,IACnB,yBAAyB;AAAA,IACzB,yBAAyB;AAAA,EAC1B;AAAA,EACA,kCAAkC;AAAA,IACjC,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,kBAAkB;AAAA,EACnB;AAAA,EACA,4BAA4B;AAAA,IAC3B,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,EACd;AAAA,EACA,gCAAgC;AAAA,IAC/B,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA;AAAA,IACZ,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,kBAAkB;AAAA,IAClB,OAAO;AAAA,MACN;AAAA,QACC,eAAe;AAAA,QACf,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,iBAAiB;AAAA,MAClB;AAAA,MACA;AAAA,QACC,eAAe;AAAA,QACf,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,iBAAiB;AAAA,MAClB;AAAA,IACD;AAAA,EACD;AAAA,EACA,gCAAgC;AAAA,IAC/B,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA;AAAA,IACZ,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,kBAAkB;AAAA,IAClB,OAAO;AAAA,MACN;AAAA,QACC,eAAe;AAAA,QACf,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,iBAAiB;AAAA,MAClB;AAAA,MACA;AAAA,QACC,eAAe;AAAA,QACf,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,iBAAiB;AAAA,MAClB;AAAA,IACD;AAAA,EACD;AAAA,EACA,wBAAwB;AAAA,IACvB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,kBAAkB;AAAA,EACnB;AAAA,EACA,uCAAuC;AAAA,IACtC,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,EACd;AAAA,EACA,4BAA4B;AAAA,IAC3B,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,EACd;AAAA,EACA,uCAAuC;AAAA,IACtC,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,EACd;AAAA,EACA,sCAAsC;AAAA,IACrC,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,EACd;AAAA,EACA,wBAAwB;AAAA,IACvB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,EACd;AAAA,EACA,wBAAwB;AAAA,IACvB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA;AAAA,IACZ,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,kBAAkB;AAAA,IAClB,OAAO;AAAA,MACN;AAAA,QACC,eAAe;AAAA,QACf,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,iBAAiB;AAAA,MAClB;AAAA,MACA;AAAA,QACC,eAAe;AAAA,QACf,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,iBAAiB;AAAA,MAClB;AAAA,IACD;AAAA,EACD;AAAA,EACA,6BAA6B;AAAA,IAC5B,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,EACd;AAAA,EACA,gCAAgC;AAAA,IAC/B,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,EACd;AAAA,EACA,sBAAsB;AAAA,IACrB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,EACd;AAAA,EACA,2BAA2B;AAAA,IAC1B,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,EACd;AAAA,EACA,mBAAmB;AAAA,IAClB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,EACd;AACD;;;ACzNO,IAAM,sBAAsB;AAE5B,IAAM,wBAAmC;AAAA,EAC/C,WAAW;AAAA,EACX,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,qBAAqB;AAAA,EACrB,qBAAqB;AAAA,EACrB,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,kBAAkB;AAAA,EAClB,iBAAiB;AAAA,EACjB,aACC;AACF;AAEO,IAAM,4BAA4B;;;ACPlC,IAAM,qBAAkC;AAExC,IAAM,aAAa;AAAA;AAAA,EAEzB,wBAAwB;AAAA,IACvB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,2BAA2B;AAAA,IAC1B,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,6CAA6C;AAAA,IAC5C,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,iDAAiD;AAAA,IAChD,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,oBAAoB;AAAA,IACnB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,gBAAgB;AAAA,IACf,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,iCAAiC;AAAA,IAChC,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AACD;;;AC5EO,IAAM,wBAAwB;AAE9B,IAAM,0BAAqC;AAAA,EACjD,WAAW;AAAA,EACX,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,qBAAqB;AAAA,EACrB,qBAAqB;AAAA,EACrB,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,kBAAkB;AAAA,EAClB,iBAAiB;AAClB;AAEO,IAAM,8BAA8B,oBAAI,IAAI;AAAA,EAClD;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACD,CAAC;;;AC/CM,IAAM,+BAA+B;;;ACKrC,IAAM,wBAAwC;AAE9C,IAAM,gBAAgB;AAAA,EAC5B,oBAAoB;AAAA,IACnB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,EACd;AAAA,EACA,wBAAwB;AAAA,IACvB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,EACd;AAAA,EACA,uBAAuB;AAAA,IACtB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,EACd;AAAA,EACA,uBAAuB;AAAA,IACtB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,EACd;AAAA,EACA,wBAAwB;AAAA,IACvB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,EACd;AAAA,EACA,wBAAwB;AAAA,IACvB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,EACd;AACD;AAEO,IAAM,8BAA8B;;;ACrDpC,IAAM,6BAAkD;AAExD,IAAM,qBAAqB;AAAA,EACjC,WAAW;AAAA,IACV,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,iBAAiB;AAAA,EAClB;AAAA,EACA,gBAAgB;AAAA,IACf,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,iBAAiB;AAAA,EAClB;AAAA,EACA,gBAAgB;AAAA,IACf,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,iBAAiB;AAAA,EAClB;AAAA,EACA,IAAI;AAAA,IACH,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,yBAAyB;AAAA,IACzB,iBAAiB;AAAA,EAClB;AAAA,EACA,WAAW;AAAA,IACV,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,iBAAiB;AAAA,EAClB;AAAA,EACA,UAAU;AAAA,IACT,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,iBAAiB;AAAA,EAClB;AAAA,EACA,WAAW;AAAA,IACV,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,yBAAyB;AAAA,IACzB,iBAAiB;AAAA,EAClB;AAAA,EACA,gBAAgB;AAAA,IACf,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,iBAAiB;AAAA,EAClB;AAAA,EACA,eAAe;AAAA,IACd,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,iBAAiB;AAAA,EAClB;AAAA,EACA,WAAW;AAAA,IACV,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,yBAAyB;AAAA,IACzB,iBAAiB;AAAA,EAClB;AAAA,EACA,gBAAgB;AAAA,IACf,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,iBAAiB;AAAA,EAClB;AAAA,EACA,eAAe;AAAA,IACd,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,iBAAiB;AAAA,EAClB;AAAA,EACA,IAAI;AAAA,IACH,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,iBAAiB;AAAA,EAClB;AAAA,EACA,cAAc;AAAA,IACb,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,iBAAiB;AAAA,EAClB;AAAA,EACA,WAAW;AAAA,IACV,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,iBAAiB;AAAA,EAClB;AAAA,EACA,mBAAmB;AAAA,IAClB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,iBAAiB;AAAA,EAClB;AAAA,EACA,UAAU;AAAA,IACT,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,iBAAiB;AAAA,EAClB;AAAA,EACA,eAAe;AAAA,IACd,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,iBAAiB;AAAA,EAClB;AACD;AAEO,IAAM,8BAAyC;AAAA,EACrD,WAAW;AAAA,EACX,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,qBAAqB;AAAA,EACrB,YAAY;AAAA,EACZ,aAAa;AACd;AAIO,IAAM,+BAA+B;AAErC,IAAM,oCAAoC;AAE1C,IAAM,iCAAiC;;;ACpMvC,IAAM,2BAA2B;AAEjC,IAAM,6BAAwC;AAAA,EACpD,WAAW;AAAA,EACX,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,qBAAqB;AAAA,EACrB,qBAAqB;AAAA,EACrB,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,kBAAkB;AAAA,EAClB,iBAAiB;AAAA,EACjB,aACC;AACF;AAEO,IAAM,mCAAmC;AAEzC,IAAM,oCAAoC,oBAAI,IAAI;AAAA,EACxD;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACD,CAAC;AAGM,IAAM,kCAAkC,oBAAI,IAAI;AAAA,EACtD;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACD,CAAC;AAEM,IAAM,sCAAsC,oBAAI,IAAI;AAAA,EAC1D;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACD,CAAC;AAEM,IAAM,+CAA+C,oBAAI,IAAI;AAAA,EACnE;AAAA,EACA;AACD,CAAC;;;ACtEM,IAAM,yBAAyB;AAE/B,IAAM,2BAAsC;AAAA,EAClD,WAAW;AAAA,EACX,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,qBAAqB;AAAA,EACrB,qBAAqB;AAAA,EACrB,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,kBAAkB;AAAA,EAClB,iBAAiB;AAAA,EACjB,aACC;AACF;;;AChBO,IAAM,wBAAwB;AAE9B,IAAM,0BAAqC;AAAA,EACjD,WAAW;AAAA,EACX,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,qBAAqB;AAAA,EACrB,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,kBAAkB;AAAA,EAClB,iBAAiB;AAClB;;;ACRO,IAAM,uBAAsC;AAE5C,IAAM,eAAe;AAAA,EAC3B,2CAA2C;AAAA,IAC1C,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,mBAAmB;AAAA,IACnB,yBAAyB;AAAA,IACzB,yBAAyB;AAAA,EAC1B;AAAA,EACA,kCAAkC;AAAA,IACjC,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,EACd;AAAA,EACA,2CAA2C;AAAA,IAC1C,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,mBAAmB;AAAA,IACnB,yBAAyB;AAAA,IACzB,yBAAyB;AAAA,EAC1B;AAAA,EACA,kCAAkC;AAAA,IACjC,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,EACd;AAAA,EACA,gCAAgC;AAAA,IAC/B,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,EACd;AAAA,EACA,gCAAgC;AAAA,IAC/B,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,EACd;AAAA,EACA,4BAA4B;AAAA,IAC3B,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,EACd;AAAA,EACA,4BAA4B;AAAA,IAC3B,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,EACd;AAAA,EACA,wBAAwB;AAAA,IACvB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,EACd;AAAA,EACA,6BAA6B;AAAA,IAC5B,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,EACd;AAAA,EACA,uCAAuC;AAAA,IACtC,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,EACd;AAAA,EACA,wBAAwB;AAAA,IACvB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,EACd;AAAA,EACA,sBAAsB;AAAA,IACrB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,EACd;AAAA,EACA,4BAA4B;AAAA,IAC3B,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,kBAAkB;AAAA,IAClB,iBAAiB;AAAA,IACjB,yBAAyB;AAAA,EAC1B;AAAA,EACA,0BAA0B;AAAA,IACzB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,kBAAkB;AAAA,IAClB,iBAAiB;AAAA,EAClB;AAAA,EACA,uCAAuC;AAAA,IACtC,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,kBAAkB;AAAA,IAClB,iBAAiB;AAAA,IACjB,yBAAyB;AAAA,IACzB,yBAAyB;AAAA,EAC1B;AAAA,EACA,8BAA8B;AAAA,IAC7B,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,kBAAkB;AAAA,IAClB,iBAAiB;AAAA,EAClB;AAAA,EACA,iCAAiC;AAAA,IAChC,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,kBAAkB;AAAA,IAClB,iBAAiB;AAAA,EAClB;AAAA,EACA,8BAA8B;AAAA,IAC7B,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,kBAAkB;AAAA,IAClB,iBAAiB;AAAA,EAClB;AAAA,EACA,6BAA6B;AAAA,IAC5B,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,kBAAkB;AAAA,IAClB,iBAAiB;AAAA,EAClB;AAAA,EACA,0BAA0B;AAAA,IACzB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,kBAAkB;AAAA,IAClB,iBAAiB;AAAA,EAClB;AAAA,EACA,2BAA2B;AAAA,IAC1B,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,kBAAkB;AAAA,IAClB,iBAAiB;AAAA,EAClB;AACD;AAEO,IAAM,iBAAiB;AAAA,EAC7B,EAAE,OAAO,YAAY,OAAO,WAAW;AAAA,EACvC,EAAE,OAAO,eAAe,OAAO,cAAc;AAAA,EAC7C,EAAE,OAAO,gBAAgB,OAAO,eAAe;AAAA,EAC/C,EAAE,OAAO,gBAAgB,OAAO,eAAe;AAAA,EAC/C,EAAE,OAAO,mBAAmB,OAAO,kBAAkB;AACtD;;;AC5NO,IAAM,0BAA4C;AAElD,IAAM,kBAAkB;AAAA,EAC9B,iBAAiB;AAAA,IAChB,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,MAAM;AAAA,IACN,qBAAqB;AAAA,IACrB,gBAAgB;AAAA,EACjB;AAAA,EACA,eAAe;AAAA,IACd,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,MAAM;AAAA,IACN,qBAAqB;AAAA,IACrB,gBAAgB;AAAA,EACjB;AAAA,EACA,SAAS;AAAA,IACR,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,MAAM;AAAA,IACN,qBAAqB;AAAA,IACrB,gBAAgB;AAAA,EACjB;AAAA,EACA,sBAAsB;AAAA,IACrB,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,MAAM;AAAA,IACN,qBAAqB;AAAA,IACrB,gBAAgB;AAAA,EACjB;AAAA,EACA,UAAU;AAAA,IACT,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,MAAM;AAAA,IACN,qBAAqB;AAAA,IACrB,gBAAgB;AAAA,EACjB;AAAA,EACA,IAAI;AAAA,IACH,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,MAAM;AAAA,IACN,qBAAqB;AAAA,IACrB,gBAAgB;AAAA,EACjB;AAAA,EACA,WAAW;AAAA,IACV,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,MAAM;AAAA,IACN,qBAAqB;AAAA,IACrB,gBAAgB;AAAA,EACjB;AAAA,EACA,qBAAqB;AAAA,IACpB,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,MAAM;AAAA,IACN,qBAAqB;AAAA,IACrB,gBAAgB;AAAA,EACjB;AAAA,EACA,wBAAwB;AAAA,IACvB,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,MAAM;AAAA,IACN,qBAAqB;AAAA,IACrB,gBAAgB;AAAA,EACjB;AAAA,EACA,kBAAkB;AAAA,IACjB,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,MAAM;AAAA,IACN,qBAAqB;AAAA,IACrB,gBAAgB;AAAA,EACjB;AAAA,EACA,WAAW;AAAA,IACV,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,MAAM;AAAA,IACN,qBAAqB;AAAA,IACrB,gBAAgB;AAAA,EACjB;AAAA,EACA,WAAW;AAAA,IACV,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,MAAM;AAAA,IACN,qBAAqB;AAAA,IACrB,gBAAgB;AAAA,EACjB;AACD;;;AClJO,IAAM,oBAAgC;AAEtC,IAAM,YAAY;AAAA,EACxB,eAAe;AAAA,IACd,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,oBAAoB;AAAA,IACnB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,oBAAoB;AAAA,IACnB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,IACb,yBAAyB;AAAA,EAC1B;AAAA,EACA,yBAAyB;AAAA,IACxB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,IACb,yBAAyB;AAAA,EAC1B;AAAA,EACA,UAAU;AAAA,IACT,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,eAAe;AAAA,IACd,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,eAAe;AAAA,IACd,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,IACb,yBAAyB;AAAA,EAC1B;AAAA,EACA,oBAAoB;AAAA,IACnB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,IACb,yBAAyB;AAAA,EAC1B;AAAA,EACA,iBAAiB;AAAA,IAChB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,UAAU;AAAA,IACT,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,eAAe;AAAA,IACd,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,wBAAwB;AAAA,IACvB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,iBAAiB;AAAA,IAChB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,sBAAsB;AAAA,IACrB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,oBAAoB;AAAA,IACnB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA,EACA,aAAa;AAAA,IACZ,WAAW;AAAA,IACX,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AACD;;;AC5JA,iBAAkB;AAMX,IAAM,4BAA4B,aAAE,OAAO;AAAA,EACjD,sBAAsB,aAAE,QAAQ,EAAE,SAAS;AAAA,EAC3C,wBAAwB,aAAE,OAAO,EAAE,SAAS;AAAA,EAC5C,+BAA+B,aAAE,KAAK,CAAC,UAAU,QAAQ,CAAC,EAAE,SAAS;AAAA,EACrE,8BAA8B,aAAE,OAAO,EAAE,SAAS;AAAA,EAClD,8BAA8B,aAAE,OAAO,EAAE,SAAS;AACnD,CAAC;AAQM,IAAM,4BAA4B,aAAE,OAAO;AAAA,EACjD,QAAQ,aAAE,OAAO,aAAE,OAAO,GAAG,aAAE,OAAO,EAAE,WAAW,aAAE,OAAO,EAAE,CAAC,CAAC,EAAE,SAAS;AAAA,EAC3E,QAAQ,aAAE,OAAO,aAAE,OAAO,GAAG,aAAE,OAAO,EAAE,WAAW,aAAE,OAAO,EAAE,CAAC,CAAC,EAAE,SAAS;AAC5E,CAAC;AAQM,IAAM,8BAA8B,aAAE,OAAO;AAAA,EACnD,oBAAoB,aAAE,OAAO,EAAE,SAAS;AAAA,EACxC,uBAAuB,aAAE,OAAO,EAAE,SAAS;AAC5C,CAAC;;;AClCD,IAAAA,cAAkB;AAYX,IAAM,8BAA8B,cAAE,OAAO;AAAA,EACnD,UAAU,cAAE,QAAQ;AAAA,EACpB,WAAW,cAAE;AAAA,IACZ,cAAE,OAAO;AAAA,MACR,UAAU,cAAE,QAAQ;AAAA,MACpB,QAAQ,cAAE,MAAM,cAAE,OAAO,CAAC,EAAE,SAAS;AAAA,IACtC,CAAC;AAAA,EACF;AACD,CAAC;AAIM,IAAM,yBAAgD;AAAA,EAC5D,UAAU;AAAA,EACV,WAAW,CAAC;AACb;AAMO,IAAM,6BAA6B,cAAE,OAAO;AAAA,EAClD,SAAS,cAAE,OAAO;AAAA,EAClB,iBAAiB,cACf,OAAO;AAAA,IACP,mBAAmB,cAAE,QAAQ,EAAE,SAAS;AAAA,IACxC,oBAAoB,cAAE,OAAO,EAAE,SAAS;AAAA,IACxC,mBAAmB,cAAE,OAAO,EAAE,SAAS;AAAA,IACvC,qBAAqB,cAAE,QAAQ,EAAE,SAAS;AAAA,IAC1C,iBAAiB,cAAE,OAAO,EAAE,SAAS;AAAA,IACrC,qBAAqB,cAAE,OAAO,EAAE,SAAS;AAAA,EAC1C,CAAC,EACA,SAAS;AAAA,EACX,eAAe,cACb,OAAO;AAAA,IACP,oBAAoB,cAAE,QAAQ,EAAE,SAAS;AAAA,EAC1C,CAAC,EACA,SAAS;AAAA,EACX,WAAW;AACZ,CAAC;;;ACnDD,IAAAC,cAAkB;AAQX,IAAM,gBAAgB,CAAC,iBAAiB,qBAAqB;AAE7D,IAAM,sBAAsB,cAAE,KAAK,aAAa;AAQhD,IAAM,oBAAoB,cAAE,OAAO;AAAA,EACzC,eAAe,cAAE,QAAQ;AAAA,EACzB,qBAAqB,cAAE,QAAQ;AAChC,CAAC;;;ACrBD,IAAAC,eAAkB;;;ACgBX,SAAS,SAAY;AAC3B,SAAO,CACN,SACO;AACT;;;ACpBA,IAAAC,cAAkB;;;ACAlB,IAAAC,cAAkB;AAMX,IAAM,mBAAmB,CAAC,OAAO,UAAU,MAAM;AAEjD,IAAM,yBAAyB,cAAE,KAAK,gBAAgB;AAQtD,IAAM,kBAAkB,CAAC,cAAc,eAAe,aAAa,mBAAmB;AAEtF,IAAM,wBAAwB,cAAE,KAAK,eAAe;AAIpD,IAAM,mBAAmB,CAAC,UAChC,gBAAgB,SAAS,KAAuB;AAM1C,IAAM,kBAAkB,cAAE,OAAO;AAAA,EACvC,WAAW,cAAE,OAAO,EAAE,QAAQ;AAAA,EAC9B,mBAAmB,cAAE,OAAO,EAAE,QAAQ;AAAA,EACtC,eAAe,cAAE,OAAO;AAAA,EACxB,gBAAgB,cAAE,QAAQ,EAAE,SAAS;AAAA,EACrC,qBAAqB,cAAE,QAAQ,EAAE,SAAS;AAAA,EAC1C,qBAAqB,cAAE,QAAQ;AAAA,EAC/B,yBAAyB,cAAE,QAAQ,EAAE,SAAS;AAAA,EAC9C,yBAAyB,cAAE,QAAQ,EAAE,SAAS;AAAA,EAC9C,yBAAyB,cAAE,QAAQ,EAAE,SAAS;AAAA,EAC9C,qBAAqB,cAAE,MAAM,qBAAqB,EAAE,SAAS;AAAA,EAC7D,YAAY,cAAE,OAAO,EAAE,SAAS;AAAA,EAChC,aAAa,cAAE,OAAO,EAAE,SAAS;AAAA,EACjC,kBAAkB,cAAE,OAAO,EAAE,SAAS;AAAA,EACtC,iBAAiB,cAAE,OAAO,EAAE,SAAS;AAAA,EACrC,aAAa,cAAE,OAAO,EAAE,SAAS;AAAA,EACjC,iBAAiB,uBAAuB,SAAS;AAAA,EACjD,wBAAwB,cAAE,OAAO,EAAE,SAAS;AAAA,EAC5C,gBAAgB,cAAE,OAAO,EAAE,SAAS;AAAA,EACpC,gBAAgB,cAAE,MAAM,cAAE,OAAO,CAAC,EAAE,SAAS;AAAA,EAC7C,OAAO,cACL;AAAA,IACA,cAAE,OAAO;AAAA,MACR,eAAe,cAAE,OAAO;AAAA,MACxB,YAAY,cAAE,OAAO,EAAE,SAAS;AAAA,MAChC,aAAa,cAAE,OAAO,EAAE,SAAS;AAAA,MACjC,kBAAkB,cAAE,OAAO,EAAE,SAAS;AAAA,MACtC,iBAAiB,cAAE,OAAO,EAAE,SAAS;AAAA,IACtC,CAAC;AAAA,EACF,EACC,SAAS;AACZ,CAAC;;;ADlDM,IAAM,gBAAgB;AAAA,EAC5B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACD;AAEO,IAAM,sBAAsB,cAAE,KAAK,aAAa;AAQhD,IAAM,8BAA8B,cAAE,OAAO;AAAA,EACnD,IAAI,cAAE,OAAO;AAAA,EACb,MAAM,cAAE,OAAO;AAAA,EACf,aAAa,oBAAoB,SAAS;AAC3C,CAAC;AAQD,IAAM,6BAA6B,cAAE,OAAO;AAAA,EAC3C,kBAAkB,cAAE,QAAQ,EAAE,SAAS;AAAA,EACvC,aAAa,cAAE,QAAQ,EAAE,SAAS;AAAA,EAClC,qBAAqB,cAAE,OAAO,EAAE,SAAS;AAAA,EACzC,kBAAkB,cAAE,OAAO,EAAE,QAAQ;AAAA,EACrC,kBAAkB,cAAE,OAAO,EAAE,SAAS;AAAA;AAAA,EAGtC,uBAAuB,cAAE,QAAQ,EAAE,SAAS;AAAA,EAC5C,iBAAiB,uBAAuB,SAAS;AAAA,EACjD,gBAAgB,cAAE,OAAO,EAAE,SAAS;AAAA,EACpC,wBAAwB,cAAE,OAAO,EAAE,SAAS;AAC7C,CAAC;AAGD,IAAM,gCAAgC,2BAA2B,OAAO;AAAA,EACvE,YAAY,cAAE,OAAO,EAAE,SAAS;AACjC,CAAC;AAED,IAAM,kBAAkB,8BAA8B,OAAO;AAAA,EAC5D,QAAQ,cAAE,OAAO,EAAE,SAAS;AAAA;AAAA,EAC5B,iBAAiB,cAAE,OAAO,EAAE,SAAS;AAAA,EACrC,kBAAkB,cAAE,OAAO,EAAE,SAAS;AAAA,EACtC,uBAAuB,cAAE,QAAQ,EAAE,SAAS;AAC7C,CAAC;AAED,IAAM,cAAc,2BAA2B,OAAO;AAAA,EACrD,cAAc,cAAE,OAAO,EAAE,SAAS;AAAA,EAClC,aAAa,cAAE,OAAO,EAAE,SAAS;AAClC,CAAC;AAED,IAAM,mBAAmB,2BAA2B,OAAO;AAAA,EAC1D,kBAAkB,cAAE,OAAO,EAAE,SAAS;AAAA,EACtC,mBAAmB,cAAE,OAAO,EAAE,SAAS;AAAA,EACvC,mBAAmB,cAAE,OAAO,EAAE,SAAS;AAAA,EACvC,4BAA4B,cAAE,OAAO,EAAE,SAAS;AAAA,EAChD,iCAAiC,cAAE,QAAQ,EAAE,SAAS;AACvD,CAAC;AAED,IAAM,gBAAgB,8BAA8B,OAAO;AAAA,EAC1D,cAAc,cAAE,OAAO,EAAE,SAAS;AAAA,EAClC,cAAc,cAAE,OAAO,EAAE,SAAS;AAAA,EAClC,iBAAiB,cAAE,OAAO,EAAE,SAAS;AAAA,EACrC,WAAW,cAAE,OAAO,EAAE,SAAS;AAAA,EAC/B,4BAA4B,cAAE,QAAQ,EAAE,SAAS;AAAA,EACjD,mBAAmB,cAAE,QAAQ,EAAE,SAAS;AAAA,EACxC,YAAY,cAAE,OAAO,EAAE,SAAS;AAAA,EAChC,eAAe,cAAE,QAAQ,EAAE,SAAS;AAAA,EACpC,cAAc,cAAE,OAAO,EAAE,SAAS;AAAA,EAClC,2BAA2B,cAAE,QAAQ,EAAE,SAAS;AAAA,EAChD,oBAAoB,cAAE,OAAO,EAAE,SAAS;AACzC,CAAC;AAED,IAAM,eAAe,8BAA8B,OAAO;AAAA,EACzD,eAAe,cAAE,OAAO,EAAE,SAAS;AAAA,EACnC,uBAAuB,cAAE,OAAO,EAAE,SAAS;AAAA,EAC3C,iBAAiB,cAAE,OAAO,EAAE,SAAS;AAAA,EACrC,cAAc,cAAE,OAAO,EAAE,SAAS;AACnC,CAAC;AAED,IAAM,eAAe,2BAA2B,OAAO;AAAA,EACtD,eAAe,cAAE,OAAO,EAAE,SAAS;AAAA,EACnC,cAAc,cAAE,OAAO,EAAE,SAAS;AAAA,EAClC,oBAAoB,cAAE,QAAQ,EAAE,SAAS;AAAA,EACzC,uBAAuB,cAAE,QAAQ,EAAE,SAAS;AAAA,EAC5C,eAAe,cAAE,OAAO,EAAE,SAAS;AAAA,EACnC,uBAAuB,gBAAgB,QAAQ;AAAA,EAC/C,gBAAgB,cAAE,QAAQ,EAAE,SAAS;AAAA,EACrC,iBAAiB,cAAE,OAAO,EAAE,SAAS;AAAA,EACrC,wBAAwB,cAAE,QAAQ,EAAE,SAAS;AAAA,EAC7C,kBAAkB,cAAE,OAAO,EAAE,SAAS;AAAA;AAAA,EACtC,eAAe,cAAE,OAAO,cAAE,OAAO,GAAG,cAAE,OAAO,CAAC,EAAE,SAAS;AAC1D,CAAC;AAED,IAAM,eAAe,2BAA2B,OAAO;AAAA,EACtD,eAAe,cAAE,OAAO,EAAE,SAAS;AAAA,EACnC,eAAe,cAAE,OAAO,EAAE,SAAS;AACpC,CAAC;AAED,IAAM,iBAAiB,2BAA2B,OAAO;AAAA,EACxD,uBAAuB,cACrB,OAAO;AAAA,IACP,QAAQ,cAAE,OAAO,EAAE,SAAS;AAAA,IAC5B,QAAQ,cAAE,OAAO,EAAE,SAAS;AAAA,IAC5B,SAAS,cAAE,OAAO,EAAE,SAAS;AAAA,IAC7B,IAAI,cAAE,OAAO,EAAE,SAAS;AAAA,EACzB,CAAC,EACA,SAAS;AACZ,CAAC;AAED,IAAM,iBAAiB,2BAA2B,OAAO;AAAA,EACxD,iBAAiB,cAAE,OAAO,EAAE,SAAS;AAAA,EACrC,iBAAiB,cAAE,OAAO,EAAE,SAAS;AAAA,EACrC,sBAAsB,cAAE,OAAO,EAAE,SAAS;AAAA,EAC1C,oCAAoC,cAAE,QAAQ,EAAE,SAAS;AAC1D,CAAC;AAED,IAAM,eAAe,8BAA8B,OAAO;AAAA,EACzD,cAAc,cAAE,OAAO,EAAE,SAAS;AAAA,EAClC,qBAAqB,cAAE,OAAO,EAAE,SAAS;AAC1C,CAAC;AAED,IAAM,qBAAqB,8BAA8B,OAAO;AAAA,EAC/D,oBAAoB,cAAE,OAAO,EAAE,SAAS;AAAA,EACxC,qBAAqB,cAAE,OAAO,EAAE,SAAS;AAC1C,CAAC;AAED,IAAM,gBAAgB,8BAA8B,OAAO;AAAA,EAC1D,eAAe,cAAE,OAAO,EAAE,SAAS;AAAA,EACnC,qBAAqB,cAAE,OAAO,EAAE,SAAS;AAC1C,CAAC;AAED,IAAM,iBAAiB,8BAA8B,OAAO;AAAA,EAC3D,iBAAiB,cAAE,OAAO,EAAE,SAAS;AAAA,EACrC,gBAAgB,cAAE,OAAO,EAAE,SAAS;AACrC,CAAC;AAED,IAAM,gBAAgB,2BAA2B,OAAO;AAAA,EACvD,eAAe,cAAE,OAAO,EAAE,SAAS;AAAA,EACnC,gBAAgB,cAAE,OAAO,EAAE,SAAS;AACrC,CAAC;AAED,IAAM,iBAAiB,2BAA2B,OAAO;AAAA,EACxD,gBAAgB,cAAE,OAAO,EAAE,SAAS;AAAA,EACpC,iBAAiB,cAAE,OAAO,EAAE,SAAS;AACtC,CAAC;AAED,IAAM,mBAAmB;AAEzB,IAAM,eAAe,2BAA2B,OAAO;AAAA,EACtD,QAAQ,cAAE,QAAQ,EAAE,SAAS;AAC9B,CAAC;AAED,IAAM,YAAY,8BAA8B,OAAO;AAAA,EACtD,WAAW,cAAE,OAAO,EAAE,SAAS;AAChC,CAAC;AAED,IAAM,aAAa,8BAA8B,OAAO;AAAA,EACvD,YAAY,cAAE,OAAO,EAAE,SAAS;AACjC,CAAC;AAED,IAAM,eAAe,8BAA8B,OAAO;AAAA,EACzD,cAAc,cAAE,OAAO,EAAE,SAAS;AACnC,CAAC;AAED,IAAM,gBAAgB,2BAA2B,OAAO;AAAA,EACvD,gBAAgB,cAAE,OAAO,EAAE,SAAS;AAAA,EACpC,eAAe,cAAE,OAAO,EAAE,SAAS;AAAA,EACnC,gBAAgB,cAAE,OAAO,EAAE,SAAS;AACrC,CAAC;AAED,IAAM,gBAAgB,cAAE,OAAO;AAAA,EAC9B,aAAa,cAAE,UAAU;AAC1B,CAAC;AAEM,IAAM,sCAAsC,cAAE,mBAAmB,eAAe;AAAA,EACtF,gBAAgB,MAAM,cAAE,OAAO,EAAE,aAAa,cAAE,QAAQ,WAAW,EAAE,CAAC,CAAC;AAAA,EACvE,YAAY,MAAM,cAAE,OAAO,EAAE,aAAa,cAAE,QAAQ,OAAO,EAAE,CAAC,CAAC;AAAA,EAC/D,iBAAiB,MAAM,cAAE,OAAO,EAAE,aAAa,cAAE,QAAQ,YAAY,EAAE,CAAC,CAAC;AAAA,EACzE,cAAc,MAAM,cAAE,OAAO,EAAE,aAAa,cAAE,QAAQ,SAAS,EAAE,CAAC,CAAC;AAAA,EACnE,aAAa,MAAM,cAAE,OAAO,EAAE,aAAa,cAAE,QAAQ,QAAQ,EAAE,CAAC,CAAC;AAAA,EACjE,aAAa,MAAM,cAAE,OAAO,EAAE,aAAa,cAAE,QAAQ,QAAQ,EAAE,CAAC,CAAC;AAAA,EACjE,aAAa,MAAM,cAAE,OAAO,EAAE,aAAa,cAAE,QAAQ,QAAQ,EAAE,CAAC,CAAC;AAAA,EACjE,eAAe,MAAM,cAAE,OAAO,EAAE,aAAa,cAAE,QAAQ,WAAW,EAAE,CAAC,CAAC;AAAA,EACtE,eAAe,MAAM,cAAE,OAAO,EAAE,aAAa,cAAE,QAAQ,UAAU,EAAE,CAAC,CAAC;AAAA,EACrE,aAAa,MAAM,cAAE,OAAO,EAAE,aAAa,cAAE,QAAQ,QAAQ,EAAE,CAAC,CAAC;AAAA,EACjE,mBAAmB,MAAM,cAAE,OAAO,EAAE,aAAa,cAAE,QAAQ,eAAe,EAAE,CAAC,CAAC;AAAA,EAC9E,cAAc,MAAM,cAAE,OAAO,EAAE,aAAa,cAAE,QAAQ,SAAS,EAAE,CAAC,CAAC;AAAA,EACnE,eAAe,MAAM,cAAE,OAAO,EAAE,aAAa,cAAE,QAAQ,UAAU,EAAE,CAAC,CAAC;AAAA,EACrE,cAAc,MAAM,cAAE,OAAO,EAAE,aAAa,cAAE,QAAQ,SAAS,EAAE,CAAC,CAAC;AAAA,EACnE,eAAe,MAAM,cAAE,OAAO,EAAE,aAAa,cAAE,QAAQ,UAAU,EAAE,CAAC,CAAC;AAAA,EACrE,iBAAiB,MAAM,cAAE,OAAO,EAAE,aAAa,cAAE,QAAQ,aAAa,EAAE,CAAC,CAAC;AAAA,EAC1E,aAAa,MAAM,cAAE,OAAO,EAAE,aAAa,cAAE,QAAQ,SAAS,EAAE,CAAC,CAAC;AAAA,EAClE,UAAU,MAAM,cAAE,OAAO,EAAE,aAAa,cAAE,QAAQ,KAAK,EAAE,CAAC,CAAC;AAAA,EAC3D,WAAW,MAAM,cAAE,OAAO,EAAE,aAAa,cAAE,QAAQ,MAAM,EAAE,CAAC,CAAC;AAAA,EAC7D,aAAa,MAAM,cAAE,OAAO,EAAE,aAAa,cAAE,QAAQ,QAAQ,EAAE,CAAC,CAAC;AAAA,EACjE,cAAc,MAAM,cAAE,OAAO,EAAE,aAAa,cAAE,QAAQ,SAAS,EAAE,CAAC,CAAC;AAAA,EACnE;AACD,CAAC;AAEM,IAAM,yBAAyB,cAAE,OAAO;AAAA,EAC9C,aAAa,oBAAoB,SAAS;AAAA,EAC1C,GAAG,gBAAgB;AAAA,EACnB,GAAG,YAAY;AAAA,EACf,GAAG,iBAAiB;AAAA,EACpB,GAAG,cAAc;AAAA,EACjB,GAAG,aAAa;AAAA,EAChB,GAAG,aAAa;AAAA,EAChB,GAAG,aAAa;AAAA,EAChB,GAAG,eAAe;AAAA,EAClB,GAAG,eAAe;AAAA,EAClB,GAAG,aAAa;AAAA,EAChB,GAAG,mBAAmB;AAAA,EACtB,GAAG,cAAc;AAAA,EACjB,GAAG,eAAe;AAAA,EAClB,GAAG,cAAc;AAAA,EACjB,GAAG,eAAe;AAAA,EAClB,GAAG,iBAAiB;AAAA,EACpB,GAAG,aAAa;AAAA,EAChB,GAAG,UAAU;AAAA,EACb,GAAG,WAAW;AAAA,EACd,GAAG,aAAa;AAAA,EAChB,GAAG,cAAc;AAAA,EACjB,GAAG,4BAA4B;AAChC,CAAC;AAIM,IAAM,yBAAyB,OAAyB,EAAE;AAAA,EAChE;AAAA;AAAA,EAEA;AAAA,EACA;AAAA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AAAA,EAEA;AAAA,EACA;AAAA;AAAA,EAEA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AAAA,EAEA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AAAA,EAEA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AAAA,EAEA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AAAA,EACA;AAAA;AAAA,EAEA;AAAA,EACA;AAAA;AAAA,EAEA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AAAA,EAEA;AAAA,EACA;AAAA;AAAA,EAEA;AAAA,EACA;AAAA;AAAA,EAEA;AAAA,EACA;AAAA;AAAA,EAEA;AAAA,EACA;AAAA;AAAA,EAEA;AAAA,EACA;AAAA;AAAA,EAEA;AAAA,EACA;AAAA;AAAA,EAEA;AAAA,EACA;AAAA;AAAA,EAEA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AAAA,EAEA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA,EACA;AAAA,EACA;AACD,CAAC;;;AEzWD,IAAAC,cAAkB;AAMX,IAAM,oBAAoB,cAAE,OAAO;AAAA,EACzC,IAAI,cAAE,OAAO;AAAA,EACb,QAAQ,cAAE,OAAO;AAAA,EACjB,IAAI,cAAE,OAAO;AAAA,EACb,MAAM,cAAE,OAAO;AAAA,EACf,UAAU,cAAE,OAAO;AAAA,EACnB,WAAW,cAAE,OAAO;AAAA,EACpB,aAAa,cAAE,OAAO,EAAE,SAAS;AAAA,EACjC,YAAY,cAAE,OAAO,EAAE,SAAS;AAAA,EAChC,WAAW,cAAE,OAAO;AAAA,EACpB,MAAM,cAAE,OAAO,EAAE,SAAS;AAAA,EAC1B,WAAW,cAAE,OAAO,EAAE,SAAS;AAAA,EAC/B,OAAO,cAAE,OAAO,EAAE,SAAS;AAAA,EAC3B,QAAQ,cAAE,QAAQ,EAAE,SAAS;AAC9B,CAAC;;;ACpBD,IAAAC,cAAkB;;;ACAlB,IAAAC,cAAkB;AAMX,IAAM,YAAY;AAAA,EACxB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACD;AAEO,IAAM,iBAAiB,cAAE,KAAK,SAAS;AAQvC,IAAM,YAAY;AAAA,EACxB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACD;AAEO,IAAM,iBAAiB,cAAE,KAAK,SAAS;AAQvC,IAAM,2BAA2B,cAAE,OAAO;AAAA,EAChD,MAAM,cAAE,OAAO,EAAE,SAAS;AAAA,EAC1B,MAAM,cAAE,OAAO,EAAE,SAAS;AAC3B,CAAC;AAQM,IAAM,wBAAwB,cAAE,OAAO;AAAA,EAC7C,MAAM,cAAE,OAAO;AAAA,EACf,mBAAmB,cAAE,OAAO;AAAA,EAC5B,kBAAkB,cAAE,OAAO;AAAA,EAC3B,SAAS,cAAE,OAAO;AACnB,CAAC;AAQM,IAAM,qBAAqB,cAAE,OAAO;AAAA,EAC1C,IAAI,cAAE,OAAO;AAAA,EACb,MAAM,cAAE,MAAM,CAAC,cAAE,QAAQ,KAAK,GAAG,cAAE,QAAQ,KAAK,CAAC,CAAC;AAAA,EAClD,KAAK,eAAe,SAAS;AAAA,EAC7B,KAAK,eAAe,SAAS;AAAA,EAC7B,MAAM,cAAE,OAAO,EAAE,SAAS;AAAA,EAC1B,QAAQ,cAAE,MAAM,cAAE,OAAO,CAAC,EAAE,SAAS;AAAA,EACrC,SAAS,cAAE,QAAQ,EAAE,SAAS;AAAA,EAC9B,WAAW,cAAE,OAAO,EAAE,SAAS;AAAA,EAC/B,0BAA0B,cAAE,OAAO,EAAE,SAAS;AAAA,EAC9C,YAAY,cAAE,OAAO,cAAE,OAAO,GAAG,cAAE,QAAQ,CAAC,EAAE,SAAS;AAAA,EACvD,gBAAgB,yBAAyB,SAAS;AAAA,EAClD,iBAAiB,sBAAsB,SAAS;AACjD,CAAC;AAQM,IAAM,mBAAmB,cAAE,OAAO;AAAA,EACxC,eAAe,cAAE,OAAO;AAAA,EACxB,gBAAgB,cAAE,OAAO;AAAA,EACzB,kBAAkB,cAAE,OAAO,EAAE,SAAS;AAAA,EACtC,iBAAiB,cAAE,OAAO,EAAE,SAAS;AAAA,EACrC,WAAW,cAAE,OAAO;AAAA,EACpB,eAAe,cAAE,OAAO;AACzB,CAAC;;;AD3GM,IAAM,oBAAoB,CAAC,SAAS,WAAW,UAAU;AAEzD,IAAM,0BAA0B,cAAE,KAAK,iBAAiB;AAQxD,IAAK,qBAAL,kBAAKC,wBAAL;AACN,EAAAA,oBAAA,kBAAe;AACf,EAAAA,oBAAA,oBAAiB;AACjB,EAAAA,oBAAA,oBAAiB;AACjB,EAAAA,oBAAA,kBAAe;AACf,EAAAA,oBAAA,+BAA4B;AAC5B,EAAAA,oBAAA,oBAAiB;AACjB,EAAAA,oBAAA,iBAAc;AACd,EAAAA,oBAAA,eAAY;AAEZ,EAAAA,oBAAA,wBAAqB;AACrB,EAAAA,oBAAA,yBAAsB;AACtB,EAAAA,oBAAA,uBAAoB;AAEpB,EAAAA,oBAAA,uBAAoB;AACpB,EAAAA,oBAAA,+BAA4B;AAE5B,EAAAA,oBAAA,sBAAmB;AACnB,EAAAA,oBAAA,qBAAkB;AAElB,EAAAA,oBAAA,0BAAuB;AAEvB,EAAAA,oBAAA,8BAA2B;AAE3B,EAAAA,oBAAA,6BAA0B;AAC1B,EAAAA,oBAAA,4BAAyB;AACzB,EAAAA,oBAAA,6BAA0B;AAC1B,EAAAA,oBAAA,+BAA4B;AA3BjB,SAAAA;AAAA,GAAA;AAkCL,IAAM,sBAAsB,cAAE,OAAO;AAAA,EAC3C,SAAS,cAAE,OAAO;AAAA,EAClB,YAAY,cAAE,OAAO;AAAA,EACrB,eAAe,cAAE,OAAO;AAAA,EACxB,UAAU,cAAE,OAAO;AAAA,EACnB,YAAY,cAAE,OAAO;AAAA,EACrB,UAAU,cAAE,OAAO;AAAA,EACnB,MAAM,cAAE,OAAO;AAChB,CAAC;AAEM,IAAM,uBAAuB,cAAE,OAAO;AAAA,EAC5C,QAAQ,cAAE,OAAO,EAAE,SAAS;AAAA,EAC5B,aAAa,cAAE,KAAK,aAAa,EAAE,SAAS;AAAA,EAC5C,SAAS,cAAE,OAAO,EAAE,SAAS;AAAA,EAC7B,cAAc,cAAE,OAAO,EAAE,SAAS;AAAA,EAClC,WAAW,cAAE,QAAQ,EAAE,SAAS;AACjC,CAAC;AAEM,IAAM,4BAA4B,cAAE,OAAO;AAAA,EACjD,GAAG,oBAAoB;AAAA,EACvB,GAAG,qBAAqB;AACzB,CAAC;AAkBM,IAAM,8BAA8B,cAAE,mBAAmB,QAAQ;AAAA,EACvE,cAAE,OAAO;AAAA,IACR,MAAM,cAAE,KAAK;AAAA,MACZ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACD,CAAC;AAAA,IACD,YAAY;AAAA,EACb,CAAC;AAAA,EACD,cAAE,OAAO;AAAA,IACR,MAAM,cAAE,QAAQ,iCAA+B;AAAA,IAC/C,YAAY,cAAE,OAAO;AAAA,MACpB,GAAG,0BAA0B;AAAA,MAC7B,QAAQ,cAAE,OAAO;AAAA,MACjB,SAAS;AAAA,IACV,CAAC;AAAA,EACF,CAAC;AAAA,EACD,cAAE,OAAO;AAAA,IACR,MAAM,cAAE,QAAQ,qCAAiC;AAAA,IACjD,YAAY,cAAE,OAAO;AAAA,MACpB,GAAG,0BAA0B;AAAA,MAC7B,aAAa,cAAE,OAAO;AAAA,MACtB,cAAc,cAAE,OAAO;AAAA,MACvB,iBAAiB,cAAE,OAAO,EAAE,SAAS;AAAA,MACrC,kBAAkB,cAAE,OAAO,EAAE,SAAS;AAAA,MACtC,MAAM,cAAE,OAAO,EAAE,SAAS;AAAA,IAC3B,CAAC;AAAA,EACF,CAAC;AACF,CAAC;;;AExID,IAAAC,eAAkB;;;ACAlB,IAAAC,cAAkB;AAMX,IAAM,aAAa,CAAC,QAAQ,QAAQ,WAAW,WAAW,OAAO,OAAO;AAExE,IAAM,mBAAmB,cAAE,KAAK,UAAU;AAQ1C,IAAM,YAAY;AAAA,EACxB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACD;AAEO,IAAM,kBAAkB,cAAE,KAAK,SAAS;AAQxC,IAAM,kBAAkB,cAAE;AAAA,EAChC;AAAA,EACA,cAAE,OAAO;AAAA,IACR,UAAU,cAAE,OAAO;AAAA,IACnB,UAAU,cAAE,OAAO;AAAA,EACpB,CAAC;AACF;;;AD3CO,IAAM,qBAAqB,eAAE,OAAO;AAAA,EAC1C,WAAW,eACT,OAAO,EACP,SAAS,EACT;AAAA,IACA,CAAC,YAAY;AACZ,UAAI,CAAC,SAAS;AACb,eAAO;AAAA,MACR;AAEA,UAAI;AACH,YAAI,OAAO,OAAO;AAClB,eAAO;AAAA,MACR,QAAQ;AACP,eAAO;AAAA,MACR;AAAA,IACD;AAAA,IACA,EAAE,SAAS,qCAAqC;AAAA,EACjD;AAAA,EACD,aAAa,eAAE,OAAO,EAAE,SAAS;AAClC,CAAC;AAQM,IAAM,mBAAmB,eAAE,MAAM,CAAC,kBAAkB,eAAE,MAAM,CAAC,kBAAkB,kBAAkB,CAAC,CAAC,CAAC;AAQ3G,IAAM,wBAAwB,eAAE,MAAM,gBAAgB,EAAE;AAAA,EACvD,CAAC,WAAW;AACX,UAAM,OAAO,oBAAI,IAAI;AAErB,WAAO,OAAO,MAAM,CAAC,UAAU;AAE9B,YAAM,YAAY,MAAM,QAAQ,KAAK,IAAI,MAAM,CAAC,IAAI;AAEpD,UAAI,KAAK,IAAI,SAAS,GAAG;AACxB,eAAO;AAAA,MACR;AAEA,WAAK,IAAI,SAAS;AAClB,aAAO;AAAA,IACR,CAAC;AAAA,EACF;AAAA,EACA,EAAE,SAAS,mCAAmC;AAC/C;AAEO,IAAM,mBAAmB,eAAE,OAAO;AAAA,EACxC,MAAM,eAAE,OAAO,EAAE,MAAM,mBAAmB,mDAAmD;AAAA,EAC7F,MAAM,eAAE,OAAO,EAAE,IAAI,GAAG,kBAAkB;AAAA,EAC1C,gBAAgB,eAAE,OAAO,EAAE,IAAI,GAAG,6BAA6B;AAAA,EAC/D,WAAW,eAAE,OAAO,EAAE,SAAS;AAAA,EAC/B,oBAAoB,eAAE,OAAO,EAAE,SAAS;AAAA,EACxC,QAAQ;AAAA,EACR,QAAQ,eAAE,KAAK,CAAC,UAAU,SAAS,CAAC,EAAE,SAAS;AAChD,CAAC;AAQM,IAAM,4BAA4B,eAAE,OAAO;AAAA,EACjD,aAAa,eAAE,MAAM,gBAAgB,EAAE;AAAA,IACtC,CAAC,UAAU;AACV,YAAM,QAAQ,oBAAI,IAAI;AAEtB,aAAO,MAAM,MAAM,CAAC,SAAS;AAC5B,YAAI,MAAM,IAAI,KAAK,IAAI,GAAG;AACzB,iBAAO;AAAA,QACR;AAEA,cAAM,IAAI,KAAK,IAAI;AACnB,eAAO;AAAA,MACR,CAAC;AAAA,IACF;AAAA,IACA;AAAA,MACC,SAAS;AAAA,IACV;AAAA,EACD;AACD,CAAC;AAQM,IAAM,wBAAwB,eAAE,OAAO;AAAA,EAC7C,gBAAgB,eAAE,OAAO,EAAE,SAAS;AAAA,EACpC,WAAW,eAAE,OAAO,EAAE,SAAS;AAAA,EAC/B,oBAAoB,eAAE,OAAO,EAAE,SAAS;AACzC,CAAC;AAQM,IAAM,0BAA0B,eAAE,OAAO,eAAE,OAAO,GAAG,sBAAsB,SAAS,CAAC;AAQrF,IAAM,6BAA6B,eAAE,OAAO,eAAE,OAAO,GAAG,eAAE,OAAO,EAAE,SAAS,CAAC;;;AE7HpF,IAAAC,eAAkB;AAMX,IAAM,gBAAgB,CAAC,eAAe,WAAW,eAAe,gBAAgB,SAAS;AAUzF,IAAM,oBAAoB,CAAC,wBAAwB,sBAAsB,wBAAwB;AAYjG,IAAM,aAAa;AAAA,EACzB;AAAA,EAEA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EAEA;AAAA,EAEA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EAEA;AAAA,EAEA;AAAA,EAEA;AAAA,EACA;AACD;AAQO,IAAM,YAAY;AAAA,EACxB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACD;AAEO,IAAM,kBAAkB,eAAE,KAAK,SAAS;AAIxC,IAAM,aAAa,CAAC,UAAqC,UAAU,SAAS,KAAiB;;;AT/D7F,IAAM,uBAAuB,eAAE,OAAO;AAAA,EAC5C,sBAAsB,eAAE,OAAO,EAAE,SAAS;AAAA,EAC1C,mBAAmB,eAAE,MAAM,2BAA2B,EAAE,SAAS;AAAA,EACjE,kBAAkB,eAAE,OAAO,eAAE,OAAO,GAAG,eAAE,QAAQ,CAAC,EAAE,SAAS;AAAA,EAE7D,yBAAyB,eAAE,OAAO,EAAE,SAAS;AAAA,EAC7C,oBAAoB,eAAE,OAAO,EAAE,SAAS;AAAA,EACxC,aAAa,eAAE,MAAM,iBAAiB,EAAE,SAAS;AAAA,EAEjD,uBAAuB,eAAE,OAAO,EAAE,SAAS;AAAA,EAC3C,wBAAwB,eAAE,OAAO,EAAE,SAAS;AAAA,EAE5C,qBAAqB,eAAE,QAAQ,EAAE,SAAS;AAAA,EAC1C,qBAAqB,eAAE,QAAQ,EAAE,SAAS;AAAA,EAC1C,qCAAqC,eAAE,QAAQ,EAAE,SAAS;AAAA,EAC1D,kBAAkB,eAAE,QAAQ,EAAE,SAAS;AAAA,EACvC,kCAAkC,eAAE,QAAQ,EAAE,SAAS;AAAA,EACvD,cAAc,eAAE,OAAO,EAAE,SAAS;AAAA,EAClC,oBAAoB,eAAE,QAAQ,EAAE,SAAS;AAAA,EACzC,uBAAuB,eAAE,QAAQ,EAAE,SAAS;AAAA,EAC5C,qBAAqB,eAAE,OAAO,EAAE,SAAS;AAAA,EACzC,gBAAgB,eAAE,QAAQ,EAAE,SAAS;AAAA,EACrC,uBAAuB,eAAE,QAAQ,EAAE,SAAS;AAAA,EAC5C,qBAAqB,eAAE,QAAQ,EAAE,SAAS;AAAA,EAC1C,oBAAoB,eAAE,QAAQ,EAAE,SAAS;AAAA,EACzC,iBAAiB,eAAE,MAAM,eAAE,OAAO,CAAC,EAAE,SAAS;AAAA,EAC9C,oBAAoB,eAAE,OAAO,EAAE,QAAQ;AAAA,EACvC,qBAAqB,eAAE,QAAQ,EAAE,SAAS;AAAA,EAC1C,4BAA4B,eAAE,OAAO,EAAE,SAAS;AAAA,EAC/C,wBAAwB,eAAE,OAAO,EAAE,SAAS;AAAA,EAE7C,oBAAoB,eAAE,QAAQ,EAAE,SAAS;AAAA,EACzC,qBAAqB,eAAE,OAAO,EAAE,SAAS;AAAA,EACzC,mBAAmB,eAAE,OAAO,EAAE,SAAS;AAAA,EACvC,sBAAsB,eAAE,QAAQ,EAAE,SAAS;AAAA,EAC3C,mBAAmB,eAAE,OAAO,EAAE,SAAS;AAAA,EACvC,qBAAqB,eAAE,OAAO,EAAE,SAAS;AAAA,EAEzC,mBAAmB,eAAE,QAAQ,EAAE,SAAS;AAAA,EAExC,YAAY,eAAE,QAAQ,EAAE,SAAS;AAAA,EACjC,UAAU,eAAE,OAAO,EAAE,SAAS;AAAA,EAC9B,cAAc,eAAE,QAAQ,EAAE,SAAS;AAAA,EACnC,aAAa,eAAE,OAAO,EAAE,SAAS;AAAA,EAEjC,oBAAoB,eAAE,OAAO,EAAE,SAAS;AAAA,EACxC,mBAAmB,eAAE,OAAO,EAAE,SAAS;AAAA,EACvC,qBAAqB,eAAE,QAAQ,EAAE,SAAS;AAAA,EAC1C,iBAAiB,eAAE,OAAO,EAAE,SAAS;AAAA,EAErC,yBAAyB,eAAE,OAAO,EAAE,SAAS;AAAA,EAC7C,iCAAiC,eAAE,OAAO,EAAE,SAAS;AAAA,EACrD,kCAAkC,eAAE,QAAQ,EAAE,SAAS;AAAA,EACvD,sBAAsB,eAAE,OAAO,EAAE,SAAS;AAAA,EAC1C,2BAA2B,eAAE,QAAQ,EAAE,SAAS;AAAA,EAChD,yBAAyB,eAAE,QAAQ,EAAE,SAAS;AAAA,EAC9C,iBAAiB,eAAE,QAAQ,EAAE,SAAS;AAAA,EACtC,iBAAiB,eAAE,QAAQ,EAAE,SAAS;AAAA,EACtC,iBAAiB,eAAE,QAAQ,EAAE,SAAS;AAAA,EACtC,6BAA6B,eAAE,QAAQ,EAAE,SAAS;AAAA,EAElD,kBAAkB,eAAE,OAAO,EAAE,SAAS;AAAA,EACtC,aAAa,eAAE,QAAQ,EAAE,SAAS;AAAA,EAClC,qBAAqB,eAAE,OAAO,EAAE,SAAS;AAAA,EACzC,aAAa,kBAAkB,SAAS;AAAA,EAExC,qBAAqB,0BAA0B,SAAS;AAAA,EACxD,qBAAqB,0BAA0B,SAAS;AAAA,EAExD,UAAU,gBAAgB,SAAS;AAAA,EAEnC,kBAAkB,wBAAwB,SAAS;AAAA,EAEnD,YAAY,eAAE,QAAQ,EAAE,SAAS;AAAA,EACjC,yBAAyB,eAAE,QAAQ,EAAE,SAAS;AAAA,EAE9C,MAAM,eAAE,OAAO,EAAE,SAAS;AAAA,EAC1B,gBAAgB,eAAE,OAAO,eAAE,OAAO,GAAG,eAAE,OAAO,CAAC,EAAE,SAAS;AAAA,EAC1D,aAAa,eAAE,MAAM,gBAAgB,EAAE,SAAS;AAAA,EAChD,mBAAmB,wBAAwB,SAAS;AAAA,EACpD,sBAAsB,2BAA2B,SAAS;AAAA,EAC1D,wBAAwB,eAAE,OAAO,EAAE,SAAS;AAAA,EAC5C,yBAAyB,eAAE,QAAQ,EAAE,SAAS;AAC/C,CAAC;AAIM,IAAM,uBAAuB,OAAuB,EAAE;AAAA,EAC5D;AAAA,EACA;AAAA,EACA;AAAA,EAEA;AAAA,EACA;AAAA,EACA;AAAA,EAEA;AAAA,EACA;AAAA,EAEA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EAEA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EAEA;AAAA,EAEA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EAEA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EAEA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EAEA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EAEA;AAAA,EACA;AAAA,EAEA;AAAA,EAEA;AAAA,EACA;AAAA,EACA;AAAA,EAEA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACD,CAAC;AAMM,IAAM,wBAAwB,uBAAuB,MAAM,oBAAoB;AA+B/E,IAAM,oBAAoB,OAAoB,EAAE;AAAA,EACtD;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACD,CAAC;AAEM,IAAM,mBAAmB,CAAC,QAChC,kBAAkB,SAAS,GAAwB;AAQ7C,IAAM,oBAAoB,CAAC,GAAG,sBAAsB,GAAG,sBAAsB,EAAE;AAAA,EACrF,CAAC,QAA+B,CAAC,kBAAkB,SAAS,GAAwB;AACrF;AAEO,IAAM,mBAAmB,CAAC,QAChC,kBAAkB,SAAS,GAAwB;;;AUvQpD,IAAAC,eAAkB;AAUX,IAAK,mBAAL,kBAAKC,sBAAL;AACN,EAAAA,kBAAA,aAAU;AACV,EAAAA,kBAAA,iBAAc;AACd,EAAAA,kBAAA,iBAAc;AACd,EAAAA,kBAAA,sBAAmB;AACnB,EAAAA,kBAAA,gBAAa;AACb,EAAAA,kBAAA,kBAAe;AACf,EAAAA,kBAAA,sBAAmB;AACnB,EAAAA,kBAAA,iBAAc;AACd,EAAAA,kBAAA,iBAAc;AACd,EAAAA,kBAAA,mBAAgB;AAChB,EAAAA,kBAAA,2BAAwB;AACxB,EAAAA,kBAAA,oBAAiB;AAZN,SAAAA;AAAA,GAAA;AAeL,IAAM,sBAAsB,eAAE,OAAO;AAAA,EAC3C,CAAC,uBAAwB,GAAG,eAAE,MAAM;AAAA,IACnC,eAAE,OAAO;AAAA,MACR,QAAQ,eAAE,OAAO;AAAA,MACjB,QAAQ,eAAE,MAAM,CAAC,eAAE,QAAQ,SAAS,GAAG,eAAE,QAAQ,SAAS,CAAC,CAAC;AAAA,MAC5D,SAAS;AAAA,IACV,CAAC;AAAA,EACF,CAAC;AAAA,EACD,CAAC,+BAA4B,GAAG,eAAE,MAAM,CAAC,eAAE,OAAO,CAAC,CAAC;AAAA,EACpD,CAAC,+BAA4B,GAAG,eAAE,MAAM,CAAC,eAAE,OAAO,CAAC,CAAC;AAAA,EACpD,CAAC,yCAAiC,GAAG,eAAE,MAAM,CAAC,eAAE,OAAO,GAAG,eAAE,OAAO,CAAC,CAAC;AAAA,EACrE,CAAC,6BAA2B,GAAG,eAAE,MAAM,CAAC,eAAE,OAAO,CAAC,CAAC;AAAA,EACnD,CAAC,iCAA6B,GAAG,eAAE,MAAM,CAAC,eAAE,OAAO,CAAC,CAAC;AAAA,EACrD,CAAC,yCAAiC,GAAG,eAAE,MAAM,CAAC,eAAE,OAAO,CAAC,CAAC;AAAA,EACzD,CAAC,+BAA4B,GAAG,eAAE,MAAM,CAAC,eAAE,OAAO,CAAC,CAAC;AAAA,EACpD,CAAC,+BAA4B,GAAG,eAAE,MAAM,CAAC,eAAE,OAAO,GAAG,eAAE,OAAO,CAAC,CAAC;AAAA,EAChE,CAAC,mCAA8B,GAAG,eAAE,MAAM,CAAC,eAAE,OAAO,GAAG,kBAAkB,eAAe,CAAC;AAAA,EACzF,CAAC,mDAAsC,GAAG,eAAE,MAAM,CAAC,eAAE,OAAO,GAAG,gBAAgB,CAAC;AAAA,EAChF,CAAC,qCAA+B,GAAG,eAAE,MAAM,CAAC,eAAE,OAAO,GAAG,iBAAiB,eAAE,OAAO,CAAC,CAAC;AACrF,CAAC;AAQM,IAAM,YAAY,eAAE,OAAO;AAAA,EACjC,UAAU,eAAE,OAAO;AAAA,EACnB,KAAK,eAAE,OAAO;AAAA,EACd,MAAM,eAAE,OAAO;AAChB,CAAC;AAQM,IAAK,kBAAL,kBAAKC,qBAAL;AACN,EAAAA,iBAAA,kBAAe;AACf,EAAAA,iBAAA,gBAAa;AACb,EAAAA,iBAAA,eAAY;AAHD,SAAAA;AAAA,GAAA;AAML,IAAM,oBAAoB,eAAE,mBAAmB,eAAe;AAAA,EACpE,eAAE,OAAO;AAAA,IACR,aAAa,eAAE,QAAQ,iCAA4B;AAAA,IACnD,MAAM,eAAE,OAAO;AAAA,MACd,eAAe;AAAA,MACf,MAAM,eAAE,OAAO;AAAA,MACf,QAAQ,eAAE,MAAM,eAAE,OAAO,CAAC,EAAE,SAAS;AAAA,MACrC,QAAQ,eAAE,QAAQ,EAAE,SAAS;AAAA,IAC9B,CAAC;AAAA,EACF,CAAC;AAAA,EACD,eAAE,OAAO;AAAA,IACR,aAAa,eAAE,QAAQ,6BAA0B;AAAA,IACjD,MAAM,eAAE,OAAO;AAAA,EAChB,CAAC;AAAA,EACD,eAAE,OAAO;AAAA,IACR,aAAa,eAAE,QAAQ,2BAAyB;AAAA,IAChD,MAAM,eAAE,OAAO;AAAA,EAChB,CAAC;AACF,CAAC;AAQM,IAAM,kBAAkB,eAAE,mBAAmB,aAAa;AAAA,EAChE,eAAE,OAAO;AAAA,IACR,WAAW,eAAE,QAAQ,uBAAwB;AAAA,IAC7C,SAAS,oBAAoB,MAAM,uBAAwB;AAAA,EAC5D,CAAC;AAAA,EACD,eAAE,OAAO;AAAA,IACR,WAAW,eAAE,QAAQ,+BAA4B;AAAA,IACjD,SAAS,oBAAoB,MAAM,+BAA4B;AAAA,EAChE,CAAC;AAAA,EACD,eAAE,OAAO;AAAA,IACR,WAAW,eAAE,QAAQ,+BAA4B;AAAA,IACjD,SAAS,oBAAoB,MAAM,+BAA4B;AAAA,EAChE,CAAC;AAAA,EACD,eAAE,OAAO;AAAA,IACR,WAAW,eAAE,QAAQ,yCAAiC;AAAA,IACtD,SAAS,oBAAoB,MAAM,yCAAiC;AAAA,EACrE,CAAC;AAAA,EACD,eAAE,OAAO;AAAA,IACR,WAAW,eAAE,QAAQ,6BAA2B;AAAA,IAChD,SAAS,oBAAoB,MAAM,6BAA2B;AAAA,EAC/D,CAAC;AAAA,EACD,eAAE,OAAO;AAAA,IACR,WAAW,eAAE,QAAQ,iCAA6B;AAAA,IAClD,SAAS,oBAAoB,MAAM,iCAA6B;AAAA,EACjE,CAAC;AAAA,EACD,eAAE,OAAO;AAAA,IACR,WAAW,eAAE,QAAQ,yCAAiC;AAAA,IACtD,SAAS,oBAAoB,MAAM,yCAAiC;AAAA,EACrE,CAAC;AAAA,EACD,eAAE,OAAO;AAAA,IACR,WAAW,eAAE,QAAQ,+BAA4B;AAAA,IACjD,SAAS,oBAAoB,MAAM,+BAA4B;AAAA,EAChE,CAAC;AAAA,EACD,eAAE,OAAO;AAAA,IACR,WAAW,eAAE,QAAQ,+BAA4B;AAAA,IACjD,SAAS,oBAAoB,MAAM,+BAA4B;AAAA,EAChE,CAAC;AAAA,EACD,eAAE,OAAO;AAAA,IACR,WAAW,eAAE,QAAQ,mCAA8B;AAAA,IACnD,SAAS,oBAAoB,MAAM,mCAA8B;AAAA,EAClE,CAAC;AAAA,EACD,eAAE,OAAO;AAAA,IACR,WAAW,eAAE,QAAQ,mDAAsC;AAAA,IAC3D,SAAS,oBAAoB,MAAM,mDAAsC;AAAA,EAC1E,CAAC;AACF,CAAC;AAQM,IAAK,iBAAL,kBAAKC,oBAAL;AACN,EAAAA,gBAAA,aAAU;AACV,EAAAA,gBAAA,gBAAa;AACb,EAAAA,gBAAA,SAAM;AACN,EAAAA,gBAAA,iBAAc;AACd,EAAAA,gBAAA,eAAY;AALD,SAAAA;AAAA,GAAA;AAQL,IAAK,YAAL,kBAAKC,eAAL;AACN,EAAAA,WAAA,YAAS;AACT,EAAAA,WAAA,YAAS;AAFE,SAAAA;AAAA,GAAA;AAKL,IAAM,mBAAmB,eAAE,mBAAmB,QAAQ;AAAA,EAC5D,eAAE,OAAO;AAAA,IACR,MAAM,eAAE,QAAQ,eAAkB;AAAA,IAClC,QAAQ,eAAE,QAAQ,qBAAgB;AAAA,IAClC,MAAM;AAAA,EACP,CAAC;AAAA,EACD,eAAE,OAAO;AAAA,IACR,MAAM,eAAE,QAAQ,+BAA0B;AAAA,IAC1C,QAAQ,eAAE,QAAQ,qBAAgB;AAAA,IAClC,UAAU,eAAE,OAAO;AAAA,IACnB,MAAM;AAAA,EACP,CAAC;AAAA,EACD,eAAE,OAAO;AAAA,IACR,MAAM,eAAE,QAAQ,2BAAwB;AAAA,IACxC,QAAQ,eAAE,QAAQ,qBAAgB;AAAA,IAClC,eAAe,eAAE,OAAO,EAAE,SAAS;AAAA,IACnC,MAAM;AAAA,EACP,CAAC;AACF,CAAC;;;ACpLD,IAAAC,eAAkB;AAMX,IAAM,+BAA+B,eAAE,mBAAmB,UAAU;AAAA,EAC1E,eAAE,OAAO;AAAA,IACR,aAAa,eAAE,OAAO;AAAA,IACtB,QAAQ,eAAE,QAAQ,SAAS;AAAA,IAC3B,KAAK,eAAE,OAAO,EAAE,SAAS;AAAA,IACzB,SAAS,eAAE,OAAO;AAAA,EACnB,CAAC;AAAA,EACD,eAAE,OAAO;AAAA,IACR,aAAa,eAAE,OAAO;AAAA,IACtB,QAAQ,eAAE,QAAQ,QAAQ;AAAA,IAC1B,QAAQ,eAAE,OAAO;AAAA,EAClB,CAAC;AAAA,EACD,eAAE,OAAO;AAAA,IACR,aAAa,eAAE,OAAO;AAAA,IACtB,QAAQ,eAAE,QAAQ,QAAQ;AAAA,IAC1B,UAAU,eAAE,OAAO,EAAE,SAAS;AAAA,EAC/B,CAAC;AAAA,EACD,eAAE,OAAO;AAAA,IACR,aAAa,eAAE,OAAO;AAAA,IACtB,QAAQ,eAAE,QAAQ,UAAU;AAAA,EAC7B,CAAC;AACF,CAAC;;;AC3BD,IAAAC,eAAkB;AAKX,IAAK,mBAAL,kBAAKC,sBAAL;AACN,EAAAA,kBAAA,gBAAa;AACb,EAAAA,kBAAA,WAAQ;AACR,EAAAA,kBAAA,SAAM;AACN,EAAAA,kBAAA,gBAAa;AAJF,SAAAA;AAAA,GAAA;AAOL,IAAK,qBAAL,kBAAKC,wBAAL;AACN,EAAAA,oBAAA,YAAS;AACT,EAAAA,oBAAA,WAAQ;AACR,EAAAA,oBAAA,aAAU;AACV,EAAAA,oBAAA,eAAY;AACZ,EAAAA,oBAAA,eAAY;AALD,SAAAA;AAAA,GAAA;AA6IL,IAAM,qBAA4D;AAAA,EACxE,CAAC,6BAA2B,GAAG;AAAA,IAC9B,mBAAmB;AAAA;AAAA,IACnB,kBAAkB;AAAA;AAAA,IAClB,oBAAoB;AAAA,IACpB,mBAAmB;AAAA,IACnB,kBAAkB;AAAA,IAClB,eAAe;AAAA,MACd;AAAA,MACA;AAAA,MACA;AAAA,IACD;AAAA,IACA,uBAAuB;AAAA,IACvB,qBAAqB;AAAA,IACrB,mBAAmB;AAAA,IACnB,kBAAkB;AAAA,EACnB;AAAA,EACA,CAAC,mBAAsB,GAAG;AAAA,IACzB,mBAAmB;AAAA;AAAA,IACnB,kBAAkB;AAAA;AAAA,IAClB,oBAAoB;AAAA,IACpB,mBAAmB;AAAA,IACnB,kBAAkB;AAAA,IAClB,eAAe;AAAA,MACd;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACD;AAAA,IACA,uBAAuB;AAAA,IACvB,qBAAqB;AAAA,IACrB,mBAAmB;AAAA,IACnB,kBAAkB;AAAA,EACnB;AAAA,EACA,CAAC,eAAoB,GAAG;AAAA,IACvB,mBAAmB;AAAA;AAAA,IACnB,kBAAkB;AAAA;AAAA,IAClB,oBAAoB;AAAA,IACpB,mBAAmB;AAAA,IACnB,kBAAkB;AAAA,IAClB,eAAe;AAAA,MACd;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACD;AAAA,IACA,uBAAuB;AAAA,IACvB,qBAAqB;AAAA,IACrB,mBAAmB;AAAA,IACnB,kBAAkB;AAAA,EACnB;AAAA,EACA,CAAC,6BAA2B,GAAG;AAAA,IAC9B,mBAAmB;AAAA;AAAA,IACnB,kBAAkB;AAAA;AAAA,IAClB,oBAAoB;AAAA,IACpB,mBAAmB;AAAA,IACnB,kBAAkB;AAAA,IAClB,eAAe,CAAC;AAAA;AAAA,IAChB,uBAAuB;AAAA,IACvB,qBAAqB;AAAA,IACrB,mBAAmB;AAAA,IACnB,kBAAkB;AAAA,EACnB;AACD;AAKO,IAAM,oBAAoB,eAAE,OAAO;AAAA,EACzC,mBAAmB,eAAE,OAAO,EAAE,IAAI,CAAC;AAAA,EACnC,kBAAkB,eAAE,OAAO,EAAE,IAAI,CAAC;AAAA,EAClC,oBAAoB,eAAE,OAAO,EAAE,IAAI,CAAC;AAAA,EACpC,mBAAmB,eAAE,OAAO,EAAE,IAAI,CAAC;AAAA,EACnC,kBAAkB,eAAE,OAAO,EAAE,IAAI,CAAC;AAAA,EAClC,eAAe,eAAE,MAAM,eAAE,OAAO,CAAC;AAAA,EACjC,uBAAuB,eAAE,QAAQ;AAAA,EACjC,qBAAqB,eAAE,QAAQ;AAAA,EAC/B,mBAAmB,eAAE,QAAQ;AAAA,EAC7B,kBAAkB,eAAE,QAAQ;AAC7B,CAAC;AAEM,IAAM,qBAAqB,eAAE,OAAO;AAAA,EAC1C,oBAAoB,eAAE,OAAO,EAAE,IAAI,CAAC;AAAA,EACpC,kBAAkB,eAAE,OAAO,EAAE,IAAI,CAAC;AAAA,EAClC,qBAAqB,eAAE,OAAO,EAAE,IAAI,CAAC;AAAA,EACrC,oBAAoB,eAAE,OAAO,EAAE,IAAI,CAAC;AAAA,EACpC,iBAAiB,eAAE,OAAO,EAAE,IAAI,CAAC;AAAA,EACjC,kBAAkB,eAAE,OAAO,EAAE,IAAI,CAAC;AAAA,EAClC,mBAAmB,eAAE,OAAO,EAAE,IAAI,CAAC;AAAA,EACnC,kBAAkB,eAAE,KAAK;AAAA,EACzB,iBAAiB,eAAE,KAAK;AAAA,EACxB,gBAAgB,eAAE,KAAK;AAAA,EACvB,YAAY,eAAE,OAAO,eAAE,OAAO;AAAA,IAC7B,QAAQ,eAAE,OAAO,EAAE,IAAI,CAAC;AAAA,IACxB,MAAM,eAAE,OAAO,EAAE,IAAI,CAAC;AAAA,IACtB,UAAU,eAAE,OAAO,EAAE,IAAI,CAAC;AAAA,EAC3B,CAAC,CAAC;AACH,CAAC;AAEM,IAAM,wBAAwB,eAAE,OAAO;AAAA,EAC7C,sBAAsB,eAAE,QAAQ;AAAA,EAChC,0BAA0B,eAAE,QAAQ;AAAA,EACpC,uBAAuB,eAAE,QAAQ;AAAA,EACjC,mBAAmB,eAAE,QAAQ;AAAA,EAC7B,oBAAoB,eAAE,OAAO,EAAE,IAAI,CAAC,EAAE,IAAI,GAAG;AAAA,EAC7C,oBAAoB,eAAE,QAAQ;AAAA,EAC9B,sBAAsB,eAAE,WAAW,gBAAgB;AACpD,CAAC;AAEM,IAAM,oBAAoB,eAAE,OAAO;AAAA,EACzC,IAAI,eAAE,OAAO;AAAA,EACb,OAAO,eAAE,OAAO,EAAE,MAAM;AAAA,EACxB,MAAM,eAAE,OAAO,EAAE,SAAS;AAAA,EAC1B,SAAS,eAAE,OAAO,EAAE,SAAS;AAAA,EAC7B,kBAAkB,eAAE,WAAW,gBAAgB;AAAA,EAC/C,oBAAoB,eAAE,WAAW,kBAAkB;AAAA,EACnD,uBAAuB,eAAE,KAAK;AAAA,EAC9B,qBAAqB,eAAE,KAAK,EAAE,SAAS;AAAA,EACvC,gBAAgB,eAAE,KAAK,EAAE,SAAS;AAAA,EAClC,cAAc,eAAE,KAAK,EAAE,SAAS;AAAA,EAChC,iBAAiB,eAAE,OAAO,EAAE,IAAI,CAAC;AAAA,EACjC,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,aAAa;AAAA,EACb,WAAW,eAAE,KAAK;AAAA,EAClB,WAAW,eAAE,KAAK;AAAA,EAClB,cAAc,eAAE,KAAK;AACtB,CAAC;", "names": ["import_zod", "import_zod", "import_zod", "import_zod", "import_zod", "import_zod", "import_zod", "import_zod", "TelemetryEventName", "import_zod", "import_zod", "import_zod", "import_zod", "RooCodeEventName", "TaskCommandName", "IpcMessageType", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "import_zod", "import_zod", "SubscriptionTier", "SubscriptionStatus"]}