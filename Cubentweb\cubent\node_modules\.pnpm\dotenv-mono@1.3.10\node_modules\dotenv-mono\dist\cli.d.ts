/**
 * Generic object.
 */
type GenericObject<T = any> = {
    [key: string]: T;
};
/**
 * Configuration option types.
 */
export declare enum OptionType {
    boolean = 0,
    number = 1,
    string = 2,
    array = 3,
    object = 4,
    mapOfNumbers = 5
}
/**
 * List of all Dotenv configuration options type.
 */
export declare const DotenvOptionsType: GenericObject<OptionType>;
/**
 * Parse CLI parameter type.
 * @param option - value to parse
 * @param type - value type
 * @returns parsed option
 */
export declare function parseOption(option: string | undefined, type: OptionType): any;
/**
 * Run CLI Dotenv runners.
 * @param runner
 */
export declare function runCli(runner: Function): any;
export {};
//# sourceMappingURL=cli.d.ts.map