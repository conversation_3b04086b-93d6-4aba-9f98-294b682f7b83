var H=Object.create;var d=Object.defineProperty;var $=Object.getOwnPropertyDescriptor;var k=Object.getOwnPropertyNames;var q=Object.getPrototypeOf,J=Object.prototype.hasOwnProperty;var R=(t,e)=>{for(var o in e)d(t,o,{get:e[o],enumerable:!0})},S=(t,e,o,s)=>{if(e&&typeof e=="object"||typeof e=="function")for(let i of k(e))!J.call(t,i)&&i!==o&&d(t,i,{get:()=>e[i],enumerable:!(s=$(e,i))||s.enumerable});return t};var g=(t,e,o)=>(o=t!=null?H(q(t)):{},S(e||!t||!t.__esModule?d(o,"default",{value:t,enumerable:!0}):o,t)),W=t=>S(d({},"__esModule",{value:!0}),t);var A={};R(A,{default:()=>E,dtsPlugin:()=>E,getCompilerOptions:()=>b,humanizeFileSize:()=>v,resolveTSConfig:()=>y});module.exports=W(A);var w=require("crypto"),D=require("path"),T=g(require("typescript"));function b(t){var o,s,i,a;let e=T.default.convertCompilerOptionsFromJson(t.tsconfig.compilerOptions,process.cwd()).options;if(e.declaration=!0,e.emitDeclarationOnly=!0,e.declarationDir||(e.declarationDir=(s=(o=e.declarationDir)!=null?o:t.esbuildOptions.outdir)!=null?s:e.outDir),e.incremental&&!e.tsBuildInfoFile){let l=(0,w.createHash)("sha256").update(JSON.stringify({compilerOptions:e,__buildContext:(i=t.pluginOptions)==null?void 0:i.__buildContext})).digest("hex"),c=(0,D.resolve)(require.resolve("esbuild/package.json"),"../../.cache/esbuild-plugin-d.ts");e.tsBuildInfoFile=(0,D.resolve)((a=t.pluginOptions.buildInfoDir)!=null?a:c,`esbuild-plugin-dts-${l}.tsbuildinfo`)}return e.listEmittedFiles=!0,e}var L=require("fs"),M=g(require("lodash.merge")),x=require("path"),h=g(require("typescript"));function j(t){try{return require.resolve(t)}catch{return}}function y(t){var s,i,a;let e=(i=t.configPath)!=null?i:h.default.findConfigFile((s=t.searchPath)!=null?s:process.cwd(),h.default.sys.fileExists,t.configName);if(!e)throw new Error("No config file found");e.startsWith(".")&&(e=require.resolve(e));let o=h.default.readConfigFile(e,l=>(0,L.readFileSync)(l,"utf-8"));if(o.config.extends){let l=y({...t,configPath:(a=j(o.config.extends))!=null?a:(0,x.resolve)((0,x.dirname)(e),o.config.extends)});o.config=(0,M.default)(l,o.config)}if(o.error)throw o.error;return o.config}function v(t){let e=Math.floor(Math.log(t)/Math.log(1024));return Math.round(t/Math.pow(1024,e)*100)/100+["b","kb","mb","gb","tb"][e]}var P=g(require("chalk")),O=require("fs"),C=require("path"),f=g(require("typescript"));function N(t){let e=["verbose","debug","info","warning","error","silent"];for(let o of e){if(o===t)break;e.splice(e.indexOf(o),1)}return{info:(...o)=>{e.includes("info")&&console.log(...o)}}}var E=(t={})=>({name:"dts-plugin",async setup(e){let o=N(e.initialOptions.logLevel),s=t.tsconfig&&typeof t.tsconfig!="string"?t.tsconfig:y({configPath:t.tsconfig}),i=b({tsconfig:s,pluginOptions:t,esbuildOptions:e.initialOptions}),a=i.incremental?f.default.createIncrementalCompilerHost(i):f.default.createCompilerHost(i),l=[];e.onLoad({filter:/(\.tsx|\.ts)$/},async c=>{var p;l.push(c.path);let m=[];return a.getSourceFile(c.path,(p=i.target)!=null?p:f.default.ScriptTarget.Latest,u=>{m.push({detail:u})},!0),{errors:m}}),e.onEnd(()=>{let c;i.incremental?c=f.default.createIncrementalProgram({options:i,host:a,rootNames:l}):c=f.default.createProgram(l,i,a);let m=f.default.getPreEmitDiagnostics(c).map(r=>{var n;return{text:typeof r.messageText=="string"?r.messageText:r.messageText.messageText,detail:r,location:{file:(n=r.file)==null?void 0:n.fileName,namespace:"file"},category:r.category}}),p=m.filter(r=>r.category===f.default.DiagnosticCategory.Error).map(({category:r,...n})=>n),u=m.filter(r=>r.category===f.default.DiagnosticCategory.Warning).map(({category:r,...n})=>n);if(p.length>0)return{errors:p,warnings:u};let I=Date.now(),F=c.emit();if(F.emitSkipped||typeof F.emittedFiles>"u")o.info(P.default`{yellow No declarations emitted}`);else for(let r of F.emittedFiles){let n=(0,C.resolve)(r);if((0,O.existsSync)(n)&&n!==i.tsBuildInfoFile){let _=(0,O.lstatSync)(n),z=n.replace((0,C.resolve)(process.cwd()),"").replace(/^[\\/]/,""),B=v(_.size);o.info(P.default`  {bold ${z}} {cyan ${B}}`)}}return o.info(P.default`{green Finished compiling declarations in ${Date.now()-I}ms}`),{warnings:u}})}});0&&(module.exports={dtsPlugin,getCompilerOptions,humanizeFileSize,resolveTSConfig});
//# sourceMappingURL=index.js.map