{"version": 3, "file": "module-test.js", "sourceRoot": "", "sources": ["../ts/module-test.ts"], "names": [], "mappings": ";;;AAIA,qCAAoD;AACpD,yCAAqC;AAErC;;;;;;;;;GASG;AACH,MAAa,qBAAqB;IAGhC,YAAoB,MAAyB;QAAzB,WAAM,GAAN,MAAM,CAAmB;QAF7C,aAAQ,GAAG,IAAI,GAAG,EAAkB,CAAA;QACpC,aAAQ,GAAG,IAAI,GAAG,EAAkB,CAAA;IACY,CAAC;IAEjD,UAAU,CAAC,OAAwB;QACjC,MAAM,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;YACrC,GAAG,OAAO;YACV,cAAc,EAAE;gBACd,IAAI,mBAAQ,CAAC,SAAS,EAAE,SAAS,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;gBACvE,GAAG,CAAC,OAAO,EAAE,cAAc,IAAI,EAAE,CAAC;aACnC;SACF,CAAC,CAAA;QACF,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,CAAA;QAC1B,OAAO,OAAO,CAAA;IAChB,CAAC;IAED,UAAU,CAAC,OAAwB;QACjC,MAAM,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;YACrC,GAAG,OAAO;YACV,cAAc,EAAE;gBACd,IAAI,mBAAQ,CAAC,SAAS,EAAE,SAAS,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;gBACvE,GAAG,CAAC,OAAO,EAAE,cAAc,IAAI,EAAE,CAAC;aACnC;SACF,CAAC,CAAA;QACF,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,CAAA;QAC1B,OAAO,OAAO,CAAA;IAChB,CAAC;IAED,QAAQ,CAAC,IAAY,EAAE,OAA2B;QAChD,OAAO,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE,OAAO,CAAC,CAAA;IAC5C,CAAC;IAED,UAAU;QACR,MAAM,cAAc,GAAG,CAAC,GAAG,IAAI,CAAC,QAAQ,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAA;QAC3D,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAA;QACrB,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAA;QACrB,cAAc,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE;YAC3B,IAAI,CAAC,CAAC,KAAK,EAAE;gBACX,CAAC,CAAC,OAAO,EAAE,CAAA;aACZ;QACH,CAAC,CAAC,CAAA;IACJ,CAAC;IAED,uBAAuB;QACrB,MAAM,aAAa,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,wBAAwB,EAAE,CAAA;QAC9D,IAAI,aAAa,EAAE;YACjB,mEAAmE;YACnE,qBAAqB;YACrB,MAAM,IAAI,kCAAyB,CAAC,yCAAyC,CAAC,CAAA;SAC/E;QAED,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,GAAG,CAAC,EAAE;YAC1B,MAAM,IAAI,kCAAyB,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,kBAAkB,CAAC,CAAA;SAC7E;QAED,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,GAAG,CAAC,EAAE;YAC1B,MAAM,IAAI,kCAAyB,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,kBAAkB,CAAC,CAAA;SAC7E;IACH,CAAC;IAED,eAAe;IACf,MAAM;QACJ,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAA;IAC7B,CAAC;CACF;AAjED,sDAiEC", "sourcesContent": ["import type { QuickJSContext } from \"./context\"\nimport type { ModuleEvalOptions, QuickJSWASMModule } from \"./module\"\nimport type { QuickJSRuntime } from \"./runtime\"\nimport type { ContextOptions, RuntimeOptions } from \"./types\"\nimport { QuickJSMemoryLeakDetected } from \"./errors\"\nimport { Lifetime } from \"./lifetime\"\n\n/**\n * A test wrapper of [[QuickJSWASMModule]] that keeps a reference to each\n * context or runtime created.\n *\n * Call [[disposeAll]] to reset these sets and calls `dispose` on any left alive\n * (which may throw an error).\n *\n * Call [[assertNoMemoryAllocated]] at the end of a test, when you expect that you've\n * freed all the memory you've ever allocated.\n */\nexport class TestQuickJSWASMModule implements Pick<QuickJSWASMModule, keyof QuickJSWASMModule> {\n  contexts = new Set<QuickJSContext>()\n  runtimes = new Set<QuickJSRuntime>()\n  constructor(private parent: QuickJSWASMModule) {}\n\n  newRuntime(options?: RuntimeOptions): QuickJSRuntime {\n    const runtime = this.parent.newRuntime({\n      ...options,\n      ownedLifetimes: [\n        new Lifetime(undefined, undefined, () => this.runtimes.delete(runtime)),\n        ...(options?.ownedLifetimes ?? []),\n      ],\n    })\n    this.runtimes.add(runtime)\n    return runtime\n  }\n\n  newContext(options?: ContextOptions): QuickJSContext {\n    const context = this.parent.newContext({\n      ...options,\n      ownedLifetimes: [\n        new Lifetime(undefined, undefined, () => this.contexts.delete(context)),\n        ...(options?.ownedLifetimes ?? []),\n      ],\n    })\n    this.contexts.add(context)\n    return context\n  }\n\n  evalCode(code: string, options?: ModuleEvalOptions): unknown {\n    return this.parent.evalCode(code, options)\n  }\n\n  disposeAll() {\n    const allDisposables = [...this.contexts, ...this.runtimes]\n    this.runtimes.clear()\n    this.contexts.clear()\n    allDisposables.forEach((d) => {\n      if (d.alive) {\n        d.dispose()\n      }\n    })\n  }\n\n  assertNoMemoryAllocated() {\n    const leaksDetected = this.getFFI().QTS_RecoverableLeakCheck()\n    if (leaksDetected) {\n      // Note: this is currently only available when building from source\n      // with debug builds.\n      throw new QuickJSMemoryLeakDetected(\"Leak sanitizer detected un-freed memory\")\n    }\n\n    if (this.contexts.size > 0) {\n      throw new QuickJSMemoryLeakDetected(`${this.contexts.size} contexts leaked`)\n    }\n\n    if (this.runtimes.size > 0) {\n      throw new QuickJSMemoryLeakDetected(`${this.runtimes.size} runtimes leaked`)\n    }\n  }\n\n  /** @private */\n  getFFI() {\n    return this.parent.getFFI()\n  }\n}\n"]}