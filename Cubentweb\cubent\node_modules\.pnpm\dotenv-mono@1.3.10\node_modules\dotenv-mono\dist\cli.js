"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.runCli = exports.parseOption = exports.DotenvOptionsType = exports.OptionType = void 0;
/**
 * Configuration option types.
 */
var OptionType;
(function (OptionType) {
    OptionType[OptionType["boolean"] = 0] = "boolean";
    OptionType[OptionType["number"] = 1] = "number";
    OptionType[OptionType["string"] = 2] = "string";
    OptionType[OptionType["array"] = 3] = "array";
    OptionType[OptionType["object"] = 4] = "object";
    OptionType[OptionType["mapOfNumbers"] = 5] = "mapOfNumbers";
})(OptionType = exports.OptionType || (exports.OptionType = {}));
/**
 * List of all Dotenv configuration options type.
 */
exports.DotenvOptionsType = {
    cwd: OptionType.string,
    debug: OptionType.boolean,
    defaults: OptionType.string,
    depth: OptionType.number,
    encoding: OptionType.string,
    expand: OptionType.boolean,
    extension: OptionType.string,
    path: OptionType.string,
    override: OptionType.boolean,
    priorities: OptionType.mapOfNumbers,
};
/**
 * Parse error.
 */
class ParseError extends Error {
    constructor(message, option = "") {
        super(`${message} Parsed value: ${option}`);
        this.name = "ParseError";
    }
}
/**
 * Parse CLI parameter type.
 * @param option - value to parse
 * @param type - value type
 * @returns parsed option
 */
function parseOption(option, type) {
    // Undefined
    if (option === undefined || option === null)
        return undefined;
    // Number
    if (type === OptionType.number)
        return Number(option);
    // Boolean
    if (type === OptionType.boolean)
        return option === "true";
    // Objects
    if (type === OptionType.array ||
        type === OptionType.object ||
        type === OptionType.mapOfNumbers) {
        try {
            const result = JSON.parse(option);
            // Check if is an object
            if (typeof result !== "object") {
                throw new ParseError(`The value is not an object.`, option);
            }
            if (type === OptionType.array) {
                // Array
                return Object.values(result);
            }
            // Check if is a map of numbers, null and undefined are allowed
            if (type === OptionType.mapOfNumbers &&
                Object.values(result).some((v) => v && typeof v !== "number")) {
                throw new ParseError(`The value is not an map of numbers.`, option);
            }
            // Object
            return result;
        }
        catch (e) {
            console.error(`Invalid option value!\r\n`, e);
            return undefined;
        }
    }
    // String
    return option;
}
exports.parseOption = parseOption;
/**
 * Run CLI Dotenv runners.
 * @param runner
 */
function runCli(runner) {
    // Empty options
    const options = {};
    // Environment configuration
    Object.keys(exports.DotenvOptionsType).forEach((option) => {
        const envName = "DOTENV_CONFIG_" + option.toUpperCase();
        if (process.env[envName] != null) {
            options[option] = parseOption(process.env[envName], exports.DotenvOptionsType[option]);
        }
    });
    // CLI Parameter configuration parser
    const args = process.argv;
    const keys = Object.keys(exports.DotenvOptionsType).join("|");
    const re = new RegExp(`^dotenv_config_(${keys})=(.*?)$`, "g");
    const cliOptions = args.reduce(function (opts, cur) {
        const matches = cur.match(re);
        if (matches) {
            const option = String(matches[1]).trim();
            const match = String(matches[2]).trim();
            opts[option] = parseOption(match, exports.DotenvOptionsType[option]);
        }
        return opts;
    }, {});
    // Run command
    return runner(Object.assign(Object.assign({}, options), cliOptions));
}
exports.runCli = runCli;
//# sourceMappingURL=cli.js.map