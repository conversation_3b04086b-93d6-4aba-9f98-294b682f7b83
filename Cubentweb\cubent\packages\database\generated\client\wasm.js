
Object.defineProperty(exports, "__esModule", { value: true });

const {
  PrismaClientKnownRequestError,
  PrismaClientUnknownRequestError,
  PrismaClientRustPanicError,
  PrismaClientInitializationError,
  PrismaClientValidationError,
  getPrismaClient,
  sqltag,
  empty,
  join,
  raw,
  skip,
  Decimal,
  Debug,
  objectEnumValues,
  makeStrictEnum,
  Extensions,
  warnOnce,
  defineDmmfProperty,
  Public,
  getRuntime,
  createParam,
} = require('./runtime/wasm.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 6.4.1
 * Query Engine version: a9055b89e58b4b5bfb59600785423b1db3d0e75d
 */
Prisma.prismaVersion = {
  client: "6.4.1",
  engine: "a9055b89e58b4b5bfb59600785423b1db3d0e75d"
}

Prisma.PrismaClientKnownRequestError = PrismaClientKnownRequestError;
Prisma.PrismaClientUnknownRequestError = PrismaClientUnknownRequestError
Prisma.PrismaClientRustPanicError = PrismaClientRustPanicError
Prisma.PrismaClientInitializationError = PrismaClientInitializationError
Prisma.PrismaClientValidationError = PrismaClientValidationError
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = sqltag
Prisma.empty = empty
Prisma.join = join
Prisma.raw = raw
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = Extensions.getExtensionContext
Prisma.defineExtension = Extensions.defineExtension

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}





/**
 * Enums
 */
exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable'
});

exports.Prisma.UserScalarFieldEnum = {
  id: 'id',
  clerkId: 'clerkId',
  email: 'email',
  name: 'name',
  picture: 'picture',
  extensionApiKey: 'extensionApiKey',
  lastExtensionSync: 'lastExtensionSync',
  lastSettingsSync: 'lastSettingsSync',
  extensionEnabled: 'extensionEnabled',
  lastActiveAt: 'lastActiveAt',
  termsAccepted: 'termsAccepted',
  termsAcceptedAt: 'termsAcceptedAt',
  subscriptionTier: 'subscriptionTier',
  subscriptionStatus: 'subscriptionStatus',
  extensionSettings: 'extensionSettings',
  preferences: 'preferences',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ExtensionSessionScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  sessionId: 'sessionId',
  isActive: 'isActive',
  lastActiveAt: 'lastActiveAt',
  extensionVersion: 'extensionVersion',
  vscodeVersion: 'vscodeVersion',
  platform: 'platform',
  metadata: 'metadata',
  tokensUsed: 'tokensUsed',
  requestsMade: 'requestsMade',
  createdAt: 'createdAt'
};

exports.Prisma.UsageMetricsScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  tokensUsed: 'tokensUsed',
  requestsMade: 'requestsMade',
  costAccrued: 'costAccrued',
  date: 'date'
};

exports.Prisma.UsageAnalyticsScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  modelId: 'modelId',
  tokensUsed: 'tokensUsed',
  requestsMade: 'requestsMade',
  costAccrued: 'costAccrued',
  sessionId: 'sessionId',
  metadata: 'metadata',
  createdAt: 'createdAt'
};

exports.Prisma.ApiKeyScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  name: 'name',
  description: 'description',
  keyHash: 'keyHash',
  permissions: 'permissions',
  isActive: 'isActive',
  expiresAt: 'expiresAt',
  lastUsedAt: 'lastUsedAt',
  usageCount: 'usageCount',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.UserProfileScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  email: 'email',
  name: 'name',
  subscriptionTier: 'subscriptionTier',
  subscriptionStatus: 'subscriptionStatus',
  termsAccepted: 'termsAccepted',
  extensionEnabled: 'extensionEnabled',
  settings: 'settings',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.PendingLoginScalarFieldEnum = {
  id: 'id',
  deviceId: 'deviceId',
  state: 'state',
  token: 'token',
  createdAt: 'createdAt',
  expiresAt: 'expiresAt'
};

exports.Prisma.PageScalarFieldEnum = {
  id: 'id',
  name: 'name'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.NullableJsonNullValueInput = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull
};

exports.Prisma.JsonNullValueInput = {
  JsonNull: Prisma.JsonNull
};

exports.Prisma.QueryMode = {
  default: 'default',
  insensitive: 'insensitive'
};

exports.Prisma.JsonNullValueFilter = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull,
  AnyNull: Prisma.AnyNull
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};


exports.Prisma.ModelName = {
  User: 'User',
  ExtensionSession: 'ExtensionSession',
  UsageMetrics: 'UsageMetrics',
  UsageAnalytics: 'UsageAnalytics',
  ApiKey: 'ApiKey',
  UserProfile: 'UserProfile',
  PendingLogin: 'PendingLogin',
  Page: 'Page'
};
/**
 * Create the Client
 */
const config = {
  "generator": {
    "name": "client",
    "provider": {
      "fromEnvVar": null,
      "value": "prisma-client-js"
    },
    "output": {
      "value": "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\packages\\database\\generated\\client",
      "fromEnvVar": null
    },
    "config": {
      "engineType": "library"
    },
    "binaryTargets": [
      {
        "fromEnvVar": null,
        "value": "windows",
        "native": true
      }
    ],
    "previewFeatures": [
      "driverAdapters"
    ],
    "sourceFilePath": "C:\\Users\\<USER>\\Documents\\Cubentweb\\cubent\\packages\\database\\prisma\\schema.prisma",
    "isCustomOutput": true
  },
  "relativeEnvPaths": {
    "rootEnvPath": null,
    "schemaEnvPath": "../../.env"
  },
  "relativePath": "../../prisma",
  "clientVersion": "6.4.1",
  "engineVersion": "a9055b89e58b4b5bfb59600785423b1db3d0e75d",
  "datasourceNames": [
    "db"
  ],
  "activeProvider": "postgresql",
  "inlineDatasources": {
    "db": {
      "url": {
        "fromEnvVar": "DATABASE_URL",
        "value": null
      }
    }
  },
  "inlineSchema": "// This is your Prisma schema file,\n// learn more about it in the docs: https://pris.ly/d/prisma-schema\n\ngenerator client {\n  provider        = \"prisma-client-js\"\n  previewFeatures = [\"driverAdapters\"]\n  output          = \"../generated/client\"\n}\n\ndatasource db {\n  provider     = \"postgresql\"\n  url          = env(\"DATABASE_URL\")\n  relationMode = \"prisma\"\n}\n\nmodel User {\n  id      String  @id @default(cuid())\n  clerkId String  @unique\n  email   String  @unique\n  name    String?\n  picture String?\n\n  // Extension connection\n  extensionApiKey   String?\n  lastExtensionSync DateTime?\n  lastSettingsSync  DateTime?\n  extensionEnabled  Boolean   @default(true)\n  lastActiveAt      DateTime?\n  termsAccepted     Boolean   @default(false)\n  termsAcceptedAt   DateTime?\n\n  // Subscription (sync with extension)\n  subscriptionTier   String @default(\"FREE\")\n  subscriptionStatus String @default(\"ACTIVE\")\n\n  // Settings sync\n  extensionSettings Json?\n  preferences       Json?\n\n  // Relations\n  extensionSessions ExtensionSession[]\n  usageMetrics      UsageMetrics[]\n  apiKeys           ApiKey[]\n  usageAnalytics    UsageAnalytics[]\n\n  createdAt DateTime @default(now())\n  updatedAt DateTime @updatedAt\n}\n\nmodel ExtensionSession {\n  id     String @id @default(cuid())\n  userId String\n  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)\n\n  sessionId    String\n  isActive     Boolean  @default(true)\n  lastActiveAt DateTime @default(now())\n\n  // Extension details\n  extensionVersion String?\n  vscodeVersion    String?\n  platform         String?\n  metadata         Json?\n\n  // Usage tracking\n  tokensUsed   Int @default(0)\n  requestsMade Int @default(0)\n\n  createdAt DateTime @default(now())\n\n  @@unique([userId, sessionId])\n  @@index([userId])\n  @@index([isActive])\n}\n\nmodel UsageMetrics {\n  id     String @id @default(cuid())\n  userId String\n  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)\n\n  tokensUsed   Int   @default(0)\n  requestsMade Int   @default(0)\n  costAccrued  Float @default(0)\n\n  date DateTime @default(now())\n\n  @@index([userId])\n  @@index([date])\n}\n\nmodel UsageAnalytics {\n  id     String @id @default(cuid())\n  userId String\n  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)\n\n  modelId      String\n  tokensUsed   Int     @default(0)\n  requestsMade Int     @default(0)\n  costAccrued  Float   @default(0)\n  sessionId    String?\n  metadata     Json?\n\n  createdAt DateTime @default(now())\n\n  @@index([userId])\n  @@index([modelId])\n  @@index([createdAt])\n}\n\nmodel ApiKey {\n  id     String @id @default(cuid())\n  userId String\n  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)\n\n  name        String\n  description String?\n  keyHash     String    @unique\n  permissions Json      @default(\"[]\")\n  isActive    Boolean   @default(true)\n  expiresAt   DateTime?\n  lastUsedAt  DateTime?\n  usageCount  Int       @default(0)\n\n  createdAt DateTime @default(now())\n  updatedAt DateTime @updatedAt\n\n  @@index([userId])\n  @@index([isActive])\n}\n\nmodel UserProfile {\n  id     String @id @default(cuid())\n  userId String @unique\n\n  email String\n  name  String?\n\n  // Extension settings\n  subscriptionTier   String  @default(\"FREE\")\n  subscriptionStatus String  @default(\"ACTIVE\")\n  termsAccepted      Boolean @default(false)\n  extensionEnabled   Boolean @default(true)\n  settings           Json?\n\n  createdAt DateTime @default(now())\n  updatedAt DateTime @updatedAt\n\n  @@index([userId])\n}\n\nmodel PendingLogin {\n  id        String   @id @default(cuid())\n  deviceId  String\n  state     String\n  token     String\n  createdAt DateTime @default(now())\n  expiresAt DateTime\n\n  @@index([deviceId])\n  @@index([state])\n  @@index([expiresAt])\n}\n\n// Keep the existing Page model for now\nmodel Page {\n  id   Int    @id @default(autoincrement())\n  name String\n}\n",
  "inlineSchemaHash": "55dbb63b55246602f480bd351051ec7bf6d9a4384a63b4ab7e09772918a0b85c",
  "copyEngine": true
}
config.dirname = '/'

config.runtimeDataModel = JSON.parse("{\"models\":{\"User\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"clerkId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"email\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"name\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"picture\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"extensionApiKey\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"lastExtensionSync\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"lastSettingsSync\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"extensionEnabled\",\"kind\":\"scalar\",\"type\":\"Boolean\"},{\"name\":\"lastActiveAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"termsAccepted\",\"kind\":\"scalar\",\"type\":\"Boolean\"},{\"name\":\"termsAcceptedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"subscriptionTier\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"subscriptionStatus\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"extensionSettings\",\"kind\":\"scalar\",\"type\":\"Json\"},{\"name\":\"preferences\",\"kind\":\"scalar\",\"type\":\"Json\"},{\"name\":\"extensionSessions\",\"kind\":\"object\",\"type\":\"ExtensionSession\",\"relationName\":\"ExtensionSessionToUser\"},{\"name\":\"usageMetrics\",\"kind\":\"object\",\"type\":\"UsageMetrics\",\"relationName\":\"UsageMetricsToUser\"},{\"name\":\"apiKeys\",\"kind\":\"object\",\"type\":\"ApiKey\",\"relationName\":\"ApiKeyToUser\"},{\"name\":\"usageAnalytics\",\"kind\":\"object\",\"type\":\"UsageAnalytics\",\"relationName\":\"UsageAnalyticsToUser\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"}],\"dbName\":null},\"ExtensionSession\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"userId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"user\",\"kind\":\"object\",\"type\":\"User\",\"relationName\":\"ExtensionSessionToUser\"},{\"name\":\"sessionId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"isActive\",\"kind\":\"scalar\",\"type\":\"Boolean\"},{\"name\":\"lastActiveAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"extensionVersion\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"vscodeVersion\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"platform\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"metadata\",\"kind\":\"scalar\",\"type\":\"Json\"},{\"name\":\"tokensUsed\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"requestsMade\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"}],\"dbName\":null},\"UsageMetrics\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"userId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"user\",\"kind\":\"object\",\"type\":\"User\",\"relationName\":\"UsageMetricsToUser\"},{\"name\":\"tokensUsed\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"requestsMade\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"costAccrued\",\"kind\":\"scalar\",\"type\":\"Float\"},{\"name\":\"date\",\"kind\":\"scalar\",\"type\":\"DateTime\"}],\"dbName\":null},\"UsageAnalytics\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"userId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"user\",\"kind\":\"object\",\"type\":\"User\",\"relationName\":\"UsageAnalyticsToUser\"},{\"name\":\"modelId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"tokensUsed\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"requestsMade\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"costAccrued\",\"kind\":\"scalar\",\"type\":\"Float\"},{\"name\":\"sessionId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"metadata\",\"kind\":\"scalar\",\"type\":\"Json\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"}],\"dbName\":null},\"ApiKey\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"userId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"user\",\"kind\":\"object\",\"type\":\"User\",\"relationName\":\"ApiKeyToUser\"},{\"name\":\"name\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"description\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"keyHash\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"permissions\",\"kind\":\"scalar\",\"type\":\"Json\"},{\"name\":\"isActive\",\"kind\":\"scalar\",\"type\":\"Boolean\"},{\"name\":\"expiresAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"lastUsedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"usageCount\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"}],\"dbName\":null},\"UserProfile\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"userId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"email\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"name\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"subscriptionTier\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"subscriptionStatus\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"termsAccepted\",\"kind\":\"scalar\",\"type\":\"Boolean\"},{\"name\":\"extensionEnabled\",\"kind\":\"scalar\",\"type\":\"Boolean\"},{\"name\":\"settings\",\"kind\":\"scalar\",\"type\":\"Json\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"updatedAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"}],\"dbName\":null},\"PendingLogin\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"deviceId\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"state\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"token\",\"kind\":\"scalar\",\"type\":\"String\"},{\"name\":\"createdAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"},{\"name\":\"expiresAt\",\"kind\":\"scalar\",\"type\":\"DateTime\"}],\"dbName\":null},\"Page\":{\"fields\":[{\"name\":\"id\",\"kind\":\"scalar\",\"type\":\"Int\"},{\"name\":\"name\",\"kind\":\"scalar\",\"type\":\"String\"}],\"dbName\":null}},\"enums\":{},\"types\":{}}")
defineDmmfProperty(exports.Prisma, config.runtimeDataModel)
config.engineWasm = {
  getRuntime: () => require('./query_engine_bg.js'),
  getQueryEngineWasmModule: async () => {
    const loader = (await import('#wasm-engine-loader')).default
    const engine = (await loader).default
    return engine 
  }
}
config.compilerWasm = undefined

config.injectableEdgeEnv = () => ({
  parsed: {
    DATABASE_URL: typeof globalThis !== 'undefined' && globalThis['DATABASE_URL'] || typeof process !== 'undefined' && process.env && process.env.DATABASE_URL || undefined
  }
})

if (typeof globalThis !== 'undefined' && globalThis['DEBUG'] || typeof process !== 'undefined' && process.env && process.env.DEBUG || undefined) {
  Debug.enable(typeof globalThis !== 'undefined' && globalThis['DEBUG'] || typeof process !== 'undefined' && process.env && process.env.DEBUG || undefined)
}

const PrismaClient = getPrismaClient(config)
exports.PrismaClient = PrismaClient
Object.assign(exports, Prisma)

