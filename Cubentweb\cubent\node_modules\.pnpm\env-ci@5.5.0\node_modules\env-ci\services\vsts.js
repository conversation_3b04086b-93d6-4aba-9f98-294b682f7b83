// https://docs.microsoft.com/en-us/vsts/pipelines/build/variables
// The docs indicate that SYSTEM_PULLREQUEST_SOURCEBRANCH and SYSTEM_PULLREQUEST_TARGET<PERSON><PERSON>CH are in the long format (e.g `refs/heads/master`) however tests show they are both in the short format (e.g. `master`)
const {parseBranch} = require('../lib/utils.js');

module.exports = {
  detect({env}) {
    return Boolean(env.BUILD_BUILDURI);
  },
  configuration({env}) {
    const pr = env.SYSTEM_PULLREQUEST_PULLREQUESTID;
    const isPr = Boolean(pr);

    return {
      name: 'Visual Studio Team Services',
      service: 'vsts',
      commit: env.BUILD_SOURCEVERSION,
      build: env.BUILD_BUILDNUMBER,
      branch: parseBranch(isPr ? env.SYSTEM_PULLREQUEST_TARGETBRANCH : env.BUILD_SOURCEBRANCH),
      pr,
      isPr,
      prBranch: parseBranch(isPr ? env.SYSTEM_PULLREQUEST_SOURCEBRANCH : undefined),
      root: env.BUILD_REPOSITORY_LOCALPATH,
    };
  },
};
