{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../../src/types.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Span } from '@opentelemetry/api';\nimport { InstrumentationConfig } from '@opentelemetry/instrumentation';\nimport { ExpressLayerType } from './enums/ExpressLayerType';\n\nexport type LayerPathSegment = string | RegExp | number;\n\nexport type IgnoreMatcher = string | RegExp | ((name: string) => boolean);\n\nexport type ExpressRequestInfo<T = any> = {\n  /** An express request object */\n  request: T;\n  route: string;\n  layerType: ExpressLayerType;\n};\n\nexport type SpanNameHook = (\n  info: ExpressRequestInfo,\n  /**\n   * If no decision is taken based on RequestInfo, the default name\n   * supplied by the instrumentation can be used instead.\n   */\n  defaultName: string\n) => string;\n\n/**\n * Function that can be used to add custom attributes to the current span or the root span on\n * a Express request\n * @param span - The Express middleware layer span.\n * @param info - An instance of ExpressRequestInfo that contains info about the request such as the route, and the layer type.\n */\nexport interface ExpressRequestCustomAttributeFunction {\n  (span: Span, info: ExpressRequestInfo): void;\n}\n\n/**\n * Options available for the Express Instrumentation (see [documentation](https://github.com/open-telemetry/opentelemetry-js-contrib/tree/main/plugins/node/opentelemetry-instrumentation-express#express-instrumentation-options))\n */\nexport interface ExpressInstrumentationConfig extends InstrumentationConfig {\n  /** Ignore specific based on their name */\n  ignoreLayers?: IgnoreMatcher[];\n  /** Ignore specific layers based on their type */\n  ignoreLayersType?: ExpressLayerType[];\n  spanNameHook?: SpanNameHook;\n\n  /** Function for adding custom attributes on Express request */\n  requestHook?: ExpressRequestCustomAttributeFunction;\n}\n"]}