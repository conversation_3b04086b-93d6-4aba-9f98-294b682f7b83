/// <reference types="node" />
import { DotenvConfigOutput, DotenvParseOutput } from "dotenv";
/**
 * Environment variables list
 * @example `{ EXAMPLE: "1", EXAMPLE_2: "2" }`
 */
export type DotenvData = Record<string, any>;
/**
 * Criteria of the filename priority to load as dotenv file
 * @see https://github.com/marcocesarato/dotenv-mono
 * @example `{ '.env': 1, '.env.$(NODE_ENV)': 25, '.env.local': 50, '.env.$(NODE_ENV).local': 75 }`
 */
export type DotenvPriorities = {
    [key: string]: number;
};
/**
 * Dotenv matcher result.
 * @example `{ foundDotenv: './', foundDotenv: './.env' }`
 */
export type DotenvMatcherResult = {
    foundPath: string | null | undefined;
    foundDotenv: string | null | undefined;
};
/**
 * Dotenv matcher.
 */
export type DotenvMatcher = (dotenv: string | null | undefined, cwd: string) => DotenvMatcherResult;
/**
 * Configuration settings.
 */
export type DotenvConfig = {
    /**
     * Specify the current working directory.
     * @defaultValue `process.cwd()`
     * @example `require('dotenv-mono').load({ cwd: 'latin1' })`
     */
    cwd?: string;
    /**
     * Turn on/off logging to help debug why certain keys or values are not being set as you expect.
     * @defaultValue `false`
     * @example `require('dotenv-mono').load({ debug: true })`
     */
    debug?: boolean;
    /**
     * Specify the defaults dotenv filename.
     * @defaultValue `.env.defaults`
     * @example `require('dotenv-mono').load({ defaults: '.env.default' })`
     */
    defaults?: string;
    /**
     * Specify the max depth to reach finding up the folder from the children directory.
     * @defaultValue `4`
     * @example `require('dotenv-mono').load({ depth: 3 })`
     */
    depth?: number;
    /**
     * Specify the encoding of your file containing environment variables.
     * @defaultValue `utf8`
     * @example `require('dotenv-mono').load({ encoding: 'latin1' })`
     */
    encoding?: BufferEncoding | string;
    /**
     * Turn on/off the dotenv-expand plugin.
     * @defaultValue `true`
     * @example `require('dotenv-mono').load({ expand: false })`
     */
    expand?: boolean;
    /**
     * Specify to load specific dotenv file used only on specific apps/packages (ex. .env.server).
     * @example `require('dotenv-mono').load({ extension: 'server' })`
     */
    extension?: string;
    /**
     * Override any environment variables that have already been set on your machine with values from your .env file.
     * @defaultValue `false`
     * @example `require('dotenv-mono').load({ override: true })`
     */
    override?: boolean;
    /**
     * Specify a custom path if your file containing environment variables is located elsewhere.
     * @example `require('dotenv-mono').load({ path: '../../configs/.env' })`
     */
    path?: string;
    /**
     * Specify the criteria of the filename priority to load as dotenv file.
     * @see https://github.com/marcocesarato/dotenv-mono
     * @defaultValue `{ '.env': 1, '.env.$(NODE_ENV)': 25, '.env.local': 50, '.env.$(NODE_ENV).local': 75 }`
     * @example `require('dotenv-mono').load({ priorities: { '.env.overwrite': 100 } })`
     */
    priorities?: DotenvPriorities;
};
/**
 * Dotenv controller.
 */
export declare class Dotenv {
    #private;
    config: DotenvConfigOutput;
    env: DotenvData;
    plain: string;
    /**
     * Constructor.
     * @param cwd - current Working Directory
     * @param debug - turn on/off debugging
     * @param depth - max walking up depth
     * @param encoding - file encoding
     * @param expand - turn on/off dotenv-expand plugin
     * @param extension - add dotenv extension
     * @param override - override process variables
     * @param path - dotenv path
     * @param priorities - priorities
     */
    constructor({ cwd, debug, defaults, depth, encoding, expand, extension, override, path, priorities, }?: DotenvConfig);
    /**
     * Get debugging.
     */
    get debug(): boolean;
    /**
     * Set debugging.
     * @param value
     */
    set debug(value: boolean | undefined);
    /**
     * Get defaults filename.
     */
    get defaults(): string;
    /**
     * Set defaults filename.
     * @param value
     */
    set defaults(value: string | undefined);
    /**
     * Get encoding.
     */
    get encoding(): BufferEncoding;
    /**
     * Set encoding.
     * @param value
     */
    set encoding(value: BufferEncoding | string | undefined);
    /**
     * Get dotenv-expand plugin enabling.
     */
    get expand(): boolean;
    /**
     * Turn on/off dotenv-expand plugin.
     */
    set expand(value: boolean | undefined);
    /**
     * Get extension.
     */
    get extension(): string;
    /**
     * Set extension.
     */
    set extension(value: string | undefined);
    /**
     * Get current working directory.
     */
    get cwd(): string;
    /**
     * Set current working directory.
     * @param value
     */
    set cwd(value: string | undefined);
    /**
     * Get depth.
     */
    get depth(): number;
    /**
     * Set depth.
     * @param value
     */
    set depth(value: number | undefined);
    /**
     * Get override.
     */
    get override(): boolean;
    /**
     * Set override.
     * @param value
     */
    set override(value: boolean | undefined);
    /**
     * Get path.
     */
    get path(): string;
    /**
     * Set path.
     */
    set path(value: string | undefined);
    /**
     * Get priorities.
     */
    get priorities(): DotenvPriorities;
    /**
     * Merge priorities specified with default and check NODE_ENV.
     * @param value
     */
    set priorities(value: DotenvPriorities | undefined);
    /**
     * Parses a string or buffer in the .env file format into an object.
     * @see https://docs.dotenv.org
     * @returns an object with keys and values based on `src`. example: `{ DB_HOST : 'localhost' }`
     */
    parse(src: string | Buffer): DotenvParseOutput;
    /**
     * Loads `.env` and default file contents.
     * @param loadOnProcess - load contents inside process
     * @returns current instance
     */
    load(loadOnProcess?: boolean): this;
    /**
     * Load with dotenv package and set parsed and plain content into the instance.
     * @private
     * @param file - path to dotenv
     * @param loadOnProcess - load contents inside process
     * @param defaults - is the default dotenv
     */
    private loadDotenv;
    /**
     * Merge dotenv package configs.
     * @private
     * @param config - dotenv config
     */
    private mergeDotenvConfig;
    /**
     * Loads `.env` file contents.
     * @returns current instance
     */
    loadFile(): this;
    /**
     * Find first `.env` file walking up from cwd directory based on priority criteria.
     * @returns file matched with higher priority
     */
    find(matcher?: DotenvMatcher): string | null | undefined;
    /**
     * Dotenv matcher.
     * @private
     * @param dotenv - dotenv result
     * @param cwd - current working directory
     * @returns paths found
     */
    private dotenvMatcher;
    /**
     * Defaults dotenv matcher.
     * @private
     * @param dotenv - dotenv result
     * @param cwd - current working directory
     * @returns paths found
     */
    private dotenvDefaultsMatcher;
    /**
     * Save `.env` file contents.
     * @param changes - data to change on the dotenv
     * @returns current instance
     */
    save(changes: DotenvData): this;
    /**
     * Escape regex.
     * @param string - string to escape
     * @returns escaped string
     */
    private escapeRegExp;
}
/**
 * Load dotenv on process and return instance of Dotenv.
 * @param props - configuration
 * @returns Dotenv instance
 */
export declare function dotenvLoad(props?: DotenvConfig): Dotenv;
/**
 * @see dotenvLoad
 */
export declare const load: (props?: DotenvConfig) => Dotenv;
/**
 * Load dotenv on process and return the dotenv output.
 * @param props - configuration
 * @returns DotenvConfigOutput
 */
export declare function dotenvConfig(props?: DotenvConfig): DotenvConfigOutput;
/**
 * @see dotenvConfig
 */
export declare const config: (props?: DotenvConfig) => DotenvConfigOutput;
export default Dotenv;
//# sourceMappingURL=index.d.ts.map