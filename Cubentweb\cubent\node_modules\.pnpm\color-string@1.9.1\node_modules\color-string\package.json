{"name": "color-string", "description": "Parser and generator for CSS color strings", "version": "1.9.1", "author": "<PERSON> <<EMAIL>>", "contributors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON>"], "repository": "Qix-/color-string", "scripts": {"pretest": "xo", "test": "node test/basic.js"}, "license": "MIT", "files": ["index.js"], "xo": {"rules": {"no-cond-assign": 0, "operator-linebreak": 0}}, "dependencies": {"color-name": "^1.0.0", "simple-swizzle": "^0.2.2"}, "devDependencies": {"xo": "^0.12.1"}, "keywords": ["color", "colour", "rgb", "css"]}