{"name": "@opentelemetry/instrumentation-http", "version": "0.57.2", "description": "OpenTelemetry instrumentation for `node:http` and `node:https` http client and server modules", "main": "build/src/index.js", "types": "build/src/index.d.ts", "repository": "open-telemetry/opentelemetry-js", "scripts": {"prepublishOnly": "npm run compile", "compile": "tsc --build", "clean": "tsc --build --clean", "test:cjs": "nyc mocha test/**/*.test.ts", "test:esm": "nyc node --experimental-loader=@opentelemetry/instrumentation/hook.mjs ../../../node_modules/mocha/bin/mocha 'test/**/*.test.mjs'", "test": "npm run test:cjs && npm run test:esm", "tdd": "npm run test -- --watch-extensions ts --watch", "lint": "eslint . --ext .ts", "lint:fix": "eslint . --ext .ts --fix", "version": "node ../../../scripts/version-update.js", "watch": "tsc --build --watch", "precompile": "cross-var lerna run version --scope $npm_package_name --include-dependencies", "prewatch": "node ../../../scripts/version-update.js", "peer-api-check": "node ../../../scripts/peer-api-check.js", "align-api-deps": "node ../../../scripts/align-api-deps.js", "maint:regenerate-test-certs": "cd test/fixtures && ./regenerate.sh"}, "keywords": ["opentelemetry", "http", "nodejs", "tracing", "profiling", "instrumentation"], "author": "OpenTelemetry Authors", "license": "Apache-2.0", "engines": {"node": ">=14"}, "files": ["build/src/**/*.js", "build/src/**/*.js.map", "build/src/**/*.d.ts", "doc", "LICENSE", "README.md"], "publishConfig": {"access": "public"}, "devDependencies": {"@opentelemetry/api": "1.9.0", "@opentelemetry/context-async-hooks": "1.30.1", "@opentelemetry/sdk-metrics": "1.30.1", "@opentelemetry/sdk-trace-base": "1.30.1", "@opentelemetry/sdk-trace-node": "1.30.1", "@types/mocha": "10.0.10", "@types/node": "18.6.5", "@types/request-promise-native": "1.0.21", "@types/semver": "7.5.8", "@types/sinon": "17.0.3", "@types/superagent": "8.1.9", "axios": "1.7.9", "cross-var": "1.1.0", "lerna": "6.6.2", "mocha": "10.8.2", "nock": "13.3.8", "nyc": "15.1.0", "request": "2.88.2", "request-promise-native": "1.0.9", "sinon": "15.1.2", "superagent": "10.0.2", "typescript": "4.4.4"}, "peerDependencies": {"@opentelemetry/api": "^1.3.0"}, "dependencies": {"@opentelemetry/core": "1.30.1", "@opentelemetry/instrumentation": "0.57.2", "@opentelemetry/semantic-conventions": "1.28.0", "forwarded-parse": "2.1.2", "semver": "^7.5.2"}, "homepage": "https://github.com/open-telemetry/opentelemetry-js/tree/main/experimental/packages/opentelemetry-instrumentation-http", "sideEffects": false, "gitHead": "ac8641a5dbb5df1169bd5ed25a6667a6a6f730ca"}