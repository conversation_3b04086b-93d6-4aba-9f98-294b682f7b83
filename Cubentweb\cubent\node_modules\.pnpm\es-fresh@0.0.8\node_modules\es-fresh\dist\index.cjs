"use strict";Object.defineProperty(exports, "__esModule", {value: true});const CACHE_CONTROL_NO_CACHE_REGEXP=/(?:^|,)\s*?no-cache\s*?(?:,|$)/;const parseHttpDate=Date.parse;const compareETags=(etag,str)=>str===etag||str===`W/${etag}`||`W/${str}`===etag;function isStale(etag,noneMatch){let start=0;let end=0;for(let i=0,len=noneMatch.length;i<len;i++){switch(noneMatch.charCodeAt(i)){case 32:if(start===end)start=end=i+1;break;case 44:if(compareETags(etag,noneMatch.substring(start,end)))return!1;start=end=i+1;break;default:end=i+1;break}}if(compareETags(etag,noneMatch.substring(start,end)))return!1;return!0}function fresh(reqHeaders,resHeaders){const modifiedSince=reqHeaders["if-modified-since"];const noneMatch=reqHeaders["if-none-match"];if(!modifiedSince&&!noneMatch)return!1;const cacheControl=reqHeaders["cache-control"];if(cacheControl&&CACHE_CONTROL_NO_CACHE_REGEXP.test(cacheControl)){return!1}if(noneMatch&&noneMatch!=="*"){const etag=resHeaders.etag;if(!etag||isStale(etag,noneMatch))return!1}if(modifiedSince){const lastModified=resHeaders["last-modified"];if(!lastModified||!(parseHttpDate(lastModified)<=parseHttpDate(modifiedSince))){return!1}}return!0}exports.default = fresh;
