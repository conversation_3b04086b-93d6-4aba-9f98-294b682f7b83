{"version": 3, "file": "HostDetectorSync.js", "sourceRoot": "", "sources": ["../../../../../src/detectors/platform/node/HostDetectorSync.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAEH,OAAO,EACL,qBAAqB,EACrB,mBAAmB,EACnB,qBAAqB,GACtB,MAAM,qCAAqC,CAAC;AAC7C,OAAO,EAAE,QAAQ,EAAE,MAAM,mBAAmB,CAAC;AAG7C,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,IAAI,CAAC;AACpC,OAAO,EAAE,aAAa,EAAE,MAAM,SAAS,CAAC;AACxC,OAAO,EAAE,YAAY,EAAE,MAAM,2BAA2B,CAAC;AAEzD;;;GAGG;AACH,MAAM,gBAAgB;IACpB,MAAM,CAAC,OAAiC;QACtC,MAAM,UAAU,GAAuB;YACrC,CAAC,qBAAqB,CAAC,EAAE,QAAQ,EAAE;YACnC,CAAC,qBAAqB,CAAC,EAAE,aAAa,CAAC,IAAI,EAAE,CAAC;SAC/C,CAAC;QAEF,OAAO,IAAI,QAAQ,CAAC,UAAU,EAAE,IAAI,CAAC,mBAAmB,EAAE,CAAC,CAAC;IAC9D,CAAC;IAEO,mBAAmB;QACzB,OAAO,YAAY,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;YACrC,MAAM,UAAU,GAAuB,EAAE,CAAC;YAC1C,IAAI,SAAS,EAAE;gBACb,UAAU,CAAC,mBAAmB,CAAC,GAAG,SAAS,CAAC;aAC7C;YACD,OAAO,UAAU,CAAC;QACpB,CAAC,CAAC,CAAC;IACL,CAAC;CACF;AAED,MAAM,CAAC,MAAM,gBAAgB,GAAG,IAAI,gBAAgB,EAAE,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  SEMRESATTRS_HOST_ARCH,\n  SEMRESATTRS_HOST_ID,\n  SEMRESATTRS_HOST_NAME,\n} from '@opentelemetry/semantic-conventions';\nimport { Resource } from '../../../Resource';\nimport { DetectorSync, ResourceAttributes } from '../../../types';\nimport { ResourceDetectionConfig } from '../../../config';\nimport { arch, hostname } from 'os';\nimport { normalizeArch } from './utils';\nimport { getMachineId } from './machine-id/getMachineId';\n\n/**\n * HostDetectorSync detects the resources related to the host current process is\n * running on. Currently only non-cloud-based attributes are included.\n */\nclass HostDetectorSync implements DetectorSync {\n  detect(_config?: ResourceDetectionConfig): Resource {\n    const attributes: ResourceAttributes = {\n      [SEMRESATTRS_HOST_NAME]: hostname(),\n      [SEMRESATTRS_HOST_ARCH]: normalizeArch(arch()),\n    };\n\n    return new Resource(attributes, this._getAsyncAttributes());\n  }\n\n  private _getAsyncAttributes(): Promise<ResourceAttributes> {\n    return getMachineId().then(machineId => {\n      const attributes: ResourceAttributes = {};\n      if (machineId) {\n        attributes[SEMRESATTRS_HOST_ID] = machineId;\n      }\n      return attributes;\n    });\n  }\n}\n\nexport const hostDetectorSync = new HostDetectorSync();\n"]}