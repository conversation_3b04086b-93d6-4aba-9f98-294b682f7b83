import { type Instance } from '../setup';
import { type Options } from '../options';
export interface typeOptions {
    skipClick?: Options['skipClick'];
    skipAutoClose?: Options['skipAutoClose'];
    initialSelectionStart?: number;
    initialSelectionEnd?: number;
}
export declare function type(this: Instance, element: Element, text: string, { skipClick, skipAutoClose, initialSelectionStart, initialSelectionEnd, }?: typeOptions): Promise<void>;
