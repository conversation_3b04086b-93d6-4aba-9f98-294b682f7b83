/**
 * Polyfill for `String.fromCodePoint`. It is used to create a string from a Unicode code point.
 */
export declare const fromCodePoint: (...codePoints: number[]) => string;
/**
 * Replace the given code point with a replacement character if it is a
 * surrogate or is outside the valid range. Otherwise return the code
 * point unchanged.
 */
export declare function replaceCodePoint(codePoint: number): number;
/**
 * Replace the code point if relevant, then convert it to a string.
 *
 * @deprecated Use `fromCodePoint(replaceCodePoint(codePoint))` instead.
 * @param codePoint The code point to decode.
 * @returns The decoded code point.
 */
export default function decodeCodePoint(codePoint: number): string;
//# sourceMappingURL=decode_codepoint.d.ts.map