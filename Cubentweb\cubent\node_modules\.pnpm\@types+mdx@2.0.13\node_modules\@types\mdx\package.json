{"name": "@types/mdx", "version": "2.0.13", "description": "TypeScript definitions for mdx", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/mdx", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/ChristianMurphy"}, {"name": "Re<PERSON><PERSON>", "githubUsername": "rem<PERSON><PERSON><PERSON>", "url": "https://github.com/remcohaszing"}, {"name": "<PERSON>", "githubUsername": "wooorm", "url": "https://github.com/wooorm"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/mdx"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "e9930b09508f8573e534a6a2fb0c41a3f791f073d8596be495dc6e47b788c530", "typeScriptVersion": "4.7"}