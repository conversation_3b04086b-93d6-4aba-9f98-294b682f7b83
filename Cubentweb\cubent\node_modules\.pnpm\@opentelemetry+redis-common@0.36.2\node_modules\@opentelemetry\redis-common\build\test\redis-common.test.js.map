{"version": 3, "file": "redis-common.test.js", "sourceRoot": "", "sources": ["../../test/redis-common.test.ts"], "names": [], "mappings": ";;AAAA;;;;;;;;;;;;;;GAcG;AACH,wCAA4D;AAC5D,iCAAiC;AAEjC,QAAQ,CAAC,iCAAiC,EAAE,GAAG,EAAE;IAC/C;QACE;YACE,OAAO,EAAE,SAAS;YAClB,OAAO,EAAE,CAAC,WAAW,CAAC;YACtB,QAAQ,EAAE,6BAA6B;SACxC;QACD;YACE,OAAO,EAAE,MAAM;YACf,OAAO,EAAE,CAAC,MAAM,CAAC;YACjB,QAAQ,EAAE,0BAA0B;SACrC;QACD;YACE,OAAO,EAAE,OAAO;YAChB,OAAO,EAAE,CAAC,MAAM,EAAE,OAAO,CAAC;YAC1B,QAAQ,EAAE,gCAAgC;SAC3C;QACD;YACE,OAAO,EAAE,MAAM;YACf,OAAO,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,OAAO,CAAC;YACnC,QAAQ,EAAE,qCAAqC;SAChD;QACD;YACE,OAAO,EAAE,QAAQ;YACjB,OAAO,EAAE,CAAC,KAAK,EAAE,CAAC,CAAC;YACnB,QAAQ,EAAE,cAAc;SACzB;KACF,CAAC,OAAO,CAAC,CAAC,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,EAAE,EAAE;QAC3C,EAAE,CAAC,wDAAwD,OAAO,EAAE,EAAE,GAAG,EAAE;YACzE,MAAM,CAAC,WAAW,CAChB,IAAA,oCAA4B,EAAC,OAAO,EAAE,OAAO,CAAC,EAC9C,QAAQ,CACT,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { defaultDbStatementSerializer } from '../src/index';\nimport * as assert from 'assert';\n\ndescribe('#defaultDbStatementSerializer()', () => {\n  [\n    {\n      cmdName: 'UNKNOWN',\n      cmdArgs: ['something'],\n      expected: 'UNKNOWN [1 other arguments]',\n    },\n    {\n      cmdName: 'ECHO',\n      cmdArgs: ['echo'],\n      expected: 'ECHO [1 other arguments]',\n    },\n    {\n      cmdName: 'LPUSH',\n      cmdArgs: ['list', 'value'],\n      expected: 'LPUSH list [1 other arguments]',\n    },\n    {\n      cmdName: 'HSET',\n      cmdArgs: ['hash', 'field', 'value'],\n      expected: 'HSET hash field [1 other arguments]',\n    },\n    {\n      cmdName: 'INCRBY',\n      cmdArgs: ['key', 5],\n      expected: 'INCRBY key 5',\n    },\n  ].forEach(({ cmdName, cmdArgs, expected }) => {\n    it(`should serialize the correct number of arguments for ${cmdName}`, () => {\n      assert.strictEqual(\n        defaultDbStatementSerializer(cmdName, cmdArgs),\n        expected\n      );\n    });\n  });\n});\n"]}