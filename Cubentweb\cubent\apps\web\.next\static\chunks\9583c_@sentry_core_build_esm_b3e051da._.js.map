{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "file": "version.js", "sources": ["file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/%40sentry%2Bcore%409.22.0/node_modules/%40sentry/core/src/utils-hoist/version.ts"], "sourcesContent": ["// This is a magic string replaced by rollup\ndeclare const __SENTRY_SDK_VERSION__: string;\n\nexport const SDK_VERSION = typeof __SENTRY_SDK_VERSION__ === 'string' ? __SENTRY_SDK_VERSION__ : '0.0.0-unknown.0';\n"], "names": [], "mappings": "AAAA,4CAAA;;;;AAGO,MAAM,WAAY,GAA+C,QAAuB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 20, "column": 0}, "map": {"version": 3, "file": "worldwide.js", "sources": ["file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/%40sentry%2Bcore%409.22.0/node_modules/%40sentry/core/src/utils-hoist/worldwide.ts"], "sourcesContent": ["/**\n * NOTE: In order to avoid circular dependencies, if you add a function to this module and it needs to print something,\n * you must either a) use `console.log` rather than the logger, or b) put your function elsewhere.\n *\n * Note: This file was originally called `global.ts`, but was changed to unblock users which might be doing\n * string replaces with bundlers like Vite for `global` (would break imports that rely on importing from utils/src/global).\n *\n * Why worldwide?\n *\n * Why not?\n */\n\n/* eslint-disable @typescript-eslint/no-explicit-any */\n\nimport type { Carrier } from '../carrier';\nimport type { Client } from '../client';\nimport type { SerializedLog } from '../types-hoist/log';\nimport type { SdkSource } from './env';\n\n/** Internal global with common properties and Sentry extensions  */\nexport type InternalGlobal = {\n  navigator?: { userAgent?: string; maxTouchPoints?: number };\n  console: Console;\n  PerformanceObserver?: any;\n  Sentry?: any;\n  onerror?: {\n    (event: object | string, source?: string, lineno?: number, colno?: number, error?: Error): any;\n    __SENTRY_INSTRUMENTED__?: true;\n  };\n  onunhandledrejection?: {\n    (event: unknown): boolean;\n    __SENTRY_INSTRUMENTED__?: true;\n  };\n  SENTRY_ENVIRONMENT?: string;\n  SENTRY_DSN?: string;\n  SENTRY_RELEASE?: {\n    id?: string;\n  };\n  SENTRY_SDK_SOURCE?: SdkSource;\n  /**\n   * A map of Sentry clients to their log buffers.\n   *\n   * This is used to store logs that are sent to Sentry.\n   */\n  _sentryClientToLogBufferMap?: WeakMap<Client, Array<SerializedLog>>;\n  /**\n   * Debug IDs are indirectly injected by Sentry CLI or bundler plugins to directly reference a particular source map\n   * for resolving of a source file. The injected code will place an entry into the record for each loaded bundle/JS\n   * file.\n   */\n  _sentryDebugIds?: Record<string, string>;\n  /**\n   * Raw module metadata that is injected by bundler plugins.\n   *\n   * Keys are `error.stack` strings, values are the metadata.\n   */\n  _sentryModuleMetadata?: Record<string, any>;\n  _sentryEsmLoaderHookRegistered?: boolean;\n} & Carrier;\n\n/** Get's the global object for the current JavaScript runtime */\nexport const GLOBAL_OBJ = globalThis as unknown as InternalGlobal;\n"], "names": [], "mappings": "AAmBA,kEAAA,GAyCA,+DAAA;;;AACO,MAAM,UAAW,GAAE,UAAW", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 32, "column": 0}, "map": {"version": 3, "file": "carrier.js", "sources": ["file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/%40sentry%2Bcore%409.22.0/node_modules/%40sentry/core/src/carrier.ts"], "sourcesContent": ["import type { AsyncContextStack } from './asyncContext/stackStrategy';\nimport type { AsyncContextStrategy } from './asyncContext/types';\nimport type { Scope } from './scope';\nimport type { Logger } from './utils-hoist/logger';\nimport { SDK_VERSION } from './utils-hoist/version';\nimport { GLOBAL_OBJ } from './utils-hoist/worldwide';\n\n/**\n * An object that contains globally accessible properties and maintains a scope stack.\n * @hidden\n */\nexport interface Carrier {\n  __SENTRY__?: VersionedCarrier;\n}\n\ntype VersionedCarrier = {\n  version?: string;\n} & Record<Exclude<string, 'version'>, SentryCarrier>;\n\nexport interface SentryCarrier {\n  acs?: AsyncContextStrategy;\n  stack?: AsyncContextStack;\n\n  globalScope?: Scope;\n  defaultIsolationScope?: Scope;\n  defaultCurrentScope?: Scope;\n  logger?: Logger;\n\n  /** Overwrites TextEncoder used in `@sentry/core`, need for `react-native@0.73` and older */\n  encodePolyfill?: (input: string) => Uint8Array;\n  /** Overwrites TextDecoder used in `@sentry/core`, need for `react-native@0.73` and older */\n  decodePolyfill?: (input: Uint8Array) => string;\n}\n\n/**\n * Returns the global shim registry.\n *\n * FIXME: This function is problematic, because despite always returning a valid Carrier,\n * it has an optional `__SENTRY__` property, which then in turn requires us to always perform an unnecessary check\n * at the call-site. We always access the carrier through this function, so we can guarantee that `__SENTRY__` is there.\n **/\nexport function getMainCarrier(): Carrier {\n  // This ensures a Sentry carrier exists\n  getSentryCarrier(GLOBAL_OBJ);\n  return GLOBAL_OBJ;\n}\n\n/** Will either get the existing sentry carrier, or create a new one. */\nexport function getSentryCarrier(carrier: Carrier): SentryCarrier {\n  const __SENTRY__ = (carrier.__SENTRY__ = carrier.__SENTRY__ || {});\n\n  // For now: First SDK that sets the .version property wins\n  __SENTRY__.version = __SENTRY__.version || SDK_VERSION;\n\n  // Intentionally populating and returning the version of \"this\" SDK instance\n  // rather than what's set in .version so that \"this\" SDK always gets its carrier\n  return (__SENTRY__[SDK_VERSION] = __SENTRY__[SDK_VERSION] || {});\n}\n\n/**\n * Returns a global singleton contained in the global `__SENTRY__[]` object.\n *\n * If the singleton doesn't already exist in `__SENTRY__`, it will be created using the given factory\n * function and added to the `__SENTRY__` object.\n *\n * @param name name of the global singleton on __SENTRY__\n * @param creator creator Factory function to create the singleton if it doesn't already exist on `__SENTRY__`\n * @param obj (Optional) The global object on which to look for `__SENTRY__`, if not `GLOBAL_OBJ`'s return value\n * @returns the singleton\n */\nexport function getGlobalSingleton<Prop extends keyof SentryCarrier>(\n  name: Prop,\n  creator: () => NonNullable<SentryCarrier[Prop]>,\n  obj = GLOBAL_OBJ,\n): NonNullable<SentryCarrier[Prop]> {\n  const __SENTRY__ = (obj.__SENTRY__ = obj.__SENTRY__ || {});\n  const carrier = (__SENTRY__[SDK_VERSION] = __SENTRY__[SDK_VERSION] || {});\n  // Note: We do not want to set `carrier.version` here, as this may be called before any `init` is called, e.g. for the default scopes\n  return carrier[name] || (carrier[name] = creator());\n}\n"], "names": [], "mappings": ";;;;;;;;;AAOA;;;CAGA,GAwBA;;;;;;EAMA,GACO,SAAS,cAAc,GAAY;IAC1C,uCAAA;IACE,gBAAgB,kPAAC,aAAU,CAAC;IAC5B,wPAAO,aAAU;AACnB;AAEA,sEAAA,GACO,SAAS,gBAAgB,CAAC,OAAO,EAA0B;IAChE,MAAM,UAAA,GAAc,OAAO,CAAC,UAAA,GAAa,OAAO,CAAC,UAAA,IAAc,CAAA,CAAE,CAAC;IAEpE,0DAAA;IACE,UAAU,CAAC,OAAQ,GAAE,UAAU,CAAC,OAAA,mPAAW,cAAW;IAExD,4EAAA;IACA,gFAAA;IACE,OAAQ,UAAU,gPAAC,cAAW,CAAE,GAAE,UAAU,gPAAC,cAAW,CAAA,IAAK,CAAA,CAAE;AACjE;AAEA;;;;;;;;;;CAUA,GACO,SAAS,kBAAkB,CAChC,IAAI,EACJ,OAAO,EACP,GAAA,oPAAM,aAAU;IAEhB,MAAM,UAAA,GAAc,GAAG,CAAC,UAAA,GAAa,GAAG,CAAC,UAAA,IAAc,CAAA,CAAE,CAAC;IAC1D,MAAM,OAAQ,GAAG,UAAU,gPAAC,cAAW,CAAA,GAAI,UAAU,gPAAC,cAAW,CAAA,IAAK,CAAA,CAAE,CAAC;IAC3E,qIAAA;IACE,OAAO,OAAO,CAAC,IAAI,CAAA,IAAA,CAAM,OAAO,CAAC,IAAI,CAAE,GAAE,OAAO,EAAE,CAAC;AACrD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 87, "column": 0}, "map": {"version": 3, "file": "debug-build.js", "sources": ["file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/%40sentry%2Bcore%409.22.0/node_modules/%40sentry/core/src/debug-build.ts"], "sourcesContent": ["declare const __DEBUG_BUILD__: boolean;\n\n/**\n * This serves as a build time flag that will be true by default, but false in non-debug builds or if users replace `__SENTRY_DEBUG__` in their generated code.\n *\n * ATTENTION: This constant must never cross package boundaries (i.e. be exported) to guarantee that it can be used for tree shaking.\n */\nexport const DEBUG_BUILD = __DEBUG_BUILD__;\n"], "names": [], "mappings": "AAEA;;;;CAIA;;;AACO,MAAM,WAAY,GAAiB,OAAA,gBAAA,KAAA,WAAA,IAAA,gBAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 103, "column": 0}, "map": {"version": 3, "file": "is.js", "sources": ["file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/%40sentry%2Bcore%409.22.0/node_modules/%40sentry/core/src/utils-hoist/is.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-explicit-any */\n\nimport type { Primitive } from '../types-hoist/misc';\nimport type { ParameterizedString } from '../types-hoist/parameterize';\nimport type { PolymorphicEvent } from '../types-hoist/polymorphics';\n\n// eslint-disable-next-line @typescript-eslint/unbound-method\nconst objectToString = Object.prototype.toString;\n\n/**\n * Checks whether given value's type is one of a few Error or Error-like\n * {@link isError}.\n *\n * @param wat A value to be checked.\n * @returns A boolean representing the result.\n */\nexport function isError(wat: unknown): wat is Error {\n  switch (objectToString.call(wat)) {\n    case '[object Error]':\n    case '[object Exception]':\n    case '[object DOMException]':\n    case '[object WebAssembly.Exception]':\n      return true;\n    default:\n      return isInstanceOf(wat, Error);\n  }\n}\n/**\n * Checks whether given value is an instance of the given built-in class.\n *\n * @param wat The value to be checked\n * @param className\n * @returns A boolean representing the result.\n */\nfunction isBuiltin(wat: unknown, className: string): boolean {\n  return objectToString.call(wat) === `[object ${className}]`;\n}\n\n/**\n * Checks whether given value's type is ErrorEvent\n * {@link isErrorEvent}.\n *\n * @param wat A value to be checked.\n * @returns A boolean representing the result.\n */\nexport function isErrorEvent(wat: unknown): boolean {\n  return isBuiltin(wat, 'ErrorEvent');\n}\n\n/**\n * Checks whether given value's type is DOMError\n * {@link isDOMError}.\n *\n * @param wat A value to be checked.\n * @returns A boolean representing the result.\n */\nexport function isDOMError(wat: unknown): boolean {\n  return isBuiltin(wat, 'DOMError');\n}\n\n/**\n * Checks whether given value's type is DOMException\n * {@link isDOMException}.\n *\n * @param wat A value to be checked.\n * @returns A boolean representing the result.\n */\nexport function isDOMException(wat: unknown): boolean {\n  return isBuiltin(wat, 'DOMException');\n}\n\n/**\n * Checks whether given value's type is a string\n * {@link isString}.\n *\n * @param wat A value to be checked.\n * @returns A boolean representing the result.\n */\nexport function isString(wat: unknown): wat is string {\n  return isBuiltin(wat, 'String');\n}\n\n/**\n * Checks whether given string is parameterized\n * {@link isParameterizedString}.\n *\n * @param wat A value to be checked.\n * @returns A boolean representing the result.\n */\nexport function isParameterizedString(wat: unknown): wat is ParameterizedString {\n  return (\n    typeof wat === 'object' &&\n    wat !== null &&\n    '__sentry_template_string__' in wat &&\n    '__sentry_template_values__' in wat\n  );\n}\n\n/**\n * Checks whether given value is a primitive (undefined, null, number, boolean, string, bigint, symbol)\n * {@link isPrimitive}.\n *\n * @param wat A value to be checked.\n * @returns A boolean representing the result.\n */\nexport function isPrimitive(wat: unknown): wat is Primitive {\n  return wat === null || isParameterizedString(wat) || (typeof wat !== 'object' && typeof wat !== 'function');\n}\n\n/**\n * Checks whether given value's type is an object literal, or a class instance.\n * {@link isPlainObject}.\n *\n * @param wat A value to be checked.\n * @returns A boolean representing the result.\n */\nexport function isPlainObject(wat: unknown): wat is Record<string, unknown> {\n  return isBuiltin(wat, 'Object');\n}\n\n/**\n * Checks whether given value's type is an Event instance\n * {@link isEvent}.\n *\n * @param wat A value to be checked.\n * @returns A boolean representing the result.\n */\nexport function isEvent(wat: unknown): wat is PolymorphicEvent {\n  return typeof Event !== 'undefined' && isInstanceOf(wat, Event);\n}\n\n/**\n * Checks whether given value's type is an Element instance\n * {@link isElement}.\n *\n * @param wat A value to be checked.\n * @returns A boolean representing the result.\n */\nexport function isElement(wat: unknown): boolean {\n  return typeof Element !== 'undefined' && isInstanceOf(wat, Element);\n}\n\n/**\n * Checks whether given value's type is an regexp\n * {@link isRegExp}.\n *\n * @param wat A value to be checked.\n * @returns A boolean representing the result.\n */\nexport function isRegExp(wat: unknown): wat is RegExp {\n  return isBuiltin(wat, 'RegExp');\n}\n\n/**\n * Checks whether given value has a then function.\n * @param wat A value to be checked.\n */\nexport function isThenable(wat: any): wat is PromiseLike<any> {\n  // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n  return Boolean(wat?.then && typeof wat.then === 'function');\n}\n\n/**\n * Checks whether given value's type is a SyntheticEvent\n * {@link isSyntheticEvent}.\n *\n * @param wat A value to be checked.\n * @returns A boolean representing the result.\n */\nexport function isSyntheticEvent(wat: unknown): boolean {\n  return isPlainObject(wat) && 'nativeEvent' in wat && 'preventDefault' in wat && 'stopPropagation' in wat;\n}\n\n/**\n * Checks whether given value's type is an instance of provided constructor.\n * {@link isInstanceOf}.\n *\n * @param wat A value to be checked.\n * @param base A constructor to be used in a check.\n * @returns A boolean representing the result.\n */\nexport function isInstanceOf(wat: any, base: any): boolean {\n  try {\n    return wat instanceof base;\n  } catch (_e) {\n    return false;\n  }\n}\n\ninterface VueViewModel {\n  // Vue3\n  __isVue?: boolean;\n  // Vue2\n  _isVue?: boolean;\n}\n/**\n * Checks whether given value's type is a Vue ViewModel.\n *\n * @param wat A value to be checked.\n * @returns A boolean representing the result.\n */\nexport function isVueViewModel(wat: unknown): boolean {\n  // Not using Object.prototype.toString because in Vue 3 it would read the instance's Symbol(Symbol.toStringTag) property.\n  return !!(typeof wat === 'object' && wat !== null && ((wat as VueViewModel).__isVue || (wat as VueViewModel)._isVue));\n}\n\n/**\n * Checks whether the given parameter is a Standard Web API Request instance.\n *\n * Returns false if Request is not available in the current runtime.\n */\nexport function isRequest(request: unknown): request is Request {\n  return typeof Request !== 'undefined' && isInstanceOf(request, Request);\n}\n"], "names": [], "mappings": "AAMA,6DAAA;;;;;;;;;;;;;;;;;;;AACA,MAAM,iBAAiB,MAAM,CAAC,SAAS,CAAC,QAAQ;AAEhD;;;;;;CAMA,GACO,SAAS,OAAO,CAAC,GAAG,EAAyB;IAClD,OAAQ,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC;QAC9B,KAAK,gBAAgB;QACrB,KAAK,oBAAoB;QACzB,KAAK,uBAAuB;QAC5B,KAAK,gCAAgC;YACnC,OAAO,IAAI;QACb;YACE,OAAO,YAAY,CAAC,GAAG,EAAE,KAAK,CAAC;IACrC;AACA;AACA;;;;;;CAMA,GACA,SAAS,SAAS,CAAC,GAAG,EAAW,SAAS,EAAmB;IAC3D,OAAO,cAAc,CAAC,IAAI,CAAC,GAAG,CAAA,KAAM,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC,CAAC;AAC7D;AAEA;;;;;;CAMA,GACO,SAAS,YAAY,CAAC,GAAG,EAAoB;IAClD,OAAO,SAAS,CAAC,GAAG,EAAE,YAAY,CAAC;AACrC;AAEA;;;;;;CAMA,GACO,SAAS,UAAU,CAAC,GAAG,EAAoB;IAChD,OAAO,SAAS,CAAC,GAAG,EAAE,UAAU,CAAC;AACnC;AAEA;;;;;;CAMA,GACO,SAAS,cAAc,CAAC,GAAG,EAAoB;IACpD,OAAO,SAAS,CAAC,GAAG,EAAE,cAAc,CAAC;AACvC;AAEA;;;;;;CAMA,GACO,SAAS,QAAQ,CAAC,GAAG,EAA0B;IACpD,OAAO,SAAS,CAAC,GAAG,EAAE,QAAQ,CAAC;AACjC;AAEA;;;;;;CAMA,GACO,SAAS,qBAAqB,CAAC,GAAG,EAAuC;IAC9E,OACE,OAAO,GAAI,KAAI,QAAS,IACxB,GAAA,KAAQ,IAAK,IACb,4BAAA,IAAgC,GAAI,IACpC,gCAAgC;AAEpC;AAEA;;;;;;CAMA,GACO,SAAS,WAAW,CAAC,GAAG,EAA6B;IAC1D,OAAO,QAAQ,IAAA,IAAQ,qBAAqB,CAAC,GAAG,CAAE,IAAI,OAAO,GAAA,KAAQ,QAAS,IAAG,OAAO,GAAI,KAAI,UAAU,CAAC;AAC7G;AAEA;;;;;;CAMA,GACO,SAAS,aAAa,CAAC,GAAG,EAA2C;IAC1E,OAAO,SAAS,CAAC,GAAG,EAAE,QAAQ,CAAC;AACjC;AAEA;;;;;;CAMA,GACO,SAAS,OAAO,CAAC,GAAG,EAAoC;IAC7D,OAAO,OAAO,KAAA,KAAU,WAAA,IAAe,YAAY,CAAC,GAAG,EAAE,KAAK,CAAC;AACjE;AAEA;;;;;;CAMA,GACO,SAAS,SAAS,CAAC,GAAG,EAAoB;IAC/C,OAAO,OAAO,OAAA,KAAY,WAAA,IAAe,YAAY,CAAC,GAAG,EAAE,OAAO,CAAC;AACrE;AAEA;;;;;;CAMA,GACO,SAAS,QAAQ,CAAC,GAAG,EAA0B;IACpD,OAAO,SAAS,CAAC,GAAG,EAAE,QAAQ,CAAC;AACjC;AAEA;;;CAGA,GACO,SAAS,UAAU,CAAC,GAAG,EAAgC;IAC9D,sEAAA;IACE,OAAO,OAAO,CAAC,GAAG,EAAE,IAAA,IAAQ,OAAO,GAAG,CAAC,IAAK,KAAI,UAAU,CAAC;AAC7D;AAEA;;;;;;CAMA,GACO,SAAS,gBAAgB,CAAC,GAAG,EAAoB;IACtD,OAAO,aAAa,CAAC,GAAG,CAAA,IAAK,aAAc,IAAG,GAAI,IAAG,oBAAoB,GAAA,IAAO,iBAAA,IAAqB,GAAG;AAC1G;AAEA;;;;;;;CAOA,GACO,SAAS,YAAY,CAAC,GAAG,EAAO,IAAI,EAAgB;IACzD,IAAI;QACF,OAAO,GAAI,YAAW,IAAI;IAC9B,CAAI,CAAA,OAAO,EAAE,EAAE;QACX,OAAO,KAAK;IAChB;AACA;AAQA;;;;;CAKA,GACO,SAAS,cAAc,CAAC,GAAG,EAAoB;IACtD,yHAAA;IACE,OAAO,CAAC,CAAA,CAAE,OAAO,GAAI,KAAI,QAAS,IAAG,GAAI,KAAI,IAAK,IAAA,CAAI,AAAC,GAAA,CAAqB,OAAQ,IAAG,AAAC,GAAA,CAAqB,MAAM,CAAC,CAAC;AACvH;AAEA;;;;CAIA,GACO,SAAS,SAAS,CAAC,OAAO,EAA+B;IAC9D,OAAO,OAAO,OAAA,KAAY,WAAA,IAAe,YAAY,CAAC,OAAO,EAAE,OAAO,CAAC;AACzE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 293, "column": 0}, "map": {"version": 3, "file": "browser.js", "sources": ["file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/%40sentry%2Bcore%409.22.0/node_modules/%40sentry/core/src/utils-hoist/browser.ts"], "sourcesContent": ["import { isString } from './is';\nimport { GLOBAL_OBJ } from './worldwide';\n\nconst WINDOW = GLOBAL_OBJ as unknown as Window;\n\nconst DEFAULT_MAX_STRING_LENGTH = 80;\n\ntype SimpleNode = {\n  parentNode: SimpleNode;\n} | null;\n\n/**\n * Given a child DOM element, returns a query-selector statement describing that\n * and its ancestors\n * e.g. [HTMLElement] => body > div > input#foo.btn[name=baz]\n * @returns generated DOM path\n */\nexport function htmlTreeAsString(\n  elem: unknown,\n  options: string[] | { keyAttrs?: string[]; maxStringLength?: number } = {},\n): string {\n  if (!elem) {\n    return '<unknown>';\n  }\n\n  // try/catch both:\n  // - accessing event.target (see getsentry/raven-js#838, #768)\n  // - `htmlTreeAsString` because it's complex, and just accessing the DOM incorrectly\n  // - can throw an exception in some circumstances.\n  try {\n    let currentElem = elem as SimpleNode;\n    const MAX_TRAVERSE_HEIGHT = 5;\n    const out = [];\n    let height = 0;\n    let len = 0;\n    const separator = ' > ';\n    const sepLength = separator.length;\n    let nextStr;\n    const keyAttrs = Array.isArray(options) ? options : options.keyAttrs;\n    const maxStringLength = (!Array.isArray(options) && options.maxStringLength) || DEFAULT_MAX_STRING_LENGTH;\n\n    while (currentElem && height++ < MAX_TRAVERSE_HEIGHT) {\n      nextStr = _htmlElementAsString(currentElem, keyAttrs);\n      // bail out if\n      // - nextStr is the 'html' element\n      // - the length of the string that would be created exceeds maxStringLength\n      //   (ignore this limit if we are on the first iteration)\n      if (nextStr === 'html' || (height > 1 && len + out.length * sepLength + nextStr.length >= maxStringLength)) {\n        break;\n      }\n\n      out.push(nextStr);\n\n      len += nextStr.length;\n      currentElem = currentElem.parentNode;\n    }\n\n    return out.reverse().join(separator);\n  } catch (_oO) {\n    return '<unknown>';\n  }\n}\n\n/**\n * Returns a simple, query-selector representation of a DOM element\n * e.g. [HTMLElement] => input#foo.btn[name=baz]\n * @returns generated DOM path\n */\nfunction _htmlElementAsString(el: unknown, keyAttrs?: string[]): string {\n  const elem = el as {\n    tagName?: string;\n    id?: string;\n    className?: string;\n    getAttribute(key: string): string;\n  };\n\n  const out = [];\n\n  if (!elem?.tagName) {\n    return '';\n  }\n\n  // @ts-expect-error WINDOW has HTMLElement\n  if (WINDOW.HTMLElement) {\n    // If using the component name annotation plugin, this value may be available on the DOM node\n    if (elem instanceof HTMLElement && elem.dataset) {\n      if (elem.dataset['sentryComponent']) {\n        return elem.dataset['sentryComponent'];\n      }\n      if (elem.dataset['sentryElement']) {\n        return elem.dataset['sentryElement'];\n      }\n    }\n  }\n\n  out.push(elem.tagName.toLowerCase());\n\n  // Pairs of attribute keys defined in `serializeAttribute` and their values on element.\n  const keyAttrPairs = keyAttrs?.length\n    ? keyAttrs.filter(keyAttr => elem.getAttribute(keyAttr)).map(keyAttr => [keyAttr, elem.getAttribute(keyAttr)])\n    : null;\n\n  if (keyAttrPairs?.length) {\n    keyAttrPairs.forEach(keyAttrPair => {\n      out.push(`[${keyAttrPair[0]}=\"${keyAttrPair[1]}\"]`);\n    });\n  } else {\n    if (elem.id) {\n      out.push(`#${elem.id}`);\n    }\n\n    const className = elem.className;\n    if (className && isString(className)) {\n      const classes = className.split(/\\s+/);\n      for (const c of classes) {\n        out.push(`.${c}`);\n      }\n    }\n  }\n  const allowedAttrs = ['aria-label', 'type', 'name', 'title', 'alt'];\n  for (const k of allowedAttrs) {\n    const attr = elem.getAttribute(k);\n    if (attr) {\n      out.push(`[${k}=\"${attr}\"]`);\n    }\n  }\n\n  return out.join('');\n}\n\n/**\n * A safe form of location.href\n */\nexport function getLocationHref(): string {\n  try {\n    return WINDOW.document.location.href;\n  } catch (oO) {\n    return '';\n  }\n}\n\n/**\n * Given a DOM element, traverses up the tree until it finds the first ancestor node\n * that has the `data-sentry-component` or `data-sentry-element` attribute with `data-sentry-component` taking\n * precedence. This attribute is added at build-time by projects that have the component name annotation plugin installed.\n *\n * @returns a string representation of the component for the provided DOM element, or `null` if not found\n */\nexport function getComponentName(elem: unknown): string | null {\n  // @ts-expect-error WINDOW has HTMLElement\n  if (!WINDOW.HTMLElement) {\n    return null;\n  }\n\n  let currentElem = elem as SimpleNode;\n  const MAX_TRAVERSE_HEIGHT = 5;\n  for (let i = 0; i < MAX_TRAVERSE_HEIGHT; i++) {\n    if (!currentElem) {\n      return null;\n    }\n\n    if (currentElem instanceof HTMLElement) {\n      if (currentElem.dataset['sentryComponent']) {\n        return currentElem.dataset['sentryComponent'];\n      }\n      if (currentElem.dataset['sentryElement']) {\n        return currentElem.dataset['sentryElement'];\n      }\n    }\n\n    currentElem = currentElem.parentNode;\n  }\n\n  return null;\n}\n"], "names": [], "mappings": ";;;;;;;;;AAGA,MAAM,MAAA,GAAS,8PAAW;AAE1B,MAAM,yBAAA,GAA4B,EAAE;AAMpC;;;;;CAKA,GACO,SAAS,gBAAgB,CAC9B,IAAI,EACJ,OAAO,GAAiE,CAAA,CAAE;IAE1E,IAAI,CAAC,IAAI,EAAE;QACT,OAAO,WAAW;IACtB;IAEA,kBAAA;IACA,8DAAA;IACA,oFAAA;IACA,kDAAA;IACE,IAAI;QACF,IAAI,WAAY,GAAE,IAAK;QACvB,MAAM,mBAAoB,GAAE,CAAC;QAC7B,MAAM,GAAA,GAAM,EAAE;QACd,IAAI,MAAO,GAAE,CAAC;QACd,IAAI,GAAI,GAAE,CAAC;QACX,MAAM,SAAU,GAAE,KAAK;QACvB,MAAM,SAAA,GAAY,SAAS,CAAC,MAAM;QAClC,IAAI,OAAO;QACX,MAAM,QAAA,GAAW,KAAK,CAAC,OAAO,CAAC,OAAO,CAAA,GAAI,OAAA,GAAU,OAAO,CAAC,QAAQ;QACpE,MAAM,eAAgB,GAAE,AAAC,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,KAAK,OAAO,CAAC,eAAe,IAAK,yBAAyB;QAEzG,MAAO,WAAY,IAAG,MAAM,EAAG,GAAE,mBAAmB,CAAE;YACpD,UAAU,oBAAoB,CAAC,WAAW,EAAE,QAAQ,CAAC;YAC3D,cAAA;YACA,kCAAA;YACA,2EAAA;YACA,yDAAA;YACM,IAAI,OAAA,KAAY,MAAA,IAAW,MAAA,GAAS,CAAA,IAAK,GAAA,GAAM,GAAG,CAAC,MAAO,GAAE,SAAU,GAAE,OAAO,CAAC,MAAO,IAAG,eAAe,CAAC,CAAE;gBAC1G;YACR;YAEM,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC;YAEjB,GAAI,IAAG,OAAO,CAAC,MAAM;YACrB,WAAY,GAAE,WAAW,CAAC,UAAU;QAC1C;QAEI,OAAO,GAAG,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC;IACxC,CAAI,CAAA,OAAO,GAAG,EAAE;QACZ,OAAO,WAAW;IACtB;AACA;AAEA;;;;CAIA,GACA,SAAS,oBAAoB,CAAC,EAAE,EAAW,QAAQ,EAAqB;IACtE,MAAM,IAAK,GAAE;IAOb,MAAM,GAAA,GAAM,EAAE;IAEd,IAAI,CAAC,IAAI,EAAE,OAAO,EAAE;QAClB,OAAO,EAAE;IACb;IAEA,0CAAA;IACE,IAAI,MAAM,CAAC,WAAW,EAAE;QAC1B,6FAAA;QACI,IAAI,IAAK,YAAW,eAAe,IAAI,CAAC,OAAO,EAAE;YAC/C,IAAI,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,EAAE;gBACnC,OAAO,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC;YAC9C;YACM,IAAI,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,EAAE;gBACjC,OAAO,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC;YAC5C;QACA;IACA;IAEE,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;IAEtC,uFAAA;IACE,MAAM,YAAA,GAAe,QAAQ,EAAE,SAC3B,QAAQ,CAAC,MAAM,EAAC,OAAA,GAAW,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,OAAA,IAAW;YAAC,OAAO;YAAE,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC;SAAC,IAC3G,IAAI;IAER,IAAI,YAAY,EAAE,MAAM,EAAE;QACxB,YAAY,CAAC,OAAO,EAAC,eAAe;YAClC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACzD,CAAK,CAAC;IACN,OAAS;QACL,IAAI,IAAI,CAAC,EAAE,EAAE;YACX,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC,CAAA,CAAA;QACA;QAEA,MAAA,SAAA,GAAA,IAAA,CAAA,SAAA;QACA,IAAA,SAAA,kPAAA,WAAA,EAAA,SAAA,CAAA,EAAA;YACA,MAAA,OAAA,GAAA,SAAA,CAAA,KAAA,CAAA,KAAA,CAAA;YACA,KAAA,MAAA,CAAA,IAAA,OAAA,CAAA;gBACA,GAAA,CAAA,IAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA;YACA;QACA;IACA;IACA,MAAA,YAAA,GAAA;QAAA,YAAA;QAAA,MAAA;QAAA,MAAA;QAAA,OAAA;QAAA,KAAA;KAAA;IACA,KAAA,MAAA,CAAA,IAAA,YAAA,CAAA;QACA,MAAA,IAAA,GAAA,IAAA,CAAA,YAAA,CAAA,CAAA,CAAA;QACA,IAAA,IAAA,EAAA;YACA,GAAA,CAAA,IAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,EAAA,IAAA,CAAA,EAAA,CAAA,CAAA;QACA;IACA;IAEA,OAAA,GAAA,CAAA,IAAA,CAAA,EAAA,CAAA;AACA;AAEA;;CAEA,GACA,SAAA,eAAA,GAAA;IACA,IAAA;QACA,OAAA,MAAA,CAAA,QAAA,CAAA,QAAA,CAAA,IAAA;IACA,CAAA,CAAA,OAAA,EAAA,EAAA;QACA,OAAA,EAAA;IACA;AACA;AAEA;;;;;;CAMA,GACA,SAAA,gBAAA,CAAA,IAAA,EAAA;IACA,0CAAA;IACA,IAAA,CAAA,MAAA,CAAA,WAAA,EAAA;QACA,OAAA,IAAA;IACA;IAEA,IAAA,WAAA,GAAA,IAAA;IACA,MAAA,mBAAA,GAAA,CAAA;IACA,IAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,mBAAA,EAAA,CAAA,EAAA,CAAA;QACA,IAAA,CAAA,WAAA,EAAA;YACA,OAAA,IAAA;QACA;QAEA,IAAA,WAAA,YAAA,WAAA,EAAA;YACA,IAAA,WAAA,CAAA,OAAA,CAAA,iBAAA,CAAA,EAAA;gBACA,OAAA,WAAA,CAAA,OAAA,CAAA,iBAAA,CAAA;YACA;YACA,IAAA,WAAA,CAAA,OAAA,CAAA,eAAA,CAAA,EAAA;gBACA,OAAA,WAAA,CAAA,OAAA,CAAA,eAAA,CAAA;YACA;QACA;QAEA,WAAA,GAAA,WAAA,CAAA,UAAA;IACA;IAEA,OAAA,IAAA;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 451, "column": 0}, "map": {"version": 3, "file": "logger.js", "sources": ["file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/%40sentry%2Bcore%409.22.0/node_modules/%40sentry/core/src/utils-hoist/logger.ts"], "sourcesContent": ["import { getGlobalSingleton } from '../carrier';\nimport type { ConsoleLevel } from '../types-hoist/instrument';\nimport { DEBUG_BUILD } from './../debug-build';\nimport { GLOBAL_OBJ } from './worldwide';\n\n/** Prefix for logging strings */\nconst PREFIX = 'Sentry Logger ';\n\nexport const CONSOLE_LEVELS: readonly ConsoleLevel[] = [\n  'debug',\n  'info',\n  'warn',\n  'error',\n  'log',\n  'assert',\n  'trace',\n] as const;\n\ntype LoggerMethod = (...args: unknown[]) => void;\ntype LoggerConsoleMethods = Record<ConsoleLevel, LoggerMethod>;\n\n/** This may be mutated by the console instrumentation. */\nexport const originalConsoleMethods: {\n  [key in ConsoleLevel]?: (...args: unknown[]) => void;\n} = {};\n\n/** A Sentry Logger instance. */\nexport interface Logger extends LoggerConsoleMethods {\n  disable(): void;\n  enable(): void;\n  isEnabled(): boolean;\n}\n\n/**\n * Temporarily disable sentry console instrumentations.\n *\n * @param callback The function to run against the original `console` messages\n * @returns The results of the callback\n */\nexport function consoleSandbox<T>(callback: () => T): T {\n  if (!('console' in GLOBAL_OBJ)) {\n    return callback();\n  }\n\n  const console = GLOBAL_OBJ.console as Console;\n  const wrappedFuncs: Partial<LoggerConsoleMethods> = {};\n\n  const wrappedLevels = Object.keys(originalConsoleMethods) as ConsoleLevel[];\n\n  // Restore all wrapped console methods\n  wrappedLevels.forEach(level => {\n    const originalConsoleMethod = originalConsoleMethods[level] as LoggerMethod;\n    wrappedFuncs[level] = console[level] as LoggerMethod | undefined;\n    console[level] = originalConsoleMethod;\n  });\n\n  try {\n    return callback();\n  } finally {\n    // Revert restoration to wrapped state\n    wrappedLevels.forEach(level => {\n      console[level] = wrappedFuncs[level] as LoggerMethod;\n    });\n  }\n}\n\nfunction makeLogger(): Logger {\n  let enabled = false;\n  const logger: Partial<Logger> = {\n    enable: () => {\n      enabled = true;\n    },\n    disable: () => {\n      enabled = false;\n    },\n    isEnabled: () => enabled,\n  };\n\n  if (DEBUG_BUILD) {\n    CONSOLE_LEVELS.forEach(name => {\n      logger[name] = (...args: Parameters<(typeof GLOBAL_OBJ.console)[typeof name]>) => {\n        if (enabled) {\n          consoleSandbox(() => {\n            GLOBAL_OBJ.console[name](`${PREFIX}[${name}]:`, ...args);\n          });\n        }\n      };\n    });\n  } else {\n    CONSOLE_LEVELS.forEach(name => {\n      logger[name] = () => undefined;\n    });\n  }\n\n  return logger as Logger;\n}\n\n/**\n * This is a logger singleton which either logs things or no-ops if logging is not enabled.\n * The logger is a singleton on the carrier, to ensure that a consistent logger is used throughout the SDK.\n */\nexport const logger = getGlobalSingleton('logger', makeLogger);\n"], "names": [], "mappings": ";;;;;;;;;;;;AAKA,+BAAA,GACA,MAAM,MAAA,GAAS,gBAAgB;AAExB,MAAM,cAAc,GAA4B;IACrD,OAAO;IACP,MAAM;IACN,MAAM;IACN,OAAO;IACP,KAAK;IACL,QAAQ;IACR,OAAO;CACP;AAKF,wDAAA,SACa,yBAET,CAAA;AAEJ,8BAAA,GAOA;;;;;CAKA,GACO,SAAS,cAAc,CAAI,QAAQ,EAAc;IACtD,IAAI,CAAA,CAAE,8PAAa,aAAU,CAAC,EAAE;QAC9B,OAAO,QAAQ,EAAE;IACrB;IAEE,MAAM,OAAA,oPAAU,aAAU,CAAC,OAAQ;IACnC,MAAM,YAAY,GAAkC,CAAA,CAAE;IAEtD,MAAM,gBAAgB,MAAM,CAAC,IAAI,CAAC,sBAAsB,CAAE;IAE5D,sCAAA;IACE,aAAa,CAAC,OAAO,EAAC,SAAS;QAC7B,MAAM,qBAAsB,GAAE,sBAAsB,CAAC,KAAK,CAAE;QAC5D,YAAY,CAAC,KAAK,CAAA,GAAI,OAAO,CAAC,KAAK,CAAE;QACrC,OAAO,CAAC,KAAK,CAAA,GAAI,qBAAqB;IAC1C,CAAG,CAAC;IAEF,IAAI;QACF,OAAO,QAAQ,EAAE;IACrB,SAAY;QACZ,sCAAA;QACI,aAAa,CAAC,OAAO,EAAC,SAAS;YAC7B,OAAO,CAAC,KAAK,CAAA,GAAI,YAAY,CAAC,KAAK,CAAE;QAC3C,CAAK,CAAC;IACN;AACA;AAEA,SAAS,UAAU,GAAW;IAC5B,IAAI,OAAQ,GAAE,KAAK;IACnB,MAAM,MAAM,GAAoB;QAC9B,MAAM,EAAE,MAAM;YACZ,OAAA,GAAU,IAAI;QACpB,CAAK;QACD,OAAO,EAAE,MAAM;YACb,OAAA,GAAU,KAAK;QACrB,CAAK;QACD,SAAS,EAAE,IAAM,OAAO;IAC5B,CAAG;IAED,wOAAI,cAAW,EAAE;QACf,cAAc,CAAC,OAAO,EAAC,QAAQ;YAC7B,MAAM,CAAC,IAAI,CAAA,GAAI,CAAC,GAAG,IAAI,KAA2D;gBAChF,IAAI,OAAO,EAAE;oBACX,cAAc,CAAC,MAAM;yQACnB,aAAU,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAA,MAAA,CAAA,CAAA,EAAA,IAAA,CAAA,EAAA,CAAA,EAAA,GAAA,IAAA,CAAA;oBACA,CAAA,CAAA;gBACA;YACA,CAAA;QACA,CAAA,CAAA;IACA,CAAA,MAAA;QACA,cAAA,CAAA,OAAA,EAAA,IAAA,IAAA;YACA,MAAA,CAAA,IAAA,CAAA,GAAA,IAAA,SAAA;QACA,CAAA,CAAA;IACA;IAEA,OAAA,MAAA;AACA;AAEA;;;CAGA,GACA,MAAA,MAAA,oOAAA,qBAAA,EAAA,QAAA,EAAA,UAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 541, "column": 0}, "map": {"version": 3, "file": "string.js", "sources": ["file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/%40sentry%2Bcore%409.22.0/node_modules/%40sentry/core/src/utils-hoist/string.ts"], "sourcesContent": ["import { isRegExp, isString, isVueViewModel } from './is';\n\nexport { escapeStringForRegex } from './vendor/escapeStringForRegex';\n\n/**\n * Truncates given string to the maximum characters count\n *\n * @param str An object that contains serializable values\n * @param max Maximum number of characters in truncated string (0 = unlimited)\n * @returns string Encoded\n */\nexport function truncate(str: string, max: number = 0): string {\n  if (typeof str !== 'string' || max === 0) {\n    return str;\n  }\n  return str.length <= max ? str : `${str.slice(0, max)}...`;\n}\n\n/**\n * This is basically just `trim_line` from\n * https://github.com/getsentry/sentry/blob/master/src/sentry/lang/javascript/processor.py#L67\n *\n * @param str An object that contains serializable values\n * @param max Maximum number of characters in truncated string\n * @returns string Encoded\n */\nexport function snipLine(line: string, colno: number): string {\n  let newLine = line;\n  const lineLength = newLine.length;\n  if (lineLength <= 150) {\n    return newLine;\n  }\n  if (colno > lineLength) {\n    // eslint-disable-next-line no-param-reassign\n    colno = lineLength;\n  }\n\n  let start = Math.max(colno - 60, 0);\n  if (start < 5) {\n    start = 0;\n  }\n\n  let end = Math.min(start + 140, lineLength);\n  if (end > lineLength - 5) {\n    end = lineLength;\n  }\n  if (end === lineLength) {\n    start = Math.max(end - 140, 0);\n  }\n\n  newLine = newLine.slice(start, end);\n  if (start > 0) {\n    newLine = `'{snip} ${newLine}`;\n  }\n  if (end < lineLength) {\n    newLine += ' {snip}';\n  }\n\n  return newLine;\n}\n\n/**\n * Join values in array\n * @param input array of values to be joined together\n * @param delimiter string to be placed in-between values\n * @returns Joined values\n */\nexport function safeJoin(input: unknown[], delimiter?: string): string {\n  if (!Array.isArray(input)) {\n    return '';\n  }\n\n  const output = [];\n  // eslint-disable-next-line @typescript-eslint/prefer-for-of\n  for (let i = 0; i < input.length; i++) {\n    const value = input[i];\n    try {\n      // This is a hack to fix a Vue3-specific bug that causes an infinite loop of\n      // console warnings. This happens when a Vue template is rendered with\n      // an undeclared variable, which we try to stringify, ultimately causing\n      // Vue to issue another warning which repeats indefinitely.\n      // see: https://github.com/getsentry/sentry-javascript/pull/8981\n      if (isVueViewModel(value)) {\n        output.push('[VueViewModel]');\n      } else {\n        output.push(String(value));\n      }\n    } catch (e) {\n      output.push('[value cannot be serialized]');\n    }\n  }\n\n  return output.join(delimiter);\n}\n\n/**\n * Checks if the given value matches a regex or string\n *\n * @param value The string to test\n * @param pattern Either a regex or a string against which `value` will be matched\n * @param requireExactStringMatch If true, `value` must match `pattern` exactly. If false, `value` will match\n * `pattern` if it contains `pattern`. Only applies to string-type patterns.\n */\nexport function isMatchingPattern(\n  value: string,\n  pattern: RegExp | string,\n  requireExactStringMatch: boolean = false,\n): boolean {\n  if (!isString(value)) {\n    return false;\n  }\n\n  if (isRegExp(pattern)) {\n    return pattern.test(value);\n  }\n  if (isString(pattern)) {\n    return requireExactStringMatch ? value === pattern : value.includes(pattern);\n  }\n\n  return false;\n}\n\n/**\n * Test the given string against an array of strings and regexes. By default, string matching is done on a\n * substring-inclusion basis rather than a strict equality basis\n *\n * @param testString The string to test\n * @param patterns The patterns against which to test the string\n * @param requireExactStringMatch If true, `testString` must match one of the given string patterns exactly in order to\n * count. If false, `testString` will match a string pattern if it contains that pattern.\n * @returns\n */\nexport function stringMatchesSomePattern(\n  testString: string,\n  patterns: Array<string | RegExp> = [],\n  requireExactStringMatch: boolean = false,\n): boolean {\n  return patterns.some(pattern => isMatchingPattern(testString, pattern, requireExactStringMatch));\n}\n"], "names": [], "mappings": ";;;;;;;;;AAIA;;;;;;CAMA,GACO,SAAS,QAAQ,CAAC,GAAG,EAAU,GAAG,GAAW,CAAC,EAAU;IAC7D,IAAI,OAAO,GAAI,KAAI,YAAY,GAAA,KAAQ,CAAC,EAAE;QACxC,OAAO,GAAG;IACd;IACE,OAAO,GAAG,CAAC,MAAO,IAAG,MAAM,GAAA,GAAM,CAAC,EAAA,GAAA,CAAA,KAAA,CAAA,CAAA,EAAA,GAAA,CAAA,CAAA,GAAA,CAAA;AACA;AAEA;;;;;;;CAOA,GACA,SAAA,QAAA,CAAA,IAAA,EAAA,KAAA,EAAA;IACA,IAAA,OAAA,GAAA,IAAA;IACA,MAAA,UAAA,GAAA,OAAA,CAAA,MAAA;IACA,IAAA,UAAA,IAAA,GAAA,EAAA;QACA,OAAA,OAAA;IACA;IACA,IAAA,KAAA,GAAA,UAAA,EAAA;QACA,6CAAA;QACA,KAAA,GAAA,UAAA;IACA;IAEA,IAAA,KAAA,GAAA,IAAA,CAAA,GAAA,CAAA,KAAA,GAAA,EAAA,EAAA,CAAA,CAAA;IACA,IAAA,KAAA,GAAA,CAAA,EAAA;QACA,KAAA,GAAA,CAAA;IACA;IAEA,IAAA,GAAA,GAAA,IAAA,CAAA,GAAA,CAAA,KAAA,GAAA,GAAA,EAAA,UAAA,CAAA;IACA,IAAA,GAAA,GAAA,UAAA,GAAA,CAAA,EAAA;QACA,GAAA,GAAA,UAAA;IACA;IACA,IAAA,GAAA,KAAA,UAAA,EAAA;QACA,KAAA,GAAA,IAAA,CAAA,GAAA,CAAA,GAAA,GAAA,GAAA,EAAA,CAAA,CAAA;IACA;IAEA,OAAA,GAAA,OAAA,CAAA,KAAA,CAAA,KAAA,EAAA,GAAA,CAAA;IACA,IAAA,KAAA,GAAA,CAAA,EAAA;QACA,OAAA,GAAA,CAAA,QAAA,EAAA,OAAA,CAAA,CAAA;IACA;IACA,IAAA,GAAA,GAAA,UAAA,EAAA;QACA,OAAA,IAAA,SAAA;IACA;IAEA,OAAA,OAAA;AACA;AAEA;;;;;CAKA,GACA,SAAA,QAAA,CAAA,KAAA,EAAA,SAAA,EAAA;IACA,IAAA,CAAA,KAAA,CAAA,OAAA,CAAA,KAAA,CAAA,EAAA;QACA,OAAA,EAAA;IACA;IAEA,MAAA,MAAA,GAAA,EAAA;IACA,4DAAA;IACA,IAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA,GAAA,KAAA,CAAA,MAAA,EAAA,CAAA,EAAA,CAAA;QACA,MAAA,KAAA,GAAA,KAAA,CAAA,CAAA,CAAA;QACA,IAAA;YACA,4EAAA;YACA,sEAAA;YACA,wEAAA;YACA,2DAAA;YACA,gEAAA;YACA,QAAA,2PAAA,EAAA,KAAA,CAAA,EAAA;gBACA,MAAA,CAAA,IAAA,CAAA,gBAAA,CAAA;YACA,CAAA,MAAA;gBACA,MAAA,CAAA,IAAA,CAAA,MAAA,CAAA,KAAA,CAAA,CAAA;YACA;QACA,CAAA,CAAA,OAAA,CAAA,EAAA;YACA,MAAA,CAAA,IAAA,CAAA,8BAAA,CAAA;QACA;IACA;IAEA,OAAA,MAAA,CAAA,IAAA,CAAA,SAAA,CAAA;AACA;AAEA;;;;;;;CAOA,GACA,SAAA,iBAAA,CACA,KAAA,EACA,OAAA,EACA,uBAAA,GAAA,KAAA;IAEA,IAAA,+OAAA,WAAA,EAAA,KAAA,CAAA,EAAA;QACA,OAAA,KAAA;IACA;IAEA,kPAAA,WAAA,EAAA,OAAA,CAAA,EAAA;QACA,OAAA,OAAA,CAAA,IAAA,CAAA,KAAA,CAAA;IACA;IACA,kPAAA,WAAA,EAAA,OAAA,CAAA,EAAA;QACA,OAAA,uBAAA,GAAA,KAAA,KAAA,OAAA,GAAA,KAAA,CAAA,QAAA,CAAA,OAAA,CAAA;IACA;IAEA,OAAA,KAAA;AACA;AAEA;;;;;;;;;CASA,GACA,SAAA,wBAAA,CACA,UAAA,EACA,QAAA,GAAA,EAAA,EACA,uBAAA,GAAA,KAAA;IAEA,OAAA,QAAA,CAAA,IAAA,EAAA,OAAA,GAAA,iBAAA,CAAA,UAAA,EAAA,OAAA,EAAA,uBAAA,CAAA,CAAA;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 668, "column": 0}, "map": {"version": 3, "file": "object.js", "sources": ["file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/%40sentry%2Bcore%409.22.0/node_modules/%40sentry/core/src/utils-hoist/object.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-explicit-any */\nimport type { WrappedFunction } from '../types-hoist/wrappedfunction';\nimport { DEBUG_BUILD } from './../debug-build';\nimport { htmlTreeAsString } from './browser';\nimport { isElement, isError, isEvent, isInstanceOf, isPrimitive } from './is';\nimport { logger } from './logger';\nimport { truncate } from './string';\n\n/**\n * Replace a method in an object with a wrapped version of itself.\n *\n * If the method on the passed object is not a function, the wrapper will not be applied.\n *\n * @param source An object that contains a method to be wrapped.\n * @param name The name of the method to be wrapped.\n * @param replacementFactory A higher-order function that takes the original version of the given method and returns a\n * wrapped version. Note: The function returned by `replacementFactory` needs to be a non-arrow function, in order to\n * preserve the correct value of `this`, and the original method must be called using `origMethod.call(this, <other\n * args>)` or `origMethod.apply(this, [<other args>])` (rather than being called directly), again to preserve `this`.\n * @returns void\n */\nexport function fill(source: { [key: string]: any }, name: string, replacementFactory: (...args: any[]) => any): void {\n  if (!(name in source)) {\n    return;\n  }\n\n  // explicitly casting to unknown because we don't know the type of the method initially at all\n  const original = source[name] as unknown;\n\n  if (typeof original !== 'function') {\n    return;\n  }\n\n  const wrapped = replacementFactory(original) as WrappedFunction;\n\n  // Make sure it's a function first, as we need to attach an empty prototype for `defineProperties` to work\n  // otherwise it'll throw \"TypeError: Object.defineProperties called on non-object\"\n  if (typeof wrapped === 'function') {\n    markFunctionWrapped(wrapped, original);\n  }\n\n  try {\n    source[name] = wrapped;\n  } catch {\n    DEBUG_BUILD && logger.log(`Failed to replace method \"${name}\" in object`, source);\n  }\n}\n\n/**\n * Defines a non-enumerable property on the given object.\n *\n * @param obj The object on which to set the property\n * @param name The name of the property to be set\n * @param value The value to which to set the property\n */\nexport function addNonEnumerableProperty(obj: object, name: string, value: unknown): void {\n  try {\n    Object.defineProperty(obj, name, {\n      // enumerable: false, // the default, so we can save on bundle size by not explicitly setting it\n      value: value,\n      writable: true,\n      configurable: true,\n    });\n  } catch (o_O) {\n    DEBUG_BUILD && logger.log(`Failed to add non-enumerable property \"${name}\" to object`, obj);\n  }\n}\n\n/**\n * Remembers the original function on the wrapped function and\n * patches up the prototype.\n *\n * @param wrapped the wrapper function\n * @param original the original function that gets wrapped\n */\nexport function markFunctionWrapped(wrapped: WrappedFunction, original: WrappedFunction): void {\n  try {\n    const proto = original.prototype || {};\n    wrapped.prototype = original.prototype = proto;\n    addNonEnumerableProperty(wrapped, '__sentry_original__', original);\n  } catch (o_O) {} // eslint-disable-line no-empty\n}\n\n/**\n * This extracts the original function if available.  See\n * `markFunctionWrapped` for more information.\n *\n * @param func the function to unwrap\n * @returns the unwrapped version of the function if available.\n */\n// eslint-disable-next-line @typescript-eslint/ban-types\nexport function getOriginalFunction<T extends Function>(func: WrappedFunction<T>): T | undefined {\n  return func.__sentry_original__;\n}\n\n/**\n * Transforms any `Error` or `Event` into a plain object with all of their enumerable properties, and some of their\n * non-enumerable properties attached.\n *\n * @param value Initial source that we have to transform in order for it to be usable by the serializer\n * @returns An Event or Error turned into an object - or the value argument itself, when value is neither an Event nor\n *  an Error.\n */\nexport function convertToPlainObject<V>(value: V):\n  | {\n      [ownProps: string]: unknown;\n      type: string;\n      target: string;\n      currentTarget: string;\n      detail?: unknown;\n    }\n  | {\n      [ownProps: string]: unknown;\n      message: string;\n      name: string;\n      stack?: string;\n    }\n  | V {\n  if (isError(value)) {\n    return {\n      message: value.message,\n      name: value.name,\n      stack: value.stack,\n      ...getOwnProperties(value),\n    };\n  } else if (isEvent(value)) {\n    const newObj: {\n      [ownProps: string]: unknown;\n      type: string;\n      target: string;\n      currentTarget: string;\n      detail?: unknown;\n    } = {\n      type: value.type,\n      target: serializeEventTarget(value.target),\n      currentTarget: serializeEventTarget(value.currentTarget),\n      ...getOwnProperties(value),\n    };\n\n    if (typeof CustomEvent !== 'undefined' && isInstanceOf(value, CustomEvent)) {\n      newObj.detail = value.detail;\n    }\n\n    return newObj;\n  } else {\n    return value;\n  }\n}\n\n/** Creates a string representation of the target of an `Event` object */\nfunction serializeEventTarget(target: unknown): string {\n  try {\n    return isElement(target) ? htmlTreeAsString(target) : Object.prototype.toString.call(target);\n  } catch (_oO) {\n    return '<unknown>';\n  }\n}\n\n/** Filters out all but an object's own properties */\nfunction getOwnProperties(obj: unknown): { [key: string]: unknown } {\n  if (typeof obj === 'object' && obj !== null) {\n    const extractedProps: { [key: string]: unknown } = {};\n    for (const property in obj) {\n      if (Object.prototype.hasOwnProperty.call(obj, property)) {\n        extractedProps[property] = (obj as Record<string, unknown>)[property];\n      }\n    }\n    return extractedProps;\n  } else {\n    return {};\n  }\n}\n\n/**\n * Given any captured exception, extract its keys and create a sorted\n * and truncated list that will be used inside the event message.\n * eg. `Non-error exception captured with keys: foo, bar, baz`\n */\nexport function extractExceptionKeysForMessage(exception: Record<string, unknown>, maxLength: number = 40): string {\n  const keys = Object.keys(convertToPlainObject(exception));\n  keys.sort();\n\n  const firstKey = keys[0];\n\n  if (!firstKey) {\n    return '[object has no keys]';\n  }\n\n  if (firstKey.length >= maxLength) {\n    return truncate(firstKey, maxLength);\n  }\n\n  for (let includedKeys = keys.length; includedKeys > 0; includedKeys--) {\n    const serialized = keys.slice(0, includedKeys).join(', ');\n    if (serialized.length > maxLength) {\n      continue;\n    }\n    if (includedKeys === keys.length) {\n      return serialized;\n    }\n    return truncate(serialized, maxLength);\n  }\n\n  return '';\n}\n\n/**\n * Given any object, return a new object having removed all fields whose value was `undefined`.\n * Works recursively on objects and arrays.\n *\n * Attention: This function keeps circular references in the returned object.\n *\n * @deprecated This function is no longer used by the SDK and will be removed in a future major version.\n */\nexport function dropUndefinedKeys<T>(inputValue: T): T {\n  // This map keeps track of what already visited nodes map to.\n  // Our Set - based memoBuilder doesn't work here because we want to the output object to have the same circular\n  // references as the input object.\n  const memoizationMap = new Map<unknown, unknown>();\n\n  // This function just proxies `_dropUndefinedKeys` to keep the `memoBuilder` out of this function's API\n  return _dropUndefinedKeys(inputValue, memoizationMap);\n}\n\nfunction _dropUndefinedKeys<T>(inputValue: T, memoizationMap: Map<unknown, unknown>): T {\n  // Early return for primitive values\n  if (inputValue === null || typeof inputValue !== 'object') {\n    return inputValue;\n  }\n\n  // Check memo map first for all object types\n  const memoVal = memoizationMap.get(inputValue);\n  if (memoVal !== undefined) {\n    return memoVal as T;\n  }\n\n  // handle arrays\n  if (Array.isArray(inputValue)) {\n    const returnValue: unknown[] = [];\n    // Store mapping to handle circular references\n    memoizationMap.set(inputValue, returnValue);\n\n    inputValue.forEach(value => {\n      returnValue.push(_dropUndefinedKeys(value, memoizationMap));\n    });\n\n    return returnValue as unknown as T;\n  }\n\n  if (isPojo(inputValue)) {\n    const returnValue: { [key: string]: unknown } = {};\n    // Store mapping to handle circular references\n    memoizationMap.set(inputValue, returnValue);\n\n    const keys = Object.keys(inputValue);\n\n    keys.forEach(key => {\n      const val = inputValue[key];\n      if (val !== undefined) {\n        returnValue[key] = _dropUndefinedKeys(val, memoizationMap);\n      }\n    });\n\n    return returnValue as T;\n  }\n\n  // For other object types, return as is\n  return inputValue;\n}\n\nfunction isPojo(input: unknown): input is Record<string, unknown> {\n  // Plain objects have Object as constructor or no constructor\n  const constructor = (input as object).constructor;\n  return constructor === Object || constructor === undefined;\n}\n\n/**\n * Ensure that something is an object.\n *\n * Turns `undefined` and `null` into `String`s and all other primitives into instances of their respective wrapper\n * classes (String, Boolean, Number, etc.). Acts as the identity function on non-primitives.\n *\n * @param wat The subject of the objectification\n * @returns A version of `wat` which can safely be used with `Object` class methods\n */\nexport function objectify(wat: unknown): typeof Object {\n  let objectified;\n  switch (true) {\n    // this will catch both undefined and null\n    case wat == undefined:\n      objectified = new String(wat);\n      break;\n\n    // Though symbols and bigints do have wrapper classes (`Symbol` and `BigInt`, respectively), for whatever reason\n    // those classes don't have constructors which can be used with the `new` keyword. We therefore need to cast each as\n    // an object in order to wrap it.\n    case typeof wat === 'symbol' || typeof wat === 'bigint':\n      objectified = Object(wat);\n      break;\n\n    // this will catch the remaining primitives: `String`, `Number`, and `Boolean`\n    case isPrimitive(wat):\n      // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n      objectified = new (wat as any).constructor(wat);\n      break;\n\n    // by process of elimination, at this point we know that `wat` must already be an object\n    default:\n      objectified = wat;\n      break;\n  }\n  return objectified;\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;AAQA;;;;;;;;;;;;CAYA,GACO,SAAS,IAAI,CAAC,MAAM,EAA0B,IAAI,EAAU,kBAAkB,EAAiC;IACpH,IAAI,CAAA,CAAE,QAAQ,MAAM,CAAC,EAAE;QACrB;IACJ;IAEA,8FAAA;IACE,MAAM,QAAS,GAAE,MAAM,CAAC,IAAI,CAAE;IAE9B,IAAI,OAAO,QAAS,KAAI,UAAU,EAAE;QAClC;IACJ;IAEE,MAAM,OAAQ,GAAE,kBAAkB,CAAC,QAAQ,CAAE;IAE/C,0GAAA;IACA,kFAAA;IACE,IAAI,OAAO,OAAQ,KAAI,UAAU,EAAE;QACjC,mBAAmB,CAAC,OAAO,EAAE,QAAQ,CAAC;IAC1C;IAEE,IAAI;QACF,MAAM,CAAC,IAAI,CAAA,GAAI,OAAO;IAC1B,EAAI,OAAM;4OACN,cAAY,IAAG,uPAAM,CAAC,GAAG,CAAC,CAAC,0BAA0B,EAAE,IAAI,CAAC,WAAW,CAAC,EAAE,MAAM,CAAC;IACrF;AACA;AAEA;;;;;;CAMA,GACO,SAAS,wBAAwB,CAAC,GAAG,EAAU,IAAI,EAAU,KAAK,EAAiB;IACxF,IAAI;QACF,MAAM,CAAC,cAAc,CAAC,GAAG,EAAE,IAAI,EAAE;YACrC,gGAAA;YACM,KAAK,EAAE,KAAK;YACZ,QAAQ,EAAE,IAAI;YACd,YAAY,EAAE,IAAI;QACxB,CAAK,CAAC;IACN,CAAI,CAAA,OAAO,GAAG,EAAE;4OACZ,cAAY,kPAAG,SAAM,CAAC,GAAG,CAAC,CAAC,uCAAuC,EAAE,IAAI,CAAC,WAAW,CAAC,EAAE,GAAG,CAAC;IAC/F;AACA;AAEA;;;;;;CAMA,GACO,SAAS,mBAAmB,CAAC,OAAO,EAAmB,QAAQ,EAAyB;IAC7F,IAAI;QACF,MAAM,QAAQ,QAAQ,CAAC,SAAU,IAAG,CAAA,CAAE;QACtC,OAAO,CAAC,SAAU,GAAE,QAAQ,CAAC,SAAA,GAAY,KAAK;QAC9C,wBAAwB,CAAC,OAAO,EAAE,qBAAqB,EAAE,QAAQ,CAAC;IACtE,CAAI,CAAA,OAAO,GAAG,EAAE,CAAA,CAAC,CAAA,+BAAA;AACjB;AAEA;;;;;;CAMA,GACA,wDAAA;AACO,SAAS,mBAAmB,CAAqB,IAAI,EAAqC;IAC/F,OAAO,IAAI,CAAC,mBAAmB;AACjC;AAEA;;;;;;;CAOA,GACO,SAAS,oBAAoB,CAAI,KAAK;IAe3C,kPAAI,UAAA,AAAO,EAAC,KAAK,CAAC,EAAE;QAClB,OAAO;YACL,OAAO,EAAE,KAAK,CAAC,OAAO;YACtB,IAAI,EAAE,KAAK,CAAC,IAAI;YAChB,KAAK,EAAE,KAAK,CAAC,KAAK;YAClB,GAAG,gBAAgB,CAAC,KAAK,CAAC;QAChC,CAAK;IACL,CAAE,MAAO,KAAI,uPAAA,AAAO,EAAC,KAAK,CAAC,EAAE;QACzB,MAAM,SAMF;YACF,IAAI,EAAE,KAAK,CAAC,IAAI;YAChB,MAAM,EAAE,oBAAoB,CAAC,KAAK,CAAC,MAAM,CAAC;YAC1C,aAAa,EAAE,oBAAoB,CAAC,KAAK,CAAC,aAAa,CAAC;YACxD,GAAG,gBAAgB,CAAC,KAAK,CAAC;QAChC,CAAK;QAED,IAAI,OAAO,WAAA,KAAgB,WAAA,kPAAe,eAAA,AAAY,EAAC,KAAK,EAAE,WAAW,CAAC,EAAE;YAC1E,MAAM,CAAC,MAAA,GAAS,KAAK,CAAC,MAAM;QAClC;QAEI,OAAO,MAAM;IACjB,OAAS;QACL,OAAO,KAAK;IAChB;AACA;AAEA,uEAAA,GACA,SAAS,oBAAoB,CAAC,MAAM,EAAmB;IACrD,IAAI;QACF,qPAAO,YAAA,AAAS,EAAC,MAAM,QAAI,kQAAA,AAAgB,EAAC,MAAM,CAAA,GAAI,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC;IAChG,CAAI,CAAA,OAAO,GAAG,EAAE;QACZ,OAAO,WAAW;IACtB;AACA;AAEA,mDAAA,GACA,SAAS,gBAAgB,CAAC,GAAG,EAAuC;IAClE,IAAI,OAAO,GAAI,KAAI,YAAY,GAAA,KAAQ,IAAI,EAAE;QAC3C,MAAM,cAAc,GAA+B,CAAA,CAAE;QACrD,IAAK,MAAM,QAAS,IAAG,GAAG,CAAE;YAC1B,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,EAAE,QAAQ,CAAC,EAAE;gBACvD,cAAc,CAAC,QAAQ,CAAE,GAAE,AAAC,GAAI,CAA4B,QAAQ,CAAC;YAC7E;QACA;QACI,OAAO,cAAc;IACzB,OAAS;QACL,OAAO,CAAA,CAAE;IACb;AACA;AAEA;;;;CAIA,GACO,SAAS,8BAA8B,CAAC,SAAS,EAA2B,SAAS,GAAW,EAAE,EAAU;IACjH,MAAM,IAAK,GAAE,MAAM,CAAC,IAAI,CAAC,oBAAoB,CAAC,SAAS,CAAC,CAAC;IACzD,IAAI,CAAC,IAAI,EAAE;IAEX,MAAM,QAAS,GAAE,IAAI,CAAC,CAAC,CAAC;IAExB,IAAI,CAAC,QAAQ,EAAE;QACb,OAAO,sBAAsB;IACjC;IAEE,IAAI,QAAQ,CAAC,MAAO,IAAG,SAAS,EAAE;QAChC,yPAAO,WAAA,AAAQ,EAAC,QAAQ,EAAE,SAAS,CAAC;IACxC;IAEE,IAAK,IAAI,YAAA,GAAe,IAAI,CAAC,MAAM,EAAE,eAAe,CAAC,EAAE,YAAY,EAAE,CAAE;QACrE,MAAM,UAAA,GAAa,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;QACzD,IAAI,UAAU,CAAC,MAAO,GAAE,SAAS,EAAE;YACjC;QACN;QACI,IAAI,YAAA,KAAiB,IAAI,CAAC,MAAM,EAAE;YAChC,OAAO,UAAU;QACvB;QACI,yPAAO,WAAA,AAAQ,EAAC,UAAU,EAAE,SAAS,CAAC;IAC1C;IAEE,OAAO,EAAE;AACX;AAEA;;;;;;;CAOA,GACO,SAAS,iBAAiB,CAAI,UAAU,EAAQ;IACvD,6DAAA;IACA,+GAAA;IACA,kCAAA;IACE,MAAM,cAAe,GAAE,IAAI,GAAG,EAAoB;IAEpD,uGAAA;IACE,OAAO,kBAAkB,CAAC,UAAU,EAAE,cAAc,CAAC;AACvD;AAEA,SAAS,kBAAkB,CAAI,UAAU,EAAK,cAAc,EAA4B;IACxF,oCAAA;IACE,IAAI,UAAA,KAAe,IAAA,IAAQ,OAAO,UAAA,KAAe,QAAQ,EAAE;QACzD,OAAO,UAAU;IACrB;IAEA,4CAAA;IACE,MAAM,UAAU,cAAc,CAAC,GAAG,CAAC,UAAU,CAAC;IAC9C,IAAI,OAAQ,KAAI,SAAS,EAAE;QACzB,OAAO,OAAQ;IACnB;IAEA,gBAAA;IACE,IAAI,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE;QAC7B,MAAM,WAAW,GAAc,EAAE;QACrC,8CAAA;QACI,cAAc,CAAC,GAAG,CAAC,UAAU,EAAE,WAAW,CAAC;QAE3C,UAAU,CAAC,OAAO,EAAC,SAAS;YAC1B,WAAW,CAAC,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,cAAc,CAAC,CAAC;QACjE,CAAK,CAAC;QAEF,OAAO,WAAY;IACvB;IAEE,IAAI,MAAM,CAAC,UAAU,CAAC,EAAE;QACtB,MAAM,WAAW,GAA+B,CAAA,CAAE;QACtD,8CAAA;QACI,cAAc,CAAC,GAAG,CAAC,UAAU,EAAE,WAAW,CAAC;QAE3C,MAAM,OAAO,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;QAEpC,IAAI,CAAC,OAAO,CAAC,OAAO;YAClB,MAAM,GAAI,GAAE,UAAU,CAAC,GAAG,CAAC;YAC3B,IAAI,GAAI,KAAI,SAAS,EAAE;gBACrB,WAAW,CAAC,GAAG,CAAE,GAAE,kBAAkB,CAAC,GAAG,EAAE,cAAc,CAAC;YAClE;QACA,CAAK,CAAC;QAEF,OAAO,WAAY;IACvB;IAEA,uCAAA;IACE,OAAO,UAAU;AACnB;AAEA,SAAS,MAAM,CAAC,KAAK,EAA6C;IAClE,6DAAA;IACE,MAAM,WAAY,GAAE,AAAC,KAAM,CAAW,WAAW;IACjD,OAAO,WAAY,KAAI,UAAU,WAAA,KAAgB,SAAS;AAC5D;AAEA;;;;;;;;CAQA,GACO,SAAS,SAAS,CAAC,GAAG,EAA0B;IACrD,IAAI,WAAW;IACf,OAAQ,IAAI;QACd,0CAAA;QACI,KAAK,GAAI,IAAG,SAAS;YACnB,cAAc,IAAI,MAAM,CAAC,GAAG,CAAC;YAC7B;QAEN,gHAAA;QACA,oHAAA;QACA,iCAAA;QACI,KAAK,OAAO,GAAI,KAAI,YAAY,OAAO,GAAI,KAAI,QAAQ;YACrD,WAAY,GAAE,MAAM,CAAC,GAAG,CAAC;YACzB;QAEN,8EAAA;QACI,mPAAK,cAAA,AAAW,EAAC,GAAG,CAAC;YACzB,sEAAA;YACM,WAAA,GAAc,IAAI,AAAC,GAAA,CAAY,WAAW,CAAC,GAAG,CAAC;YAC/C;QAEN,wFAAA;QACI;YACE,WAAA,GAAc,GAAG;YACjB;IACN;IACE,OAAO,WAAW;AACpB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 935, "column": 0}, "map": {"version": 3, "file": "misc.js", "sources": ["file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/%40sentry%2Bcore%409.22.0/node_modules/%40sentry/core/src/utils-hoist/misc.ts"], "sourcesContent": ["import type { Event } from '../types-hoist/event';\nimport type { Exception } from '../types-hoist/exception';\nimport type { Mechanism } from '../types-hoist/mechanism';\nimport type { StackFrame } from '../types-hoist/stackframe';\nimport { addNonEnumerableProperty } from './object';\nimport { snipLine } from './string';\nimport { GLOBAL_OBJ } from './worldwide';\n\ninterface CryptoInternal {\n  getRandomValues(array: Uint8Array): Uint8Array;\n  randomUUID?(): string;\n}\n\n/** An interface for common properties on global */\ninterface CryptoGlobal {\n  msCrypto?: CryptoInternal;\n  crypto?: CryptoInternal;\n}\n\nfunction getCrypto(): CryptoInternal | undefined {\n  const gbl = GLOBAL_OBJ as typeof GLOBAL_OBJ & CryptoGlobal;\n  return gbl.crypto || gbl.msCrypto;\n}\n\n/**\n * UUID4 generator\n * @param crypto Object that provides the crypto API.\n * @returns string Generated UUID4.\n */\nexport function uuid4(crypto = getCrypto()): string {\n  let getRandomByte = (): number => Math.random() * 16;\n  try {\n    if (crypto?.randomUUID) {\n      return crypto.randomUUID().replace(/-/g, '');\n    }\n    if (crypto?.getRandomValues) {\n      getRandomByte = () => {\n        // crypto.getRandomValues might return undefined instead of the typed array\n        // in old Chromium versions (e.g. 23.0.1235.0 (151422))\n        // However, `typedArray` is still filled in-place.\n        // @see https://developer.mozilla.org/en-US/docs/Web/API/Crypto/getRandomValues#typedarray\n        const typedArray = new Uint8Array(1);\n        crypto.getRandomValues(typedArray);\n        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n        return typedArray[0]!;\n      };\n    }\n  } catch (_) {\n    // some runtimes can crash invoking crypto\n    // https://github.com/getsentry/sentry-javascript/issues/8935\n  }\n\n  // http://stackoverflow.com/questions/105034/how-to-create-a-guid-uuid-in-javascript/2117523#2117523\n  // Concatenating the following numbers as strings results in '10000000100040008000100000000000'\n  return (([1e7] as unknown as string) + 1e3 + 4e3 + 8e3 + 1e11).replace(/[018]/g, c =>\n    // eslint-disable-next-line no-bitwise\n    ((c as unknown as number) ^ ((getRandomByte() & 15) >> ((c as unknown as number) / 4))).toString(16),\n  );\n}\n\nfunction getFirstException(event: Event): Exception | undefined {\n  return event.exception?.values?.[0];\n}\n\n/**\n * Extracts either message or type+value from an event that can be used for user-facing logs\n * @returns event's description\n */\nexport function getEventDescription(event: Event): string {\n  const { message, event_id: eventId } = event;\n  if (message) {\n    return message;\n  }\n\n  const firstException = getFirstException(event);\n  if (firstException) {\n    if (firstException.type && firstException.value) {\n      return `${firstException.type}: ${firstException.value}`;\n    }\n    return firstException.type || firstException.value || eventId || '<unknown>';\n  }\n  return eventId || '<unknown>';\n}\n\n/**\n * Adds exception values, type and value to an synthetic Exception.\n * @param event The event to modify.\n * @param value Value of the exception.\n * @param type Type of the exception.\n * @hidden\n */\nexport function addExceptionTypeValue(event: Event, value?: string, type?: string): void {\n  const exception = (event.exception = event.exception || {});\n  const values = (exception.values = exception.values || []);\n  const firstException = (values[0] = values[0] || {});\n  if (!firstException.value) {\n    firstException.value = value || '';\n  }\n  if (!firstException.type) {\n    firstException.type = type || 'Error';\n  }\n}\n\n/**\n * Adds exception mechanism data to a given event. Uses defaults if the second parameter is not passed.\n *\n * @param event The event to modify.\n * @param newMechanism Mechanism data to add to the event.\n * @hidden\n */\nexport function addExceptionMechanism(event: Event, newMechanism?: Partial<Mechanism>): void {\n  const firstException = getFirstException(event);\n  if (!firstException) {\n    return;\n  }\n\n  const defaultMechanism = { type: 'generic', handled: true };\n  const currentMechanism = firstException.mechanism;\n  firstException.mechanism = { ...defaultMechanism, ...currentMechanism, ...newMechanism };\n\n  if (newMechanism && 'data' in newMechanism) {\n    const mergedData = { ...currentMechanism?.data, ...newMechanism.data };\n    firstException.mechanism.data = mergedData;\n  }\n}\n\n// https://semver.org/#is-there-a-suggested-regular-expression-regex-to-check-a-semver-string\nconst SEMVER_REGEXP =\n  /^(0|[1-9]\\d*)\\.(0|[1-9]\\d*)\\.(0|[1-9]\\d*)(?:-((?:0|[1-9]\\d*|\\d*[a-zA-Z-][0-9a-zA-Z-]*)(?:\\.(?:0|[1-9]\\d*|\\d*[a-zA-Z-][0-9a-zA-Z-]*))*))?(?:\\+([0-9a-zA-Z-]+(?:\\.[0-9a-zA-Z-]+)*))?$/;\n\n/**\n * Represents Semantic Versioning object\n */\ninterface SemVer {\n  major?: number;\n  minor?: number;\n  patch?: number;\n  prerelease?: string;\n  buildmetadata?: string;\n}\n\nfunction _parseInt(input: string | undefined): number {\n  return parseInt(input || '', 10);\n}\n\n/**\n * Parses input into a SemVer interface\n * @param input string representation of a semver version\n */\nexport function parseSemver(input: string): SemVer {\n  const match = input.match(SEMVER_REGEXP) || [];\n  const major = _parseInt(match[1]);\n  const minor = _parseInt(match[2]);\n  const patch = _parseInt(match[3]);\n  return {\n    buildmetadata: match[5],\n    major: isNaN(major) ? undefined : major,\n    minor: isNaN(minor) ? undefined : minor,\n    patch: isNaN(patch) ? undefined : patch,\n    prerelease: match[4],\n  };\n}\n\n/**\n * This function adds context (pre/post/line) lines to the provided frame\n *\n * @param lines string[] containing all lines\n * @param frame StackFrame that will be mutated\n * @param linesOfContext number of context lines we want to add pre/post\n */\nexport function addContextToFrame(lines: string[], frame: StackFrame, linesOfContext: number = 5): void {\n  // When there is no line number in the frame, attaching context is nonsensical and will even break grouping\n  if (frame.lineno === undefined) {\n    return;\n  }\n\n  const maxLines = lines.length;\n  const sourceLine = Math.max(Math.min(maxLines - 1, frame.lineno - 1), 0);\n\n  frame.pre_context = lines\n    .slice(Math.max(0, sourceLine - linesOfContext), sourceLine)\n    .map((line: string) => snipLine(line, 0));\n\n  // We guard here to ensure this is not larger than the existing number of lines\n  const lineIndex = Math.min(maxLines - 1, sourceLine);\n\n  // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n  frame.context_line = snipLine(lines[lineIndex]!, frame.colno || 0);\n\n  frame.post_context = lines\n    .slice(Math.min(sourceLine + 1, maxLines), sourceLine + 1 + linesOfContext)\n    .map((line: string) => snipLine(line, 0));\n}\n\n/**\n * Checks whether or not we've already captured the given exception (note: not an identical exception - the very object\n * in question), and marks it captured if not.\n *\n * This is useful because it's possible for an error to get captured by more than one mechanism. After we intercept and\n * record an error, we rethrow it (assuming we've intercepted it before it's reached the top-level global handlers), so\n * that we don't interfere with whatever effects the error might have had were the SDK not there. At that point, because\n * the error has been rethrown, it's possible for it to bubble up to some other code we've instrumented. If it's not\n * caught after that, it will bubble all the way up to the global handlers (which of course we also instrument). This\n * function helps us ensure that even if we encounter the same error more than once, we only record it the first time we\n * see it.\n *\n * Note: It will ignore primitives (always return `false` and not mark them as seen), as properties can't be set on\n * them. {@link: Object.objectify} can be used on exceptions to convert any that are primitives into their equivalent\n * object wrapper forms so that this check will always work. However, because we need to flag the exact object which\n * will get rethrown, and because that rethrowing happens outside of the event processing pipeline, the objectification\n * must be done before the exception captured.\n *\n * @param A thrown exception to check or flag as having been seen\n * @returns `true` if the exception has already been captured, `false` if not (with the side effect of marking it seen)\n */\nexport function checkOrSetAlreadyCaught(exception: unknown): boolean {\n  if (isAlreadyCaptured(exception)) {\n    return true;\n  }\n\n  try {\n    // set it this way rather than by assignment so that it's not ennumerable and therefore isn't recorded by the\n    // `ExtraErrorData` integration\n    addNonEnumerableProperty(exception as { [key: string]: unknown }, '__sentry_captured__', true);\n  } catch (err) {\n    // `exception` is a primitive, so we can't mark it seen\n  }\n\n  return false;\n}\n\nfunction isAlreadyCaptured(exception: unknown): boolean | void {\n  try {\n    return (exception as { __sentry_captured__?: boolean }).__sentry_captured__;\n  } catch {} // eslint-disable-line no-empty\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAmBA,SAAS,SAAS,GAA+B;IAC/C,MAAM,GAAI,oPAAE,aAAW;IACvB,OAAO,GAAG,CAAC,MAAA,IAAU,GAAG,CAAC,QAAQ;AACnC;AAEA;;;;CAIA,GACO,SAAS,KAAK,CAAC,MAAA,GAAS,SAAS,EAAE,EAAU;IAClD,IAAI,aAAc,GAAE,IAAc,IAAI,CAAC,MAAM,EAAG,GAAE,EAAE;IACpD,IAAI;QACF,IAAI,MAAM,EAAE,UAAU,EAAE;YACtB,OAAO,MAAM,CAAC,UAAU,EAAE,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC;QAClD;QACI,IAAI,MAAM,EAAE,eAAe,EAAE;YAC3B,aAAc,GAAE,MAAM;gBAC5B,2EAAA;gBACA,uDAAA;gBACA,kDAAA;gBACA,0FAAA;gBACQ,MAAM,UAAW,GAAE,IAAI,UAAU,CAAC,CAAC,CAAC;gBACpC,MAAM,CAAC,eAAe,CAAC,UAAU,CAAC;gBAC1C,oEAAA;gBACQ,OAAO,UAAU,CAAC,CAAC,CAAC;YAC5B,CAAO;QACP;IACA,CAAI,CAAA,OAAO,CAAC,EAAE;IACd,0CAAA;IACA,6DAAA;IACA;IAEA,oGAAA;IACA,+FAAA;IACE,OAAO,CAAC,AAAC;QAAC,GAAG;KAAE,GAAwB,GAAI,GAAE,MAAM,GAAA,GAAM,IAAI,EAAE,OAAO,CAAC,QAAQ,GAAE,CAAE,GACrF,sCAAA;QACI,CAAC,AAAC,CAAA,GAA2B,CAAC,aAAa,EAAG,GAAE,EAAE,KAAM,AAAC,CAAA,GAA0B,CAAC,AAAE,CAAD,CAAG,QAAQ,CAAC,EAAE,CAAC;AAExG;AAEA,SAAS,iBAAiB,CAAC,KAAK,EAAgC;IAC9D,OAAO,KAAK,CAAC,SAAS,EAAE,MAAM,EAAA,CAAG,CAAC,CAAC;AACrC;AAEA;;;CAGA,GACO,SAAS,mBAAmB,CAAC,KAAK,EAAiB;IACxD,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,OAAA,EAAU,GAAE,KAAK;IAC5C,IAAI,OAAO,EAAE;QACX,OAAO,OAAO;IAClB;IAEE,MAAM,cAAe,GAAE,iBAAiB,CAAC,KAAK,CAAC;IAC/C,IAAI,cAAc,EAAE;QAClB,IAAI,cAAc,CAAC,IAAA,IAAQ,cAAc,CAAC,KAAK,EAAE;YAC/C,OAAO,CAAC,EAAA,cAAA,CAAA,IAAA,CAAA,EAAA,EAAA,cAAA,CAAA,KAAA,CAAA,CAAA;QACA;QACA,OAAA,cAAA,CAAA,IAAA,IAAA,cAAA,CAAA,KAAA,IAAA,OAAA,IAAA,WAAA;IACA;IACA,OAAA,OAAA,IAAA,WAAA;AACA;AAEA;;;;;;CAMA,GACA,SAAA,qBAAA,CAAA,KAAA,EAAA,KAAA,EAAA,IAAA,EAAA;IACA,MAAA,SAAA,GAAA,KAAA,CAAA,SAAA,GAAA,KAAA,CAAA,SAAA,IAAA,CAAA,CAAA,CAAA;IACA,MAAA,MAAA,GAAA,SAAA,CAAA,MAAA,GAAA,SAAA,CAAA,MAAA,IAAA,EAAA,CAAA;IACA,MAAA,cAAA,GAAA,MAAA,CAAA,CAAA,CAAA,GAAA,MAAA,CAAA,CAAA,CAAA,IAAA,CAAA,CAAA,CAAA;IACA,IAAA,CAAA,cAAA,CAAA,KAAA,EAAA;QACA,cAAA,CAAA,KAAA,GAAA,KAAA,IAAA,EAAA;IACA;IACA,IAAA,CAAA,cAAA,CAAA,IAAA,EAAA;QACA,cAAA,CAAA,IAAA,GAAA,IAAA,IAAA,OAAA;IACA;AACA;AAEA;;;;;;CAMA,GACA,SAAA,qBAAA,CAAA,KAAA,EAAA,YAAA,EAAA;IACA,MAAA,cAAA,GAAA,iBAAA,CAAA,KAAA,CAAA;IACA,IAAA,CAAA,cAAA,EAAA;QACA;IACA;IAEA,MAAA,gBAAA,GAAA;QAAA,IAAA,EAAA,SAAA;QAAA,OAAA,EAAA,IAAA;IAAA,CAAA;IACA,MAAA,gBAAA,GAAA,cAAA,CAAA,SAAA;IACA,cAAA,CAAA,SAAA,GAAA;QAAA,GAAA,gBAAA;QAAA,GAAA,gBAAA;QAAA,GAAA,YAAA;IAAA,CAAA;IAEA,IAAA,YAAA,IAAA,MAAA,IAAA,YAAA,EAAA;QACA,MAAA,UAAA,GAAA;YAAA,GAAA,gBAAA,EAAA,IAAA;YAAA,GAAA,YAAA,CAAA,IAAA;QAAA,CAAA;QACA,cAAA,CAAA,SAAA,CAAA,IAAA,GAAA,UAAA;IACA;AACA;AAEA,6FAAA;AACA,MAAA,aAAA,GACA,qLAAA;AAEA;;CAEA,GASA,SAAA,SAAA,CAAA,KAAA,EAAA;IACA,OAAA,QAAA,CAAA,KAAA,IAAA,EAAA,EAAA,EAAA,CAAA;AACA;AAEA;;;CAGA,GACA,SAAA,WAAA,CAAA,KAAA,EAAA;IACA,MAAA,KAAA,GAAA,KAAA,CAAA,KAAA,CAAA,aAAA,CAAA,IAAA,EAAA;IACA,MAAA,KAAA,GAAA,SAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA;IACA,MAAA,KAAA,GAAA,SAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA;IACA,MAAA,KAAA,GAAA,SAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA;IACA,OAAA;QACA,aAAA,EAAA,KAAA,CAAA,CAAA,CAAA;QACA,KAAA,EAAA,KAAA,CAAA,KAAA,CAAA,GAAA,SAAA,GAAA,KAAA;QACA,KAAA,EAAA,KAAA,CAAA,KAAA,CAAA,GAAA,SAAA,GAAA,KAAA;QACA,KAAA,EAAA,KAAA,CAAA,KAAA,CAAA,GAAA,SAAA,GAAA,KAAA;QACA,UAAA,EAAA,KAAA,CAAA,CAAA,CAAA;IACA,CAAA;AACA;AAEA;;;;;;CAMA,GACA,SAAA,iBAAA,CAAA,KAAA,EAAA,KAAA,EAAA,cAAA,GAAA,CAAA,EAAA;IACA,2GAAA;IACA,IAAA,KAAA,CAAA,MAAA,KAAA,SAAA,EAAA;QACA;IACA;IAEA,MAAA,QAAA,GAAA,KAAA,CAAA,MAAA;IACA,MAAA,UAAA,GAAA,IAAA,CAAA,GAAA,CAAA,IAAA,CAAA,GAAA,CAAA,QAAA,GAAA,CAAA,EAAA,KAAA,CAAA,MAAA,GAAA,CAAA,CAAA,EAAA,CAAA,CAAA;IAEA,KAAA,CAAA,WAAA,GAAA,MACA,KAAA,CAAA,IAAA,CAAA,GAAA,CAAA,CAAA,EAAA,UAAA,GAAA,cAAA,CAAA,EAAA,UAAA,EACA,GAAA,CAAA,CAAA,IAAA,qPAAA,WAAA,EAAA,IAAA,EAAA,CAAA,CAAA,CAAA;IAEA,+EAAA;IACA,MAAA,SAAA,GAAA,IAAA,CAAA,GAAA,CAAA,QAAA,GAAA,CAAA,EAAA,UAAA,CAAA;IAEA,oEAAA;IACA,KAAA,CAAA,YAAA,qPAAA,WAAA,EAAA,KAAA,CAAA,SAAA,CAAA,EAAA,KAAA,CAAA,KAAA,IAAA,CAAA,CAAA;IAEA,KAAA,CAAA,YAAA,GAAA,MACA,KAAA,CAAA,IAAA,CAAA,GAAA,CAAA,UAAA,GAAA,CAAA,EAAA,QAAA,CAAA,EAAA,UAAA,GAAA,CAAA,GAAA,cAAA,EACA,GAAA,CAAA,CAAA,IAAA,qPAAA,WAAA,EAAA,IAAA,EAAA,CAAA,CAAA,CAAA;AACA;AAEA;;;;;;;;;;;;;;;;;;;;CAoBA,GACA,SAAA,uBAAA,CAAA,SAAA,EAAA;IACA,IAAA,iBAAA,CAAA,SAAA,CAAA,EAAA;QACA,OAAA,IAAA;IACA;IAEA,IAAA;QACA,6GAAA;QACA,+BAAA;0PACA,2BAAA,EAAA,SAAA,EAAA,qBAAA,EAAA,IAAA,CAAA;IACA,CAAA,CAAA,OAAA,GAAA,EAAA;IACA,uDAAA;IACA;IAEA,OAAA,KAAA;AACA;AAEA,SAAA,iBAAA,CAAA,SAAA,EAAA;IACA,IAAA;QACA,OAAA,SAAA,CAAA,mBAAA;IACA,CAAA,CAAA,OAAA,CAAA,CAAA,CAAA,+BAAA;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1142, "column": 0}, "map": {"version": 3, "file": "time.js", "sources": ["file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/%40sentry%2Bcore%409.22.0/node_modules/%40sentry/core/src/utils-hoist/time.ts"], "sourcesContent": ["import { GLOBAL_OBJ } from './worldwide';\n\nconst ONE_SECOND_IN_MS = 1000;\n\n/**\n * A partial definition of the [Performance Web API]{@link https://developer.mozilla.org/en-US/docs/Web/API/Performance}\n * for accessing a high-resolution monotonic clock.\n */\ninterface Performance {\n  /**\n   * The millisecond timestamp at which measurement began, measured in Unix time.\n   */\n  timeOrigin: number;\n  /**\n   * Returns the current millisecond timestamp, where 0 represents the start of measurement.\n   */\n  now(): number;\n}\n\n/**\n * Returns a timestamp in seconds since the UNIX epoch using the Date API.\n */\nexport function dateTimestampInSeconds(): number {\n  return Date.now() / ONE_SECOND_IN_MS;\n}\n\n/**\n * Returns a wrapper around the native Performance API browser implementation, or undefined for browsers that do not\n * support the API.\n *\n * Wrapping the native API works around differences in behavior from different browsers.\n */\nfunction createUnixTimestampInSecondsFunc(): () => number {\n  const { performance } = GLOBAL_OBJ as typeof GLOBAL_OBJ & { performance?: Performance };\n  if (!performance?.now) {\n    return dateTimestampInSeconds;\n  }\n\n  // Some browser and environments don't have a timeOrigin, so we fallback to\n  // using Date.now() to compute the starting time.\n  const approxStartingTimeOrigin = Date.now() - performance.now();\n  const timeOrigin = performance.timeOrigin == undefined ? approxStartingTimeOrigin : performance.timeOrigin;\n\n  // performance.now() is a monotonic clock, which means it starts at 0 when the process begins. To get the current\n  // wall clock time (actual UNIX timestamp), we need to add the starting time origin and the current time elapsed.\n  //\n  // TODO: This does not account for the case where the monotonic clock that powers performance.now() drifts from the\n  // wall clock time, which causes the returned timestamp to be inaccurate. We should investigate how to detect and\n  // correct for this.\n  // See: https://github.com/getsentry/sentry-javascript/issues/2590\n  // See: https://github.com/mdn/content/issues/4713\n  // See: https://dev.to/noamr/when-a-millisecond-is-not-a-millisecond-3h6\n  return () => {\n    return (timeOrigin + performance.now()) / ONE_SECOND_IN_MS;\n  };\n}\n\n/**\n * Returns a timestamp in seconds since the UNIX epoch using either the Performance or Date APIs, depending on the\n * availability of the Performance API.\n *\n * BUG: Note that because of how browsers implement the Performance API, the clock might stop when the computer is\n * asleep. This creates a skew between `dateTimestampInSeconds` and `timestampInSeconds`. The\n * skew can grow to arbitrary amounts like days, weeks or months.\n * See https://github.com/getsentry/sentry-javascript/issues/2590.\n */\nexport const timestampInSeconds = createUnixTimestampInSecondsFunc();\n\n/**\n * Cached result of getBrowserTimeOrigin.\n */\nlet cachedTimeOrigin: [number | undefined, string] | undefined;\n\n/**\n * Gets the time origin and the mode used to determine it.\n */\nfunction getBrowserTimeOrigin(): [number | undefined, string] {\n  // Unfortunately browsers may report an inaccurate time origin data, through either performance.timeOrigin or\n  // performance.timing.navigationStart, which results in poor results in performance data. We only treat time origin\n  // data as reliable if they are within a reasonable threshold of the current time.\n\n  const { performance } = GLOBAL_OBJ as typeof GLOBAL_OBJ & Window;\n  if (!performance?.now) {\n    return [undefined, 'none'];\n  }\n\n  const threshold = 3600 * 1000;\n  const performanceNow = performance.now();\n  const dateNow = Date.now();\n\n  // if timeOrigin isn't available set delta to threshold so it isn't used\n  const timeOriginDelta = performance.timeOrigin\n    ? Math.abs(performance.timeOrigin + performanceNow - dateNow)\n    : threshold;\n  const timeOriginIsReliable = timeOriginDelta < threshold;\n\n  // While performance.timing.navigationStart is deprecated in favor of performance.timeOrigin, performance.timeOrigin\n  // is not as widely supported. Namely, performance.timeOrigin is undefined in Safari as of writing.\n  // Also as of writing, performance.timing is not available in Web Workers in mainstream browsers, so it is not always\n  // a valid fallback. In the absence of an initial time provided by the browser, fallback to the current time from the\n  // Date API.\n  // eslint-disable-next-line deprecation/deprecation\n  const navigationStart = performance.timing?.navigationStart;\n  const hasNavigationStart = typeof navigationStart === 'number';\n  // if navigationStart isn't available set delta to threshold so it isn't used\n  const navigationStartDelta = hasNavigationStart ? Math.abs(navigationStart + performanceNow - dateNow) : threshold;\n  const navigationStartIsReliable = navigationStartDelta < threshold;\n\n  if (timeOriginIsReliable || navigationStartIsReliable) {\n    // Use the more reliable time origin\n    if (timeOriginDelta <= navigationStartDelta) {\n      return [performance.timeOrigin, 'timeOrigin'];\n    } else {\n      return [navigationStart, 'navigationStart'];\n    }\n  }\n\n  // Either both timeOrigin and navigationStart are skewed or neither is available, fallback to Date.\n  return [dateNow, 'dateNow'];\n}\n\n/**\n * The number of milliseconds since the UNIX epoch. This value is only usable in a browser, and only when the\n * performance API is available.\n */\nexport function browserPerformanceTimeOrigin(): number | undefined {\n  if (!cachedTimeOrigin) {\n    cachedTimeOrigin = getBrowserTimeOrigin();\n  }\n\n  return cachedTimeOrigin[0];\n}\n"], "names": [], "mappings": ";;;;;;;AAEA,MAAM,gBAAA,GAAmB,IAAI;AAE7B;;;CAGA,GAYA;;CAEA,GACO,SAAS,sBAAsB,GAAW;IAC/C,OAAO,IAAI,CAAC,GAAG,EAAC,GAAI,gBAAgB;AACtC;AAEA;;;;;CAKA,GACA,SAAS,gCAAgC,GAAiB;IACxD,MAAM,EAAE,WAAY,EAAA,oPAAI,aAAW;IACnC,IAAI,CAAC,WAAW,EAAE,GAAG,EAAE;QACrB,OAAO,sBAAsB;IACjC;IAEA,2EAAA;IACA,iDAAA;IACE,MAAM,wBAAA,GAA2B,IAAI,CAAC,GAAG,EAAC,GAAI,WAAW,CAAC,GAAG,EAAE;IAC/D,MAAM,UAAA,GAAa,WAAW,CAAC,UAAA,IAAc,SAAA,GAAY,wBAAA,GAA2B,WAAW,CAAC,UAAU;IAE5G,iHAAA;IACA,iHAAA;IACA,EAAA;IACA,mHAAA;IACA,iHAAA;IACA,oBAAA;IACA,kEAAA;IACA,kDAAA;IACA,wEAAA;IACE,OAAO,MAAM;QACX,OAAO,CAAC,UAAA,GAAa,WAAW,CAAC,GAAG,EAAE,IAAI,gBAAgB;IAC9D,CAAG;AACH;AAEA;;;;;;;;CAQA,GACa,MAAA,kBAAA,GAAqB,gCAAgC;AAElE;;CAEA,GACA,IAAI,gBAAgB;AAEpB;;CAEA,GACA,SAAS,oBAAoB,GAAiC;IAC9D,6GAAA;IACA,mHAAA;IACA,kFAAA;IAEE,MAAM,EAAE,WAAY,EAAA,oPAAI,aAAW;IACnC,IAAI,CAAC,WAAW,EAAE,GAAG,EAAE;QACrB,OAAO;YAAC,SAAS;YAAE,MAAM;SAAC;IAC9B;IAEE,MAAM,SAAA,GAAY,IAAA,GAAO,IAAI;IAC7B,MAAM,cAAe,GAAE,WAAW,CAAC,GAAG,EAAE;IACxC,MAAM,OAAQ,GAAE,IAAI,CAAC,GAAG,EAAE;IAE5B,wEAAA;IACE,MAAM,eAAA,GAAkB,WAAW,CAAC,UAAA,GAChC,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,UAAW,GAAE,cAAe,GAAE,OAAO,IAC1D,SAAS;IACb,MAAM,oBAAA,GAAuB,eAAA,GAAkB,SAAS;IAE1D,oHAAA;IACA,mGAAA;IACA,qHAAA;IACA,qHAAA;IACA,YAAA;IACA,mDAAA;IACE,MAAM,eAAgB,GAAE,WAAW,CAAC,MAAM,EAAE,eAAe;IAC3D,MAAM,kBAAmB,GAAE,OAAO,eAAA,KAAoB,QAAQ;IAChE,6EAAA;IACE,MAAM,oBAAqB,GAAE,kBAAmB,GAAE,IAAI,CAAC,GAAG,CAAC,eAAA,GAAkB,cAAe,GAAE,OAAO,CAAA,GAAI,SAAS;IAClH,MAAM,yBAAA,GAA4B,oBAAA,GAAuB,SAAS;IAElE,IAAI,oBAAqB,IAAG,yBAAyB,EAAE;QACzD,oCAAA;QACI,IAAI,eAAgB,IAAG,oBAAoB,EAAE;YAC3C,OAAO;gBAAC,WAAW,CAAC,UAAU;gBAAE,YAAY;aAAC;QACnD,OAAW;YACL,OAAO;gBAAC,eAAe;gBAAE,iBAAiB;aAAC;QACjD;IACA;IAEA,mGAAA;IACE,OAAO;QAAC,OAAO;QAAE,SAAS;KAAC;AAC7B;AAEA;;;CAGA,GACO,SAAS,4BAA4B,GAAuB;IACjE,IAAI,CAAC,gBAAgB,EAAE;QACrB,gBAAiB,GAAE,oBAAoB,EAAE;IAC7C;IAEE,OAAO,gBAAgB,CAAC,CAAC,CAAC;AAC5B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1264, "column": 0}, "map": {"version": 3, "file": "session.js", "sources": ["file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/%40sentry%2Bcore%409.22.0/node_modules/%40sentry/core/src/session.ts"], "sourcesContent": ["import type { SerializedSession, Session, SessionContext, SessionStatus } from './types-hoist/session';\nimport { uuid4 } from './utils-hoist/misc';\nimport { timestampInSeconds } from './utils-hoist/time';\n\n/**\n * Creates a new `Session` object by setting certain default parameters. If optional @param context\n * is passed, the passed properties are applied to the session object.\n *\n * @param context (optional) additional properties to be applied to the returned session object\n *\n * @returns a new `Session` object\n */\nexport function makeSession(context?: Omit<SessionContext, 'started' | 'status'>): Session {\n  // Both timestamp and started are in seconds since the UNIX epoch.\n  const startingTime = timestampInSeconds();\n\n  const session: Session = {\n    sid: uuid4(),\n    init: true,\n    timestamp: startingTime,\n    started: startingTime,\n    duration: 0,\n    status: 'ok',\n    errors: 0,\n    ignoreDuration: false,\n    toJSON: () => sessionToJSON(session),\n  };\n\n  if (context) {\n    updateSession(session, context);\n  }\n\n  return session;\n}\n\n/**\n * Updates a session object with the properties passed in the context.\n *\n * Note that this function mutates the passed object and returns void.\n * (Had to do this instead of returning a new and updated session because closing and sending a session\n * makes an update to the session after it was passed to the sending logic.\n * @see Client.captureSession )\n *\n * @param session the `Session` to update\n * @param context the `SessionContext` holding the properties that should be updated in @param session\n */\n// eslint-disable-next-line complexity\nexport function updateSession(session: Session, context: SessionContext = {}): void {\n  if (context.user) {\n    if (!session.ipAddress && context.user.ip_address) {\n      session.ipAddress = context.user.ip_address;\n    }\n\n    if (!session.did && !context.did) {\n      session.did = context.user.id || context.user.email || context.user.username;\n    }\n  }\n\n  session.timestamp = context.timestamp || timestampInSeconds();\n\n  if (context.abnormal_mechanism) {\n    session.abnormal_mechanism = context.abnormal_mechanism;\n  }\n\n  if (context.ignoreDuration) {\n    session.ignoreDuration = context.ignoreDuration;\n  }\n  if (context.sid) {\n    // Good enough uuid validation. — Kamil\n    session.sid = context.sid.length === 32 ? context.sid : uuid4();\n  }\n  if (context.init !== undefined) {\n    session.init = context.init;\n  }\n  if (!session.did && context.did) {\n    session.did = `${context.did}`;\n  }\n  if (typeof context.started === 'number') {\n    session.started = context.started;\n  }\n  if (session.ignoreDuration) {\n    session.duration = undefined;\n  } else if (typeof context.duration === 'number') {\n    session.duration = context.duration;\n  } else {\n    const duration = session.timestamp - session.started;\n    session.duration = duration >= 0 ? duration : 0;\n  }\n  if (context.release) {\n    session.release = context.release;\n  }\n  if (context.environment) {\n    session.environment = context.environment;\n  }\n  if (!session.ipAddress && context.ipAddress) {\n    session.ipAddress = context.ipAddress;\n  }\n  if (!session.userAgent && context.userAgent) {\n    session.userAgent = context.userAgent;\n  }\n  if (typeof context.errors === 'number') {\n    session.errors = context.errors;\n  }\n  if (context.status) {\n    session.status = context.status;\n  }\n}\n\n/**\n * Closes a session by setting its status and updating the session object with it.\n * Internally calls `updateSession` to update the passed session object.\n *\n * Note that this function mutates the passed session (@see updateSession for explanation).\n *\n * @param session the `Session` object to be closed\n * @param status the `SessionStatus` with which the session was closed. If you don't pass a status,\n *               this function will keep the previously set status, unless it was `'ok'` in which case\n *               it is changed to `'exited'`.\n */\nexport function closeSession(session: Session, status?: Exclude<SessionStatus, 'ok'>): void {\n  let context = {};\n  if (status) {\n    context = { status };\n  } else if (session.status === 'ok') {\n    context = { status: 'exited' };\n  }\n\n  updateSession(session, context);\n}\n\n/**\n * Serializes a passed session object to a JSON object with a slightly different structure.\n * This is necessary because the Sentry backend requires a slightly different schema of a session\n * than the one the JS SDKs use internally.\n *\n * @param session the session to be converted\n *\n * @returns a JSON object of the passed session\n */\nfunction sessionToJSON(session: Session): SerializedSession {\n  return {\n    sid: `${session.sid}`,\n    init: session.init,\n    // Make sure that sec is converted to ms for date constructor\n    started: new Date(session.started * 1000).toISOString(),\n    timestamp: new Date(session.timestamp * 1000).toISOString(),\n    status: session.status,\n    errors: session.errors,\n    did: typeof session.did === 'number' || typeof session.did === 'string' ? `${session.did}` : undefined,\n    duration: session.duration,\n    abnormal_mechanism: session.abnormal_mechanism,\n    attrs: {\n      release: session.release,\n      environment: session.environment,\n      ip_address: session.ipAddress,\n      user_agent: session.userAgent,\n    },\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;;AAIA;;;;;;;CAOA,GACO,SAAS,WAAW,CAAC,OAAO,EAAwD;IAC3F,kEAAA;IACE,MAAM,YAAA,mPAAe,qBAAA,AAAkB,EAAE;IAEzC,MAAM,OAAO,GAAY;QACvB,GAAG,kPAAE,QAAA,AAAK,EAAE;QACZ,IAAI,EAAE,IAAI;QACV,SAAS,EAAE,YAAY;QACvB,OAAO,EAAE,YAAY;QACrB,QAAQ,EAAE,CAAC;QACX,MAAM,EAAE,IAAI;QACZ,MAAM,EAAE,CAAC;QACT,cAAc,EAAE,KAAK;QACrB,MAAM,EAAE,IAAM,aAAa,CAAC,OAAO,CAAC;IACxC,CAAG;IAED,IAAI,OAAO,EAAE;QACX,aAAa,CAAC,OAAO,EAAE,OAAO,CAAC;IACnC;IAEE,OAAO,OAAO;AAChB;AAEA;;;;;;;;;;CAUA,GACA,sCAAA;AACO,SAAS,aAAa,CAAC,OAAO,EAAW,OAAO,GAAmB,CAAA,CAAE,EAAQ;IAClF,IAAI,OAAO,CAAC,IAAI,EAAE;QAChB,IAAI,CAAC,OAAO,CAAC,SAAA,IAAa,OAAO,CAAC,IAAI,CAAC,UAAU,EAAE;YACjD,OAAO,CAAC,SAAU,GAAE,OAAO,CAAC,IAAI,CAAC,UAAU;QACjD;QAEI,IAAI,CAAC,OAAO,CAAC,GAAI,IAAG,CAAC,OAAO,CAAC,GAAG,EAAE;YAChC,OAAO,CAAC,GAAI,GAAE,OAAO,CAAC,IAAI,CAAC,EAAG,IAAG,OAAO,CAAC,IAAI,CAAC,KAAM,IAAG,OAAO,CAAC,IAAI,CAAC,QAAQ;QAClF;IACA;IAEE,OAAO,CAAC,SAAA,GAAY,OAAO,CAAC,SAAU,oPAAG,qBAAA,AAAkB,EAAE;IAE7D,IAAI,OAAO,CAAC,kBAAkB,EAAE;QAC9B,OAAO,CAAC,kBAAA,GAAqB,OAAO,CAAC,kBAAkB;IAC3D;IAEE,IAAI,OAAO,CAAC,cAAc,EAAE;QAC1B,OAAO,CAAC,cAAA,GAAiB,OAAO,CAAC,cAAc;IACnD;IACE,IAAI,OAAO,CAAC,GAAG,EAAE;QACnB,uCAAA;QACI,OAAO,CAAC,GAAA,GAAM,OAAO,CAAC,GAAG,CAAC,MAAA,KAAW,EAAA,GAAK,OAAO,CAAC,GAAA,GAAM,wPAAA,AAAK,EAAE;IACnE;IACE,IAAI,OAAO,CAAC,IAAK,KAAI,SAAS,EAAE;QAC9B,OAAO,CAAC,IAAA,GAAO,OAAO,CAAC,IAAI;IAC/B;IACE,IAAI,CAAC,OAAO,CAAC,GAAA,IAAO,OAAO,CAAC,GAAG,EAAE;QAC/B,OAAO,CAAC,GAAI,GAAE,CAAC,EAAA,OAAA,CAAA,GAAA,CAAA,CAAA;IACA;IACA,IAAA,OAAA,OAAA,CAAA,OAAA,KAAA,QAAA,EAAA;QACA,OAAA,CAAA,OAAA,GAAA,OAAA,CAAA,OAAA;IACA;IACA,IAAA,OAAA,CAAA,cAAA,EAAA;QACA,OAAA,CAAA,QAAA,GAAA,SAAA;IACA,CAAA,MAAA,IAAA,OAAA,OAAA,CAAA,QAAA,KAAA,QAAA,EAAA;QACA,OAAA,CAAA,QAAA,GAAA,OAAA,CAAA,QAAA;IACA,CAAA,MAAA;QACA,MAAA,QAAA,GAAA,OAAA,CAAA,SAAA,GAAA,OAAA,CAAA,OAAA;QACA,OAAA,CAAA,QAAA,GAAA,QAAA,IAAA,CAAA,GAAA,QAAA,GAAA,CAAA;IACA;IACA,IAAA,OAAA,CAAA,OAAA,EAAA;QACA,OAAA,CAAA,OAAA,GAAA,OAAA,CAAA,OAAA;IACA;IACA,IAAA,OAAA,CAAA,WAAA,EAAA;QACA,OAAA,CAAA,WAAA,GAAA,OAAA,CAAA,WAAA;IACA;IACA,IAAA,CAAA,OAAA,CAAA,SAAA,IAAA,OAAA,CAAA,SAAA,EAAA;QACA,OAAA,CAAA,SAAA,GAAA,OAAA,CAAA,SAAA;IACA;IACA,IAAA,CAAA,OAAA,CAAA,SAAA,IAAA,OAAA,CAAA,SAAA,EAAA;QACA,OAAA,CAAA,SAAA,GAAA,OAAA,CAAA,SAAA;IACA;IACA,IAAA,OAAA,OAAA,CAAA,MAAA,KAAA,QAAA,EAAA;QACA,OAAA,CAAA,MAAA,GAAA,OAAA,CAAA,MAAA;IACA;IACA,IAAA,OAAA,CAAA,MAAA,EAAA;QACA,OAAA,CAAA,MAAA,GAAA,OAAA,CAAA,MAAA;IACA;AACA;AAEA;;;;;;;;;;CAUA,GACA,SAAA,YAAA,CAAA,OAAA,EAAA,MAAA,EAAA;IACA,IAAA,OAAA,GAAA,CAAA,CAAA;IACA,IAAA,MAAA,EAAA;QACA,OAAA,GAAA;YAAA,MAAA;QAAA,CAAA;IACA,CAAA,MAAA,IAAA,OAAA,CAAA,MAAA,KAAA,IAAA,EAAA;QACA,OAAA,GAAA;YAAA,MAAA,EAAA,QAAA;QAAA,CAAA;IACA;IAEA,aAAA,CAAA,OAAA,EAAA,OAAA,CAAA;AACA;AAEA;;;;;;;;CAQA,GACA,SAAA,aAAA,CAAA,OAAA,EAAA;IACA,OAAA;QACA,GAAA,EAAA,CAAA,EAAA,OAAA,CAAA,GAAA,CAAA,CAAA;QACA,IAAA,EAAA,OAAA,CAAA,IAAA;QACA,6DAAA;QACA,OAAA,EAAA,IAAA,IAAA,CAAA,OAAA,CAAA,OAAA,GAAA,IAAA,CAAA,CAAA,WAAA,EAAA;QACA,SAAA,EAAA,IAAA,IAAA,CAAA,OAAA,CAAA,SAAA,GAAA,IAAA,CAAA,CAAA,WAAA,EAAA;QACA,MAAA,EAAA,OAAA,CAAA,MAAA;QACA,MAAA,EAAA,OAAA,CAAA,MAAA;QACA,GAAA,EAAA,OAAA,OAAA,CAAA,GAAA,KAAA,QAAA,IAAA,OAAA,OAAA,CAAA,GAAA,KAAA,QAAA,GAAA,CAAA,EAAA,OAAA,CAAA,GAAA,CAAA,CAAA,GAAA,SAAA;QACA,QAAA,EAAA,OAAA,CAAA,QAAA;QACA,kBAAA,EAAA,OAAA,CAAA,kBAAA;QACA,KAAA,EAAA;YACA,OAAA,EAAA,OAAA,CAAA,OAAA;YACA,WAAA,EAAA,OAAA,CAAA,WAAA;YACA,UAAA,EAAA,OAAA,CAAA,SAAA;YACA,UAAA,EAAA,OAAA,CAAA,SAAA;QACA,CAAA;IACA,CAAA;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1425, "column": 0}, "map": {"version": 3, "file": "merge.js", "sources": ["file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/%40sentry%2Bcore%409.22.0/node_modules/%40sentry/core/src/utils/merge.ts"], "sourcesContent": ["/**\n * Shallow merge two objects.\n * Does not mutate the passed in objects.\n * Undefined/empty values in the merge object will overwrite existing values.\n *\n * By default, this merges 2 levels deep.\n */\nexport function merge<T>(initialObj: T, mergeObj: T, levels = 2): T {\n  // If the merge value is not an object, or we have no merge levels left,\n  // we just set the value to the merge value\n  if (!mergeObj || typeof mergeObj !== 'object' || levels <= 0) {\n    return mergeObj;\n  }\n\n  // If the merge object is an empty object, and the initial object is not undefined, we return the initial object\n  if (initialObj && Object.keys(mergeObj).length === 0) {\n    return initialObj;\n  }\n\n  // Clone object\n  const output = { ...initialObj };\n\n  // Merge values into output, resursively\n  for (const key in mergeObj) {\n    if (Object.prototype.hasOwnProperty.call(mergeObj, key)) {\n      output[key] = merge(output[key], mergeObj[key], levels - 1);\n    }\n  }\n\n  return output;\n}\n"], "names": [], "mappings": "AAAA;;;;;;CAMA;;;AACO,SAAS,KAAK,CAAI,UAAU,EAAK,QAAQ,EAAK,MAAO,GAAE,CAAC,EAAK;IACpE,wEAAA;IACA,2CAAA;IACE,IAAI,CAAC,QAAA,IAAY,OAAO,QAAS,KAAI,QAAS,IAAG,MAAO,IAAG,CAAC,EAAE;QAC5D,OAAO,QAAQ;IACnB;IAEA,gHAAA;IACE,IAAI,UAAW,IAAG,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,MAAO,KAAI,CAAC,EAAE;QACpD,OAAO,UAAU;IACrB;IAEA,eAAA;IACE,MAAM,MAAO,GAAE;QAAE,GAAG,UAAA;IAAA,CAAY;IAElC,wCAAA;IACE,IAAK,MAAM,GAAI,IAAG,QAAQ,CAAE;QAC1B,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,QAAQ,EAAE,GAAG,CAAC,EAAE;YACvD,MAAM,CAAC,GAAG,CAAA,GAAI,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,QAAQ,CAAC,GAAG,CAAC,EAAE,MAAO,GAAE,CAAC,CAAC;QACjE;IACA;IAEE,OAAO,MAAM;AACf", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1464, "column": 0}, "map": {"version": 3, "file": "spanOnScope.js", "sources": ["file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/%40sentry%2Bcore%409.22.0/node_modules/%40sentry/core/src/utils/spanOnScope.ts"], "sourcesContent": ["import type { Scope } from '../scope';\nimport type { Span } from '../types-hoist/span';\nimport { addNonEnumerableProperty } from '../utils-hoist/object';\n\nconst SCOPE_SPAN_FIELD = '_sentrySpan';\n\ntype ScopeWithMaybeSpan = Scope & {\n  [SCOPE_SPAN_FIELD]?: Span;\n};\n\n/**\n * Set the active span for a given scope.\n * NOTE: This should NOT be used directly, but is only used internally by the trace methods.\n */\nexport function _setSpanForScope(scope: Scope, span: Span | undefined): void {\n  if (span) {\n    addNonEnumerableProperty(scope as ScopeWithMaybeSpan, SCOPE_SPAN_FIELD, span);\n  } else {\n    // eslint-disable-next-line @typescript-eslint/no-dynamic-delete\n    delete (scope as ScopeWithMaybeSpan)[SCOPE_SPAN_FIELD];\n  }\n}\n\n/**\n * Get the active span for a given scope.\n * NOTE: This should NOT be used directly, but is only used internally by the trace methods.\n */\nexport function _getSpanForScope(scope: ScopeWithMaybeSpan): Span | undefined {\n  return scope[SCOPE_SPAN_FIELD];\n}\n"], "names": [], "mappings": ";;;;;;AAIA,MAAM,gBAAA,GAAmB,aAAa;AAMtC;;;CAGA,GACO,SAAS,gBAAgB,CAAC,KAAK,EAAS,IAAI,EAA0B;IAC3E,IAAI,IAAI,EAAE;0PACR,2BAAA,AAAwB,EAAC,KAAA,EAA6B,gBAAgB,EAAE,IAAI,CAAC;IACjF,OAAS;QACT,gEAAA;QACI,OAAO,AAAC,KAAA,CAA6B,gBAAgB,CAAC;IAC1D;AACA;AAEA;;;CAGA,GACO,SAAS,gBAAgB,CAAC,KAAK,EAAwC;IAC5E,OAAO,KAAK,CAAC,gBAAgB,CAAC;AAChC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1496, "column": 0}, "map": {"version": 3, "file": "propagationContext.js", "sources": ["file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/%40sentry%2Bcore%409.22.0/node_modules/%40sentry/core/src/utils-hoist/propagationContext.ts"], "sourcesContent": ["import { uuid4 } from './misc';\n\n/**\n * Generate a random, valid trace ID.\n */\nexport function generateTraceId(): string {\n  return uuid4();\n}\n\n/**\n * Generate a random, valid span ID.\n */\nexport function generateSpanId(): string {\n  return uuid4().substring(16);\n}\n"], "names": [], "mappings": ";;;;;;AAEA;;CAEA,GACO,SAAS,eAAe,GAAW;IACxC,uPAAO,QAAA,AAAK,EAAE;AAChB;AAEA;;CAEA,GACO,SAAS,cAAc,GAAW;IACvC,uPAAO,QAAA,AAAK,EAAE,EAAC,SAAS,CAAC,EAAE,CAAC;AAC9B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1520, "column": 0}, "map": {"version": 3, "file": "scope.js", "sources": ["file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/%40sentry%2Bcore%409.22.0/node_modules/%40sentry/core/src/scope.ts"], "sourcesContent": ["/* eslint-disable max-lines */\nimport type { Client } from './client';\nimport { updateSession } from './session';\nimport type { Attachment } from './types-hoist/attachment';\nimport type { Breadcrumb } from './types-hoist/breadcrumb';\nimport type { Context, Contexts } from './types-hoist/context';\nimport type { DynamicSamplingContext } from './types-hoist/envelope';\nimport type { Event, EventHint } from './types-hoist/event';\nimport type { EventProcessor } from './types-hoist/eventprocessor';\nimport type { Extra, Extras } from './types-hoist/extra';\nimport type { Primitive } from './types-hoist/misc';\nimport type { RequestEventData } from './types-hoist/request';\nimport type { Session } from './types-hoist/session';\nimport type { SeverityLevel } from './types-hoist/severity';\nimport type { Span } from './types-hoist/span';\nimport type { PropagationContext } from './types-hoist/tracing';\nimport type { User } from './types-hoist/user';\nimport { merge } from './utils/merge';\nimport { _getSpanForScope, _setSpanForScope } from './utils/spanOnScope';\nimport { isPlainObject } from './utils-hoist/is';\nimport { logger } from './utils-hoist/logger';\nimport { uuid4 } from './utils-hoist/misc';\nimport { generateTraceId } from './utils-hoist/propagationContext';\nimport { truncate } from './utils-hoist/string';\nimport { dateTimestampInSeconds } from './utils-hoist/time';\n\n/**\n * Default value for maximum number of breadcrumbs added to an event.\n */\nconst DEFAULT_MAX_BREADCRUMBS = 100;\n\n/**\n * A context to be used for capturing an event.\n * This can either be a Scope, or a partial ScopeContext,\n * or a callback that receives the current scope and returns a new scope to use.\n */\nexport type CaptureContext = Scope | Partial<ScopeContext> | ((scope: Scope) => Scope);\n\n/**\n * Data that can be converted to a Scope.\n */\nexport interface ScopeContext {\n  user: User;\n  level: SeverityLevel;\n  extra: Extras;\n  contexts: Contexts;\n  tags: { [key: string]: Primitive };\n  fingerprint: string[];\n  propagationContext: PropagationContext;\n}\n\nexport interface SdkProcessingMetadata {\n  [key: string]: unknown;\n  requestSession?: {\n    status: 'ok' | 'errored' | 'crashed';\n  };\n  normalizedRequest?: RequestEventData;\n  dynamicSamplingContext?: Partial<DynamicSamplingContext>;\n  capturedSpanScope?: Scope;\n  capturedSpanIsolationScope?: Scope;\n  spanCountBeforeProcessing?: number;\n  ipAddress?: string;\n}\n\n/**\n * Normalized data of the Scope, ready to be used.\n */\nexport interface ScopeData {\n  eventProcessors: EventProcessor[];\n  breadcrumbs: Breadcrumb[];\n  user: User;\n  tags: { [key: string]: Primitive };\n  extra: Extras;\n  contexts: Contexts;\n  attachments: Attachment[];\n  propagationContext: PropagationContext;\n  sdkProcessingMetadata: SdkProcessingMetadata;\n  fingerprint: string[];\n  level?: SeverityLevel;\n  transactionName?: string;\n  span?: Span;\n}\n\n/**\n * Holds additional event information.\n */\nexport class Scope {\n  /** Flag if notifying is happening. */\n  protected _notifyingListeners: boolean;\n\n  /** Callback for client to receive scope changes. */\n  protected _scopeListeners: Array<(scope: Scope) => void>;\n\n  /** Callback list that will be called during event processing. */\n  protected _eventProcessors: EventProcessor[];\n\n  /** Array of breadcrumbs. */\n  protected _breadcrumbs: Breadcrumb[];\n\n  /** User */\n  protected _user: User;\n\n  /** Tags */\n  protected _tags: { [key: string]: Primitive };\n\n  /** Extra */\n  protected _extra: Extras;\n\n  /** Contexts */\n  protected _contexts: Contexts;\n\n  /** Attachments */\n  protected _attachments: Attachment[];\n\n  /** Propagation Context for distributed tracing */\n  protected _propagationContext: PropagationContext;\n\n  /**\n   * A place to stash data which is needed at some point in the SDK's event processing pipeline but which shouldn't get\n   * sent to Sentry\n   */\n  protected _sdkProcessingMetadata: SdkProcessingMetadata;\n\n  /** Fingerprint */\n  protected _fingerprint?: string[];\n\n  /** Severity */\n  protected _level?: SeverityLevel;\n\n  /**\n   * Transaction Name\n   *\n   * IMPORTANT: The transaction name on the scope has nothing to do with root spans/transaction objects.\n   * It's purpose is to assign a transaction to the scope that's added to non-transaction events.\n   */\n  protected _transactionName?: string;\n\n  /** Session */\n  protected _session?: Session;\n\n  /** The client on this scope */\n  protected _client?: Client;\n\n  /** Contains the last event id of a captured event.  */\n  protected _lastEventId?: string;\n\n  // NOTE: Any field which gets added here should get added not only to the constructor but also to the `clone` method.\n\n  public constructor() {\n    this._notifyingListeners = false;\n    this._scopeListeners = [];\n    this._eventProcessors = [];\n    this._breadcrumbs = [];\n    this._attachments = [];\n    this._user = {};\n    this._tags = {};\n    this._extra = {};\n    this._contexts = {};\n    this._sdkProcessingMetadata = {};\n    this._propagationContext = {\n      traceId: generateTraceId(),\n      sampleRand: Math.random(),\n    };\n  }\n\n  /**\n   * Clone all data from this scope into a new scope.\n   */\n  public clone(): Scope {\n    const newScope = new Scope();\n    newScope._breadcrumbs = [...this._breadcrumbs];\n    newScope._tags = { ...this._tags };\n    newScope._extra = { ...this._extra };\n    newScope._contexts = { ...this._contexts };\n    if (this._contexts.flags) {\n      // We need to copy the `values` array so insertions on a cloned scope\n      // won't affect the original array.\n      newScope._contexts.flags = {\n        values: [...this._contexts.flags.values],\n      };\n    }\n\n    newScope._user = this._user;\n    newScope._level = this._level;\n    newScope._session = this._session;\n    newScope._transactionName = this._transactionName;\n    newScope._fingerprint = this._fingerprint;\n    newScope._eventProcessors = [...this._eventProcessors];\n    newScope._attachments = [...this._attachments];\n    newScope._sdkProcessingMetadata = { ...this._sdkProcessingMetadata };\n    newScope._propagationContext = { ...this._propagationContext };\n    newScope._client = this._client;\n    newScope._lastEventId = this._lastEventId;\n\n    _setSpanForScope(newScope, _getSpanForScope(this));\n\n    return newScope;\n  }\n\n  /**\n   * Update the client assigned to this scope.\n   * Note that not every scope will have a client assigned - isolation scopes & the global scope will generally not have a client,\n   * as well as manually created scopes.\n   */\n  public setClient(client: Client | undefined): void {\n    this._client = client;\n  }\n\n  /**\n   * Set the ID of the last captured error event.\n   * This is generally only captured on the isolation scope.\n   */\n  public setLastEventId(lastEventId: string | undefined): void {\n    this._lastEventId = lastEventId;\n  }\n\n  /**\n   * Get the client assigned to this scope.\n   */\n  public getClient<C extends Client>(): C | undefined {\n    return this._client as C | undefined;\n  }\n\n  /**\n   * Get the ID of the last captured error event.\n   * This is generally only available on the isolation scope.\n   */\n  public lastEventId(): string | undefined {\n    return this._lastEventId;\n  }\n\n  /**\n   * @inheritDoc\n   */\n  public addScopeListener(callback: (scope: Scope) => void): void {\n    this._scopeListeners.push(callback);\n  }\n\n  /**\n   * Add an event processor that will be called before an event is sent.\n   */\n  public addEventProcessor(callback: EventProcessor): this {\n    this._eventProcessors.push(callback);\n    return this;\n  }\n\n  /**\n   * Set the user for this scope.\n   * Set to `null` to unset the user.\n   */\n  public setUser(user: User | null): this {\n    // If null is passed we want to unset everything, but still define keys,\n    // so that later down in the pipeline any existing values are cleared.\n    this._user = user || {\n      email: undefined,\n      id: undefined,\n      ip_address: undefined,\n      username: undefined,\n    };\n\n    if (this._session) {\n      updateSession(this._session, { user });\n    }\n\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * Get the user from this scope.\n   */\n  public getUser(): User | undefined {\n    return this._user;\n  }\n\n  /**\n   * Set an object that will be merged into existing tags on the scope,\n   * and will be sent as tags data with the event.\n   */\n  public setTags(tags: { [key: string]: Primitive }): this {\n    this._tags = {\n      ...this._tags,\n      ...tags,\n    };\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * Set a single tag that will be sent as tags data with the event.\n   */\n  public setTag(key: string, value: Primitive): this {\n    this._tags = { ...this._tags, [key]: value };\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * Set an object that will be merged into existing extra on the scope,\n   * and will be sent as extra data with the event.\n   */\n  public setExtras(extras: Extras): this {\n    this._extra = {\n      ...this._extra,\n      ...extras,\n    };\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * Set a single key:value extra entry that will be sent as extra data with the event.\n   */\n  public setExtra(key: string, extra: Extra): this {\n    this._extra = { ...this._extra, [key]: extra };\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * Sets the fingerprint on the scope to send with the events.\n   * @param {string[]} fingerprint Fingerprint to group events in Sentry.\n   */\n  public setFingerprint(fingerprint: string[]): this {\n    this._fingerprint = fingerprint;\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * Sets the level on the scope for future events.\n   */\n  public setLevel(level: SeverityLevel): this {\n    this._level = level;\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * Sets the transaction name on the scope so that the name of e.g. taken server route or\n   * the page location is attached to future events.\n   *\n   * IMPORTANT: Calling this function does NOT change the name of the currently active\n   * root span. If you want to change the name of the active root span, use\n   * `Sentry.updateSpanName(rootSpan, 'new name')` instead.\n   *\n   * By default, the SDK updates the scope's transaction name automatically on sensible\n   * occasions, such as a page navigation or when handling a new request on the server.\n   */\n  public setTransactionName(name?: string): this {\n    this._transactionName = name;\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * Sets context data with the given name.\n   * Data passed as context will be normalized. You can also pass `null` to unset the context.\n   * Note that context data will not be merged - calling `setContext` will overwrite an existing context with the same key.\n   */\n  public setContext(key: string, context: Context | null): this {\n    if (context === null) {\n      // eslint-disable-next-line @typescript-eslint/no-dynamic-delete\n      delete this._contexts[key];\n    } else {\n      this._contexts[key] = context;\n    }\n\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * Set the session for the scope.\n   */\n  public setSession(session?: Session): this {\n    if (!session) {\n      delete this._session;\n    } else {\n      this._session = session;\n    }\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * Get the session from the scope.\n   */\n  public getSession(): Session | undefined {\n    return this._session;\n  }\n\n  /**\n   * Updates the scope with provided data. Can work in three variations:\n   * - plain object containing updatable attributes\n   * - Scope instance that'll extract the attributes from\n   * - callback function that'll receive the current scope as an argument and allow for modifications\n   */\n  public update(captureContext?: CaptureContext): this {\n    if (!captureContext) {\n      return this;\n    }\n\n    const scopeToMerge = typeof captureContext === 'function' ? captureContext(this) : captureContext;\n\n    const scopeInstance =\n      scopeToMerge instanceof Scope\n        ? scopeToMerge.getScopeData()\n        : isPlainObject(scopeToMerge)\n          ? (captureContext as ScopeContext)\n          : undefined;\n\n    const { tags, extra, user, contexts, level, fingerprint = [], propagationContext } = scopeInstance || {};\n\n    this._tags = { ...this._tags, ...tags };\n    this._extra = { ...this._extra, ...extra };\n    this._contexts = { ...this._contexts, ...contexts };\n\n    if (user && Object.keys(user).length) {\n      this._user = user;\n    }\n\n    if (level) {\n      this._level = level;\n    }\n\n    if (fingerprint.length) {\n      this._fingerprint = fingerprint;\n    }\n\n    if (propagationContext) {\n      this._propagationContext = propagationContext;\n    }\n\n    return this;\n  }\n\n  /**\n   * Clears the current scope and resets its properties.\n   * Note: The client will not be cleared.\n   */\n  public clear(): this {\n    // client is not cleared here on purpose!\n    this._breadcrumbs = [];\n    this._tags = {};\n    this._extra = {};\n    this._user = {};\n    this._contexts = {};\n    this._level = undefined;\n    this._transactionName = undefined;\n    this._fingerprint = undefined;\n    this._session = undefined;\n    _setSpanForScope(this, undefined);\n    this._attachments = [];\n    this.setPropagationContext({ traceId: generateTraceId(), sampleRand: Math.random() });\n\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * Adds a breadcrumb to the scope.\n   * By default, the last 100 breadcrumbs are kept.\n   */\n  public addBreadcrumb(breadcrumb: Breadcrumb, maxBreadcrumbs?: number): this {\n    const maxCrumbs = typeof maxBreadcrumbs === 'number' ? maxBreadcrumbs : DEFAULT_MAX_BREADCRUMBS;\n\n    // No data has been changed, so don't notify scope listeners\n    if (maxCrumbs <= 0) {\n      return this;\n    }\n\n    const mergedBreadcrumb: Breadcrumb = {\n      timestamp: dateTimestampInSeconds(),\n      ...breadcrumb,\n      // Breadcrumb messages can theoretically be infinitely large and they're held in memory so we truncate them not to leak (too much) memory\n      message: breadcrumb.message ? truncate(breadcrumb.message, 2048) : breadcrumb.message,\n    };\n\n    this._breadcrumbs.push(mergedBreadcrumb);\n    if (this._breadcrumbs.length > maxCrumbs) {\n      this._breadcrumbs = this._breadcrumbs.slice(-maxCrumbs);\n      this._client?.recordDroppedEvent('buffer_overflow', 'log_item');\n    }\n\n    this._notifyScopeListeners();\n\n    return this;\n  }\n\n  /**\n   * Get the last breadcrumb of the scope.\n   */\n  public getLastBreadcrumb(): Breadcrumb | undefined {\n    return this._breadcrumbs[this._breadcrumbs.length - 1];\n  }\n\n  /**\n   * Clear all breadcrumbs from the scope.\n   */\n  public clearBreadcrumbs(): this {\n    this._breadcrumbs = [];\n    this._notifyScopeListeners();\n    return this;\n  }\n\n  /**\n   * Add an attachment to the scope.\n   */\n  public addAttachment(attachment: Attachment): this {\n    this._attachments.push(attachment);\n    return this;\n  }\n\n  /**\n   * Clear all attachments from the scope.\n   */\n  public clearAttachments(): this {\n    this._attachments = [];\n    return this;\n  }\n\n  /**\n   * Get the data of this scope, which should be applied to an event during processing.\n   */\n  public getScopeData(): ScopeData {\n    return {\n      breadcrumbs: this._breadcrumbs,\n      attachments: this._attachments,\n      contexts: this._contexts,\n      tags: this._tags,\n      extra: this._extra,\n      user: this._user,\n      level: this._level,\n      fingerprint: this._fingerprint || [],\n      eventProcessors: this._eventProcessors,\n      propagationContext: this._propagationContext,\n      sdkProcessingMetadata: this._sdkProcessingMetadata,\n      transactionName: this._transactionName,\n      span: _getSpanForScope(this),\n    };\n  }\n\n  /**\n   * Add data which will be accessible during event processing but won't get sent to Sentry.\n   */\n  public setSDKProcessingMetadata(newData: SdkProcessingMetadata): this {\n    this._sdkProcessingMetadata = merge(this._sdkProcessingMetadata, newData, 2);\n    return this;\n  }\n\n  /**\n   * Add propagation context to the scope, used for distributed tracing\n   */\n  public setPropagationContext(context: PropagationContext): this {\n    this._propagationContext = context;\n    return this;\n  }\n\n  /**\n   * Get propagation context from the scope, used for distributed tracing\n   */\n  public getPropagationContext(): PropagationContext {\n    return this._propagationContext;\n  }\n\n  /**\n   * Capture an exception for this scope.\n   *\n   * @returns {string} The id of the captured Sentry event.\n   */\n  public captureException(exception: unknown, hint?: EventHint): string {\n    const eventId = hint?.event_id || uuid4();\n\n    if (!this._client) {\n      logger.warn('No client configured on scope - will not capture exception!');\n      return eventId;\n    }\n\n    const syntheticException = new Error('Sentry syntheticException');\n\n    this._client.captureException(\n      exception,\n      {\n        originalException: exception,\n        syntheticException,\n        ...hint,\n        event_id: eventId,\n      },\n      this,\n    );\n\n    return eventId;\n  }\n\n  /**\n   * Capture a message for this scope.\n   *\n   * @returns {string} The id of the captured message.\n   */\n  public captureMessage(message: string, level?: SeverityLevel, hint?: EventHint): string {\n    const eventId = hint?.event_id || uuid4();\n\n    if (!this._client) {\n      logger.warn('No client configured on scope - will not capture message!');\n      return eventId;\n    }\n\n    const syntheticException = new Error(message);\n\n    this._client.captureMessage(\n      message,\n      level,\n      {\n        originalException: message,\n        syntheticException,\n        ...hint,\n        event_id: eventId,\n      },\n      this,\n    );\n\n    return eventId;\n  }\n\n  /**\n   * Capture a Sentry event for this scope.\n   *\n   * @returns {string} The id of the captured event.\n   */\n  public captureEvent(event: Event, hint?: EventHint): string {\n    const eventId = hint?.event_id || uuid4();\n\n    if (!this._client) {\n      logger.warn('No client configured on scope - will not capture event!');\n      return eventId;\n    }\n\n    this._client.captureEvent(event, { ...hint, event_id: eventId }, this);\n\n    return eventId;\n  }\n\n  /**\n   * This will be called on every set call.\n   */\n  protected _notifyScopeListeners(): void {\n    // We need this check for this._notifyingListeners to be able to work on scope during updates\n    // If this check is not here we'll produce endless recursion when something is done with the scope\n    // during the callback.\n    if (!this._notifyingListeners) {\n      this._notifyingListeners = true;\n      this._scopeListeners.forEach(callback => {\n        callback(this);\n      });\n      this._notifyingListeners = false;\n    }\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;AA0BA;;CAEA,GACA,MAAM,uBAAA,GAA0B,GAAG;AAEnC;;;;CAIA,GAgDA;;CAEA,GACO,MAAM,KAAM,CAAA;IACnB,oCAAA,GAGA,kDAAA,GAGA,+DAAA,GAGA,0BAAA,GAGA,SAAA,GAGA,SAAA,GAGA,UAAA,GAGA,aAAA,GAGA,gBAAA,GAGA,gDAAA,GAGA;;;GAGA,GAGA,gBAAA,GAGA,aAAA,GAGA;;;;;GAKA,GAGA,YAAA,GAGA,6BAAA,GAGA,qDAAA,GAGA,qHAAA;IAES,WAAW,EAAG;QACnB,IAAI,CAAC,mBAAoB,GAAE,KAAK;QAChC,IAAI,CAAC,eAAgB,GAAE,EAAE;QACzB,IAAI,CAAC,gBAAiB,GAAE,EAAE;QAC1B,IAAI,CAAC,YAAa,GAAE,EAAE;QACtB,IAAI,CAAC,YAAa,GAAE,EAAE;QACtB,IAAI,CAAC,KAAM,GAAE,CAAA,CAAE;QACf,IAAI,CAAC,KAAM,GAAE,CAAA,CAAE;QACf,IAAI,CAAC,MAAO,GAAE,CAAA,CAAE;QAChB,IAAI,CAAC,SAAU,GAAE,CAAA,CAAE;QACnB,IAAI,CAAC,sBAAuB,GAAE,CAAA,CAAE;QAChC,IAAI,CAAC,mBAAA,GAAsB;YACzB,OAAO,gQAAE,kBAAA,AAAe,EAAE;YAC1B,UAAU,EAAE,IAAI,CAAC,MAAM,EAAE;QAC/B,CAAK;IACL;IAEA;;GAEA,GACS,KAAK,GAAU;QACpB,MAAM,QAAS,GAAE,IAAI,KAAK,EAAE;QAC5B,QAAQ,CAAC,YAAa,GAAE,CAAC;eAAG,IAAI,CAAC,YAAY;SAAC;QAC9C,QAAQ,CAAC,KAAM,GAAE;YAAE,GAAG,IAAI,CAAC,KAAA;QAAA,CAAO;QAClC,QAAQ,CAAC,MAAO,GAAE;YAAE,GAAG,IAAI,CAAC,MAAA;QAAA,CAAQ;QACpC,QAAQ,CAAC,SAAU,GAAE;YAAE,GAAG,IAAI,CAAC,SAAA;QAAA,CAAW;QAC1C,IAAI,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE;YAC9B,qEAAA;YACA,mCAAA;YACM,QAAQ,CAAC,SAAS,CAAC,KAAA,GAAQ;gBACzB,MAAM,EAAE,CAAC;uBAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,MAAM;iBAAC;YAChD,CAAO;QACP;QAEI,QAAQ,CAAC,KAAA,GAAQ,IAAI,CAAC,KAAK;QAC3B,QAAQ,CAAC,MAAA,GAAS,IAAI,CAAC,MAAM;QAC7B,QAAQ,CAAC,QAAA,GAAW,IAAI,CAAC,QAAQ;QACjC,QAAQ,CAAC,gBAAA,GAAmB,IAAI,CAAC,gBAAgB;QACjD,QAAQ,CAAC,YAAA,GAAe,IAAI,CAAC,YAAY;QACzC,QAAQ,CAAC,gBAAiB,GAAE,CAAC;eAAG,IAAI,CAAC,gBAAgB;SAAC;QACtD,QAAQ,CAAC,YAAa,GAAE,CAAC;eAAG,IAAI,CAAC,YAAY;SAAC;QAC9C,QAAQ,CAAC,sBAAuB,GAAE;YAAE,GAAG,IAAI,CAAC,sBAAA;QAAA,CAAwB;QACpE,QAAQ,CAAC,mBAAoB,GAAE;YAAE,GAAG,IAAI,CAAC,mBAAA;QAAA,CAAqB;QAC9D,QAAQ,CAAC,OAAA,GAAU,IAAI,CAAC,OAAO;QAC/B,QAAQ,CAAC,YAAA,GAAe,IAAI,CAAC,YAAY;YAEzC,6PAAA,AAAgB,EAAC,QAAQ,gPAAE,mBAAA,AAAgB,EAAC,IAAI,CAAC,CAAC;QAElD,OAAO,QAAQ;IACnB;IAEA;;;;GAIA,GACS,SAAS,CAAC,MAAM,EAA4B;QACjD,IAAI,CAAC,OAAQ,GAAE,MAAM;IACzB;IAEA;;;GAGA,GACS,cAAc,CAAC,WAAW,EAA4B;QAC3D,IAAI,CAAC,YAAa,GAAE,WAAW;IACnC;IAEA;;GAEA,GACS,SAAS,GAAoC;QAClD,OAAO,IAAI,CAAC,OAAQ;IACxB;IAEA;;;GAGA,GACS,WAAW,GAAuB;QACvC,OAAO,IAAI,CAAC,YAAY;IAC5B;IAEA;;GAEA,GACS,gBAAgB,CAAC,QAAQ,EAAgC;QAC9D,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,QAAQ,CAAC;IACvC;IAEA;;GAEA,GACS,iBAAiB,CAAC,QAAQ,EAAwB;QACvD,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,QAAQ,CAAC;QACpC,OAAO,IAAI;IACf;IAEA;;;GAGA,GACS,OAAO,CAAC,IAAI,EAAqB;QAC1C,wEAAA;QACA,sEAAA;QACI,IAAI,CAAC,KAAM,GAAE,QAAQ;YACnB,KAAK,EAAE,SAAS;YAChB,EAAE,EAAE,SAAS;YACb,UAAU,EAAE,SAAS;YACrB,QAAQ,EAAE,SAAS;QACzB,CAAK;QAED,IAAI,IAAI,CAAC,QAAQ,EAAE;6OACjB,gBAAA,AAAa,EAAC,IAAI,CAAC,QAAQ,EAAE;gBAAE,IAAK;YAAA,CAAC,CAAC;QAC5C;QAEI,IAAI,CAAC,qBAAqB,EAAE;QAC5B,OAAO,IAAI;IACf;IAEA;;GAEA,GACS,OAAO,GAAqB;QACjC,OAAO,IAAI,CAAC,KAAK;IACrB;IAEA;;;GAGA,GACS,OAAO,CAAC,IAAI,EAAsC;QACvD,IAAI,CAAC,KAAA,GAAQ;YACX,GAAG,IAAI,CAAC,KAAK;YACb,GAAG,IAAI;QACb,CAAK;QACD,IAAI,CAAC,qBAAqB,EAAE;QAC5B,OAAO,IAAI;IACf;IAEA;;GAEA,GACS,MAAM,CAAC,GAAG,EAAU,KAAK,EAAmB;QACjD,IAAI,CAAC,KAAM,GAAE;YAAE,GAAG,IAAI,CAAC,KAAK;YAAE,CAAC,GAAG,CAAA,EAAG;QAAA,CAAO;QAC5C,IAAI,CAAC,qBAAqB,EAAE;QAC5B,OAAO,IAAI;IACf;IAEA;;;GAGA,GACS,SAAS,CAAC,MAAM,EAAgB;QACrC,IAAI,CAAC,MAAA,GAAS;YACZ,GAAG,IAAI,CAAC,MAAM;YACd,GAAG,MAAM;QACf,CAAK;QACD,IAAI,CAAC,qBAAqB,EAAE;QAC5B,OAAO,IAAI;IACf;IAEA;;GAEA,GACS,QAAQ,CAAC,GAAG,EAAU,KAAK,EAAe;QAC/C,IAAI,CAAC,MAAO,GAAE;YAAE,GAAG,IAAI,CAAC,MAAM;YAAE,CAAC,GAAG,CAAA,EAAG;QAAA,CAAO;QAC9C,IAAI,CAAC,qBAAqB,EAAE;QAC5B,OAAO,IAAI;IACf;IAEA;;;GAGA,GACS,cAAc,CAAC,WAAW,EAAkB;QACjD,IAAI,CAAC,YAAa,GAAE,WAAW;QAC/B,IAAI,CAAC,qBAAqB,EAAE;QAC5B,OAAO,IAAI;IACf;IAEA;;GAEA,GACS,QAAQ,CAAC,KAAK,EAAuB;QAC1C,IAAI,CAAC,MAAO,GAAE,KAAK;QACnB,IAAI,CAAC,qBAAqB,EAAE;QAC5B,OAAO,IAAI;IACf;IAEA;;;;;;;;;;GAUA,GACS,kBAAkB,CAAC,IAAI,EAAiB;QAC7C,IAAI,CAAC,gBAAiB,GAAE,IAAI;QAC5B,IAAI,CAAC,qBAAqB,EAAE;QAC5B,OAAO,IAAI;IACf;IAEA;;;;GAIA,GACS,UAAU,CAAC,GAAG,EAAU,OAAO,EAAwB;QAC5D,IAAI,OAAQ,KAAI,IAAI,EAAE;YAC1B,gEAAA;YACM,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC;QAChC,OAAW;YACL,IAAI,CAAC,SAAS,CAAC,GAAG,CAAA,GAAI,OAAO;QACnC;QAEI,IAAI,CAAC,qBAAqB,EAAE;QAC5B,OAAO,IAAI;IACf;IAEA;;GAEA,GACS,UAAU,CAAC,OAAO,EAAkB;QACzC,IAAI,CAAC,OAAO,EAAE;YACZ,OAAO,IAAI,CAAC,QAAQ;QAC1B,OAAW;YACL,IAAI,CAAC,QAAS,GAAE,OAAO;QAC7B;QACI,IAAI,CAAC,qBAAqB,EAAE;QAC5B,OAAO,IAAI;IACf;IAEA;;GAEA,GACS,UAAU,GAAwB;QACvC,OAAO,IAAI,CAAC,QAAQ;IACxB;IAEA;;;;;GAKA,GACS,MAAM,CAAC,cAAc,EAAyB;QACnD,IAAI,CAAC,cAAc,EAAE;YACnB,OAAO,IAAI;QACjB;QAEI,MAAM,YAAA,GAAe,OAAO,cAAe,KAAI,UAAW,GAAE,cAAc,CAAC,IAAI,CAAA,GAAI,cAAc;QAEjG,MAAM,aAAc,GAClB,wBAAwB,QACpB,YAAY,CAAC,YAAY,SACzB,0PAAA,AAAa,EAAC,YAAY,IACvB,cAAe,GAChB,SAAS;QAEjB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,WAAY,GAAE,EAAE,EAAE,kBAAA,EAAA,GAAuB,aAAA,IAAiB,CAAA,CAAE;QAExG,IAAI,CAAC,KAAM,GAAE;YAAE,GAAG,IAAI,CAAC,KAAK;YAAE,GAAG,IAAA;QAAA,CAAM;QACvC,IAAI,CAAC,MAAO,GAAE;YAAE,GAAG,IAAI,CAAC,MAAM;YAAE,GAAG,KAAA;QAAA,CAAO;QAC1C,IAAI,CAAC,SAAU,GAAE;YAAE,GAAG,IAAI,CAAC,SAAS;YAAE,GAAG,QAAA;QAAA,CAAU;QAEnD,IAAI,IAAK,IAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE;YACpC,IAAI,CAAC,KAAM,GAAE,IAAI;QACvB;QAEI,IAAI,KAAK,EAAE;YACT,IAAI,CAAC,MAAO,GAAE,KAAK;QACzB;QAEI,IAAI,WAAW,CAAC,MAAM,EAAE;YACtB,IAAI,CAAC,YAAa,GAAE,WAAW;QACrC;QAEI,IAAI,kBAAkB,EAAE;YACtB,IAAI,CAAC,mBAAoB,GAAE,kBAAkB;QACnD;QAEI,OAAO,IAAI;IACf;IAEA;;;GAGA,GACS,KAAK,GAAS;QACvB,yCAAA;QACI,IAAI,CAAC,YAAa,GAAE,EAAE;QACtB,IAAI,CAAC,KAAM,GAAE,CAAA,CAAE;QACf,IAAI,CAAC,MAAO,GAAE,CAAA,CAAE;QAChB,IAAI,CAAC,KAAM,GAAE,CAAA,CAAE;QACf,IAAI,CAAC,SAAU,GAAE,CAAA,CAAE;QACnB,IAAI,CAAC,MAAO,GAAE,SAAS;QACvB,IAAI,CAAC,gBAAiB,GAAE,SAAS;QACjC,IAAI,CAAC,YAAa,GAAE,SAAS;QAC7B,IAAI,CAAC,QAAS,GAAE,SAAS;sPACzB,mBAAA,AAAgB,EAAC,IAAI,EAAE,SAAS,CAAC;QACjC,IAAI,CAAC,YAAa,GAAE,EAAE;QACtB,IAAI,CAAC,qBAAqB,CAAC;YAAE,OAAO,gQAAE,kBAAA,AAAe,EAAE;YAAE,UAAU,EAAE,IAAI,CAAC,MAAM,EAAC;QAAA,CAAG,CAAC;QAErF,IAAI,CAAC,qBAAqB,EAAE;QAC5B,OAAO,IAAI;IACf;IAEA;;;GAGA,GACS,aAAa,CAAC,UAAU,EAAc,cAAc,EAAiB;QAC1E,MAAM,SAAU,GAAE,OAAO,cAAA,KAAmB,QAAS,GAAE,cAAe,GAAE,uBAAuB;QAEnG,4DAAA;QACI,IAAI,SAAU,IAAG,CAAC,EAAE;YAClB,OAAO,IAAI;QACjB;QAEI,MAAM,gBAAgB,GAAe;YACnC,SAAS,kPAAE,yBAAA,AAAsB,EAAE;YACnC,GAAG,UAAU;YACnB,yIAAA;YACM,OAAO,EAAE,UAAU,CAAC,OAAA,OAAU,yPAAA,AAAQ,EAAC,UAAU,CAAC,OAAO,EAAE,IAAI,IAAI,UAAU,CAAC,OAAO;QAC3F,CAAK;QAED,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,gBAAgB,CAAC;QACxC,IAAI,IAAI,CAAC,YAAY,CAAC,MAAA,GAAS,SAAS,EAAE;YACxC,IAAI,CAAC,YAAa,GAAE,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC;YACvD,IAAI,CAAC,OAAO,EAAE,kBAAkB,CAAC,iBAAiB,EAAE,UAAU,CAAC;QACrE;QAEI,IAAI,CAAC,qBAAqB,EAAE;QAE5B,OAAO,IAAI;IACf;IAEA;;GAEA,GACS,iBAAiB,GAA2B;QACjD,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,YAAY,CAAC,MAAA,GAAS,CAAC,CAAC;IAC1D;IAEA;;GAEA,GACS,gBAAgB,GAAS;QAC9B,IAAI,CAAC,YAAa,GAAE,EAAE;QACtB,IAAI,CAAC,qBAAqB,EAAE;QAC5B,OAAO,IAAI;IACf;IAEA;;GAEA,GACS,aAAa,CAAC,UAAU,EAAoB;QACjD,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC;QAClC,OAAO,IAAI;IACf;IAEA;;GAEA,GACS,gBAAgB,GAAS;QAC9B,IAAI,CAAC,YAAa,GAAE,EAAE;QACtB,OAAO,IAAI;IACf;IAEA;;GAEA,GACS,YAAY,GAAc;QAC/B,OAAO;YACL,WAAW,EAAE,IAAI,CAAC,YAAY;YAC9B,WAAW,EAAE,IAAI,CAAC,YAAY;YAC9B,QAAQ,EAAE,IAAI,CAAC,SAAS;YACxB,IAAI,EAAE,IAAI,CAAC,KAAK;YAChB,KAAK,EAAE,IAAI,CAAC,MAAM;YAClB,IAAI,EAAE,IAAI,CAAC,KAAK;YAChB,KAAK,EAAE,IAAI,CAAC,MAAM;YAClB,WAAW,EAAE,IAAI,CAAC,YAAa,IAAG,EAAE;YACpC,eAAe,EAAE,IAAI,CAAC,gBAAgB;YACtC,kBAAkB,EAAE,IAAI,CAAC,mBAAmB;YAC5C,qBAAqB,EAAE,IAAI,CAAC,sBAAsB;YAClD,eAAe,EAAE,IAAI,CAAC,gBAAgB;YACtC,IAAI,gPAAE,mBAAA,AAAgB,EAAC,IAAI,CAAC;QAClC,CAAK;IACL;IAEA;;GAEA,GACS,wBAAwB,CAAC,OAAO,EAA+B;QACpE,IAAI,CAAC,sBAAuB,2OAAE,QAAA,AAAK,EAAC,IAAI,CAAC,sBAAsB,EAAE,OAAO,EAAE,CAAC,CAAC;QAC5E,OAAO,IAAI;IACf;IAEA;;GAEA,GACS,qBAAqB,CAAC,OAAO,EAA4B;QAC9D,IAAI,CAAC,mBAAoB,GAAE,OAAO;QAClC,OAAO,IAAI;IACf;IAEA;;GAEA,GACS,qBAAqB,GAAuB;QACjD,OAAO,IAAI,CAAC,mBAAmB;IACnC;IAEA;;;;GAIA,GACS,gBAAgB,CAAC,SAAS,EAAW,IAAI,EAAsB;QACpE,MAAM,UAAU,IAAI,EAAE,QAAS,QAAG,oPAAA,AAAK,EAAE;QAEzC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;0PACjB,SAAM,CAAC,IAAI,CAAC,6DAA6D,CAAC;YAC1E,OAAO,OAAO;QACpB;QAEI,MAAM,kBAAmB,GAAE,IAAI,KAAK,CAAC,2BAA2B,CAAC;QAEjE,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAC3B,SAAS,EACT;YACE,iBAAiB,EAAE,SAAS;YAC5B,kBAAkB;YAClB,GAAG,IAAI;YACP,QAAQ,EAAE,OAAO;QACzB,CAAO,EACD,IAAI;QAGN,OAAO,OAAO;IAClB;IAEA;;;;GAIA,GACS,cAAc,CAAC,OAAO,EAAU,KAAK,EAAkB,IAAI,EAAsB;QACtF,MAAM,UAAU,IAAI,EAAE,QAAS,oPAAG,QAAA,AAAK,EAAE;QAEzC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;0PACjB,SAAM,CAAC,IAAI,CAAC,2DAA2D,CAAC;YACxE,OAAO,OAAO;QACpB;QAEI,MAAM,kBAAmB,GAAE,IAAI,KAAK,CAAC,OAAO,CAAC;QAE7C,IAAI,CAAC,OAAO,CAAC,cAAc,CACzB,OAAO,EACP,KAAK,EACL;YACE,iBAAiB,EAAE,OAAO;YAC1B,kBAAkB;YAClB,GAAG,IAAI;YACP,QAAQ,EAAE,OAAO;QACzB,CAAO,EACD,IAAI;QAGN,OAAO,OAAO;IAClB;IAEA;;;;GAIA,GACS,YAAY,CAAC,KAAK,EAAS,IAAI,EAAsB;QAC1D,MAAM,UAAU,IAAI,EAAE,QAAS,KAAG,uPAAA,AAAK,EAAE;QAEzC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;0PACjB,SAAM,CAAC,IAAI,CAAC,yDAAyD,CAAC;YACtE,OAAO,OAAO;QACpB;QAEI,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,KAAK,EAAE;YAAE,GAAG,IAAI;YAAE,QAAQ,EAAE;QAAA,CAAS,EAAE,IAAI,CAAC;QAEtE,OAAO,OAAO;IAClB;IAEA;;GAEA,GACY,qBAAqB,GAAS;QAC1C,6FAAA;QACA,kGAAA;QACA,uBAAA;QACI,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE;YAC7B,IAAI,CAAC,mBAAoB,GAAE,IAAI;YAC/B,IAAI,CAAC,eAAe,CAAC,OAAO,EAAC,YAAY;gBACvC,QAAQ,CAAC,IAAI,CAAC;YACtB,CAAO,CAAC;YACF,IAAI,CAAC,mBAAoB,GAAE,KAAK;QACtC;IACA;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2006, "column": 0}, "map": {"version": 3, "file": "defaultScopes.js", "sources": ["file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/%40sentry%2Bcore%409.22.0/node_modules/%40sentry/core/src/defaultScopes.ts"], "sourcesContent": ["import { getGlobalSingleton } from './carrier';\nimport { Scope } from './scope';\n\n/** Get the default current scope. */\nexport function getDefaultCurrentScope(): Scope {\n  return getGlobalSingleton('defaultCurrentScope', () => new Scope());\n}\n\n/** Get the default isolation scope. */\nexport function getDefaultIsolationScope(): Scope {\n  return getGlobalSingleton('defaultIsolationScope', () => new Scope());\n}\n"], "names": [], "mappings": ";;;;;;;;AAGA,mCAAA,GACO,SAAS,sBAAsB,GAAU;IAC9C,wOAAO,qBAAA,AAAkB,EAAC,qBAAqB,EAAE,IAAM,+NAAI,QAAK,EAAE,CAAC;AACrE;AAEA,qCAAA,GACO,SAAS,wBAAwB,GAAU;IAChD,wOAAO,qBAAA,AAAkB,EAAC,uBAAuB,EAAE,IAAM,+NAAI,QAAK,EAAE,CAAC;AACvE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2028, "column": 0}, "map": {"version": 3, "file": "stackStrategy.js", "sources": ["file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/%40sentry%2Bcore%409.22.0/node_modules/%40sentry/core/src/asyncContext/stackStrategy.ts"], "sourcesContent": ["import type { Client } from '../client';\nimport { getDefaultCurrentScope, getDefaultIsolationScope } from '../defaultScopes';\nimport { Scope } from '../scope';\nimport { isThenable } from '../utils-hoist/is';\nimport { getMainCarrier, getSentryCarrier } from './../carrier';\nimport type { AsyncContextStrategy } from './types';\n\ninterface Layer {\n  client?: Client;\n  scope: Scope;\n}\n\n/**\n * This is an object that holds a stack of scopes.\n */\nexport class AsyncContextStack {\n  private readonly _stack: [Layer, ...Layer[]];\n  private _isolationScope: Scope;\n\n  public constructor(scope?: Scope, isolationScope?: Scope) {\n    let assignedScope;\n    if (!scope) {\n      assignedScope = new Scope();\n    } else {\n      assignedScope = scope;\n    }\n\n    let assignedIsolationScope;\n    if (!isolationScope) {\n      assignedIsolationScope = new Scope();\n    } else {\n      assignedIsolationScope = isolationScope;\n    }\n\n    // scope stack for domains or the process\n    this._stack = [{ scope: assignedScope }];\n    this._isolationScope = assignedIsolationScope;\n  }\n\n  /**\n   * Fork a scope for the stack.\n   */\n  public withScope<T>(callback: (scope: Scope) => T): T {\n    const scope = this._pushScope();\n\n    let maybePromiseResult: T;\n    try {\n      maybePromiseResult = callback(scope);\n    } catch (e) {\n      this._popScope();\n      throw e;\n    }\n\n    if (isThenable(maybePromiseResult)) {\n      // @ts-expect-error - isThenable returns the wrong type\n      return maybePromiseResult.then(\n        res => {\n          this._popScope();\n          return res;\n        },\n        e => {\n          this._popScope();\n          throw e;\n        },\n      );\n    }\n\n    this._popScope();\n    return maybePromiseResult;\n  }\n\n  /**\n   * Get the client of the stack.\n   */\n  public getClient<C extends Client>(): C | undefined {\n    return this.getStackTop().client as C;\n  }\n\n  /**\n   * Returns the scope of the top stack.\n   */\n  public getScope(): Scope {\n    return this.getStackTop().scope;\n  }\n\n  /**\n   * Get the isolation scope for the stack.\n   */\n  public getIsolationScope(): Scope {\n    return this._isolationScope;\n  }\n\n  /**\n   * Returns the topmost scope layer in the order domain > local > process.\n   */\n  public getStackTop(): Layer {\n    return this._stack[this._stack.length - 1] as Layer;\n  }\n\n  /**\n   * Push a scope to the stack.\n   */\n  private _pushScope(): Scope {\n    // We want to clone the content of prev scope\n    const scope = this.getScope().clone();\n    this._stack.push({\n      client: this.getClient(),\n      scope,\n    });\n    return scope;\n  }\n\n  /**\n   * Pop a scope from the stack.\n   */\n  private _popScope(): boolean {\n    if (this._stack.length <= 1) return false;\n    return !!this._stack.pop();\n  }\n}\n\n/**\n * Get the global async context stack.\n * This will be removed during the v8 cycle and is only here to make migration easier.\n */\nfunction getAsyncContextStack(): AsyncContextStack {\n  const registry = getMainCarrier();\n  const sentry = getSentryCarrier(registry);\n\n  return (sentry.stack = sentry.stack || new AsyncContextStack(getDefaultCurrentScope(), getDefaultIsolationScope()));\n}\n\nfunction withScope<T>(callback: (scope: Scope) => T): T {\n  return getAsyncContextStack().withScope(callback);\n}\n\nfunction withSetScope<T>(scope: Scope, callback: (scope: Scope) => T): T {\n  const stack = getAsyncContextStack() as AsyncContextStack;\n  return stack.withScope(() => {\n    stack.getStackTop().scope = scope;\n    return callback(scope);\n  });\n}\n\nfunction withIsolationScope<T>(callback: (isolationScope: Scope) => T): T {\n  return getAsyncContextStack().withScope(() => {\n    return callback(getAsyncContextStack().getIsolationScope());\n  });\n}\n\n/**\n * Get the stack-based async context strategy.\n */\nexport function getStackAsyncContextStrategy(): AsyncContextStrategy {\n  return {\n    withIsolationScope,\n    withScope,\n    withSetScope,\n    withSetIsolationScope: <T>(_isolationScope: Scope, callback: (isolationScope: Scope) => T) => {\n      return withIsolationScope(callback);\n    },\n    getCurrentScope: () => getAsyncContextStack().getScope(),\n    getIsolationScope: () => getAsyncContextStack().getIsolationScope(),\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AAYA;;CAEA,GACO,MAAM,iBAAkB,CAAA;IAItB,WAAW,CAAC,KAAK,EAAU,cAAc,CAAU;QACxD,IAAI,aAAa;QACjB,IAAI,CAAC,KAAK,EAAE;YACV,aAAc,GAAE,+NAAI,QAAK,EAAE;QACjC,OAAW;YACL,aAAA,GAAgB,KAAK;QAC3B;QAEI,IAAI,sBAAsB;QAC1B,IAAI,CAAC,cAAc,EAAE;YACnB,sBAAuB,GAAE,+NAAI,QAAK,EAAE;QAC1C,OAAW;YACL,sBAAA,GAAyB,cAAc;QAC7C;QAEA,yCAAA;QACI,IAAI,CAAC,MAAA,GAAS;YAAC;gBAAE,KAAK,EAAE,aAAc;YAAA,CAAC;SAAC;QACxC,IAAI,CAAC,eAAgB,GAAE,sBAAsB;IACjD;IAEA;;GAEA,GACS,SAAS,CAAI,QAAQ,EAA0B;QACpD,MAAM,KAAM,GAAE,IAAI,CAAC,UAAU,EAAE;QAE/B,IAAI,kBAAkB;QACtB,IAAI;YACF,kBAAmB,GAAE,QAAQ,CAAC,KAAK,CAAC;QAC1C,CAAM,CAAA,OAAO,CAAC,EAAE;YACV,IAAI,CAAC,SAAS,EAAE;YAChB,MAAM,CAAC;QACb;QAEI,kPAAI,aAAA,AAAU,EAAC,kBAAkB,CAAC,EAAE;YACxC,uDAAA;YACM,OAAO,kBAAkB,CAAC,IAAI,CAC5B,OAAO;gBACL,IAAI,CAAC,SAAS,EAAE;gBAChB,OAAO,GAAG;YACpB,CAAS,GACD,KAAK;gBACH,IAAI,CAAC,SAAS,EAAE;gBAChB,MAAM,CAAC;YACjB,CAAS;QAET;QAEI,IAAI,CAAC,SAAS,EAAE;QAChB,OAAO,kBAAkB;IAC7B;IAEA;;GAEA,GACS,SAAS,GAAoC;QAClD,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC,MAAO;IACrC;IAEA;;GAEA,GACS,QAAQ,GAAU;QACvB,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC,KAAK;IACnC;IAEA;;GAEA,GACS,iBAAiB,GAAU;QAChC,OAAO,IAAI,CAAC,eAAe;IAC/B;IAEA;;GAEA,GACS,WAAW,GAAU;QAC1B,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,MAAA,GAAS,CAAC,CAAE;IAC/C;IAEA;;GAEA,GACU,UAAU,GAAU;QAC9B,6CAAA;QACI,MAAM,KAAM,GAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,KAAK,EAAE;QACrC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;YACf,MAAM,EAAE,IAAI,CAAC,SAAS,EAAE;YACxB,KAAK;QACX,CAAK,CAAC;QACF,OAAO,KAAK;IAChB;IAEA;;GAEA,GACU,SAAS,GAAY;QAC3B,IAAI,IAAI,CAAC,MAAM,CAAC,MAAA,IAAU,CAAC,EAAE,OAAO,KAAK;QACzC,OAAO,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE;IAC9B;AACA;AAEA;;;CAGA,GACA,SAAS,oBAAoB,GAAsB;IACjD,MAAM,QAAA,IAAW,iPAAA,AAAc,EAAE;IACjC,MAAM,MAAO,oOAAE,mBAAA,AAAgB,EAAC,QAAQ,CAAC;IAEzC,OAAQ,MAAM,CAAC,KAAA,GAAQ,MAAM,CAAC,KAAM,IAAG,IAAI,iBAAiB,wOAAC,yBAAA,AAAsB,EAAE,0OAAE,2BAAA,AAAwB,EAAE,CAAC;AACpH;AAEA,SAAS,SAAS,CAAI,QAAQ,EAA0B;IACtD,OAAO,oBAAoB,EAAE,CAAC,SAAS,CAAC,QAAQ,CAAC;AACnD;AAEA,SAAS,YAAY,CAAI,KAAK,EAAS,QAAQ,EAA0B;IACvE,MAAM,KAAA,GAAQ,oBAAoB,EAAG;IACrC,OAAO,KAAK,CAAC,SAAS,CAAC,MAAM;QAC3B,KAAK,CAAC,WAAW,EAAE,CAAC,KAAA,GAAQ,KAAK;QACjC,OAAO,QAAQ,CAAC,KAAK,CAAC;IAC1B,CAAG,CAAC;AACJ;AAEA,SAAS,kBAAkB,CAAI,QAAQ,EAAmC;IACxE,OAAO,oBAAoB,EAAE,CAAC,SAAS,CAAC,MAAM;QAC5C,OAAO,QAAQ,CAAC,oBAAoB,EAAE,CAAC,iBAAiB,EAAE,CAAC;IAC/D,CAAG,CAAC;AACJ;AAEA;;CAEA,GACO,SAAS,4BAA4B,GAAyB;IACnE,OAAO;QACL,kBAAkB;QAClB,SAAS;QACT,YAAY;QACZ,qBAAqB,EAAE,CAAI,eAAe,EAAS,QAAQ,KAAmC;YAC5F,OAAO,kBAAkB,CAAC,QAAQ,CAAC;QACzC,CAAK;QACD,eAAe,EAAE,IAAM,oBAAoB,EAAE,CAAC,QAAQ,EAAE;QACxD,iBAAiB,EAAE,IAAM,oBAAoB,EAAE,CAAC,iBAAiB,EAAE;IACvE,CAAG;AACH", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2171, "column": 0}, "map": {"version": 3, "file": "index.js", "sources": ["file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/%40sentry%2Bcore%409.22.0/node_modules/%40sentry/core/src/asyncContext/index.ts"], "sourcesContent": ["import type { Carrier } from './../carrier';\nimport { getMainCarrier, getSentryCarrier } from './../carrier';\nimport { getStackAsyncContextStrategy } from './stackStrategy';\nimport type { AsyncContextStrategy } from './types';\n\n/**\n * @private Private API with no semver guarantees!\n *\n * Sets the global async context strategy\n */\nexport function setAsyncContextStrategy(strategy: AsyncContextStrategy | undefined): void {\n  // Get main carrier (global for every environment)\n  const registry = getMainCarrier();\n  const sentry = getSentryCarrier(registry);\n  sentry.acs = strategy;\n}\n\n/**\n * Get the current async context strategy.\n * If none has been setup, the default will be used.\n */\nexport function getAsyncContextStrategy(carrier: Carrier): AsyncContextStrategy {\n  const sentry = getSentryCarrier(carrier);\n\n  if (sentry.acs) {\n    return sentry.acs;\n  }\n\n  // Otherwise, use the default one (stack)\n  return getStackAsyncContextStrategy();\n}\n"], "names": [], "mappings": ";;;;;;;;AAKA;;;;CAIA,GACO,SAAS,uBAAuB,CAAC,QAAQ,EAA0C;IAC1F,kDAAA;IACE,MAAM,QAAA,oOAAW,iBAAA,AAAc,EAAE;IACjC,MAAM,MAAO,IAAE,mPAAA,AAAgB,EAAC,QAAQ,CAAC;IACzC,MAAM,CAAC,GAAI,GAAE,QAAQ;AACvB;AAEA;;;CAGA,GACO,SAAS,uBAAuB,CAAC,OAAO,EAAiC;IAC9E,MAAM,MAAO,oOAAE,mBAAA,AAAgB,EAAC,OAAO,CAAC;IAExC,IAAI,MAAM,CAAC,GAAG,EAAE;QACd,OAAO,MAAM,CAAC,GAAG;IACrB;IAEA,yCAAA;IACE,8PAAO,+BAAA,AAA4B,EAAE;AACvC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2208, "column": 0}, "map": {"version": 3, "file": "currentScopes.js", "sources": ["file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/%40sentry%2Bcore%409.22.0/node_modules/%40sentry/core/src/currentScopes.ts"], "sourcesContent": ["import { getAsyncContextStrategy } from './asyncContext';\nimport { getGlobal<PERSON>ingleton, getMainCarrier } from './carrier';\nimport type { Client } from './client';\nimport { Scope } from './scope';\nimport type { TraceContext } from './types-hoist/context';\nimport { generateSpanId } from './utils-hoist/propagationContext';\n\n/**\n * Get the currently active scope.\n */\nexport function getCurrentScope(): Scope {\n  const carrier = getMainCarrier();\n  const acs = getAsyncContextStrategy(carrier);\n  return acs.getCurrentScope();\n}\n\n/**\n * Get the currently active isolation scope.\n * The isolation scope is active for the current execution context.\n */\nexport function getIsolationScope(): Scope {\n  const carrier = getMainCarrier();\n  const acs = getAsyncContextStrategy(carrier);\n  return acs.getIsolationScope();\n}\n\n/**\n * Get the global scope.\n * This scope is applied to _all_ events.\n */\nexport function getGlobalScope(): Scope {\n  return getGlobalSingleton('globalScope', () => new Scope());\n}\n\n/**\n * Creates a new scope with and executes the given operation within.\n * The scope is automatically removed once the operation\n * finishes or throws.\n */\nexport function withScope<T>(callback: (scope: Scope) => T): T;\n/**\n * Set the given scope as the active scope in the callback.\n */\nexport function withScope<T>(scope: Scope | undefined, callback: (scope: Scope) => T): T;\n/**\n * Either creates a new active scope, or sets the given scope as active scope in the given callback.\n */\nexport function withScope<T>(\n  ...rest: [callback: (scope: Scope) => T] | [scope: Scope | undefined, callback: (scope: Scope) => T]\n): T {\n  const carrier = getMainCarrier();\n  const acs = getAsyncContextStrategy(carrier);\n\n  // If a scope is defined, we want to make this the active scope instead of the default one\n  if (rest.length === 2) {\n    const [scope, callback] = rest;\n\n    if (!scope) {\n      return acs.withScope(callback);\n    }\n\n    return acs.withSetScope(scope, callback);\n  }\n\n  return acs.withScope(rest[0]);\n}\n\n/**\n * Attempts to fork the current isolation scope and the current scope based on the current async context strategy. If no\n * async context strategy is set, the isolation scope and the current scope will not be forked (this is currently the\n * case, for example, in the browser).\n *\n * Usage of this function in environments without async context strategy is discouraged and may lead to unexpected behaviour.\n *\n * This function is intended for Sentry SDK and SDK integration development. It is not recommended to be used in \"normal\"\n * applications directly because it comes with pitfalls. Use at your own risk!\n */\nexport function withIsolationScope<T>(callback: (isolationScope: Scope) => T): T;\n/**\n * Set the provided isolation scope as active in the given callback. If no\n * async context strategy is set, the isolation scope and the current scope will not be forked (this is currently the\n * case, for example, in the browser).\n *\n * Usage of this function in environments without async context strategy is discouraged and may lead to unexpected behaviour.\n *\n * This function is intended for Sentry SDK and SDK integration development. It is not recommended to be used in \"normal\"\n * applications directly because it comes with pitfalls. Use at your own risk!\n *\n * If you pass in `undefined` as a scope, it will fork a new isolation scope, the same as if no scope is passed.\n */\nexport function withIsolationScope<T>(isolationScope: Scope | undefined, callback: (isolationScope: Scope) => T): T;\n/**\n * Either creates a new active isolation scope, or sets the given isolation scope as active scope in the given callback.\n */\nexport function withIsolationScope<T>(\n  ...rest:\n    | [callback: (isolationScope: Scope) => T]\n    | [isolationScope: Scope | undefined, callback: (isolationScope: Scope) => T]\n): T {\n  const carrier = getMainCarrier();\n  const acs = getAsyncContextStrategy(carrier);\n\n  // If a scope is defined, we want to make this the active scope instead of the default one\n  if (rest.length === 2) {\n    const [isolationScope, callback] = rest;\n\n    if (!isolationScope) {\n      return acs.withIsolationScope(callback);\n    }\n\n    return acs.withSetIsolationScope(isolationScope, callback);\n  }\n\n  return acs.withIsolationScope(rest[0]);\n}\n\n/**\n * Get the currently active client.\n */\nexport function getClient<C extends Client>(): C | undefined {\n  return getCurrentScope().getClient<C>();\n}\n\n/**\n * Get a trace context for the given scope.\n */\nexport function getTraceContextFromScope(scope: Scope): TraceContext {\n  const propagationContext = scope.getPropagationContext();\n\n  const { traceId, parentSpanId, propagationSpanId } = propagationContext;\n\n  const traceContext: TraceContext = {\n    trace_id: traceId,\n    span_id: propagationSpanId || generateSpanId(),\n  };\n\n  if (parentSpanId) {\n    traceContext.parent_span_id = parentSpanId;\n  }\n\n  return traceContext;\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAOA;;CAEA,GACO,SAAS,eAAe,GAAU;IACvC,MAAM,OAAA,oOAAU,iBAAA,AAAc,EAAE;IAChC,MAAM,GAAI,iPAAE,2BAAA,AAAuB,EAAC,OAAO,CAAC;IAC5C,OAAO,GAAG,CAAC,eAAe,EAAE;AAC9B;AAEA;;;CAGA,GACO,SAAS,iBAAiB,GAAU;IACzC,MAAM,OAAA,oOAAU,iBAAA,AAAc,EAAE;IAChC,MAAM,GAAI,IAAE,wQAAA,AAAuB,EAAC,OAAO,CAAC;IAC5C,OAAO,GAAG,CAAC,iBAAiB,EAAE;AAChC;AAEA;;;CAGA,GACO,SAAS,cAAc,GAAU;IACtC,WAAO,kPAAA,AAAkB,EAAC,aAAa,EAAE,IAAM,+NAAI,QAAK,EAAE,CAAC;AAC7D;AAEA;;;;CAIA,GAMA;;CAEA,GACO,SAAS,SAAS,CACvB,GAAG,IAAA;IAEH,MAAM,OAAA,oOAAU,iBAAA,AAAc,EAAE;IAChC,MAAM,GAAI,OAAE,qQAAA,AAAuB,EAAC,OAAO,CAAC;IAE9C,0FAAA;IACE,IAAI,IAAI,CAAC,MAAO,KAAI,CAAC,EAAE;QACrB,MAAM,CAAC,KAAK,EAAE,QAAQ,CAAA,GAAI,IAAI;QAE9B,IAAI,CAAC,KAAK,EAAE;YACV,OAAO,GAAG,CAAC,SAAS,CAAC,QAAQ,CAAC;QACpC;QAEI,OAAO,GAAG,CAAC,YAAY,CAAC,KAAK,EAAE,QAAQ,CAAC;IAC5C;IAEE,OAAO,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAC/B;AAEA;;;;;;;;;CASA,GAeA;;CAEA,GACO,SAAS,kBAAkB,CAChC,GAAG,IAAA;IAIH,MAAM,OAAA,oOAAU,iBAAA,AAAc,EAAE;IAChC,MAAM,GAAI,OAAE,qQAAA,AAAuB,EAAC,OAAO,CAAC;IAE9C,0FAAA;IACE,IAAI,IAAI,CAAC,MAAO,KAAI,CAAC,EAAE;QACrB,MAAM,CAAC,cAAc,EAAE,QAAQ,CAAA,GAAI,IAAI;QAEvC,IAAI,CAAC,cAAc,EAAE;YACnB,OAAO,GAAG,CAAC,kBAAkB,CAAC,QAAQ,CAAC;QAC7C;QAEI,OAAO,GAAG,CAAC,qBAAqB,CAAC,cAAc,EAAE,QAAQ,CAAC;IAC9D;IAEE,OAAO,GAAG,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACxC;AAEA;;CAEA,GACO,SAAS,SAAS,GAAoC;IAC3D,OAAO,eAAe,EAAE,CAAC,SAAS,EAAK;AACzC;AAEA;;CAEA,GACO,SAAS,wBAAwB,CAAC,KAAK,EAAuB;IACnE,MAAM,kBAAmB,GAAE,KAAK,CAAC,qBAAqB,EAAE;IAExD,MAAM,EAAE,OAAO,EAAE,YAAY,EAAE,iBAAA,EAAoB,GAAE,kBAAkB;IAEvE,MAAM,YAAY,GAAiB;QACjC,QAAQ,EAAE,OAAO;QACjB,OAAO,EAAE,iBAAA,kQAAqB,iBAAA,AAAc,EAAE;IAClD,CAAG;IAED,IAAI,YAAY,EAAE;QAChB,YAAY,CAAC,cAAe,GAAE,YAAY;IAC9C;IAEE,OAAO,YAAY;AACrB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2316, "column": 0}, "map": {"version": 3, "file": "constants.js", "sources": ["file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/%40sentry%2Bcore%409.22.0/node_modules/%40sentry/core/src/constants.ts"], "sourcesContent": ["export const DEFAULT_ENVIRONMENT = 'production';\n"], "names": [], "mappings": ";;;AAAO,MAAM,mBAAoB,GAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2328, "column": 0}, "map": {"version": 3, "file": "syncpromise.js", "sources": ["file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/%40sentry%2Bcore%409.22.0/node_modules/%40sentry/core/src/utils-hoist/syncpromise.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-explicit-any */\nimport { isThenable } from './is';\n\n/** SyncPromise internal states */\nconst enum States {\n  /** Pending */\n  PENDING = 0,\n  /** Resolved / OK */\n  RESOLVED = 1,\n  /** Rejected / Error */\n  REJECTED = 2,\n}\n\n// Overloads so we can call resolvedSyncPromise without arguments and generic argument\nexport function resolvedSyncPromise(): PromiseLike<void>;\nexport function resolvedSyncPromise<T>(value: T | PromiseLike<T>): PromiseLike<T>;\n\n/**\n * Creates a resolved sync promise.\n *\n * @param value the value to resolve the promise with\n * @returns the resolved sync promise\n */\nexport function resolvedSyncPromise<T>(value?: T | PromiseLike<T>): PromiseLike<T> {\n  return new SyncPromise(resolve => {\n    resolve(value);\n  });\n}\n\n/**\n * Creates a rejected sync promise.\n *\n * @param value the value to reject the promise with\n * @returns the rejected sync promise\n */\nexport function rejectedSyncPromise<T = never>(reason?: any): PromiseLike<T> {\n  return new SyncPromise((_, reject) => {\n    reject(reason);\n  });\n}\n\ntype Executor<T> = (resolve: (value?: T | PromiseLike<T> | null) => void, reject: (reason?: any) => void) => void;\n\n/**\n * Thenable class that behaves like a Promise and follows it's interface\n * but is not async internally\n */\nexport class SyncPromise<T> implements PromiseLike<T> {\n  private _state: States;\n  private _handlers: Array<[boolean, (value: T) => void, (reason: any) => any]>;\n  private _value: any;\n\n  public constructor(executor: Executor<T>) {\n    this._state = States.PENDING;\n    this._handlers = [];\n\n    this._runExecutor(executor);\n  }\n\n  /** @inheritdoc */\n  public then<TResult1 = T, TResult2 = never>(\n    onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | null,\n    onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | null,\n  ): PromiseLike<TResult1 | TResult2> {\n    return new SyncPromise((resolve, reject) => {\n      this._handlers.push([\n        false,\n        result => {\n          if (!onfulfilled) {\n            // TODO: ¯\\_(ツ)_/¯\n            // TODO: FIXME\n            resolve(result as any);\n          } else {\n            try {\n              resolve(onfulfilled(result));\n            } catch (e) {\n              reject(e);\n            }\n          }\n        },\n        reason => {\n          if (!onrejected) {\n            reject(reason);\n          } else {\n            try {\n              resolve(onrejected(reason));\n            } catch (e) {\n              reject(e);\n            }\n          }\n        },\n      ]);\n      this._executeHandlers();\n    });\n  }\n\n  /** @inheritdoc */\n  public catch<TResult = never>(\n    onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | null,\n  ): PromiseLike<T | TResult> {\n    return this.then(val => val, onrejected);\n  }\n\n  /** @inheritdoc */\n  public finally<TResult>(onfinally?: (() => void) | null): PromiseLike<TResult> {\n    return new SyncPromise<TResult>((resolve, reject) => {\n      let val: TResult | any;\n      let isRejected: boolean;\n\n      return this.then(\n        value => {\n          isRejected = false;\n          val = value;\n          if (onfinally) {\n            onfinally();\n          }\n        },\n        reason => {\n          isRejected = true;\n          val = reason;\n          if (onfinally) {\n            onfinally();\n          }\n        },\n      ).then(() => {\n        if (isRejected) {\n          reject(val);\n          return;\n        }\n\n        resolve(val as unknown as any);\n      });\n    });\n  }\n\n  /** Excute the resolve/reject handlers. */\n  private _executeHandlers(): void {\n    if (this._state === States.PENDING) {\n      return;\n    }\n\n    const cachedHandlers = this._handlers.slice();\n    this._handlers = [];\n\n    cachedHandlers.forEach(handler => {\n      if (handler[0]) {\n        return;\n      }\n\n      if (this._state === States.RESOLVED) {\n        handler[1](this._value as unknown as any);\n      }\n\n      if (this._state === States.REJECTED) {\n        handler[2](this._value);\n      }\n\n      handler[0] = true;\n    });\n  }\n\n  /** Run the executor for the SyncPromise. */\n  private _runExecutor(executor: Executor<T>): void {\n    const setResult = (state: States, value?: T | PromiseLike<T> | any): void => {\n      if (this._state !== States.PENDING) {\n        return;\n      }\n\n      if (isThenable(value)) {\n        void (value as PromiseLike<T>).then(resolve, reject);\n        return;\n      }\n\n      this._state = state;\n      this._value = value;\n\n      this._executeHandlers();\n    };\n\n    const resolve = (value: unknown): void => {\n      setResult(States.RESOLVED, value);\n    };\n\n    const reject = (reason: unknown): void => {\n      setResult(States.REJECTED, reason);\n    };\n\n    try {\n      executor(resolve, reject);\n    } catch (e) {\n      reject(e);\n    }\n  }\n}\n"], "names": [], "mappings": ";;;;;;;AAAA,qDAAA,GAGA,gCAAA,GACA,IAAkB,MAAA,CAAA;AAAA,CAAA,SAAA,MAAA,EAAA;IAClB,YAAA,GACE,MAAA,OAAA,GAAU,CAAC,CAAA;IAAA,MAAA,CAAA,MAAA,CAAA,SAAA,CAAA,GAAA,OAAA,CAAA,GAAA,SAAA;IACb,kBAAA,GACE,MAAA,QAAA,GAAW,CAAC,CAAA;IAAA,MAAA,CAAA,MAAA,CAAA,UAAA,CAAA,GAAA,QAAA,CAAA,GAAA,UAAA;IACd,qBAAA,GACE,MAAA,QAAA,GAAW,CAAC,CAAA;IAAA,MAAA,CAAA,MAAA,CAAA,UAAA,CAAA,GAAA,QAAA,CAAA,GAAA,UAAA;AACd,CAAA,EAAA,MAAA,IAAA,CAAA,MAAA,GAAA,CAAA,CAAA,CAAA,CAAA;AAEA,sFAAA;AAIA;;;;;CAKA,GACO,SAAS,mBAAmB,CAAI,KAAK,EAAuC;IACjF,OAAO,IAAI,WAAW,EAAC,WAAW;QAChC,OAAO,CAAC,KAAK,CAAC;IAClB,CAAG,CAAC;AACJ;AAEA;;;;;CAKA,GACO,SAAS,mBAAmB,CAAY,MAAM,EAAwB;IAC3E,OAAO,IAAI,WAAW,CAAC,CAAC,CAAC,EAAE,MAAM,KAAK;QACpC,MAAM,CAAC,MAAM,CAAC;IAClB,CAAG,CAAC;AACJ;AAIA;;;CAGA,GACO,MAAM,WAAW,CAA8B;IAK7C,WAAW,CAAC,QAAQ,CAAe;QACxC,IAAI,CAAC,MAAA,GAAS,MAAM,CAAC,OAAO;QAC5B,IAAI,CAAC,SAAU,GAAE,EAAE;QAEnB,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC;IAC/B;IAEA,gBAAA,GACS,IAAI,CACT,WAAW,EACX,UAAU,EACwB;QAClC,OAAO,IAAI,WAAW,CAAC,CAAC,OAAO,EAAE,MAAM,KAAK;YAC1C,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;gBAClB,KAAK;iBACL,UAAU;oBACR,IAAI,CAAC,WAAW,EAAE;wBAC5B,kBAAA;wBACA,cAAA;wBACY,OAAO,CAAC,MAAA,EAAc;oBAClC,OAAiB;wBACL,IAAI;4BACF,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;wBAC1C,CAAc,CAAA,OAAO,CAAC,EAAE;4BACV,MAAM,CAAC,CAAC,CAAC;wBACvB;oBACA;gBACA,CAAS;iBACD,UAAU;oBACR,IAAI,CAAC,UAAU,EAAE;wBACf,MAAM,CAAC,MAAM,CAAC;oBAC1B,OAAiB;wBACL,IAAI;4BACF,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;wBACzC,CAAc,CAAA,OAAO,CAAC,EAAE;4BACV,MAAM,CAAC,CAAC,CAAC;wBACvB;oBACA;gBACA,CAAS;aACF,CAAC;YACF,IAAI,CAAC,gBAAgB,EAAE;QAC7B,CAAK,CAAC;IACN;IAEA,gBAAA,GACS,KAAK,CACV,UAAU,EACgB;QAC1B,OAAO,IAAI,CAAC,IAAI,EAAC,MAAO,GAAG,EAAE,UAAU,CAAC;IAC5C;IAEA,gBAAA,GACS,OAAO,CAAU,SAAS,EAA8C;QAC7E,OAAO,IAAI,WAAW,CAAU,CAAC,OAAO,EAAE,MAAM,KAAK;YACnD,IAAI,GAAG;YACP,IAAI,UAAU;YAEd,OAAO,IAAI,CAAC,IAAI,EACd,SAAS;gBACP,UAAA,GAAa,KAAK;gBAClB,GAAA,GAAM,KAAK;gBACX,IAAI,SAAS,EAAE;oBACb,SAAS,EAAE;gBACvB;YACA,CAAS,EACD,UAAU;gBACR,UAAA,GAAa,IAAI;gBACjB,GAAA,GAAM,MAAM;gBACZ,IAAI,SAAS,EAAE;oBACb,SAAS,EAAE;gBACvB;YACA,CAAS,EACD,IAAI,CAAC,MAAM;gBACX,IAAI,UAAU,EAAE;oBACd,MAAM,CAAC,GAAG,CAAC;oBACX;gBACV;gBAEQ,OAAO,CAAC,GAAA,EAAsB;YACtC,CAAO,CAAC;QACR,CAAK,CAAC;IACN;IAEA,wCAAA,GACU,gBAAgB,GAAS;QAC/B,IAAI,IAAI,CAAC,MAAA,KAAW,MAAM,CAAC,OAAO,EAAE;YAClC;QACN;QAEI,MAAM,iBAAiB,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE;QAC7C,IAAI,CAAC,SAAU,GAAE,EAAE;QAEnB,cAAc,CAAC,OAAO,EAAC,WAAW;YAChC,IAAI,OAAO,CAAC,CAAC,CAAC,EAAE;gBACd;YACR;YAEM,IAAI,IAAI,CAAC,MAAA,KAAW,MAAM,CAAC,QAAQ,EAAE;gBACnC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAA,EAAyB;YACjD;YAEM,IAAI,IAAI,CAAC,MAAA,KAAW,MAAM,CAAC,QAAQ,EAAE;gBACnC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC;YAC/B;YAEM,OAAO,CAAC,CAAC,CAAA,GAAI,IAAI;QACvB,CAAK,CAAC;IACN;IAEA,0CAAA,GACU,YAAY,CAAC,QAAQ,EAAqB;QAChD,MAAM,SAAU,GAAE,CAAC,KAAK,EAAU,KAAK,KAAsC;YAC3E,IAAI,IAAI,CAAC,MAAA,KAAW,MAAM,CAAC,OAAO,EAAE;gBAClC;YACR;YAEM,KAAI,0PAAA,AAAU,EAAC,KAAK,CAAC,EAAE;gBACrB,KAAK,AAAC,KAAA,CAAyB,IAAI,CAAC,OAAO,EAAE,MAAM,CAAC;gBACpD;YACR;YAEM,IAAI,CAAC,MAAO,GAAE,KAAK;YACnB,IAAI,CAAC,MAAO,GAAE,KAAK;YAEnB,IAAI,CAAC,gBAAgB,EAAE;QAC7B,CAAK;QAED,MAAM,OAAA,GAAU,CAAC,KAAK,KAAoB;YACxC,SAAS,CAAC,MAAM,CAAC,QAAQ,EAAE,KAAK,CAAC;QACvC,CAAK;QAED,MAAM,MAAA,GAAS,CAAC,MAAM,KAAoB;YACxC,SAAS,CAAC,MAAM,CAAC,QAAQ,EAAE,MAAM,CAAC;QACxC,CAAK;QAED,IAAI;YACF,QAAQ,CAAC,OAAO,EAAE,MAAM,CAAC;QAC/B,CAAM,CAAA,OAAO,CAAC,EAAE;YACV,MAAM,CAAC,CAAC,CAAC;QACf;IACA;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2487, "column": 0}, "map": {"version": 3, "file": "eventProcessors.js", "sources": ["file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/%40sentry%2Bcore%409.22.0/node_modules/%40sentry/core/src/eventProcessors.ts"], "sourcesContent": ["import { DEBUG_BUILD } from './debug-build';\nimport type { Event, EventHint } from './types-hoist/event';\nimport type { EventProcessor } from './types-hoist/eventprocessor';\nimport { isThenable } from './utils-hoist/is';\nimport { logger } from './utils-hoist/logger';\nimport { SyncPromise } from './utils-hoist/syncpromise';\n\n/**\n * Process an array of event processors, returning the processed event (or `null` if the event was dropped).\n */\nexport function notifyEventProcessors(\n  processors: EventProcessor[],\n  event: Event | null,\n  hint: EventHint,\n  index: number = 0,\n): PromiseLike<Event | null> {\n  return new SyncPromise<Event | null>((resolve, reject) => {\n    const processor = processors[index];\n    if (event === null || typeof processor !== 'function') {\n      resolve(event);\n    } else {\n      const result = processor({ ...event }, hint) as Event | null;\n\n      DEBUG_BUILD && processor.id && result === null && logger.log(`Event processor \"${processor.id}\" dropped event`);\n\n      if (isThenable(result)) {\n        void result\n          .then(final => notifyEventProcessors(processors, final, hint, index + 1).then(resolve))\n          .then(null, reject);\n      } else {\n        void notifyEventProcessors(processors, result, hint, index + 1)\n          .then(resolve)\n          .then(null, reject);\n      }\n    }\n  });\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAOA;;CAEA,GACO,SAAS,qBAAqB,CACnC,UAAU,EACV,KAAK,EACL,IAAI,EACJ,KAAK,GAAW,CAAC;IAEjB,OAAO,sPAAI,eAAW,CAAe,CAAC,OAAO,EAAE,MAAM,KAAK;QACxD,MAAM,SAAU,GAAE,UAAU,CAAC,KAAK,CAAC;QACnC,IAAI,KAAA,KAAU,IAAA,IAAQ,OAAO,SAAA,KAAc,UAAU,EAAE;YACrD,OAAO,CAAC,KAAK,CAAC;QACpB,OAAW;YACL,MAAM,MAAO,GAAE,SAAS,CAAC;gBAAE,GAAG,KAAM;YAAA,CAAC,EAAE,IAAI,CAAE;gPAE7C,cAAA,IAAe,SAAS,CAAC,EAAA,IAAM,MAAA,KAAW,IAAA,kPAAQ,SAAM,CAAC,GAAG,CAAC,CAAC,iBAAiB,EAAE,SAAS,CAAC,EAAE,CAAC,eAAe,CAAC,CAAC;YAE/G,kPAAI,aAAA,AAAU,EAAC,MAAM,CAAC,EAAE;gBACtB,KAAK,OACF,IAAI,EAAC,KAAA,GAAS,qBAAqB,CAAC,UAAU,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,EACrF,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC;YAC7B,OAAa;gBACL,KAAK,qBAAqB,CAAC,UAAU,EAAE,MAAM,EAAE,IAAI,EAAE,KAAM,GAAE,CAAC,EAC3D,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC;YAC7B;QACA;IACA,CAAG,CAAC;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2526, "column": 0}, "map": {"version": 3, "file": "debug-ids.js", "sources": ["file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/%40sentry%2Bcore%409.22.0/node_modules/%40sentry/core/src/utils-hoist/debug-ids.ts"], "sourcesContent": ["import type { DebugImage } from '../types-hoist/debugMeta';\nimport type { StackParser } from '../types-hoist/stacktrace';\nimport { GLOBAL_OBJ } from './worldwide';\n\ntype StackString = string;\ntype CachedResult = [string, string];\n\nlet parsedStackResults: Record<StackString, CachedResult> | undefined;\nlet lastKeysCount: number | undefined;\nlet cachedFilenameDebugIds: Record<string, string> | undefined;\n\n/**\n * Returns a map of filenames to debug identifiers.\n */\nexport function getFilenameToDebugIdMap(stackParser: StackParser): Record<string, string> {\n  const debugIdMap = GLOBAL_OBJ._sentryDebugIds;\n  if (!debugIdMap) {\n    return {};\n  }\n\n  const debugIdKeys = Object.keys(debugIdMap);\n\n  // If the count of registered globals hasn't changed since the last call, we\n  // can just return the cached result.\n  if (cachedFilenameDebugIds && debugIdKeys.length === lastKeysCount) {\n    return cachedFilenameDebugIds;\n  }\n\n  lastKeysCount = debugIdKeys.length;\n\n  // Build a map of filename -> debug_id.\n  cachedFilenameDebugIds = debugIdKeys.reduce<Record<string, string>>((acc, stackKey) => {\n    if (!parsedStackResults) {\n      parsedStackResults = {};\n    }\n\n    const result = parsedStackResults[stackKey];\n\n    if (result) {\n      acc[result[0]] = result[1];\n    } else {\n      const parsedStack = stackParser(stackKey);\n\n      for (let i = parsedStack.length - 1; i >= 0; i--) {\n        const stackFrame = parsedStack[i];\n        const filename = stackFrame?.filename;\n        const debugId = debugIdMap[stackKey];\n\n        if (filename && debugId) {\n          acc[filename] = debugId;\n          parsedStackResults[stackKey] = [filename, debugId];\n          break;\n        }\n      }\n    }\n\n    return acc;\n  }, {});\n\n  return cachedFilenameDebugIds;\n}\n\n/**\n * Returns a list of debug images for the given resources.\n */\nexport function getDebugImagesForResources(\n  stackParser: StackParser,\n  resource_paths: ReadonlyArray<string>,\n): DebugImage[] {\n  const filenameDebugIdMap = getFilenameToDebugIdMap(stackParser);\n\n  if (!filenameDebugIdMap) {\n    return [];\n  }\n\n  const images: DebugImage[] = [];\n  for (const path of resource_paths) {\n    if (path && filenameDebugIdMap[path]) {\n      images.push({\n        type: 'sourcemap',\n        code_file: path,\n        debug_id: filenameDebugIdMap[path] as string,\n      });\n    }\n  }\n\n  return images;\n}\n"], "names": [], "mappings": ";;;;;;AAOA,IAAI,kBAAkB;AACtB,IAAI,aAAa;AACjB,IAAI,sBAAsB;AAE1B;;CAEA,GACO,SAAS,uBAAuB,CAAC,WAAW,EAAuC;IACxF,MAAM,UAAA,oPAAa,aAAU,CAAC,eAAe;IAC7C,IAAI,CAAC,UAAU,EAAE;QACf,OAAO,CAAA,CAAE;IACb;IAEE,MAAM,cAAc,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;IAE7C,4EAAA;IACA,qCAAA;IACE,IAAI,sBAAuB,IAAG,WAAW,CAAC,MAAA,KAAW,aAAa,EAAE;QAClE,OAAO,sBAAsB;IACjC;IAEE,aAAc,GAAE,WAAW,CAAC,MAAM;IAEpC,uCAAA;IACE,sBAAA,GAAyB,WAAW,CAAC,MAAM,CAAyB,CAAC,GAAG,EAAE,QAAQ,KAAK;QACrF,IAAI,CAAC,kBAAkB,EAAE;YACvB,kBAAA,GAAqB,CAAA,CAAE;QAC7B;QAEI,MAAM,MAAO,GAAE,kBAAkB,CAAC,QAAQ,CAAC;QAE3C,IAAI,MAAM,EAAE;YACV,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAA,GAAI,MAAM,CAAC,CAAC,CAAC;QAChC,OAAW;YACL,MAAM,WAAY,GAAE,WAAW,CAAC,QAAQ,CAAC;YAEzC,IAAK,IAAI,CAAE,GAAE,WAAW,CAAC,MAAA,GAAS,CAAC,EAAE,CAAE,IAAG,CAAC,EAAE,CAAC,EAAE,CAAE;gBAChD,MAAM,UAAW,GAAE,WAAW,CAAC,CAAC,CAAC;gBACjC,MAAM,QAAA,GAAW,UAAU,EAAE,QAAQ;gBACrC,MAAM,OAAQ,GAAE,UAAU,CAAC,QAAQ,CAAC;gBAEpC,IAAI,QAAS,IAAG,OAAO,EAAE;oBACvB,GAAG,CAAC,QAAQ,CAAA,GAAI,OAAO;oBACvB,kBAAkB,CAAC,QAAQ,CAAA,GAAI;wBAAC,QAAQ;wBAAE,OAAO;qBAAC;oBAClD;gBACV;YACA;QACA;QAEI,OAAO,GAAG;IACd,CAAG,EAAE,CAAA,CAAE,CAAC;IAEN,OAAO,sBAAsB;AAC/B;AAEA;;CAEA,GACO,SAAS,0BAA0B,CACxC,WAAW,EACX,cAAc;IAEd,MAAM,kBAAmB,GAAE,uBAAuB,CAAC,WAAW,CAAC;IAE/D,IAAI,CAAC,kBAAkB,EAAE;QACvB,OAAO,EAAE;IACb;IAEE,MAAM,MAAM,GAAiB,EAAE;IAC/B,KAAK,MAAM,IAAK,IAAG,cAAc,CAAE;QACjC,IAAI,IAAK,IAAG,kBAAkB,CAAC,IAAI,CAAC,EAAE;YACpC,MAAM,CAAC,IAAI,CAAC;gBACV,IAAI,EAAE,WAAW;gBACjB,SAAS,EAAE,IAAI;gBACf,QAAQ,EAAE,kBAAkB,CAAC,IAAI,CAAE;YAC3C,CAAO,CAAC;QACR;IACA;IAEE,OAAO,MAAM;AACf", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2604, "column": 0}, "map": {"version": 3, "file": "stacktrace.js", "sources": ["file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/%40sentry%2Bcore%409.22.0/node_modules/%40sentry/core/src/utils-hoist/stacktrace.ts"], "sourcesContent": ["import type { Event } from '../types-hoist/event';\nimport type { StackFrame } from '../types-hoist/stackframe';\nimport type { <PERSON>ack<PERSON>ineParser, StackParser } from '../types-hoist/stacktrace';\n\nconst STACKTRACE_FRAME_LIMIT = 50;\nexport const UNKNOWN_FUNCTION = '?';\n// Used to sanitize webpack (error: *) wrapped stack errors\nconst WEBPACK_ERROR_REGEXP = /\\(error: (.*)\\)/;\nconst STRIP_FRAME_REGEXP = /captureMessage|captureException/;\n\n/**\n * Creates a stack parser with the supplied line parsers\n *\n * StackFrames are returned in the correct order for Sentry Exception\n * frames and with Sentry SDK internal frames removed from the top and bottom\n *\n */\nexport function createStackParser(...parsers: StackLineParser[]): StackParser {\n  const sortedParsers = parsers.sort((a, b) => a[0] - b[0]).map(p => p[1]);\n\n  return (stack: string, skipFirstLines: number = 0, framesToPop: number = 0): StackFrame[] => {\n    const frames: StackFrame[] = [];\n    const lines = stack.split('\\n');\n\n    for (let i = skipFirstLines; i < lines.length; i++) {\n      const line = lines[i] as string;\n      // Ignore lines over 1kb as they are unlikely to be stack frames.\n      // Many of the regular expressions use backtracking which results in run time that increases exponentially with\n      // input size. Huge strings can result in hangs/Denial of Service:\n      // https://github.com/getsentry/sentry-javascript/issues/2286\n      if (line.length > 1024) {\n        continue;\n      }\n\n      // https://github.com/getsentry/sentry-javascript/issues/5459\n      // Remove webpack (error: *) wrappers\n      const cleanedLine = WEBPACK_ERROR_REGEXP.test(line) ? line.replace(WEBPACK_ERROR_REGEXP, '$1') : line;\n\n      // https://github.com/getsentry/sentry-javascript/issues/7813\n      // Skip Error: lines\n      if (cleanedLine.match(/\\S*Error: /)) {\n        continue;\n      }\n\n      for (const parser of sortedParsers) {\n        const frame = parser(cleanedLine);\n\n        if (frame) {\n          frames.push(frame);\n          break;\n        }\n      }\n\n      if (frames.length >= STACKTRACE_FRAME_LIMIT + framesToPop) {\n        break;\n      }\n    }\n\n    return stripSentryFramesAndReverse(frames.slice(framesToPop));\n  };\n}\n\n/**\n * Gets a stack parser implementation from Options.stackParser\n * @see Options\n *\n * If options contains an array of line parsers, it is converted into a parser\n */\nexport function stackParserFromStackParserOptions(stackParser: StackParser | StackLineParser[]): StackParser {\n  if (Array.isArray(stackParser)) {\n    return createStackParser(...stackParser);\n  }\n  return stackParser;\n}\n\n/**\n * Removes Sentry frames from the top and bottom of the stack if present and enforces a limit of max number of frames.\n * Assumes stack input is ordered from top to bottom and returns the reverse representation so call site of the\n * function that caused the crash is the last frame in the array.\n * @hidden\n */\nexport function stripSentryFramesAndReverse(stack: ReadonlyArray<StackFrame>): StackFrame[] {\n  if (!stack.length) {\n    return [];\n  }\n\n  const localStack = Array.from(stack);\n\n  // If stack starts with one of our API calls, remove it (starts, meaning it's the top of the stack - aka last call)\n  if (/sentryWrapped/.test(getLastStackFrame(localStack).function || '')) {\n    localStack.pop();\n  }\n\n  // Reversing in the middle of the procedure allows us to just pop the values off the stack\n  localStack.reverse();\n\n  // If stack ends with one of our internal API calls, remove it (ends, meaning it's the bottom of the stack - aka top-most call)\n  if (STRIP_FRAME_REGEXP.test(getLastStackFrame(localStack).function || '')) {\n    localStack.pop();\n\n    // When using synthetic events, we will have a 2 levels deep stack, as `new Error('Sentry syntheticException')`\n    // is produced within the scope itself, making it:\n    //\n    //   Sentry.captureException()\n    //   scope.captureException()\n    //\n    // instead of just the top `Sentry` call itself.\n    // This forces us to possibly strip an additional frame in the exact same was as above.\n    if (STRIP_FRAME_REGEXP.test(getLastStackFrame(localStack).function || '')) {\n      localStack.pop();\n    }\n  }\n\n  return localStack.slice(0, STACKTRACE_FRAME_LIMIT).map(frame => ({\n    ...frame,\n    filename: frame.filename || getLastStackFrame(localStack).filename,\n    function: frame.function || UNKNOWN_FUNCTION,\n  }));\n}\n\nfunction getLastStackFrame(arr: StackFrame[]): StackFrame {\n  return arr[arr.length - 1] || {};\n}\n\nconst defaultFunctionName = '<anonymous>';\n\n/**\n * Safely extract function name from itself\n */\nexport function getFunctionName(fn: unknown): string {\n  try {\n    if (!fn || typeof fn !== 'function') {\n      return defaultFunctionName;\n    }\n    return fn.name || defaultFunctionName;\n  } catch (e) {\n    // Just accessing custom props in some Selenium environments\n    // can cause a \"Permission denied\" exception (see raven-js#495).\n    return defaultFunctionName;\n  }\n}\n\n/**\n * Get's stack frames from an event without needing to check for undefined properties.\n */\nexport function getFramesFromEvent(event: Event): StackFrame[] | undefined {\n  const exception = event.exception;\n\n  if (exception) {\n    const frames: StackFrame[] = [];\n    try {\n      // @ts-expect-error Object could be undefined\n      exception.values.forEach(value => {\n        // @ts-expect-error Value could be undefined\n        if (value.stacktrace.frames) {\n          // @ts-expect-error Value could be undefined\n          frames.push(...value.stacktrace.frames);\n        }\n      });\n      return frames;\n    } catch (_oO) {\n      return undefined;\n    }\n  }\n  return undefined;\n}\n"], "names": [], "mappings": ";;;;;;;;AAIA,MAAM,sBAAA,GAAyB,EAAE;AAC1B,MAAM,gBAAiB,GAAE;AAChC,2DAAA;AACA,MAAM,oBAAA,GAAuB,iBAAiB;AAC9C,MAAM,kBAAA,GAAqB,iCAAiC;AAE5D;;;;;;CAMA,GACO,SAAS,iBAAiB,CAAC,GAAG,OAAO,EAAkC;IAC5E,MAAM,aAAA,GAAgB,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,GAAK,CAAC,CAAC,CAAC,CAAE,GAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,EAAC,CAAA,GAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IAExE,OAAO,CAAC,KAAK,EAAU,cAAc,GAAW,CAAC,EAAE,WAAW,GAAW,CAAC,KAAmB;QAC3F,MAAM,MAAM,GAAiB,EAAE;QAC/B,MAAM,QAAQ,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC;QAE/B,IAAK,IAAI,CAAA,GAAI,cAAc,EAAE,CAAE,GAAE,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,CAAE;YAClD,MAAM,IAAK,GAAE,KAAK,CAAC,CAAC,CAAE;YAC5B,iEAAA;YACA,+GAAA;YACA,kEAAA;YACA,6DAAA;YACM,IAAI,IAAI,CAAC,MAAO,GAAE,IAAI,EAAE;gBACtB;YACR;YAEA,6DAAA;YACA,qCAAA;YACM,MAAM,WAAY,GAAE,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAA,GAAI,IAAI,CAAC,OAAO,CAAC,oBAAoB,EAAE,IAAI,CAAA,GAAI,IAAI;YAE3G,6DAAA;YACA,oBAAA;YACM,IAAI,WAAW,CAAC,KAAK,CAAC,YAAY,CAAC,EAAE;gBACnC;YACR;YAEM,KAAK,MAAM,MAAO,IAAG,aAAa,CAAE;gBAClC,MAAM,KAAM,GAAE,MAAM,CAAC,WAAW,CAAC;gBAEjC,IAAI,KAAK,EAAE;oBACT,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC;oBAClB;gBACV;YACA;YAEM,IAAI,MAAM,CAAC,MAAA,IAAU,sBAAA,GAAyB,WAAW,EAAE;gBACzD;YACR;QACA;QAEI,OAAO,2BAA2B,CAAC,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;IACjE,CAAG;AACH;AAEA;;;;;CAKA,GACO,SAAS,iCAAiC,CAAC,WAAW,EAAgD;IAC3G,IAAI,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE;QAC9B,OAAO,iBAAiB,CAAC,GAAG,WAAW,CAAC;IAC5C;IACE,OAAO,WAAW;AACpB;AAEA;;;;;CAKA,GACO,SAAS,2BAA2B,CAAC,KAAK,EAA2C;IAC1F,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;QACjB,OAAO,EAAE;IACb;IAEE,MAAM,aAAa,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC;IAEtC,mHAAA;IACE,IAAI,eAAe,CAAC,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC,QAAA,IAAY,EAAE,CAAC,EAAE;QACtE,UAAU,CAAC,GAAG,EAAE;IACpB;IAEA,0FAAA;IACE,UAAU,CAAC,OAAO,EAAE;IAEtB,+HAAA;IACE,IAAI,kBAAkB,CAAC,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC,QAAA,IAAY,EAAE,CAAC,EAAE;QACzE,UAAU,CAAC,GAAG,EAAE;QAEpB,+GAAA;QACA,kDAAA;QACA,EAAA;QACA,8BAAA;QACA,6BAAA;QACA,EAAA;QACA,gDAAA;QACA,uFAAA;QACI,IAAI,kBAAkB,CAAC,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC,QAAA,IAAY,EAAE,CAAC,EAAE;YACzE,UAAU,CAAC,GAAG,EAAE;QACtB;IACA;IAEE,OAAO,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE,sBAAsB,CAAC,CAAC,GAAG,EAAC,KAAA,GAAA,CAAU;YAC/D,GAAG,KAAK;YACR,QAAQ,EAAE,KAAK,CAAC,QAAA,IAAY,iBAAiB,CAAC,UAAU,CAAC,CAAC,QAAQ;YAClE,QAAQ,EAAE,KAAK,CAAC,QAAA,IAAY,gBAAgB;QAChD,CAAG,CAAC,CAAC;AACL;AAEA,SAAS,iBAAiB,CAAC,GAAG,EAA4B;IACxD,OAAO,GAAG,CAAC,GAAG,CAAC,MAAO,GAAE,CAAC,CAAA,IAAK,CAAA,CAAE;AAClC;AAEA,MAAM,mBAAA,GAAsB,aAAa;AAEzC;;CAEA,GACO,SAAS,eAAe,CAAC,EAAE,EAAmB;IACnD,IAAI;QACF,IAAI,CAAC,EAAA,IAAM,OAAO,EAAA,KAAO,UAAU,EAAE;YACnC,OAAO,mBAAmB;QAChC;QACI,OAAO,EAAE,CAAC,IAAA,IAAQ,mBAAmB;IACzC,CAAI,CAAA,OAAO,CAAC,EAAE;QACd,4DAAA;QACA,gEAAA;QACI,OAAO,mBAAmB;IAC9B;AACA;AAEA;;CAEA,GACO,SAAS,kBAAkB,CAAC,KAAK,EAAmC;IACzE,MAAM,SAAA,GAAY,KAAK,CAAC,SAAS;IAEjC,IAAI,SAAS,EAAE;QACb,MAAM,MAAM,GAAiB,EAAE;QAC/B,IAAI;YACR,6CAAA;YACM,SAAS,CAAC,MAAM,CAAC,OAAO,EAAC,SAAS;gBACxC,4CAAA;gBACQ,IAAI,KAAK,CAAC,UAAU,CAAC,MAAM,EAAE;oBACrC,4CAAA;oBACU,MAAM,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,UAAU,CAAC,MAAM,CAAC;gBACjD;YACA,CAAO,CAAC;YACF,OAAO,MAAM;QACnB,CAAM,CAAA,OAAO,GAAG,EAAE;YACZ,OAAO,SAAS;QACtB;IACA;IACE,OAAO,SAAS;AAClB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2755, "column": 0}, "map": {"version": 3, "file": "normalize.js", "sources": ["file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/%40sentry%2Bcore%409.22.0/node_modules/%40sentry/core/src/utils-hoist/normalize.ts"], "sourcesContent": ["import type { Primitive } from '../types-hoist/misc';\nimport { isSyntheticEvent, isVueViewModel } from './is';\nimport { convertToPlainObject } from './object';\nimport { getFunctionName } from './stacktrace';\n\ntype Prototype = { constructor?: (...args: unknown[]) => unknown };\n// This is a hack to placate TS, relying on the fact that technically, arrays are objects with integer keys. Normally we\n// think of those keys as actual numbers, but `arr['0']` turns out to work just as well as `arr[0]`, and doing it this\n// way lets us use a single type in the places where behave as if we are only dealing with objects, even if some of them\n// might be arrays.\ntype ObjOrArray<T> = { [key: string]: T };\n\ntype MemoFunc = [\n  // memoize\n  (obj: object) => boolean,\n  // unmemoize\n  (obj: object) => void,\n];\n\n/**\n * Recursively normalizes the given object.\n *\n * - Creates a copy to prevent original input mutation\n * - Skips non-enumerable properties\n * - When stringifying, calls `toJSON` if implemented\n * - Removes circular references\n * - Translates non-serializable values (`undefined`/`NaN`/functions) to serializable format\n * - Translates known global objects/classes to a string representations\n * - Takes care of `Error` object serialization\n * - Optionally limits depth of final output\n * - Optionally limits number of properties/elements included in any single object/array\n *\n * @param input The object to be normalized.\n * @param depth The max depth to which to normalize the object. (Anything deeper stringified whole.)\n * @param maxProperties The max number of elements or properties to be included in any single array or\n * object in the normalized output.\n * @returns A normalized version of the object, or `\"**non-serializable**\"` if any errors are thrown during normalization.\n */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport function normalize(input: unknown, depth: number = 100, maxProperties: number = +Infinity): any {\n  try {\n    // since we're at the outermost level, we don't provide a key\n    return visit('', input, depth, maxProperties);\n  } catch (err) {\n    return { ERROR: `**non-serializable** (${err})` };\n  }\n}\n\n/** JSDoc */\nexport function normalizeToSize<T>(\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  object: { [key: string]: any },\n  // Default Node.js REPL depth\n  depth: number = 3,\n  // 100kB, as 200kB is max payload size, so half sounds reasonable\n  maxSize: number = 100 * 1024,\n): T {\n  const normalized = normalize(object, depth);\n\n  if (jsonSize(normalized) > maxSize) {\n    return normalizeToSize(object, depth - 1, maxSize);\n  }\n\n  return normalized as T;\n}\n\n/**\n * Visits a node to perform normalization on it\n *\n * @param key The key corresponding to the given node\n * @param value The node to be visited\n * @param depth Optional number indicating the maximum recursion depth\n * @param maxProperties Optional maximum number of properties/elements included in any single object/array\n * @param memo Optional Memo class handling decycling\n */\nfunction visit(\n  key: string,\n  value: unknown,\n  depth: number = +Infinity,\n  maxProperties: number = +Infinity,\n  memo = memoBuilder(),\n): Primitive | ObjOrArray<unknown> {\n  const [memoize, unmemoize] = memo;\n\n  // Get the simple cases out of the way first\n  if (\n    value == null || // this matches null and undefined -> eqeq not eqeqeq\n    ['boolean', 'string'].includes(typeof value) ||\n    (typeof value === 'number' && Number.isFinite(value))\n  ) {\n    return value as Primitive;\n  }\n\n  const stringified = stringifyValue(key, value);\n\n  // Anything we could potentially dig into more (objects or arrays) will have come back as `\"[object XXXX]\"`.\n  // Everything else will have already been serialized, so if we don't see that pattern, we're done.\n  if (!stringified.startsWith('[object ')) {\n    return stringified;\n  }\n\n  // From here on, we can assert that `value` is either an object or an array.\n\n  // Do not normalize objects that we know have already been normalized. As a general rule, the\n  // \"__sentry_skip_normalization__\" property should only be used sparingly and only should only be set on objects that\n  // have already been normalized.\n  if ((value as ObjOrArray<unknown>)['__sentry_skip_normalization__']) {\n    return value as ObjOrArray<unknown>;\n  }\n\n  // We can set `__sentry_override_normalization_depth__` on an object to ensure that from there\n  // We keep a certain amount of depth.\n  // This should be used sparingly, e.g. we use it for the redux integration to ensure we get a certain amount of state.\n  const remainingDepth =\n    typeof (value as ObjOrArray<unknown>)['__sentry_override_normalization_depth__'] === 'number'\n      ? ((value as ObjOrArray<unknown>)['__sentry_override_normalization_depth__'] as number)\n      : depth;\n\n  // We're also done if we've reached the max depth\n  if (remainingDepth === 0) {\n    // At this point we know `serialized` is a string of the form `\"[object XXXX]\"`. Clean it up so it's just `\"[XXXX]\"`.\n    return stringified.replace('object ', '');\n  }\n\n  // If we've already visited this branch, bail out, as it's circular reference. If not, note that we're seeing it now.\n  if (memoize(value)) {\n    return '[Circular ~]';\n  }\n\n  // If the value has a `toJSON` method, we call it to extract more information\n  const valueWithToJSON = value as unknown & { toJSON?: () => unknown };\n  if (valueWithToJSON && typeof valueWithToJSON.toJSON === 'function') {\n    try {\n      const jsonValue = valueWithToJSON.toJSON();\n      // We need to normalize the return value of `.toJSON()` in case it has circular references\n      return visit('', jsonValue, remainingDepth - 1, maxProperties, memo);\n    } catch (err) {\n      // pass (The built-in `toJSON` failed, but we can still try to do it ourselves)\n    }\n  }\n\n  // At this point we know we either have an object or an array, we haven't seen it before, and we're going to recurse\n  // because we haven't yet reached the max depth. Create an accumulator to hold the results of visiting each\n  // property/entry, and keep track of the number of items we add to it.\n  const normalized = (Array.isArray(value) ? [] : {}) as ObjOrArray<unknown>;\n  let numAdded = 0;\n\n  // Before we begin, convert`Error` and`Event` instances into plain objects, since some of each of their relevant\n  // properties are non-enumerable and otherwise would get missed.\n  const visitable = convertToPlainObject(value as ObjOrArray<unknown>);\n\n  for (const visitKey in visitable) {\n    // Avoid iterating over fields in the prototype if they've somehow been exposed to enumeration.\n    if (!Object.prototype.hasOwnProperty.call(visitable, visitKey)) {\n      continue;\n    }\n\n    if (numAdded >= maxProperties) {\n      normalized[visitKey] = '[MaxProperties ~]';\n      break;\n    }\n\n    // Recursively visit all the child nodes\n    const visitValue = visitable[visitKey];\n    normalized[visitKey] = visit(visitKey, visitValue, remainingDepth - 1, maxProperties, memo);\n\n    numAdded++;\n  }\n\n  // Once we've visited all the branches, remove the parent from memo storage\n  unmemoize(value);\n\n  // Return accumulated values\n  return normalized;\n}\n\n/* eslint-disable complexity */\n/**\n * Stringify the given value. Handles various known special values and types.\n *\n * Not meant to be used on simple primitives which already have a string representation, as it will, for example, turn\n * the number 1231 into \"[Object Number]\", nor on `null`, as it will throw.\n *\n * @param value The value to stringify\n * @returns A stringified representation of the given value\n */\nfunction stringifyValue(\n  key: unknown,\n  // this type is a tiny bit of a cheat, since this function does handle NaN (which is technically a number), but for\n  // our internal use, it'll do\n  value: Exclude<unknown, string | number | boolean | null>,\n): string {\n  try {\n    if (key === 'domain' && value && typeof value === 'object' && (value as { _events: unknown })._events) {\n      return '[Domain]';\n    }\n\n    if (key === 'domainEmitter') {\n      return '[DomainEmitter]';\n    }\n\n    // It's safe to use `global`, `window`, and `document` here in this manner, as we are asserting using `typeof` first\n    // which won't throw if they are not present.\n\n    if (typeof global !== 'undefined' && value === global) {\n      return '[Global]';\n    }\n\n    // eslint-disable-next-line no-restricted-globals\n    if (typeof window !== 'undefined' && value === window) {\n      return '[Window]';\n    }\n\n    // eslint-disable-next-line no-restricted-globals\n    if (typeof document !== 'undefined' && value === document) {\n      return '[Document]';\n    }\n\n    if (isVueViewModel(value)) {\n      return '[VueViewModel]';\n    }\n\n    // React's SyntheticEvent thingy\n    if (isSyntheticEvent(value)) {\n      return '[SyntheticEvent]';\n    }\n\n    if (typeof value === 'number' && !Number.isFinite(value)) {\n      return `[${value}]`;\n    }\n\n    if (typeof value === 'function') {\n      return `[Function: ${getFunctionName(value)}]`;\n    }\n\n    if (typeof value === 'symbol') {\n      return `[${String(value)}]`;\n    }\n\n    // stringified BigInts are indistinguishable from regular numbers, so we need to label them to avoid confusion\n    if (typeof value === 'bigint') {\n      return `[BigInt: ${String(value)}]`;\n    }\n\n    // Now that we've knocked out all the special cases and the primitives, all we have left are objects. Simply casting\n    // them to strings means that instances of classes which haven't defined their `toStringTag` will just come out as\n    // `\"[object Object]\"`. If we instead look at the constructor's name (which is the same as the name of the class),\n    // we can make sure that only plain objects come out that way.\n    const objName = getConstructorName(value);\n\n    // Handle HTML Elements\n    if (/^HTML(\\w*)Element$/.test(objName)) {\n      return `[HTMLElement: ${objName}]`;\n    }\n\n    return `[object ${objName}]`;\n  } catch (err) {\n    return `**non-serializable** (${err})`;\n  }\n}\n/* eslint-enable complexity */\n\nfunction getConstructorName(value: unknown): string {\n  const prototype: Prototype | null = Object.getPrototypeOf(value);\n\n  return prototype?.constructor ? prototype.constructor.name : 'null prototype';\n}\n\n/** Calculates bytes size of input string */\nfunction utf8Length(value: string): number {\n  // eslint-disable-next-line no-bitwise\n  return ~-encodeURI(value).split(/%..|./).length;\n}\n\n/** Calculates bytes size of input object */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction jsonSize(value: any): number {\n  return utf8Length(JSON.stringify(value));\n}\n\n/**\n * Normalizes URLs in exceptions and stacktraces to a base path so Sentry can fingerprint\n * across platforms and working directory.\n *\n * @param url The URL to be normalized.\n * @param basePath The application base path.\n * @returns The normalized URL.\n */\nexport function normalizeUrlToBase(url: string, basePath: string): string {\n  const escapedBase = basePath\n    // Backslash to forward\n    .replace(/\\\\/g, '/')\n    // Escape RegExp special characters\n    .replace(/[|\\\\{}()[\\]^$+*?.]/g, '\\\\$&');\n\n  let newUrl = url;\n  try {\n    newUrl = decodeURI(url);\n  } catch (_Oo) {\n    // Sometime this breaks\n  }\n  return (\n    newUrl\n      .replace(/\\\\/g, '/')\n      .replace(/webpack:\\/?/g, '') // Remove intermediate base path\n      // eslint-disable-next-line @sentry-internal/sdk/no-regexp-constructor\n      .replace(new RegExp(`(file://)?/*${escapedBase}/*`, 'ig'), 'app:///')\n  );\n}\n\n/**\n * Helper to decycle json objects\n */\nfunction memoBuilder(): MemoFunc {\n  const inner = new WeakSet<object>();\n  function memoize(obj: object): boolean {\n    if (inner.has(obj)) {\n      return true;\n    }\n    inner.add(obj);\n    return false;\n  }\n\n  function unmemoize(obj: object): void {\n    inner.delete(obj);\n  }\n  return [memoize, unmemoize];\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAmBA;;;;;;;;;;;;;;;;;;CAkBA,GACA,8DAAA;AACO,SAAS,SAAS,CAAC,KAAK,EAAW,KAAK,GAAW,GAAG,EAAE,aAAa,GAAW,CAAC,QAAQ,EAAO;IACrG,IAAI;QACN,6DAAA;QACI,OAAO,KAAK,CAAC,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,aAAa,CAAC;IACjD,CAAI,CAAA,OAAO,GAAG,EAAE;QACZ,OAAO;YAAE,KAAK,EAAE,CAAC,sBAAsB,EAAE,GAAG,CAAC,CAAC,CAAA;QAAA,CAAG;IACrD;AACA;AAEA,UAAA,GACO,SAAS,eAAe,CAC/B,8DAAA;AACE,MAAM,EACR,6BAAA;AACE,KAAK,GAAW,CAAC,EACnB,iEAAA;AACE,OAAO,GAAW,GAAA,GAAM,IAAI;IAE5B,MAAM,aAAa,SAAS,CAAC,MAAM,EAAE,KAAK,CAAC;IAE3C,IAAI,QAAQ,CAAC,UAAU,CAAE,GAAE,OAAO,EAAE;QAClC,OAAO,eAAe,CAAC,MAAM,EAAE,QAAQ,CAAC,EAAE,OAAO,CAAC;IACtD;IAEE,OAAO,UAAW;AACpB;AAEA;;;;;;;;CAQA,GACA,SAAS,KAAK,CACZ,GAAG,EACH,KAAK,EACL,KAAK,GAAW,CAAC,QAAQ,EACzB,aAAa,GAAW,CAAC,QAAQ,EACjC,IAAK,GAAE,WAAW,EAAE;IAEpB,MAAM,CAAC,OAAO,EAAE,SAAS,CAAA,GAAI,IAAI;IAEnC,4CAAA;IACE,IACE,KAAA,IAAS,IAAK,IAAA,qDAAA;IACd;QAAC,SAAS;QAAE,QAAQ;KAAC,CAAC,QAAQ,CAAC,OAAO,KAAK,CAAE,IAC5C,OAAO,KAAA,KAAU,QAAA,IAAY,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,EACpD;QACA,OAAO,KAAM;IACjB;IAEE,MAAM,cAAc,cAAc,CAAC,GAAG,EAAE,KAAK,CAAC;IAEhD,4GAAA;IACA,kGAAA;IACE,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE;QACvC,OAAO,WAAW;IACtB;IAEA,4EAAA;IAEA,6FAAA;IACA,qHAAA;IACA,gCAAA;IACE,IAAI,AAAC,KAAA,CAA8B,+BAA+B,CAAC,EAAE;QACnE,OAAO,KAAM;IACjB;IAEA,8FAAA;IACA,qCAAA;IACA,sHAAA;IACE,MAAM,cAAe,GACnB,OAAO,AAAC,KAAA,CAA8B,yCAAyC,CAAA,KAAM,WAChF,AAAC,KAAA,CAA8B,yCAAyC,CAAE,GAC3E,KAAK;IAEb,iDAAA;IACE,IAAI,cAAe,KAAI,CAAC,EAAE;QAC5B,qHAAA;QACI,OAAO,WAAW,CAAC,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC;IAC7C;IAEA,qHAAA;IACE,IAAI,OAAO,CAAC,KAAK,CAAC,EAAE;QAClB,OAAO,cAAc;IACzB;IAEA,6EAAA;IACE,MAAM,eAAgB,GAAE,KAAM;IAC9B,IAAI,eAAA,IAAmB,OAAO,eAAe,CAAC,MAAA,KAAW,UAAU,EAAE;QACnE,IAAI;YACF,MAAM,SAAU,GAAE,eAAe,CAAC,MAAM,EAAE;YAChD,0FAAA;YACM,OAAO,KAAK,CAAC,EAAE,EAAE,SAAS,EAAE,cAAe,GAAE,CAAC,EAAE,aAAa,EAAE,IAAI,CAAC;QAC1E,CAAM,CAAA,OAAO,GAAG,EAAE;QAClB,+EAAA;QACA;IACA;IAEA,oHAAA;IACA,2GAAA;IACA,sEAAA;IACE,MAAM,UAAW,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAA,GAAI,EAAC,GAAI,CAAA,CAAE,CAAE;IACpD,IAAI,QAAS,GAAE,CAAC;IAElB,gHAAA;IACA,gEAAA;IACE,MAAM,SAAU,qPAAE,uBAAA,AAAoB,EAAC,OAA6B;IAEpE,IAAK,MAAM,QAAS,IAAG,SAAS,CAAE;QACpC,+FAAA;QACI,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,EAAE,QAAQ,CAAC,EAAE;YAC9D;QACN;QAEI,IAAI,QAAS,IAAG,aAAa,EAAE;YAC7B,UAAU,CAAC,QAAQ,CAAA,GAAI,mBAAmB;YAC1C;QACN;QAEA,wCAAA;QACI,MAAM,UAAW,GAAE,SAAS,CAAC,QAAQ,CAAC;QACtC,UAAU,CAAC,QAAQ,CAAA,GAAI,KAAK,CAAC,QAAQ,EAAE,UAAU,EAAE,iBAAiB,CAAC,EAAE,aAAa,EAAE,IAAI,CAAC;QAE3F,QAAQ,EAAE;IACd;IAEA,2EAAA;IACE,SAAS,CAAC,KAAK,CAAC;IAElB,4BAAA;IACE,OAAO,UAAU;AACnB;AAEA,6BAAA,GACA;;;;;;;;CAQA,GACA,SAAS,cAAc,CACrB,GAAG,EACL,mHAAA;AACA,6BAAA;AACE,KAAK;IAEL,IAAI;QACF,IAAI,GAAA,KAAQ,QAAS,IAAG,SAAS,OAAO,KAAM,KAAI,YAAY,AAAC,MAA+B,OAAO,EAAE;YACrG,OAAO,UAAU;QACvB;QAEI,IAAI,GAAI,KAAI,eAAe,EAAE;YAC3B,OAAO,iBAAiB;QAC9B;QAEA,oHAAA;QACA,6CAAA;QAEI,IAAI,OAAO,MAAO,KAAI,eAAe,KAAA,KAAU,MAAM,EAAE;YACrD,OAAO,UAAU;QACvB;QAEA,iDAAA;QACI,IAAI,OAAO,MAAO,KAAI,eAAe,KAAA,KAAU,MAAM,EAAE;YACrD,OAAO,UAAU;QACvB;QAEA,iDAAA;QACI,IAAI,OAAO,QAAS,KAAI,eAAe,KAAA,KAAU,QAAQ,EAAE;YACzD,OAAO,YAAY;QACzB;QAEI,kPAAI,iBAAA,AAAc,EAAC,KAAK,CAAC,EAAE;YACzB,OAAO,gBAAgB;QAC7B;QAEA,gCAAA;QACI,kPAAI,mBAAA,AAAgB,EAAC,KAAK,CAAC,EAAE;YAC3B,OAAO,kBAAkB;QAC/B;QAEI,IAAI,OAAO,KAAA,KAAU,QAAS,IAAG,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;YACxD,OAAO,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;QACzB;QAEI,IAAI,OAAO,KAAM,KAAI,UAAU,EAAE;YAC/B,OAAO,CAAC,WAAW,wPAAE,kBAAA,AAAe,EAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACpD;QAEI,IAAI,OAAO,KAAM,KAAI,QAAQ,EAAE;YAC7B,OAAO,CAAC,CAAC,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACjC;QAEA,8GAAA;QACI,IAAI,OAAO,KAAM,KAAI,QAAQ,EAAE;YAC7B,OAAO,CAAC,SAAS,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACzC;QAEA,oHAAA;QACA,kHAAA;QACA,kHAAA;QACA,8DAAA;QACI,MAAM,OAAQ,GAAE,kBAAkB,CAAC,KAAK,CAAC;QAE7C,uBAAA;QACI,IAAI,oBAAoB,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE;YACtC,OAAO,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC,CAAC;QACxC;QAEI,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC,CAAC;IAChC,CAAI,CAAA,OAAO,GAAG,EAAE;QACZ,OAAO,CAAC,sBAAsB,EAAE,GAAG,CAAC,CAAC,CAAC;IAC1C;AACA;AACA,4BAAA,GAEA,SAAS,kBAAkB,CAAC,KAAK,EAAmB;IAClD,MAAM,SAAS,GAAqB,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC;IAEhE,OAAO,SAAS,EAAE,WAAY,GAAE,SAAS,CAAC,WAAW,CAAC,IAAK,GAAE,gBAAgB;AAC/E;AAEA,0CAAA,GACA,SAAS,UAAU,CAAC,KAAK,EAAkB;IAC3C,sCAAA;IACE,OAAO,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,MAAM;AACjD;AAEA,0CAAA,GACA,8DAAA;AACA,SAAS,QAAQ,CAAC,KAAK,EAAe;IACpC,OAAO,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;AAC1C;AAEA;;;;;;;CAOA,GACO,SAAS,kBAAkB,CAAC,GAAG,EAAU,QAAQ,EAAkB;IACxE,MAAM,cAAc,QACtB,uBAAA;KACK,OAAO,CAAC,KAAK,EAAE,GAAG,CACvB,mCAAA;KACK,OAAO,CAAC,qBAAqB,EAAE,MAAM,CAAC;IAEzC,IAAI,MAAO,GAAE,GAAG;IAChB,IAAI;QACF,MAAO,GAAE,SAAS,CAAC,GAAG,CAAC;IAC3B,CAAI,CAAA,OAAO,GAAG,EAAE;IAChB,uBAAA;IACA;IACE,OACE,OACG,OAAO,CAAC,KAAK,EAAE,GAAG,EAClB,OAAO,CAAC,cAAc,EAAE,EAAE,CAAA,CAAA,gCAAA;IACjC,sEAAA;KACO,OAAO,CAAC,IAAI,MAAM,CAAC,CAAC,YAAY,EAAE,WAAW,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,EAAE,SAAS;AAE1E;AAEA;;CAEA,GACA,SAAS,WAAW,GAAa;IAC/B,MAAM,KAAM,GAAE,IAAI,OAAO,EAAU;IACnC,SAAS,OAAO,CAAC,GAAG,EAAmB;QACrC,IAAI,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;YAClB,OAAO,IAAI;QACjB;QACI,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC;QACd,OAAO,KAAK;IAChB;IAEE,SAAS,SAAS,CAAC,GAAG,EAAgB;QACpC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC;IACrB;IACE,OAAO;QAAC,OAAO;QAAE,SAAS;KAAC;AAC7B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3012, "column": 0}, "map": {"version": 3, "file": "semanticAttributes.js", "sources": ["file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/%40sentry%2Bcore%409.22.0/node_modules/%40sentry/core/src/semanticAttributes.ts"], "sourcesContent": ["/**\n * Use this attribute to represent the source of a span.\n * Should be one of: custom, url, route, view, component, task, unknown\n *\n */\nexport const SEMANTIC_ATTRIBUTE_SENTRY_SOURCE = 'sentry.source';\n\n/**\n * Attributes that holds the sample rate that was locally applied to a span.\n * If this attribute is not defined, it means that the span inherited a sampling decision.\n *\n * NOTE: Is only defined on root spans.\n */\nexport const SEMANTIC_ATTRIBUTE_SENTRY_SAMPLE_RATE = 'sentry.sample_rate';\n\n/**\n * Attribute holding the sample rate of the previous trace.\n * This is used to sample consistently across subsequent traces in the browser SDK.\n *\n * Note: Only defined on root spans, if opted into consistent sampling\n */\nexport const SEMANTIC_ATTRIBUTE_SENTRY_PREVIOUS_TRACE_SAMPLE_RATE = 'sentry.previous_trace_sample_rate';\n\n/**\n * Use this attribute to represent the operation of a span.\n */\nexport const SEMANTIC_ATTRIBUTE_SENTRY_OP = 'sentry.op';\n\n/**\n * Use this attribute to represent the origin of a span.\n */\nexport const SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN = 'sentry.origin';\n\n/** The reason why an idle span finished. */\nexport const SEMANTIC_ATTRIBUTE_SENTRY_IDLE_SPAN_FINISH_REASON = 'sentry.idle_span_finish_reason';\n\n/** The unit of a measurement, which may be stored as a TimedEvent. */\nexport const SEMANTIC_ATTRIBUTE_SENTRY_MEASUREMENT_UNIT = 'sentry.measurement_unit';\n\n/** The value of a measurement, which may be stored as a TimedEvent. */\nexport const SEMANTIC_ATTRIBUTE_SENTRY_MEASUREMENT_VALUE = 'sentry.measurement_value';\n\n/**\n * A custom span name set by users guaranteed to be taken over any automatically\n * inferred name. This attribute is removed before the span is sent.\n *\n * @internal only meant for internal SDK usage\n * @hidden\n */\nexport const SEMANTIC_ATTRIBUTE_SENTRY_CUSTOM_SPAN_NAME = 'sentry.custom_span_name';\n\n/**\n * The id of the profile that this span occurred in.\n */\nexport const SEMANTIC_ATTRIBUTE_PROFILE_ID = 'sentry.profile_id';\n\nexport const SEMANTIC_ATTRIBUTE_EXCLUSIVE_TIME = 'sentry.exclusive_time';\n\nexport const SEMANTIC_ATTRIBUTE_CACHE_HIT = 'cache.hit';\n\nexport const SEMANTIC_ATTRIBUTE_CACHE_KEY = 'cache.key';\n\nexport const SEMANTIC_ATTRIBUTE_CACHE_ITEM_SIZE = 'cache.item_size';\n\n/** TODO: Remove these once we update to latest semantic conventions */\nexport const SEMANTIC_ATTRIBUTE_HTTP_REQUEST_METHOD = 'http.request.method';\nexport const SEMANTIC_ATTRIBUTE_URL_FULL = 'url.full';\n\n/**\n * A span link attribute to mark the link as a special span link.\n *\n * Known values:\n * - `previous_trace`: The span links to the frontend root span of the previous trace.\n * - `next_trace`: The span links to the frontend root span of the next trace. (Not set by the SDK)\n *\n * Other values may be set as appropriate.\n * @see https://develop.sentry.dev/sdk/telemetry/traces/span-links/#link-types\n */\nexport const SEMANTIC_LINK_ATTRIBUTE_LINK_TYPE = 'sentry.link.type';\n"], "names": [], "mappings": "AAAA;;;;CAIA;;;;;;;;;;;;;;;;;;;AACO,MAAM,gCAAiC,GAAE;AAEhD;;;;;CAKA,GACO,MAAM,qCAAsC,GAAE;AAErD;;;;;CAKA,GACO,MAAM,oDAAqD,GAAE;AAEpE;;CAEA,GACO,MAAM,4BAA6B,GAAE;AAE5C;;CAEA,GACO,MAAM,gCAAiC,GAAE;AAEhD,0CAAA,GACO,MAAM,iDAAkD,GAAE;AAEjE,oEAAA,GACO,MAAM,0CAA2C,GAAE;AAE1D,qEAAA,GACO,MAAM,2CAA4C,GAAE;AAE3D;;;;;;CAMA,GACO,MAAM,0CAA2C,GAAE;AAE1D;;CAEA,GACO,MAAM,6BAA8B,GAAE;AAEtC,MAAM,iCAAkC,GAAE;AAE1C,MAAM,4BAA6B,GAAE;AAErC,MAAM,4BAA6B,GAAE;AAErC,MAAM,kCAAmC,GAAE;AAElD,qEAAA,GACO,MAAM,sCAAuC,GAAE;AAC/C,MAAM,2BAA4B,GAAE;AAE3C;;;;;;;;;CASA,GACO,MAAM,iCAAkC,GAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3091, "column": 0}, "map": {"version": 3, "file": "hasSpansEnabled.js", "sources": ["file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/%40sentry%2Bcore%409.22.0/node_modules/%40sentry/core/src/utils/hasSpansEnabled.ts"], "sourcesContent": ["import { getClient } from '../currentScopes';\nimport type { Options } from '../types-hoist/options';\n\n// Treeshakable guard to remove all code related to tracing\ndeclare const __SENTRY_TRACING__: boolean | undefined;\n\n/**\n * Determines if span recording is currently enabled.\n *\n * Spans are recorded when at least one of `tracesSampleRate` and `tracesSampler`\n * is defined in the SDK config. This function does not make any assumption about\n * sampling decisions, it only checks if the SDK is configured to record spans.\n *\n * Important: This function only determines if span recording is enabled. Trace\n * continuation and propagation is separately controlled and not covered by this function.\n * If this function returns `false`, traces can still be propagated (which is what\n * we refer to by \"Tracing without Performance\")\n * @see https://develop.sentry.dev/sdk/telemetry/traces/tracing-without-performance/\n *\n * @param maybeOptions An SDK options object to be passed to this function.\n * If this option is not provided, the function will use the current client's options.\n */\nexport function hasSpansEnabled(\n  maybeOptions?: Pick<Options, 'tracesSampleRate' | 'tracesSampler'> | undefined,\n): boolean {\n  if (typeof __SENTRY_TRACING__ === 'boolean' && !__SENTRY_TRACING__) {\n    return false;\n  }\n\n  const options = maybeOptions || getClient()?.getOptions();\n  return (\n    !!options &&\n    // Note: This check is `!= null`, meaning \"nullish\". `0` is not \"nullish\", `undefined` and `null` are. (This comment was brought to you by 15 minutes of questioning life)\n    (options.tracesSampleRate != null || !!options.tracesSampler)\n  );\n}\n\n/**\n * @see JSDoc of `hasSpansEnabled`\n * @deprecated Use `hasSpansEnabled` instead, which is a more accurately named version of this function.\n * This function will be removed in the next major version of the SDK.\n */\n// TODO(v10): Remove this export\nexport const hasTracingEnabled = hasSpansEnabled;\n"], "names": [], "mappings": ";;;;;;AAGA,2DAAA;AAGA;;;;;;;;;;;;;;;CAeA,GACO,SAAS,eAAe,CAC7B,YAAY;IAEZ,IAAI,OAAO,kBAAA,KAAuB,SAAU,IAAG,CAAC,kBAAkB,EAAE;QAClE,OAAO,KAAK;IAChB;IAEE,MAAM,OAAQ,GAAE,YAAa,2OAAG,YAAA,AAAS,EAAE,GAAE,UAAU,EAAE;IACzD,OACE,CAAC,CAAC,OAAQ,IACd,0KAAA;IACA,CAAK,OAAO,CAAC,gBAAiB,IAAG,IAAK,IAAG,CAAC,CAAC,OAAO,CAAC,aAAa;AAEhE;AAEA;;;;CAIA,GACA,gCAAA;AACO,MAAM,iBAAkB,GAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3135, "column": 0}, "map": {"version": 3, "file": "spanstatus.js", "sources": ["file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/%40sentry%2Bcore%409.22.0/node_modules/%40sentry/core/src/tracing/spanstatus.ts"], "sourcesContent": ["import type { Span } from '../types-hoist/span';\nimport type { SpanStatus } from '../types-hoist/spanStatus';\n\nexport const SPAN_STATUS_UNSET = 0;\nexport const SPAN_STATUS_OK = 1;\nexport const SPAN_STATUS_ERROR = 2;\n\n/**\n * Converts a HTTP status code into a sentry status with a message.\n *\n * @param httpStatus The HTTP response status code.\n * @returns The span status or unknown_error.\n */\n// https://develop.sentry.dev/sdk/event-payloads/span/\nexport function getSpanStatusFromHttpCode(httpStatus: number): SpanStatus {\n  if (httpStatus < 400 && httpStatus >= 100) {\n    return { code: SPAN_STATUS_OK };\n  }\n\n  if (httpStatus >= 400 && httpStatus < 500) {\n    switch (httpStatus) {\n      case 401:\n        return { code: SPAN_STATUS_ERROR, message: 'unauthenticated' };\n      case 403:\n        return { code: SPAN_STATUS_ERROR, message: 'permission_denied' };\n      case 404:\n        return { code: SPAN_STATUS_ERROR, message: 'not_found' };\n      case 409:\n        return { code: SPAN_STATUS_ERROR, message: 'already_exists' };\n      case 413:\n        return { code: SPAN_STATUS_ERROR, message: 'failed_precondition' };\n      case 429:\n        return { code: SPAN_STATUS_ERROR, message: 'resource_exhausted' };\n      case 499:\n        return { code: SPAN_STATUS_ERROR, message: 'cancelled' };\n      default:\n        return { code: SPAN_STATUS_ERROR, message: 'invalid_argument' };\n    }\n  }\n\n  if (httpStatus >= 500 && httpStatus < 600) {\n    switch (httpStatus) {\n      case 501:\n        return { code: SPAN_STATUS_ERROR, message: 'unimplemented' };\n      case 503:\n        return { code: SPAN_STATUS_ERROR, message: 'unavailable' };\n      case 504:\n        return { code: SPAN_STATUS_ERROR, message: 'deadline_exceeded' };\n      default:\n        return { code: SPAN_STATUS_ERROR, message: 'internal_error' };\n    }\n  }\n\n  return { code: SPAN_STATUS_ERROR, message: 'unknown_error' };\n}\n\n/**\n * Sets the Http status attributes on the current span based on the http code.\n * Additionally, the span's status is updated, depending on the http code.\n */\nexport function setHttpStatus(span: Span, httpStatus: number): void {\n  span.setAttribute('http.response.status_code', httpStatus);\n\n  const spanStatus = getSpanStatusFromHttpCode(httpStatus);\n  if (spanStatus.message !== 'unknown_error') {\n    span.setStatus(spanStatus);\n  }\n}\n"], "names": [], "mappings": ";;;;;;;AAGO,MAAM,iBAAkB,GAAE;AAC1B,MAAM,cAAe,GAAE;AACvB,MAAM,iBAAkB,GAAE;AAEjC;;;;;CAKA,GACA,sDAAA;AACO,SAAS,yBAAyB,CAAC,UAAU,EAAsB;IACxE,IAAI,UAAW,GAAE,OAAO,UAAA,IAAc,GAAG,EAAE;QACzC,OAAO;YAAE,IAAI,EAAE;QAAA,CAAgB;IACnC;IAEE,IAAI,UAAW,IAAG,OAAO,UAAA,GAAa,GAAG,EAAE;QACzC,OAAQ,UAAU;YAChB,KAAK,GAAG;gBACN,OAAO;oBAAE,IAAI,EAAE,iBAAiB;oBAAE,OAAO,EAAE,iBAAA;gBAAA,CAAmB;YAChE,KAAK,GAAG;gBACN,OAAO;oBAAE,IAAI,EAAE,iBAAiB;oBAAE,OAAO,EAAE,mBAAA;gBAAA,CAAqB;YAClE,KAAK,GAAG;gBACN,OAAO;oBAAE,IAAI,EAAE,iBAAiB;oBAAE,OAAO,EAAE,WAAA;gBAAA,CAAa;YAC1D,KAAK,GAAG;gBACN,OAAO;oBAAE,IAAI,EAAE,iBAAiB;oBAAE,OAAO,EAAE,gBAAA;gBAAA,CAAkB;YAC/D,KAAK,GAAG;gBACN,OAAO;oBAAE,IAAI,EAAE,iBAAiB;oBAAE,OAAO,EAAE,qBAAA;gBAAA,CAAuB;YACpE,KAAK,GAAG;gBACN,OAAO;oBAAE,IAAI,EAAE,iBAAiB;oBAAE,OAAO,EAAE,oBAAA;gBAAA,CAAsB;YACnE,KAAK,GAAG;gBACN,OAAO;oBAAE,IAAI,EAAE,iBAAiB;oBAAE,OAAO,EAAE,WAAA;gBAAA,CAAa;YAC1D;gBACE,OAAO;oBAAE,IAAI,EAAE,iBAAiB;oBAAE,OAAO,EAAE,kBAAA;gBAAA,CAAoB;QACvE;IACA;IAEE,IAAI,UAAW,IAAG,OAAO,UAAA,GAAa,GAAG,EAAE;QACzC,OAAQ,UAAU;YAChB,KAAK,GAAG;gBACN,OAAO;oBAAE,IAAI,EAAE,iBAAiB;oBAAE,OAAO,EAAE,eAAA;gBAAA,CAAiB;YAC9D,KAAK,GAAG;gBACN,OAAO;oBAAE,IAAI,EAAE,iBAAiB;oBAAE,OAAO,EAAE,aAAA;gBAAA,CAAe;YAC5D,KAAK,GAAG;gBACN,OAAO;oBAAE,IAAI,EAAE,iBAAiB;oBAAE,OAAO,EAAE,mBAAA;gBAAA,CAAqB;YAClE;gBACE,OAAO;oBAAE,IAAI,EAAE,iBAAiB;oBAAE,OAAO,EAAE,gBAAA;gBAAA,CAAkB;QACrE;IACA;IAEE,OAAO;QAAE,IAAI,EAAE,iBAAiB;QAAE,OAAO,EAAE,eAAA;IAAA,CAAiB;AAC9D;AAEA;;;CAGA,GACO,SAAS,aAAa,CAAC,IAAI,EAAQ,UAAU,EAAgB;IAClE,IAAI,CAAC,YAAY,CAAC,2BAA2B,EAAE,UAAU,CAAC;IAE1D,MAAM,UAAW,GAAE,yBAAyB,CAAC,UAAU,CAAC;IACxD,IAAI,UAAU,CAAC,OAAQ,KAAI,eAAe,EAAE;QAC1C,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC;IAC9B;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3248, "column": 0}, "map": {"version": 3, "file": "utils.js", "sources": ["file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/%40sentry%2Bcore%409.22.0/node_modules/%40sentry/core/src/tracing/utils.ts"], "sourcesContent": ["import type { Scope } from '../scope';\nimport type { Span } from '../types-hoist/span';\nimport { addNonEnumerableProperty } from '../utils-hoist/object';\n\nconst SCOPE_ON_START_SPAN_FIELD = '_sentryScope';\nconst ISOLATION_SCOPE_ON_START_SPAN_FIELD = '_sentryIsolationScope';\n\ntype SpanWithScopes = Span & {\n  [SCOPE_ON_START_SPAN_FIELD]?: Scope;\n  [ISOLATION_SCOPE_ON_START_SPAN_FIELD]?: Scope;\n};\n\n/** Store the scope & isolation scope for a span, which can the be used when it is finished. */\nexport function setCapturedScopesOnSpan(span: Span | undefined, scope: Scope, isolationScope: Scope): void {\n  if (span) {\n    addNonEnumerableProperty(span, ISOLATION_SCOPE_ON_START_SPAN_FIELD, isolationScope);\n    addNonEnumerableProperty(span, SCOPE_ON_START_SPAN_FIELD, scope);\n  }\n}\n\n/**\n * Grabs the scope and isolation scope off a span that were active when the span was started.\n */\nexport function getCapturedScopesOnSpan(span: Span): { scope?: Scope; isolationScope?: Scope } {\n  return {\n    scope: (span as SpanWithScopes)[SCOPE_ON_START_SPAN_FIELD],\n    isolationScope: (span as SpanWithScopes)[ISOLATION_SCOPE_ON_START_SPAN_FIELD],\n  };\n}\n"], "names": [], "mappings": ";;;;;;AAIA,MAAM,yBAAA,GAA4B,cAAc;AAChD,MAAM,mCAAA,GAAsC,uBAAuB;AAOnE,6FAAA,GACO,SAAS,uBAAuB,CAAC,IAAI,EAAoB,KAAK,EAAS,cAAc,EAAe;IACzG,IAAI,IAAI,EAAE;0PACR,2BAAA,AAAwB,EAAC,IAAI,EAAE,mCAAmC,EAAE,cAAc,CAAC;0PACnF,2BAAA,AAAwB,EAAC,IAAI,EAAE,yBAAyB,EAAE,KAAK,CAAC;IACpE;AACA;AAEA;;CAEA,GACO,SAAS,uBAAuB,CAAC,IAAI,EAAmD;IAC7F,OAAO;QACL,KAAK,EAAE,AAAC,IAAA,CAAwB,yBAAyB,CAAC;QAC1D,cAAc,EAAE,AAAC,IAAA,CAAwB,mCAAmC,CAAC;IACjF,CAAG;AACH", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3278, "column": 0}, "map": {"version": 3, "file": "parseSampleRate.js", "sources": ["file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/%40sentry%2Bcore%409.22.0/node_modules/%40sentry/core/src/utils/parseSampleRate.ts"], "sourcesContent": ["/**\n * Parse a sample rate from a given value.\n * This will either return a boolean or number sample rate, if the sample rate is valid (between 0 and 1).\n * If a string is passed, we try to convert it to a number.\n *\n * Any invalid sample rate will return `undefined`.\n */\nexport function parseSampleRate(sampleRate: unknown): number | undefined {\n  if (typeof sampleRate === 'boolean') {\n    return Number(sampleRate);\n  }\n\n  const rate = typeof sampleRate === 'string' ? parseFloat(sampleRate) : sampleRate;\n  if (typeof rate !== 'number' || isNaN(rate) || rate < 0 || rate > 1) {\n    return undefined;\n  }\n\n  return rate;\n}\n"], "names": [], "mappings": "AAAA;;;;;;CAMA;;;AACO,SAAS,eAAe,CAAC,UAAU,EAA+B;IACvE,IAAI,OAAO,UAAW,KAAI,SAAS,EAAE;QACnC,OAAO,MAAM,CAAC,UAAU,CAAC;IAC7B;IAEE,MAAM,IAAA,GAAO,OAAO,UAAW,KAAI,QAAS,GAAE,UAAU,CAAC,UAAU,CAAA,GAAI,UAAU;IACjF,IAAI,OAAO,SAAS,QAAA,IAAY,KAAK,CAAC,IAAI,CAAE,IAAG,OAAO,CAAA,IAAK,IAAK,GAAE,CAAC,EAAE;QACnE,OAAO,SAAS;IACpB;IAEE,OAAO,IAAI;AACb", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3305, "column": 0}, "map": {"version": 3, "file": "baggage.js", "sources": ["file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/%40sentry%2Bcore%409.22.0/node_modules/%40sentry/core/src/utils-hoist/baggage.ts"], "sourcesContent": ["import type { DynamicSamplingContext } from '../types-hoist/envelope';\nimport { DEBUG_BUILD } from './../debug-build';\nimport { isString } from './is';\nimport { logger } from './logger';\n\nexport const SENTRY_BAGGAGE_KEY_PREFIX = 'sentry-';\n\nexport const SENTRY_BAGGAGE_KEY_PREFIX_REGEX = /^sentry-/;\n\n/**\n * Max length of a serialized baggage string\n *\n * https://www.w3.org/TR/baggage/#limits\n */\nexport const MAX_BAGGAGE_STRING_LENGTH = 8192;\n\n/**\n * Takes a baggage header and turns it into Dynamic Sampling Context, by extracting all the \"sentry-\" prefixed values\n * from it.\n *\n * @param baggageHeader A very bread definition of a baggage header as it might appear in various frameworks.\n * @returns The Dynamic Sampling Context that was found on `baggageHeader`, if there was any, `undefined` otherwise.\n */\nexport function baggageHeaderToDynamicSamplingContext(\n  // Very liberal definition of what any incoming header might look like\n  baggageHeader: string | string[] | number | null | undefined | boolean,\n): Partial<DynamicSamplingContext> | undefined {\n  const baggageObject = parseBaggageHeader(baggageHeader);\n\n  if (!baggageObject) {\n    return undefined;\n  }\n\n  // Read all \"sentry-\" prefixed values out of the baggage object and put it onto a dynamic sampling context object.\n  const dynamicSamplingContext = Object.entries(baggageObject).reduce<Record<string, string>>((acc, [key, value]) => {\n    if (key.match(SENTRY_BAGGAGE_KEY_PREFIX_REGEX)) {\n      const nonPrefixedKey = key.slice(SENTRY_BAGGAGE_KEY_PREFIX.length);\n      acc[nonPrefixedKey] = value;\n    }\n    return acc;\n  }, {});\n\n  // Only return a dynamic sampling context object if there are keys in it.\n  // A keyless object means there were no sentry values on the header, which means that there is no DSC.\n  if (Object.keys(dynamicSamplingContext).length > 0) {\n    return dynamicSamplingContext as Partial<DynamicSamplingContext>;\n  } else {\n    return undefined;\n  }\n}\n\n/**\n * Turns a Dynamic Sampling Object into a baggage header by prefixing all the keys on the object with \"sentry-\".\n *\n * @param dynamicSamplingContext The Dynamic Sampling Context to turn into a header. For convenience and compatibility\n * with the `getDynamicSamplingContext` method on the Transaction class ,this argument can also be `undefined`. If it is\n * `undefined` the function will return `undefined`.\n * @returns a baggage header, created from `dynamicSamplingContext`, or `undefined` either if `dynamicSamplingContext`\n * was `undefined`, or if `dynamicSamplingContext` didn't contain any values.\n */\nexport function dynamicSamplingContextToSentryBaggageHeader(\n  // this also takes undefined for convenience and bundle size in other places\n  dynamicSamplingContext?: Partial<DynamicSamplingContext>,\n): string | undefined {\n  if (!dynamicSamplingContext) {\n    return undefined;\n  }\n\n  // Prefix all DSC keys with \"sentry-\" and put them into a new object\n  const sentryPrefixedDSC = Object.entries(dynamicSamplingContext).reduce<Record<string, string>>(\n    (acc, [dscKey, dscValue]) => {\n      if (dscValue) {\n        acc[`${SENTRY_BAGGAGE_KEY_PREFIX}${dscKey}`] = dscValue;\n      }\n      return acc;\n    },\n    {},\n  );\n\n  return objectToBaggageHeader(sentryPrefixedDSC);\n}\n\n/**\n * Take a baggage header and parse it into an object.\n */\nexport function parseBaggageHeader(\n  baggageHeader: string | string[] | number | null | undefined | boolean,\n): Record<string, string> | undefined {\n  if (!baggageHeader || (!isString(baggageHeader) && !Array.isArray(baggageHeader))) {\n    return undefined;\n  }\n\n  if (Array.isArray(baggageHeader)) {\n    // Combine all baggage headers into one object containing the baggage values so we can later read the Sentry-DSC-values from it\n    return baggageHeader.reduce<Record<string, string>>((acc, curr) => {\n      const currBaggageObject = baggageHeaderToObject(curr);\n      Object.entries(currBaggageObject).forEach(([key, value]) => {\n        acc[key] = value;\n      });\n      return acc;\n    }, {});\n  }\n\n  return baggageHeaderToObject(baggageHeader);\n}\n\n/**\n * Will parse a baggage header, which is a simple key-value map, into a flat object.\n *\n * @param baggageHeader The baggage header to parse.\n * @returns a flat object containing all the key-value pairs from `baggageHeader`.\n */\nfunction baggageHeaderToObject(baggageHeader: string): Record<string, string> {\n  return baggageHeader\n    .split(',')\n    .map(baggageEntry =>\n      baggageEntry.split('=').map(keyOrValue => {\n        try {\n          return decodeURIComponent(keyOrValue.trim());\n        } catch {\n          // We ignore errors here, e.g. if the value cannot be URL decoded.\n          // This will then be skipped in the next step\n          return;\n        }\n      }),\n    )\n    .reduce<Record<string, string>>((acc, [key, value]) => {\n      if (key && value) {\n        acc[key] = value;\n      }\n      return acc;\n    }, {});\n}\n\n/**\n * Turns a flat object (key-value pairs) into a baggage header, which is also just key-value pairs.\n *\n * @param object The object to turn into a baggage header.\n * @returns a baggage header string, or `undefined` if the object didn't have any values, since an empty baggage header\n * is not spec compliant.\n */\nexport function objectToBaggageHeader(object: Record<string, string>): string | undefined {\n  if (Object.keys(object).length === 0) {\n    // An empty baggage header is not spec compliant: We return undefined.\n    return undefined;\n  }\n\n  return Object.entries(object).reduce((baggageHeader, [objectKey, objectValue], currentIndex) => {\n    const baggageEntry = `${encodeURIComponent(objectKey)}=${encodeURIComponent(objectValue)}`;\n    const newBaggageHeader = currentIndex === 0 ? baggageEntry : `${baggageHeader},${baggageEntry}`;\n    if (newBaggageHeader.length > MAX_BAGGAGE_STRING_LENGTH) {\n      DEBUG_BUILD &&\n        logger.warn(\n          `Not adding key: ${objectKey} with val: ${objectValue} to baggage header due to exceeding baggage size limits.`,\n        );\n      return baggageHeader;\n    } else {\n      return newBaggageHeader;\n    }\n  }, '');\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAKO,MAAM,yBAA0B,GAAE;AAElC,MAAM,+BAAgC,GAAE;AAE/C;;;;CAIA,GACO,MAAM,yBAA0B,GAAE;AAEzC;;;;;;CAMA,GACO,SAAS,qCAAqC,CACrD,sEAAA;AACE,aAAa;IAEb,MAAM,aAAc,GAAE,kBAAkB,CAAC,aAAa,CAAC;IAEvD,IAAI,CAAC,aAAa,EAAE;QAClB,OAAO,SAAS;IACpB;IAEA,kHAAA;IACE,MAAM,yBAAyB,MAAM,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,MAAM,CAAyB,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,KAAK;QACjH,IAAI,GAAG,CAAC,KAAK,CAAC,+BAA+B,CAAC,EAAE;YAC9C,MAAM,cAAe,GAAE,GAAG,CAAC,KAAK,CAAC,yBAAyB,CAAC,MAAM,CAAC;YAClE,GAAG,CAAC,cAAc,CAAA,GAAI,KAAK;QACjC;QACI,OAAO,GAAG;IACd,CAAG,EAAE,CAAA,CAAE,CAAC;IAER,yEAAA;IACA,sGAAA;IACE,IAAI,MAAM,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC,MAAA,GAAS,CAAC,EAAE;QAClD,OAAO,sBAAuB;IAClC,OAAS;QACL,OAAO,SAAS;IACpB;AACA;AAEA;;;;;;;;CAQA,GACO,SAAS,2CAA2C,CAC3D,4EAAA;AACE,sBAAsB;IAEtB,IAAI,CAAC,sBAAsB,EAAE;QAC3B,OAAO,SAAS;IACpB;IAEA,oEAAA;IACE,MAAM,iBAAkB,GAAE,MAAM,CAAC,OAAO,CAAC,sBAAsB,CAAC,CAAC,MAAM,CACrE,CAAC,GAAG,EAAE,CAAC,MAAM,EAAE,QAAQ,CAAC,KAAK;QAC3B,IAAI,QAAQ,EAAE;YACZ,GAAG,CAAC,CAAC,EAAA,yBAAA,CAAA,EAAA,MAAA,CAAA,CAAA,CAAA,GAAA,QAAA;QACA;QACA,OAAA,GAAA;IACA,CAAA,EACA,CAAA,CAAA;IAGA,OAAA,qBAAA,CAAA,iBAAA,CAAA;AACA;AAEA;;CAEA,GACA,SAAA,kBAAA,CACA,aAAA;IAEA,IAAA,CAAA,aAAA,IAAA,+OAAA,WAAA,EAAA,aAAA,CAAA,IAAA,CAAA,KAAA,CAAA,OAAA,CAAA,aAAA,CAAA,CAAA,CAAA;QACA,OAAA,SAAA;IACA;IAEA,IAAA,KAAA,CAAA,OAAA,CAAA,aAAA,CAAA,EAAA;QACA,+HAAA;QACA,OAAA,aAAA,CAAA,MAAA,CAAA,CAAA,GAAA,EAAA,IAAA,KAAA;YACA,MAAA,iBAAA,GAAA,qBAAA,CAAA,IAAA,CAAA;YACA,MAAA,CAAA,OAAA,CAAA,iBAAA,CAAA,CAAA,OAAA,CAAA,CAAA,CAAA,GAAA,EAAA,KAAA,CAAA,KAAA;gBACA,GAAA,CAAA,GAAA,CAAA,GAAA,KAAA;YACA,CAAA,CAAA;YACA,OAAA,GAAA;QACA,CAAA,EAAA,CAAA,CAAA,CAAA;IACA;IAEA,OAAA,qBAAA,CAAA,aAAA,CAAA;AACA;AAEA;;;;;CAKA,GACA,SAAA,qBAAA,CAAA,aAAA,EAAA;IACA,OAAA,cACA,KAAA,CAAA,GAAA,EACA,GAAA,EAAA,YAAA,GACA,YAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA,GAAA,EAAA,UAAA,IAAA;YACA,IAAA;gBACA,OAAA,kBAAA,CAAA,UAAA,CAAA,IAAA,EAAA,CAAA;YACA,CAAA,CAAA,OAAA;gBACA,kEAAA;gBACA,6CAAA;gBACA;YACA;QACA,CAAA,CAAA,EAEA,MAAA,CAAA,CAAA,GAAA,EAAA,CAAA,GAAA,EAAA,KAAA,CAAA,KAAA;QACA,IAAA,GAAA,IAAA,KAAA,EAAA;YACA,GAAA,CAAA,GAAA,CAAA,GAAA,KAAA;QACA;QACA,OAAA,GAAA;IACA,CAAA,EAAA,CAAA,CAAA,CAAA;AACA;AAEA;;;;;;CAMA,GACA,SAAA,qBAAA,CAAA,MAAA,EAAA;IACA,IAAA,MAAA,CAAA,IAAA,CAAA,MAAA,CAAA,CAAA,MAAA,KAAA,CAAA,EAAA;QACA,sEAAA;QACA,OAAA,SAAA;IACA;IAEA,OAAA,MAAA,CAAA,OAAA,CAAA,MAAA,CAAA,CAAA,MAAA,CAAA,CAAA,aAAA,EAAA,CAAA,SAAA,EAAA,WAAA,CAAA,EAAA,YAAA,KAAA;QACA,MAAA,YAAA,GAAA,CAAA,EAAA,kBAAA,CAAA,SAAA,CAAA,CAAA,CAAA,EAAA,kBAAA,CAAA,WAAA,CAAA,CAAA,CAAA;QACA,MAAA,gBAAA,GAAA,YAAA,KAAA,CAAA,GAAA,YAAA,GAAA,CAAA,EAAA,aAAA,CAAA,CAAA,EAAA,YAAA,CAAA,CAAA;QACA,IAAA,gBAAA,CAAA,MAAA,GAAA,yBAAA,EAAA;gPACA,cAAA,kPACA,SAAA,CAAA,IAAA,CACA,CAAA,gBAAA,EAAA,SAAA,CAAA,WAAA,EAAA,WAAA,CAAA,wDAAA,CAAA;YAEA,OAAA,aAAA;QACA,CAAA,MAAA;YACA,OAAA,gBAAA;QACA;IACA,CAAA,EAAA,EAAA,CAAA;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3446, "column": 0}, "map": {"version": 3, "file": "tracing.js", "sources": ["file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/%40sentry%2Bcore%409.22.0/node_modules/%40sentry/core/src/utils-hoist/tracing.ts"], "sourcesContent": ["import type { DynamicSamplingContext } from '../types-hoist/envelope';\nimport type { PropagationContext } from '../types-hoist/tracing';\nimport type { TraceparentData } from '../types-hoist/transaction';\nimport { parseSampleRate } from '../utils/parseSampleRate';\nimport { baggageHeaderToDynamicSamplingContext } from './baggage';\nimport { generateSpanId, generateTraceId } from './propagationContext';\n\n// eslint-disable-next-line @sentry-internal/sdk/no-regexp-constructor -- RegExp is used for readability here\nexport const TRACEPARENT_REGEXP = new RegExp(\n  '^[ \\\\t]*' + // whitespace\n    '([0-9a-f]{32})?' + // trace_id\n    '-?([0-9a-f]{16})?' + // span_id\n    '-?([01])?' + // sampled\n    '[ \\\\t]*$', // whitespace\n);\n\n/**\n * Extract transaction context data from a `sentry-trace` header.\n *\n * @param traceparent Traceparent string\n *\n * @returns Object containing data from the header, or undefined if traceparent string is malformed\n */\nexport function extractTraceparentData(traceparent?: string): TraceparentData | undefined {\n  if (!traceparent) {\n    return undefined;\n  }\n\n  const matches = traceparent.match(TRACEPARENT_REGEXP);\n  if (!matches) {\n    return undefined;\n  }\n\n  let parentSampled: boolean | undefined;\n  if (matches[3] === '1') {\n    parentSampled = true;\n  } else if (matches[3] === '0') {\n    parentSampled = false;\n  }\n\n  return {\n    traceId: matches[1],\n    parentSampled,\n    parentSpanId: matches[2],\n  };\n}\n\n/**\n * Create a propagation context from incoming headers or\n * creates a minimal new one if the headers are undefined.\n */\nexport function propagationContextFromHeaders(\n  sentryTrace: string | undefined,\n  baggage: string | number | boolean | string[] | null | undefined,\n): PropagationContext {\n  const traceparentData = extractTraceparentData(sentryTrace);\n  const dynamicSamplingContext = baggageHeaderToDynamicSamplingContext(baggage);\n\n  if (!traceparentData?.traceId) {\n    return {\n      traceId: generateTraceId(),\n      sampleRand: Math.random(),\n    };\n  }\n\n  const sampleRand = getSampleRandFromTraceparentAndDsc(traceparentData, dynamicSamplingContext);\n\n  // The sample_rand on the DSC needs to be generated based on traceparent + baggage.\n  if (dynamicSamplingContext) {\n    dynamicSamplingContext.sample_rand = sampleRand.toString();\n  }\n\n  const { traceId, parentSpanId, parentSampled } = traceparentData;\n\n  return {\n    traceId,\n    parentSpanId,\n    sampled: parentSampled,\n    dsc: dynamicSamplingContext || {}, // If we have traceparent data but no DSC it means we are not head of trace and we must freeze it\n    sampleRand,\n  };\n}\n\n/**\n * Create sentry-trace header from span context values.\n */\nexport function generateSentryTraceHeader(\n  traceId: string | undefined = generateTraceId(),\n  spanId: string | undefined = generateSpanId(),\n  sampled?: boolean,\n): string {\n  let sampledString = '';\n  if (sampled !== undefined) {\n    sampledString = sampled ? '-1' : '-0';\n  }\n  return `${traceId}-${spanId}${sampledString}`;\n}\n\n/**\n * Given any combination of an incoming trace, generate a sample rand based on its defined semantics.\n *\n * Read more: https://develop.sentry.dev/sdk/telemetry/traces/#propagated-random-value\n */\nfunction getSampleRandFromTraceparentAndDsc(\n  traceparentData: TraceparentData | undefined,\n  dsc: Partial<DynamicSamplingContext> | undefined,\n): number {\n  // When there is an incoming sample rand use it.\n  const parsedSampleRand = parseSampleRate(dsc?.sample_rand);\n  if (parsedSampleRand !== undefined) {\n    return parsedSampleRand;\n  }\n\n  // Otherwise, if there is an incoming sampling decision + sample rate, generate a sample rand that would lead to the same sampling decision.\n  const parsedSampleRate = parseSampleRate(dsc?.sample_rate);\n  if (parsedSampleRate && traceparentData?.parentSampled !== undefined) {\n    return traceparentData.parentSampled\n      ? // Returns a sample rand with positive sampling decision [0, sampleRate)\n        Math.random() * parsedSampleRate\n      : // Returns a sample rand with negative sampling decision [sampleRate, 1)\n        parsedSampleRate + Math.random() * (1 - parsedSampleRate);\n  } else {\n    // If nothing applies, return a random sample rand.\n    return Math.random();\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AAOA,6GAAA;AACa,MAAA,kBAAA,GAAqB,IAAI,MAAM,CAC1C,UAAW,GAAA,aAAA;AACT,iBAAkB,GAAA,WAAA;AAClB,mBAAoB,GAAA,UAAA;AACpB,WAAY,GAAA,UAAA;AACZ,UAAU;AAGd;;;;;;CAMA,GACO,SAAS,sBAAsB,CAAC,WAAW,EAAwC;IACxF,IAAI,CAAC,WAAW,EAAE;QAChB,OAAO,SAAS;IACpB;IAEE,MAAM,UAAU,WAAW,CAAC,KAAK,CAAC,kBAAkB,CAAC;IACrD,IAAI,CAAC,OAAO,EAAE;QACZ,OAAO,SAAS;IACpB;IAEE,IAAI,aAAa;IACjB,IAAI,OAAO,CAAC,CAAC,CAAE,KAAI,GAAG,EAAE;QACtB,aAAA,GAAgB,IAAI;IACxB,CAAE,MAAO,IAAI,OAAO,CAAC,CAAC,CAAA,KAAM,GAAG,EAAE;QAC7B,aAAA,GAAgB,KAAK;IACzB;IAEE,OAAO;QACL,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC;QACnB,aAAa;QACb,YAAY,EAAE,OAAO,CAAC,CAAC,CAAC;IAC5B,CAAG;AACH;AAEA;;;CAGA,GACO,SAAS,6BAA6B,CAC3C,WAAW,EACX,OAAO;IAEP,MAAM,eAAgB,GAAE,sBAAsB,CAAC,WAAW,CAAC;IAC3D,MAAM,sBAAuB,sPAAE,wCAAA,AAAqC,EAAC,OAAO,CAAC;IAE7E,IAAI,CAAC,eAAe,EAAE,OAAO,EAAE;QAC7B,OAAO;YACL,OAAO,gQAAE,kBAAA,AAAe,EAAE;YAC1B,UAAU,EAAE,IAAI,CAAC,MAAM,EAAE;QAC/B,CAAK;IACL;IAEE,MAAM,aAAa,kCAAkC,CAAC,eAAe,EAAE,sBAAsB,CAAC;IAEhG,mFAAA;IACE,IAAI,sBAAsB,EAAE;QAC1B,sBAAsB,CAAC,WAAY,GAAE,UAAU,CAAC,QAAQ,EAAE;IAC9D;IAEE,MAAM,EAAE,OAAO,EAAE,YAAY,EAAE,aAAA,EAAgB,GAAE,eAAe;IAEhE,OAAO;QACL,OAAO;QACP,YAAY;QACZ,OAAO,EAAE,aAAa;QACtB,GAAG,EAAE,sBAAuB,IAAG,CAAA,CAAE;QACjC,UAAU;IACd,CAAG;AACH;AAEA;;CAEA,GACO,SAAS,yBAAyB,CACvC,OAAO,iQAAuB,kBAAA,AAAe,GAAE,EAC/C,MAAM,iQAAuB,iBAAA,AAAc,GAAE,EAC7C,OAAO;IAEP,IAAI,aAAc,GAAE,EAAE;IACtB,IAAI,OAAQ,KAAI,SAAS,EAAE;QACzB,gBAAgB,OAAA,GAAU,IAAA,GAAO,IAAI;IACzC;IACE,OAAO,CAAC,EAAA,OAAA,CAAA,CAAA,EAAA,MAAA,CAAA,EAAA,aAAA,CAAA,CAAA;AACA;AAEA;;;;CAIA,GACA,SAAA,kCAAA,CACA,eAAA,EACA,GAAA;IAEA,gDAAA;IACA,MAAA,gBAAA,IAAA,mQAAA,EAAA,GAAA,EAAA,WAAA,CAAA;IACA,IAAA,gBAAA,KAAA,SAAA,EAAA;QACA,OAAA,gBAAA;IACA;IAEA,4IAAA;IACA,MAAA,gBAAA,qPAAA,kBAAA,EAAA,GAAA,EAAA,WAAA,CAAA;IACA,IAAA,gBAAA,IAAA,eAAA,EAAA,aAAA,KAAA,SAAA,EAAA;QACA,OAAA,eAAA,CAAA,aAAA,GAEA,IAAA,CAAA,MAAA,EAAA,GAAA,mBAEA,gBAAA,GAAA,IAAA,CAAA,MAAA,EAAA,GAAA,CAAA,CAAA,GAAA,gBAAA,CAAA;IACA,CAAA,MAAA;QACA,mDAAA;QACA,OAAA,IAAA,CAAA,MAAA,EAAA;IACA;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3552, "column": 0}, "map": {"version": 3, "file": "spanUtils.js", "sources": ["file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/%40sentry%2Bcore%409.22.0/node_modules/%40sentry/core/src/utils/spanUtils.ts"], "sourcesContent": ["import { getAsyncContextStrategy } from '../asyncContext';\nimport { getMainCarrier } from '../carrier';\nimport { getCurrentScope } from '../currentScopes';\nimport {\n  SEMANTIC_ATTRIBUTE_SENTRY_CUSTOM_SPAN_NAME,\n  SEMANTIC_ATTRIBUTE_SENTRY_OP,\n  SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN,\n  SEMANTIC_ATTRIBUTE_SENTRY_SOURCE,\n} from '../semanticAttributes';\nimport type { SentrySpan } from '../tracing/sentrySpan';\nimport { SPAN_STATUS_OK, SPAN_STATUS_UNSET } from '../tracing/spanstatus';\nimport { getCapturedScopesOnSpan } from '../tracing/utils';\nimport type { TraceContext } from '../types-hoist/context';\nimport type { SpanLink, SpanLinkJSON } from '../types-hoist/link';\nimport type { Span, SpanAttributes, SpanJSON, SpanOrigin, SpanTimeInput } from '../types-hoist/span';\nimport type { SpanStatus } from '../types-hoist/spanStatus';\nimport { consoleSandbox } from '../utils-hoist/logger';\nimport { addNonEnumerableProperty } from '../utils-hoist/object';\nimport { generateSpanId } from '../utils-hoist/propagationContext';\nimport { timestampInSeconds } from '../utils-hoist/time';\nimport { generateSentryTraceHeader } from '../utils-hoist/tracing';\nimport { _getSpanForScope } from './spanOnScope';\n\n// These are aligned with OpenTelemetry trace flags\nexport const TRACE_FLAG_NONE = 0x0;\nexport const TRACE_FLAG_SAMPLED = 0x1;\n\nlet hasShownSpanDropWarning = false;\n\n/**\n * Convert a span to a trace context, which can be sent as the `trace` context in an event.\n * By default, this will only include trace_id, span_id & parent_span_id.\n * If `includeAllData` is true, it will also include data, op, status & origin.\n */\nexport function spanToTransactionTraceContext(span: Span): TraceContext {\n  const { spanId: span_id, traceId: trace_id } = span.spanContext();\n  const { data, op, parent_span_id, status, origin, links } = spanToJSON(span);\n\n  return {\n    parent_span_id,\n    span_id,\n    trace_id,\n    data,\n    op,\n    status,\n    origin,\n    links,\n  };\n}\n\n/**\n * Convert a span to a trace context, which can be sent as the `trace` context in a non-transaction event.\n */\nexport function spanToTraceContext(span: Span): TraceContext {\n  const { spanId, traceId: trace_id, isRemote } = span.spanContext();\n\n  // If the span is remote, we use a random/virtual span as span_id to the trace context,\n  // and the remote span as parent_span_id\n  const parent_span_id = isRemote ? spanId : spanToJSON(span).parent_span_id;\n  const scope = getCapturedScopesOnSpan(span).scope;\n\n  const span_id = isRemote ? scope?.getPropagationContext().propagationSpanId || generateSpanId() : spanId;\n\n  return {\n    parent_span_id,\n    span_id,\n    trace_id,\n  };\n}\n\n/**\n * Convert a Span to a Sentry trace header.\n */\nexport function spanToTraceHeader(span: Span): string {\n  const { traceId, spanId } = span.spanContext();\n  const sampled = spanIsSampled(span);\n  return generateSentryTraceHeader(traceId, spanId, sampled);\n}\n\n/**\n *  Converts the span links array to a flattened version to be sent within an envelope.\n *\n *  If the links array is empty, it returns `undefined` so the empty value can be dropped before it's sent.\n */\nexport function convertSpanLinksForEnvelope(links?: SpanLink[]): SpanLinkJSON[] | undefined {\n  if (links && links.length > 0) {\n    return links.map(({ context: { spanId, traceId, traceFlags, ...restContext }, attributes }) => ({\n      span_id: spanId,\n      trace_id: traceId,\n      sampled: traceFlags === TRACE_FLAG_SAMPLED,\n      attributes,\n      ...restContext,\n    }));\n  } else {\n    return undefined;\n  }\n}\n\n/**\n * Convert a span time input into a timestamp in seconds.\n */\nexport function spanTimeInputToSeconds(input: SpanTimeInput | undefined): number {\n  if (typeof input === 'number') {\n    return ensureTimestampInSeconds(input);\n  }\n\n  if (Array.isArray(input)) {\n    // See {@link HrTime} for the array-based time format\n    return input[0] + input[1] / 1e9;\n  }\n\n  if (input instanceof Date) {\n    return ensureTimestampInSeconds(input.getTime());\n  }\n\n  return timestampInSeconds();\n}\n\n/**\n * Converts a timestamp to second, if it was in milliseconds, or keeps it as second.\n */\nfunction ensureTimestampInSeconds(timestamp: number): number {\n  const isMs = timestamp > 9999999999;\n  return isMs ? timestamp / 1000 : timestamp;\n}\n\n/**\n * Convert a span to a JSON representation.\n */\n// Note: Because of this, we currently have a circular type dependency (which we opted out of in package.json).\n// This is not avoidable as we need `spanToJSON` in `spanUtils.ts`, which in turn is needed by `span.ts` for backwards compatibility.\n// And `spanToJSON` needs the Span class from `span.ts` to check here.\nexport function spanToJSON(span: Span): SpanJSON {\n  if (spanIsSentrySpan(span)) {\n    return span.getSpanJSON();\n  }\n\n  const { spanId: span_id, traceId: trace_id } = span.spanContext();\n\n  // Handle a span from @opentelemetry/sdk-base-trace's `Span` class\n  if (spanIsOpenTelemetrySdkTraceBaseSpan(span)) {\n    const { attributes, startTime, name, endTime, status, links } = span;\n\n    // In preparation for the next major of OpenTelemetry, we want to support\n    // looking up the parent span id according to the new API\n    // In OTel v1, the parent span id is accessed as `parentSpanId`\n    // In OTel v2, the parent span id is accessed as `spanId` on the `parentSpanContext`\n    const parentSpanId =\n      'parentSpanId' in span\n        ? span.parentSpanId\n        : 'parentSpanContext' in span\n          ? (span.parentSpanContext as { spanId?: string } | undefined)?.spanId\n          : undefined;\n\n    return {\n      span_id,\n      trace_id,\n      data: attributes,\n      description: name,\n      parent_span_id: parentSpanId,\n      start_timestamp: spanTimeInputToSeconds(startTime),\n      // This is [0,0] by default in OTEL, in which case we want to interpret this as no end time\n      timestamp: spanTimeInputToSeconds(endTime) || undefined,\n      status: getStatusMessage(status),\n      op: attributes[SEMANTIC_ATTRIBUTE_SENTRY_OP],\n      origin: attributes[SEMANTIC_ATTRIBUTE_SENTRY_ORIGIN] as SpanOrigin | undefined,\n      links: convertSpanLinksForEnvelope(links),\n    };\n  }\n\n  // Finally, at least we have `spanContext()`....\n  // This should not actually happen in reality, but we need to handle it for type safety.\n  return {\n    span_id,\n    trace_id,\n    start_timestamp: 0,\n    data: {},\n  };\n}\n\nfunction spanIsOpenTelemetrySdkTraceBaseSpan(span: Span): span is OpenTelemetrySdkTraceBaseSpan {\n  const castSpan = span as Partial<OpenTelemetrySdkTraceBaseSpan>;\n  return !!castSpan.attributes && !!castSpan.startTime && !!castSpan.name && !!castSpan.endTime && !!castSpan.status;\n}\n\n/** Exported only for tests. */\nexport interface OpenTelemetrySdkTraceBaseSpan extends Span {\n  attributes: SpanAttributes;\n  startTime: SpanTimeInput;\n  name: string;\n  status: SpanStatus;\n  endTime: SpanTimeInput;\n  parentSpanId?: string;\n  links?: SpanLink[];\n}\n\n/**\n * Sadly, due to circular dependency checks we cannot actually import the Span class here and check for instanceof.\n * :( So instead we approximate this by checking if it has the `getSpanJSON` method.\n */\nfunction spanIsSentrySpan(span: Span): span is SentrySpan {\n  return typeof (span as SentrySpan).getSpanJSON === 'function';\n}\n\n/**\n * Returns true if a span is sampled.\n * In most cases, you should just use `span.isRecording()` instead.\n * However, this has a slightly different semantic, as it also returns false if the span is finished.\n * So in the case where this distinction is important, use this method.\n */\nexport function spanIsSampled(span: Span): boolean {\n  // We align our trace flags with the ones OpenTelemetry use\n  // So we also check for sampled the same way they do.\n  const { traceFlags } = span.spanContext();\n  return traceFlags === TRACE_FLAG_SAMPLED;\n}\n\n/** Get the status message to use for a JSON representation of a span. */\nexport function getStatusMessage(status: SpanStatus | undefined): string | undefined {\n  if (!status || status.code === SPAN_STATUS_UNSET) {\n    return undefined;\n  }\n\n  if (status.code === SPAN_STATUS_OK) {\n    return 'ok';\n  }\n\n  return status.message || 'unknown_error';\n}\n\nconst CHILD_SPANS_FIELD = '_sentryChildSpans';\nconst ROOT_SPAN_FIELD = '_sentryRootSpan';\n\ntype SpanWithPotentialChildren = Span & {\n  [CHILD_SPANS_FIELD]?: Set<Span>;\n  [ROOT_SPAN_FIELD]?: Span;\n};\n\n/**\n * Adds an opaque child span reference to a span.\n */\nexport function addChildSpanToSpan(span: SpanWithPotentialChildren, childSpan: Span): void {\n  // We store the root span reference on the child span\n  // We need this for `getRootSpan()` to work\n  const rootSpan = span[ROOT_SPAN_FIELD] || span;\n  addNonEnumerableProperty(childSpan as SpanWithPotentialChildren, ROOT_SPAN_FIELD, rootSpan);\n\n  // We store a list of child spans on the parent span\n  // We need this for `getSpanDescendants()` to work\n  if (span[CHILD_SPANS_FIELD]) {\n    span[CHILD_SPANS_FIELD].add(childSpan);\n  } else {\n    addNonEnumerableProperty(span, CHILD_SPANS_FIELD, new Set([childSpan]));\n  }\n}\n\n/** This is only used internally by Idle Spans. */\nexport function removeChildSpanFromSpan(span: SpanWithPotentialChildren, childSpan: Span): void {\n  if (span[CHILD_SPANS_FIELD]) {\n    span[CHILD_SPANS_FIELD].delete(childSpan);\n  }\n}\n\n/**\n * Returns an array of the given span and all of its descendants.\n */\nexport function getSpanDescendants(span: SpanWithPotentialChildren): Span[] {\n  const resultSet = new Set<Span>();\n\n  function addSpanChildren(span: SpanWithPotentialChildren): void {\n    // This exit condition is required to not infinitely loop in case of a circular dependency.\n    if (resultSet.has(span)) {\n      return;\n      // We want to ignore unsampled spans (e.g. non recording spans)\n    } else if (spanIsSampled(span)) {\n      resultSet.add(span);\n      const childSpans = span[CHILD_SPANS_FIELD] ? Array.from(span[CHILD_SPANS_FIELD]) : [];\n      for (const childSpan of childSpans) {\n        addSpanChildren(childSpan);\n      }\n    }\n  }\n\n  addSpanChildren(span);\n\n  return Array.from(resultSet);\n}\n\n/**\n * Returns the root span of a given span.\n */\nexport function getRootSpan(span: SpanWithPotentialChildren): Span {\n  return span[ROOT_SPAN_FIELD] || span;\n}\n\n/**\n * Returns the currently active span.\n */\nexport function getActiveSpan(): Span | undefined {\n  const carrier = getMainCarrier();\n  const acs = getAsyncContextStrategy(carrier);\n  if (acs.getActiveSpan) {\n    return acs.getActiveSpan();\n  }\n\n  return _getSpanForScope(getCurrentScope());\n}\n\n/**\n * Logs a warning once if `beforeSendSpan` is used to drop spans.\n */\nexport function showSpanDropWarning(): void {\n  if (!hasShownSpanDropWarning) {\n    consoleSandbox(() => {\n      // eslint-disable-next-line no-console\n      console.warn(\n        '[Sentry] Returning null from `beforeSendSpan` is disallowed. To drop certain spans, configure the respective integrations directly.',\n      );\n    });\n    hasShownSpanDropWarning = true;\n  }\n}\n\n/**\n * Updates the name of the given span and ensures that the span name is not\n * overwritten by the Sentry SDK.\n *\n * Use this function instead of `span.updateName()` if you want to make sure that\n * your name is kept. For some spans, for example root `http.server` spans the\n * Sentry SDK would otherwise overwrite the span name with a high-quality name\n * it infers when the span ends.\n *\n * Use this function in server code or when your span is started on the server\n * and on the client (browser). If you only update a span name on the client,\n * you can also use `span.updateName()` the SDK does not overwrite the name.\n *\n * @param span - The span to update the name of.\n * @param name - The name to set on the span.\n */\nexport function updateSpanName(span: Span, name: string): void {\n  span.updateName(name);\n  span.setAttributes({\n    [SEMANTIC_ATTRIBUTE_SENTRY_SOURCE]: 'custom',\n    [SEMANTIC_ATTRIBUTE_SENTRY_CUSTOM_SPAN_NAME]: name,\n  });\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuBA,mDAAA;AACO,MAAM,eAAgB,GAAE;AACxB,MAAM,kBAAmB,GAAE;AAElC,IAAI,uBAAA,GAA0B,KAAK;AAEnC;;;;CAIA,GACO,SAAS,6BAA6B,CAAC,IAAI,EAAsB;IACtE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,QAAA,EAAA,GAAa,IAAI,CAAC,WAAW,EAAE;IACjE,MAAM,EAAE,IAAI,EAAE,EAAE,EAAE,cAAc,EAAE,MAAM,EAAE,MAAM,EAAE,KAAM,EAAA,GAAI,UAAU,CAAC,IAAI,CAAC;IAE5E,OAAO;QACL,cAAc;QACd,OAAO;QACP,QAAQ;QACR,IAAI;QACJ,EAAE;QACF,MAAM;QACN,MAAM;QACN,KAAK;IACT,CAAG;AACH;AAEA;;CAEA,GACO,SAAS,kBAAkB,CAAC,IAAI,EAAsB;IAC3D,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAA,EAAA,GAAa,IAAI,CAAC,WAAW,EAAE;IAEpE,uFAAA;IACA,wCAAA;IACE,MAAM,cAAA,GAAiB,QAAA,GAAW,MAAA,GAAS,UAAU,CAAC,IAAI,CAAC,CAAC,cAAc;IAC1E,MAAM,kPAAQ,0BAAA,AAAuB,EAAC,IAAI,CAAC,CAAC,KAAK;IAEjD,MAAM,OAAQ,GAAE,QAAS,GAAE,KAAK,EAAE,qBAAqB,EAAE,CAAC,iBAAkB,kQAAG,iBAAA,AAAc,EAAC,IAAI,MAAM;IAExG,OAAO;QACL,cAAc;QACd,OAAO;QACP,QAAQ;IACZ,CAAG;AACH;AAEA;;CAEA,GACO,SAAS,iBAAiB,CAAC,IAAI,EAAgB;IACpD,MAAM,EAAE,OAAO,EAAE,MAAA,EAAS,GAAE,IAAI,CAAC,WAAW,EAAE;IAC9C,MAAM,OAAQ,GAAE,aAAa,CAAC,IAAI,CAAC;IACnC,0PAAO,4BAAA,AAAyB,EAAC,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC;AAC5D;AAEA;;;;CAIA,GACO,SAAS,2BAA2B,CAAC,KAAK,EAA2C;IAC1F,IAAI,KAAM,IAAG,KAAK,CAAC,MAAA,GAAS,CAAC,EAAE;QAC7B,OAAO,KAAK,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,EAAE,EAAE,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,GAAG,WAAY,EAAC,EAAE,UAAW,EAAC,GAAA,CAAM;gBAC9F,OAAO,EAAE,MAAM;gBACf,QAAQ,EAAE,OAAO;gBACjB,OAAO,EAAE,UAAW,KAAI,kBAAkB;gBAC1C,UAAU;gBACV,GAAG,WAAW;YACpB,CAAK,CAAC,CAAC;IACP,OAAS;QACL,OAAO,SAAS;IACpB;AACA;AAEA;;CAEA,GACO,SAAS,sBAAsB,CAAC,KAAK,EAAqC;IAC/E,IAAI,OAAO,KAAM,KAAI,QAAQ,EAAE;QAC7B,OAAO,wBAAwB,CAAC,KAAK,CAAC;IAC1C;IAEE,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;QAC5B,qDAAA;QACI,OAAO,KAAK,CAAC,CAAC,CAAA,GAAI,KAAK,CAAC,CAAC,CAAE,GAAE,GAAG;IACpC;IAEE,IAAI,KAAM,YAAW,IAAI,EAAE;QACzB,OAAO,wBAAwB,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;IACpD;IAEE,uPAAO,qBAAA,AAAkB,EAAE;AAC7B;AAEA;;CAEA,GACA,SAAS,wBAAwB,CAAC,SAAS,EAAkB;IAC3D,MAAM,IAAA,GAAO,SAAA,GAAY,UAAU;IACnC,OAAO,IAAK,GAAE,YAAY,IAAA,GAAO,SAAS;AAC5C;AAEA;;CAEA,GACA,+GAAA;AACA,qIAAA;AACA,sEAAA;AACO,SAAS,UAAU,CAAC,IAAI,EAAkB;IAC/C,IAAI,gBAAgB,CAAC,IAAI,CAAC,EAAE;QAC1B,OAAO,IAAI,CAAC,WAAW,EAAE;IAC7B;IAEE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,QAAA,EAAA,GAAa,IAAI,CAAC,WAAW,EAAE;IAEnE,kEAAA;IACE,IAAI,mCAAmC,CAAC,IAAI,CAAC,EAAE;QAC7C,MAAM,EAAE,UAAU,EAAE,SAAS,EAAE,IAAI,EAAE,OAAO,EAAE,MAAM,EAAE,KAAM,EAAA,GAAI,IAAI;QAExE,yEAAA;QACA,yDAAA;QACA,+DAAA;QACA,oFAAA;QACI,MAAM,YAAa,GACjB,kBAAkB,OACd,IAAI,CAAC,YAAA,GACL,uBAAuB,OACpB,IAAI,CAAC,iBAAA,EAAuD,SAC7D,SAAS;QAEjB,OAAO;YACL,OAAO;YACP,QAAQ;YACR,IAAI,EAAE,UAAU;YAChB,WAAW,EAAE,IAAI;YACjB,cAAc,EAAE,YAAY;YAC5B,eAAe,EAAE,sBAAsB,CAAC,SAAS,CAAC;YACxD,2FAAA;YACM,SAAS,EAAE,sBAAsB,CAAC,OAAO,CAAA,IAAK,SAAS;YACvD,MAAM,EAAE,gBAAgB,CAAC,MAAM,CAAC;YAChC,EAAE,EAAE,UAAU,yOAAC,+BAA4B,CAAC;YAC5C,MAAM,EAAE,UAAU,wOAAC,oCAAgC,CAAE;YACrD,KAAK,EAAE,2BAA2B,CAAC,KAAK,CAAC;QAC/C,CAAK;IACL;IAEA,gDAAA;IACA,wFAAA;IACE,OAAO;QACL,OAAO;QACP,QAAQ;QACR,eAAe,EAAE,CAAC;QAClB,IAAI,EAAE,CAAA,CAAE;IACZ,CAAG;AACH;AAEA,SAAS,mCAAmC,CAAC,IAAI,EAA+C;IAC9F,MAAM,QAAS,GAAE,IAAK;IACtB,OAAO,CAAC,CAAC,QAAQ,CAAC,UAAA,IAAc,CAAC,CAAC,QAAQ,CAAC,SAAA,IAAa,CAAC,CAAC,QAAQ,CAAC,IAAA,IAAQ,CAAC,CAAC,QAAQ,CAAC,OAAA,IAAW,CAAC,CAAC,QAAQ,CAAC,MAAM;AACpH;AAEA,6BAAA,GAWA;;;CAGA,GACA,SAAS,gBAAgB,CAAC,IAAI,EAA4B;IACxD,OAAO,OAAO,AAAC,IAAA,CAAoB,WAAA,KAAgB,UAAU;AAC/D;AAEA;;;;;CAKA,GACO,SAAS,aAAa,CAAC,IAAI,EAAiB;IACnD,2DAAA;IACA,qDAAA;IACE,MAAM,EAAE,UAAW,EAAA,GAAI,IAAI,CAAC,WAAW,EAAE;IACzC,OAAO,UAAW,KAAI,kBAAkB;AAC1C;AAEA,uEAAA,GACO,SAAS,gBAAgB,CAAC,MAAM,EAA8C;IACnF,IAAI,CAAC,MAAO,IAAG,MAAM,CAAC,IAAA,gPAAS,oBAAiB,EAAE;QAChD,OAAO,SAAS;IACpB;IAEE,IAAI,MAAM,CAAC,IAAK,+OAAI,kBAAc,EAAE;QAClC,OAAO,IAAI;IACf;IAEE,OAAO,MAAM,CAAC,OAAA,IAAW,eAAe;AAC1C;AAEA,MAAM,iBAAA,GAAoB,mBAAmB;AAC7C,MAAM,eAAA,GAAkB,iBAAiB;AAOzC;;CAEA,GACO,SAAS,kBAAkB,CAAC,IAAI,EAA6B,SAAS,EAAc;IAC3F,qDAAA;IACA,2CAAA;IACE,MAAM,WAAW,IAAI,CAAC,eAAe,CAAA,IAAK,IAAI;sPAC9C,2BAAA,AAAwB,EAAC,SAAA,EAAwC,eAAe,EAAE,QAAQ,CAAC;IAE7F,oDAAA;IACA,kDAAA;IACE,IAAI,IAAI,CAAC,iBAAiB,CAAC,EAAE;QAC3B,IAAI,CAAC,iBAAiB,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC;IAC1C,OAAS;0PACL,2BAAA,AAAwB,EAAC,IAAI,EAAE,iBAAiB,EAAE,IAAI,GAAG,CAAC;YAAC,SAAS;SAAC,CAAC,CAAC;IAC3E;AACA;AAEA,gDAAA,GACO,SAAS,uBAAuB,CAAC,IAAI,EAA6B,SAAS,EAAc;IAC9F,IAAI,IAAI,CAAC,iBAAiB,CAAC,EAAE;QAC3B,IAAI,CAAC,iBAAiB,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC;IAC7C;AACA;AAEA;;CAEA,GACO,SAAS,kBAAkB,CAAC,IAAI,EAAqC;IAC1E,MAAM,SAAU,GAAE,IAAI,GAAG,EAAQ;IAEjC,SAAS,eAAe,CAAC,IAAI,EAAmC;QAClE,2FAAA;QACI,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;YACvB;QACN,+DAAA;QACA,CAAI,MAAO,IAAI,aAAa,CAAC,IAAI,CAAC,EAAE;YAC9B,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC;YACnB,MAAM,UAAW,GAAE,IAAI,CAAC,iBAAiB,CAAA,GAAI,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAE,GAAE,EAAE;YACrF,KAAK,MAAM,SAAU,IAAG,UAAU,CAAE;gBAClC,eAAe,CAAC,SAAS,CAAC;YAClC;QACA;IACA;IAEE,eAAe,CAAC,IAAI,CAAC;IAErB,OAAO,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC;AAC9B;AAEA;;CAEA,GACO,SAAS,WAAW,CAAC,IAAI,EAAmC;IACjE,OAAO,IAAI,CAAC,eAAe,CAAA,IAAK,IAAI;AACtC;AAEA;;CAEA,GACO,SAAS,aAAa,GAAqB;IAChD,MAAM,OAAA,oOAAU,iBAAA,AAAc,EAAE;IAChC,MAAM,GAAI,GAAE,yQAAA,AAAuB,EAAC,OAAO,CAAC;IAC5C,IAAI,GAAG,CAAC,aAAa,EAAE;QACrB,OAAO,GAAG,CAAC,aAAa,EAAE;IAC9B;IAEE,OAAO,iQAAA,AAAgB,yOAAC,kBAAA,AAAe,EAAE,CAAC;AAC5C;AAEA;;CAEA,GACO,SAAS,mBAAmB,GAAS;IAC1C,IAAI,CAAC,uBAAuB,EAAE;QAC5B,mQAAA,AAAc,EAAC,MAAM;YACzB,sCAAA;YACM,OAAO,CAAC,IAAI,CACV,qIAAqI;QAE7I,CAAK,CAAC;QACF,uBAAA,GAA0B,IAAI;IAClC;AACA;AAEA;;;;;;;;;;;;;;;CAeA,GACO,SAAS,cAAc,CAAC,IAAI,EAAQ,IAAI,EAAgB;IAC7D,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;IACrB,IAAI,CAAC,aAAa,CAAC;QACjB,yOAAC,mCAAgC,CAAA,EAAG,QAAQ;QAC5C,yOAAC,6CAA0C,CAAA,EAAG,IAAI;IACtD,CAAG,CAAC;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3849, "column": 0}, "map": {"version": 3, "file": "dsn.js", "sources": ["file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/%40sentry%2Bcore%409.22.0/node_modules/%40sentry/core/src/utils-hoist/dsn.ts"], "sourcesContent": ["import type { Dsn<PERSON><PERSON><PERSON>, Dsn<PERSON><PERSON>, DsnProtocol } from '../types-hoist/dsn';\nimport { DEBUG_BUILD } from './../debug-build';\nimport { consoleSandbox, logger } from './logger';\n\n/** Regular expression used to extract org ID from a DSN host. */\nconst ORG_ID_REGEX = /^o(\\d+)\\./;\n\n/** Regular expression used to parse a Dsn. */\nconst DSN_REGEX = /^(?:(\\w+):)\\/\\/(?:(\\w+)(?::(\\w+)?)?@)([\\w.-]+)(?::(\\d+))?\\/(.+)/;\n\nfunction isValidProtocol(protocol?: string): protocol is DsnProtocol {\n  return protocol === 'http' || protocol === 'https';\n}\n\n/**\n * Renders the string representation of this Dsn.\n *\n * By default, this will render the public representation without the password\n * component. To get the deprecated private representation, set `withPassword`\n * to true.\n *\n * @param withPassword When set to true, the password will be included.\n */\nexport function dsnToString(dsn: DsnComponents, withPassword: boolean = false): string {\n  const { host, path, pass, port, projectId, protocol, publicKey } = dsn;\n  return (\n    `${protocol}://${publicKey}${withPassword && pass ? `:${pass}` : ''}` +\n    `@${host}${port ? `:${port}` : ''}/${path ? `${path}/` : path}${projectId}`\n  );\n}\n\n/**\n * Parses a Dsn from a given string.\n *\n * @param str A Dsn as string\n * @returns Dsn as DsnComponents or undefined if @param str is not a valid DSN string\n */\nexport function dsnFromString(str: string): DsnComponents | undefined {\n  const match = DSN_REGEX.exec(str);\n\n  if (!match) {\n    // This should be logged to the console\n    consoleSandbox(() => {\n      // eslint-disable-next-line no-console\n      console.error(`Invalid Sentry Dsn: ${str}`);\n    });\n    return undefined;\n  }\n\n  const [protocol, publicKey, pass = '', host = '', port = '', lastPath = ''] = match.slice(1);\n  let path = '';\n  let projectId = lastPath;\n\n  const split = projectId.split('/');\n  if (split.length > 1) {\n    path = split.slice(0, -1).join('/');\n    projectId = split.pop() as string;\n  }\n\n  if (projectId) {\n    const projectMatch = projectId.match(/^\\d+/);\n    if (projectMatch) {\n      projectId = projectMatch[0];\n    }\n  }\n\n  return dsnFromComponents({ host, pass, path, projectId, port, protocol: protocol as DsnProtocol, publicKey });\n}\n\nfunction dsnFromComponents(components: DsnComponents): DsnComponents {\n  return {\n    protocol: components.protocol,\n    publicKey: components.publicKey || '',\n    pass: components.pass || '',\n    host: components.host,\n    port: components.port || '',\n    path: components.path || '',\n    projectId: components.projectId,\n  };\n}\n\nfunction validateDsn(dsn: DsnComponents): boolean {\n  if (!DEBUG_BUILD) {\n    return true;\n  }\n\n  const { port, projectId, protocol } = dsn;\n\n  const requiredComponents: ReadonlyArray<keyof DsnComponents> = ['protocol', 'publicKey', 'host', 'projectId'];\n  const hasMissingRequiredComponent = requiredComponents.find(component => {\n    if (!dsn[component]) {\n      logger.error(`Invalid Sentry Dsn: ${component} missing`);\n      return true;\n    }\n    return false;\n  });\n\n  if (hasMissingRequiredComponent) {\n    return false;\n  }\n\n  if (!projectId.match(/^\\d+$/)) {\n    logger.error(`Invalid Sentry Dsn: Invalid projectId ${projectId}`);\n    return false;\n  }\n\n  if (!isValidProtocol(protocol)) {\n    logger.error(`Invalid Sentry Dsn: Invalid protocol ${protocol}`);\n    return false;\n  }\n\n  if (port && isNaN(parseInt(port, 10))) {\n    logger.error(`Invalid Sentry Dsn: Invalid port ${port}`);\n    return false;\n  }\n\n  return true;\n}\n\n/**\n * Extract the org ID from a DSN host.\n *\n * @param host The host from a DSN\n * @returns The org ID if found, undefined otherwise\n */\nexport function extractOrgIdFromDsnHost(host: string): string | undefined {\n  const match = host.match(ORG_ID_REGEX);\n\n  return match?.[1];\n}\n\n/**\n * Creates a valid Sentry Dsn object, identifying a Sentry instance and project.\n * @returns a valid DsnComponents object or `undefined` if @param from is an invalid DSN source\n */\nexport function makeDsn(from: DsnLike): DsnComponents | undefined {\n  const components = typeof from === 'string' ? dsnFromString(from) : dsnFromComponents(from);\n  if (!components || !validateDsn(components)) {\n    return undefined;\n  }\n  return components;\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAIA,+DAAA,GACA,MAAM,YAAA,GAAe,WAAW;AAEhC,4CAAA,GACA,MAAM,SAAA,GAAY,iEAAiE;AAEnF,SAAS,eAAe,CAAC,QAAQ,EAAoC;IACnE,OAAO,QAAS,KAAI,UAAU,QAAA,KAAa,OAAO;AACpD;AAEA;;;;;;;;CAQA,GACO,SAAS,WAAW,CAAC,GAAG,EAAiB,YAAY,GAAY,KAAK,EAAU;IACrF,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,QAAQ,EAAE,SAAU,EAAA,GAAI,GAAG;IACtE,OACE,CAAC,EAAA,QAAA,CAAA,GAAA,EAAA,SAAA,CAAA,EAAA,YAAA,IAAA,IAAA,GAAA,CAAA,CAAA,EAAA,IAAA,CAAA,CAAA,GAAA,EAAA,CAAA,CAAA,GACA,CAAA,CAAA,EAAA,IAAA,CAAA,EAAA,IAAA,GAAA,CAAA,CAAA,EAAA,IAAA,CAAA,CAAA,GAAA,EAAA,CAAA,CAAA,EAAA,IAAA,GAAA,CAAA,EAAA,IAAA,CAAA,CAAA,CAAA,GAAA,IAAA,CAAA,EAAA,SAAA,CAAA,CAAA;AAEA;AAEA;;;;;CAKA,GACA,SAAA,aAAA,CAAA,GAAA,EAAA;IACA,MAAA,KAAA,GAAA,SAAA,CAAA,IAAA,CAAA,GAAA,CAAA;IAEA,IAAA,CAAA,KAAA,EAAA;QACA,uCAAA;YACA,+PAAA,EAAA,MAAA;YACA,sCAAA;YACA,OAAA,CAAA,KAAA,CAAA,CAAA,oBAAA,EAAA,GAAA,CAAA,CAAA,CAAA;QACA,CAAA,CAAA;QACA,OAAA,SAAA;IACA;IAEA,MAAA,CAAA,QAAA,EAAA,SAAA,EAAA,IAAA,GAAA,EAAA,EAAA,IAAA,GAAA,EAAA,EAAA,IAAA,GAAA,EAAA,EAAA,QAAA,GAAA,EAAA,CAAA,GAAA,KAAA,CAAA,KAAA,CAAA,CAAA,CAAA;IACA,IAAA,IAAA,GAAA,EAAA;IACA,IAAA,SAAA,GAAA,QAAA;IAEA,MAAA,KAAA,GAAA,SAAA,CAAA,KAAA,CAAA,GAAA,CAAA;IACA,IAAA,KAAA,CAAA,MAAA,GAAA,CAAA,EAAA;QACA,IAAA,GAAA,KAAA,CAAA,KAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,GAAA,CAAA;QACA,SAAA,GAAA,KAAA,CAAA,GAAA,EAAA;IACA;IAEA,IAAA,SAAA,EAAA;QACA,MAAA,YAAA,GAAA,SAAA,CAAA,KAAA,CAAA,MAAA,CAAA;QACA,IAAA,YAAA,EAAA;YACA,SAAA,GAAA,YAAA,CAAA,CAAA,CAAA;QACA;IACA;IAEA,OAAA,iBAAA,CAAA;QAAA,IAAA;QAAA,IAAA;QAAA,IAAA;QAAA,SAAA;QAAA,IAAA;QAAA,QAAA,EAAA,QAAA;QAAA,SAAA;IAAA,CAAA,CAAA;AACA;AAEA,SAAA,iBAAA,CAAA,UAAA,EAAA;IACA,OAAA;QACA,QAAA,EAAA,UAAA,CAAA,QAAA;QACA,SAAA,EAAA,UAAA,CAAA,SAAA,IAAA,EAAA;QACA,IAAA,EAAA,UAAA,CAAA,IAAA,IAAA,EAAA;QACA,IAAA,EAAA,UAAA,CAAA,IAAA;QACA,IAAA,EAAA,UAAA,CAAA,IAAA,IAAA,EAAA;QACA,IAAA,EAAA,UAAA,CAAA,IAAA,IAAA,EAAA;QACA,SAAA,EAAA,UAAA,CAAA,SAAA;IACA,CAAA;AACA;AAEA,SAAA,WAAA,CAAA,GAAA,EAAA;IACA,IAAA,qOAAA,cAAA,EAAA;QACA,OAAA,IAAA;IACA;IAEA,MAAA,EAAA,IAAA,EAAA,SAAA,EAAA,QAAA,EAAA,GAAA,GAAA;IAEA,MAAA,kBAAA,GAAA;QAAA,UAAA;QAAA,WAAA;QAAA,MAAA;QAAA,WAAA;KAAA;IACA,MAAA,2BAAA,GAAA,kBAAA,CAAA,IAAA,EAAA,SAAA,IAAA;QACA,IAAA,CAAA,GAAA,CAAA,SAAA,CAAA,EAAA;YACA,uPAAA,CAAA,KAAA,CAAA,CAAA,oBAAA,EAAA,SAAA,CAAA,QAAA,CAAA,CAAA;YACA,OAAA,IAAA;QACA;QACA,OAAA,KAAA;IACA,CAAA,CAAA;IAEA,IAAA,2BAAA,EAAA;QACA,OAAA,KAAA;IACA;IAEA,IAAA,CAAA,SAAA,CAAA,KAAA,CAAA,OAAA,CAAA,EAAA;sPACA,SAAA,CAAA,KAAA,CAAA,CAAA,sCAAA,EAAA,SAAA,CAAA,CAAA,CAAA;QACA,OAAA,KAAA;IACA;IAEA,IAAA,CAAA,eAAA,CAAA,QAAA,CAAA,EAAA;QACA,uPAAA,CAAA,KAAA,CAAA,CAAA,qCAAA,EAAA,QAAA,CAAA,CAAA,CAAA;QACA,OAAA,KAAA;IACA;IAEA,IAAA,IAAA,IAAA,KAAA,CAAA,QAAA,CAAA,IAAA,EAAA,EAAA,CAAA,CAAA,EAAA;qPACA,UAAA,CAAA,KAAA,CAAA,CAAA,iCAAA,EAAA,IAAA,CAAA,CAAA,CAAA;QACA,OAAA,KAAA;IACA;IAEA,OAAA,IAAA;AACA;AAEA;;;;;CAKA,GACA,SAAA,uBAAA,CAAA,IAAA,EAAA;IACA,MAAA,KAAA,GAAA,IAAA,CAAA,KAAA,CAAA,YAAA,CAAA;IAEA,OAAA,KAAA,EAAA,CAAA,CAAA,CAAA;AACA;AAEA;;;CAGA,GACA,SAAA,OAAA,CAAA,IAAA,EAAA;IACA,MAAA,UAAA,GAAA,OAAA,IAAA,KAAA,QAAA,GAAA,aAAA,CAAA,IAAA,CAAA,GAAA,iBAAA,CAAA,IAAA,CAAA;IACA,IAAA,CAAA,UAAA,IAAA,CAAA,WAAA,CAAA,UAAA,CAAA,EAAA;QACA,OAAA,SAAA;IACA;IACA,OAAA,UAAA;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3988, "column": 0}, "map": {"version": 3, "file": "dynamicSamplingContext.js", "sources": ["file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/%40sentry%2Bcore%409.22.0/node_modules/%40sentry/core/src/tracing/dynamicSamplingContext.ts"], "sourcesContent": ["import type { Client } from '../client';\nimport { DEFAULT_ENVIRONMENT } from '../constants';\nimport { getClient } from '../currentScopes';\nimport type { Scope } from '../scope';\nimport {\n  SEMANTIC_ATTRIBUTE_SENTRY_PREVIOUS_TRACE_SAMPLE_RATE,\n  SEMANTIC_ATTRIBUTE_SENTRY_SAMPLE_RATE,\n  SEMANTIC_ATTRIBUTE_SENTRY_SOURCE,\n} from '../semanticAttributes';\nimport type { DynamicSamplingContext } from '../types-hoist/envelope';\nimport type { Span } from '../types-hoist/span';\nimport { hasSpansEnabled } from '../utils/hasSpansEnabled';\nimport { getRootSpan, spanIsSampled, spanToJSON } from '../utils/spanUtils';\nimport {\n  baggageHeaderToDynamicSamplingContext,\n  dynamicSamplingContextToSentryBaggageHeader,\n} from '../utils-hoist/baggage';\nimport { extractOrgIdFromDsnHost } from '../utils-hoist/dsn';\nimport { addNonEnumerableProperty } from '../utils-hoist/object';\nimport { getCapturedScopesOnSpan } from './utils';\n\n/**\n * If you change this value, also update the terser plugin config to\n * avoid minification of the object property!\n */\nconst FROZEN_DSC_FIELD = '_frozenDsc';\n\ntype SpanWithMaybeDsc = Span & {\n  [FROZEN_DSC_FIELD]?: Partial<DynamicSamplingContext> | undefined;\n};\n\n/**\n * Freeze the given DSC on the given span.\n */\nexport function freezeDscOnSpan(span: Span, dsc: Partial<DynamicSamplingContext>): void {\n  const spanWithMaybeDsc = span as SpanWithMaybeDsc;\n  addNonEnumerableProperty(spanWithMaybeDsc, FROZEN_DSC_FIELD, dsc);\n}\n\n/**\n * Creates a dynamic sampling context from a client.\n *\n * Dispatches the `createDsc` lifecycle hook as a side effect.\n */\nexport function getDynamicSamplingContextFromClient(trace_id: string, client: Client): DynamicSamplingContext {\n  const options = client.getOptions();\n\n  const { publicKey: public_key, host } = client.getDsn() || {};\n\n  let org_id: string | undefined;\n  if (options.orgId) {\n    org_id = String(options.orgId);\n  } else if (host) {\n    org_id = extractOrgIdFromDsnHost(host);\n  }\n\n  // Instead of conditionally adding non-undefined values, we add them and then remove them if needed\n  // otherwise, the order of baggage entries changes, which \"breaks\" a bunch of tests etc.\n  const dsc: DynamicSamplingContext = {\n    environment: options.environment || DEFAULT_ENVIRONMENT,\n    release: options.release,\n    public_key,\n    trace_id,\n    org_id,\n  };\n\n  client.emit('createDsc', dsc);\n\n  return dsc;\n}\n\n/**\n * Get the dynamic sampling context for the currently active scopes.\n */\nexport function getDynamicSamplingContextFromScope(client: Client, scope: Scope): Partial<DynamicSamplingContext> {\n  const propagationContext = scope.getPropagationContext();\n  return propagationContext.dsc || getDynamicSamplingContextFromClient(propagationContext.traceId, client);\n}\n\n/**\n * Creates a dynamic sampling context from a span (and client and scope)\n *\n * @param span the span from which a few values like the root span name and sample rate are extracted.\n *\n * @returns a dynamic sampling context\n */\nexport function getDynamicSamplingContextFromSpan(span: Span): Readonly<Partial<DynamicSamplingContext>> {\n  const client = getClient();\n  if (!client) {\n    return {};\n  }\n\n  const rootSpan = getRootSpan(span);\n  const rootSpanJson = spanToJSON(rootSpan);\n  const rootSpanAttributes = rootSpanJson.data;\n  const traceState = rootSpan.spanContext().traceState;\n\n  // The span sample rate that was locally applied to the root span should also always be applied to the DSC, even if the DSC is frozen.\n  // This is so that the downstream traces/services can use parentSampleRate in their `tracesSampler` to make consistent sampling decisions across the entire trace.\n  const rootSpanSampleRate =\n    traceState?.get('sentry.sample_rate') ??\n    rootSpanAttributes[SEMANTIC_ATTRIBUTE_SENTRY_SAMPLE_RATE] ??\n    rootSpanAttributes[SEMANTIC_ATTRIBUTE_SENTRY_PREVIOUS_TRACE_SAMPLE_RATE];\n\n  function applyLocalSampleRateToDsc(dsc: Partial<DynamicSamplingContext>): Partial<DynamicSamplingContext> {\n    if (typeof rootSpanSampleRate === 'number' || typeof rootSpanSampleRate === 'string') {\n      dsc.sample_rate = `${rootSpanSampleRate}`;\n    }\n    return dsc;\n  }\n\n  // For core implementation, we freeze the DSC onto the span as a non-enumerable property\n  const frozenDsc = (rootSpan as SpanWithMaybeDsc)[FROZEN_DSC_FIELD];\n  if (frozenDsc) {\n    return applyLocalSampleRateToDsc(frozenDsc);\n  }\n\n  // For OpenTelemetry, we freeze the DSC on the trace state\n  const traceStateDsc = traceState?.get('sentry.dsc');\n\n  // If the span has a DSC, we want it to take precedence\n  const dscOnTraceState = traceStateDsc && baggageHeaderToDynamicSamplingContext(traceStateDsc);\n\n  if (dscOnTraceState) {\n    return applyLocalSampleRateToDsc(dscOnTraceState);\n  }\n\n  // Else, we generate it from the span\n  const dsc = getDynamicSamplingContextFromClient(span.spanContext().traceId, client);\n\n  // We don't want to have a transaction name in the DSC if the source is \"url\" because URLs might contain PII\n  const source = rootSpanAttributes[SEMANTIC_ATTRIBUTE_SENTRY_SOURCE];\n\n  // after JSON conversion, txn.name becomes jsonSpan.description\n  const name = rootSpanJson.description;\n  if (source !== 'url' && name) {\n    dsc.transaction = name;\n  }\n\n  // How can we even land here with hasSpansEnabled() returning false?\n  // Otel creates a Non-recording span in Tracing Without Performance mode when handling incoming requests\n  // So we end up with an active span that is not sampled (neither positively nor negatively)\n  if (hasSpansEnabled()) {\n    dsc.sampled = String(spanIsSampled(rootSpan));\n    dsc.sample_rand =\n      // In OTEL we store the sample rand on the trace state because we cannot access scopes for NonRecordingSpans\n      // The Sentry OTEL SpanSampler takes care of writing the sample rand on the root span\n      traceState?.get('sentry.sample_rand') ??\n      // On all other platforms we can actually get the scopes from a root span (we use this as a fallback)\n      getCapturedScopesOnSpan(rootSpan).scope?.getPropagationContext().sampleRand.toString();\n  }\n\n  applyLocalSampleRateToDsc(dsc);\n\n  client.emit('createDsc', dsc, rootSpan);\n\n  return dsc;\n}\n\n/**\n * Convert a Span to a baggage header.\n */\nexport function spanToBaggageHeader(span: Span): string | undefined {\n  const dsc = getDynamicSamplingContextFromSpan(span);\n  return dynamicSamplingContextToSentryBaggageHeader(dsc);\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAqBA;;;CAGA,GACA,MAAM,gBAAA,GAAmB,YAAY;AAMrC;;CAEA,GACO,SAAS,eAAe,CAAC,IAAI,EAAQ,GAAG,EAAyC;IACtF,MAAM,gBAAiB,GAAE,IAAK;sPAC9B,2BAAA,AAAwB,EAAC,gBAAgB,EAAE,gBAAgB,EAAE,GAAG,CAAC;AACnE;AAEA;;;;CAIA,GACO,SAAS,mCAAmC,CAAC,QAAQ,EAAU,MAAM,EAAkC;IAC5G,MAAM,OAAQ,GAAE,MAAM,CAAC,UAAU,EAAE;IAEnC,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,IAAK,EAAA,GAAI,MAAM,CAAC,MAAM,EAAG,IAAG,CAAA,CAAE;IAE7D,IAAI,MAAM;IACV,IAAI,OAAO,CAAC,KAAK,EAAE;QACjB,SAAS,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;IAClC,CAAI,MAAK,IAAI,IAAI,EAAE;QACf,MAAO,kPAAE,0BAAA,AAAuB,EAAC,IAAI,CAAC;IAC1C;IAEA,mGAAA;IACA,wFAAA;IACE,MAAM,GAAG,GAA2B;QAClC,WAAW,EAAE,OAAO,CAAC,WAAA,IAAe,qPAAmB;QACvD,OAAO,EAAE,OAAO,CAAC,OAAO;QACxB,UAAU;QACV,QAAQ;QACR,MAAM;IACV,CAAG;IAED,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE,GAAG,CAAC;IAE7B,OAAO,GAAG;AACZ;AAEA;;CAEA,GACO,SAAS,kCAAkC,CAAC,MAAM,EAAU,KAAK,EAA0C;IAChH,MAAM,kBAAmB,GAAE,KAAK,CAAC,qBAAqB,EAAE;IACxD,OAAO,kBAAkB,CAAC,GAAA,IAAO,mCAAmC,CAAC,kBAAkB,CAAC,OAAO,EAAE,MAAM,CAAC;AAC1G;AAEA;;;;;;CAMA,GACO,SAAS,iCAAiC,CAAC,IAAI,EAAmD;IACvG,MAAM,MAAA,GAAS,mPAAA,AAAS,EAAE;IAC1B,IAAI,CAAC,MAAM,EAAE;QACX,OAAO,CAAA,CAAE;IACb;IAEE,MAAM,QAAS,+OAAE,cAAA,AAAW,EAAC,IAAI,CAAC;IAClC,MAAM,YAAa,IAAE,wPAAA,AAAU,EAAC,QAAQ,CAAC;IACzC,MAAM,kBAAA,GAAqB,YAAY,CAAC,IAAI;IAC5C,MAAM,aAAa,QAAQ,CAAC,WAAW,EAAE,CAAC,UAAU;IAEtD,sIAAA;IACA,kKAAA;IACE,MAAM,kBAAmB,GACvB,UAAU,EAAE,GAAG,CAAC,oBAAoB,CAAE,IACtC,kBAAkB,yOAAC,wCAAqC,CAAE,IAC1D,kBAAkB,yOAAC,uDAAoD,CAAC;IAE1E,SAAS,yBAAyB,CAAC,GAAG,EAAoE;QACxG,IAAI,OAAO,kBAAmB,KAAI,QAAS,IAAG,OAAO,kBAAA,KAAuB,QAAQ,EAAE;YACpF,GAAG,CAAC,WAAY,GAAE,CAAC,EAAA,kBAAA,CAAA,CAAA;QACA;QACA,OAAA,GAAA;IACA;IAEA,wFAAA;IACA,MAAA,SAAA,GAAA,QAAA,CAAA,gBAAA,CAAA;IACA,IAAA,SAAA,EAAA;QACA,OAAA,yBAAA,CAAA,SAAA,CAAA;IACA;IAEA,0DAAA;IACA,MAAA,aAAA,GAAA,UAAA,EAAA,GAAA,CAAA,YAAA,CAAA;IAEA,uDAAA;IACA,MAAA,eAAA,GAAA,aAAA,uPAAA,wCAAA,EAAA,aAAA,CAAA;IAEA,IAAA,eAAA,EAAA;QACA,OAAA,yBAAA,CAAA,eAAA,CAAA;IACA;IAEA,qCAAA;IACA,MAAA,GAAA,GAAA,mCAAA,CAAA,IAAA,CAAA,WAAA,EAAA,CAAA,OAAA,EAAA,MAAA,CAAA;IAEA,4GAAA;IACA,MAAA,MAAA,GAAA,kBAAA,wOAAA,oCAAA,CAAA;IAEA,+DAAA;IACA,MAAA,IAAA,GAAA,YAAA,CAAA,WAAA;IACA,IAAA,MAAA,KAAA,KAAA,IAAA,IAAA,EAAA;QACA,GAAA,CAAA,WAAA,GAAA,IAAA;IACA;IAEA,oEAAA;IACA,wGAAA;IACA,2FAAA;IACA,sPAAA,kBAAA,EAAA,GAAA;QACA,GAAA,CAAA,OAAA,GAAA,MAAA,EAAA,2PAAA,EAAA,QAAA,CAAA,CAAA;QACA,GAAA,CAAA,WAAA,GACA,4GAAA;QACA,qFAAA;QACA,UAAA,EAAA,GAAA,CAAA,oBAAA,CAAA,IACA,qGAAA;kPACA,0BAAA,EAAA,QAAA,CAAA,CAAA,KAAA,EAAA,qBAAA,EAAA,CAAA,UAAA,CAAA,QAAA,EAAA;IACA;IAEA,yBAAA,CAAA,GAAA,CAAA;IAEA,MAAA,CAAA,IAAA,CAAA,WAAA,EAAA,GAAA,EAAA,QAAA,CAAA;IAEA,OAAA,GAAA;AACA;AAEA;;CAEA,GACA,SAAA,mBAAA,CAAA,IAAA,EAAA;IACA,MAAA,GAAA,GAAA,iCAAA,CAAA,IAAA,CAAA;IACA,0PAAA,8CAAA,EAAA,GAAA,CAAA;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4127, "column": 0}, "map": {"version": 3, "file": "applyScopeDataToEvent.js", "sources": ["file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/%40sentry%2Bcore%409.22.0/node_modules/%40sentry/core/src/utils/applyScopeDataToEvent.ts"], "sourcesContent": ["import type { ScopeData } from '../scope';\nimport { getDynamicSamplingContextFromSpan } from '../tracing/dynamicSamplingContext';\nimport type { Breadcrumb } from '../types-hoist/breadcrumb';\nimport type { Event } from '../types-hoist/event';\nimport type { Span } from '../types-hoist/span';\nimport { merge } from './merge';\nimport { getRootSpan, spanToJSON, spanToTraceContext } from './spanUtils';\n\n/**\n * Applies data from the scope to the event and runs all event processors on it.\n */\nexport function applyScopeDataToEvent(event: Event, data: ScopeData): void {\n  const { fingerprint, span, breadcrumbs, sdkProcessingMetadata } = data;\n\n  // Apply general data\n  applyDataToEvent(event, data);\n\n  // We want to set the trace context for normal events only if there isn't already\n  // a trace context on the event. There is a product feature in place where we link\n  // errors with transaction and it relies on that.\n  if (span) {\n    applySpanToEvent(event, span);\n  }\n\n  applyFingerprintToEvent(event, fingerprint);\n  applyBreadcrumbsToEvent(event, breadcrumbs);\n  applySdkMetadataToEvent(event, sdkProcessingMetadata);\n}\n\n/** Merge data of two scopes together. */\nexport function mergeScopeData(data: ScopeData, mergeData: ScopeData): void {\n  const {\n    extra,\n    tags,\n    user,\n    contexts,\n    level,\n    sdkProcessingMetadata,\n    breadcrumbs,\n    fingerprint,\n    eventProcessors,\n    attachments,\n    propagationContext,\n    transactionName,\n    span,\n  } = mergeData;\n\n  mergeAndOverwriteScopeData(data, 'extra', extra);\n  mergeAndOverwriteScopeData(data, 'tags', tags);\n  mergeAndOverwriteScopeData(data, 'user', user);\n  mergeAndOverwriteScopeData(data, 'contexts', contexts);\n\n  data.sdkProcessingMetadata = merge(data.sdkProcessingMetadata, sdkProcessingMetadata, 2);\n\n  if (level) {\n    data.level = level;\n  }\n\n  if (transactionName) {\n    data.transactionName = transactionName;\n  }\n\n  if (span) {\n    data.span = span;\n  }\n\n  if (breadcrumbs.length) {\n    data.breadcrumbs = [...data.breadcrumbs, ...breadcrumbs];\n  }\n\n  if (fingerprint.length) {\n    data.fingerprint = [...data.fingerprint, ...fingerprint];\n  }\n\n  if (eventProcessors.length) {\n    data.eventProcessors = [...data.eventProcessors, ...eventProcessors];\n  }\n\n  if (attachments.length) {\n    data.attachments = [...data.attachments, ...attachments];\n  }\n\n  data.propagationContext = { ...data.propagationContext, ...propagationContext };\n}\n\n/**\n * Merges certain scope data. Undefined values will overwrite any existing values.\n * Exported only for tests.\n */\nexport function mergeAndOverwriteScopeData<\n  Prop extends 'extra' | 'tags' | 'user' | 'contexts' | 'sdkProcessingMetadata',\n  Data extends ScopeData,\n>(data: Data, prop: Prop, mergeVal: Data[Prop]): void {\n  data[prop] = merge(data[prop], mergeVal, 1);\n}\n\n/** Exported only for tests */\nexport function mergeArray<Prop extends 'breadcrumbs' | 'fingerprint'>(\n  event: Event,\n  prop: Prop,\n  mergeVal: ScopeData[Prop],\n): void {\n  const prevVal = event[prop];\n  // If we are not merging any new values,\n  // we only need to proceed if there was an empty array before (as we want to replace it with undefined)\n  if (!mergeVal.length && (!prevVal || prevVal.length)) {\n    return;\n  }\n\n  const merged = [...(prevVal || []), ...mergeVal] as ScopeData[Prop];\n  event[prop] = merged.length ? merged : undefined;\n}\n\nfunction applyDataToEvent(event: Event, data: ScopeData): void {\n  const { extra, tags, user, contexts, level, transactionName } = data;\n\n  if (Object.keys(extra).length) {\n    event.extra = { ...extra, ...event.extra };\n  }\n\n  if (Object.keys(tags).length) {\n    event.tags = { ...tags, ...event.tags };\n  }\n\n  if (Object.keys(user).length) {\n    event.user = { ...user, ...event.user };\n  }\n\n  if (Object.keys(contexts).length) {\n    event.contexts = { ...contexts, ...event.contexts };\n  }\n\n  if (level) {\n    event.level = level;\n  }\n\n  // transaction events get their `transaction` from the root span name\n  if (transactionName && event.type !== 'transaction') {\n    event.transaction = transactionName;\n  }\n}\n\nfunction applyBreadcrumbsToEvent(event: Event, breadcrumbs: Breadcrumb[]): void {\n  const mergedBreadcrumbs = [...(event.breadcrumbs || []), ...breadcrumbs];\n  event.breadcrumbs = mergedBreadcrumbs.length ? mergedBreadcrumbs : undefined;\n}\n\nfunction applySdkMetadataToEvent(event: Event, sdkProcessingMetadata: ScopeData['sdkProcessingMetadata']): void {\n  event.sdkProcessingMetadata = {\n    ...event.sdkProcessingMetadata,\n    ...sdkProcessingMetadata,\n  };\n}\n\nfunction applySpanToEvent(event: Event, span: Span): void {\n  event.contexts = {\n    trace: spanToTraceContext(span),\n    ...event.contexts,\n  };\n\n  event.sdkProcessingMetadata = {\n    dynamicSamplingContext: getDynamicSamplingContextFromSpan(span),\n    ...event.sdkProcessingMetadata,\n  };\n\n  const rootSpan = getRootSpan(span);\n  const transactionName = spanToJSON(rootSpan).description;\n  if (transactionName && !event.transaction && event.type === 'transaction') {\n    event.transaction = transactionName;\n  }\n}\n\n/**\n * Applies fingerprint from the scope to the event if there's one,\n * uses message if there's one instead or get rid of empty fingerprint\n */\nfunction applyFingerprintToEvent(event: Event, fingerprint: ScopeData['fingerprint'] | undefined): void {\n  // Make sure it's an array first and we actually have something in place\n  event.fingerprint = event.fingerprint\n    ? Array.isArray(event.fingerprint)\n      ? event.fingerprint\n      : [event.fingerprint]\n    : [];\n\n  // If we have something on the scope, then merge it with event\n  if (fingerprint) {\n    event.fingerprint = event.fingerprint.concat(fingerprint);\n  }\n\n  // If we have no data at all, remove empty array default\n  if (!event.fingerprint.length) {\n    delete event.fingerprint;\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAQA;;CAEA,GACO,SAAS,qBAAqB,CAAC,KAAK,EAAS,IAAI,EAAmB;IACzE,MAAM,EAAE,WAAW,EAAE,IAAI,EAAE,WAAW,EAAE,qBAAA,EAAwB,GAAE,IAAI;IAExE,qBAAA;IACE,gBAAgB,CAAC,KAAK,EAAE,IAAI,CAAC;IAE/B,iFAAA;IACA,kFAAA;IACA,iDAAA;IACE,IAAI,IAAI,EAAE;QACR,gBAAgB,CAAC,KAAK,EAAE,IAAI,CAAC;IACjC;IAEE,uBAAuB,CAAC,KAAK,EAAE,WAAW,CAAC;IAC3C,uBAAuB,CAAC,KAAK,EAAE,WAAW,CAAC;IAC3C,uBAAuB,CAAC,KAAK,EAAE,qBAAqB,CAAC;AACvD;AAEA,uCAAA,GACO,SAAS,cAAc,CAAC,IAAI,EAAa,SAAS,EAAmB;IAC1E,MAAM,EACJ,KAAK,EACL,IAAI,EACJ,IAAI,EACJ,QAAQ,EACR,KAAK,EACL,qBAAqB,EACrB,WAAW,EACX,WAAW,EACX,eAAe,EACf,WAAW,EACX,kBAAkB,EAClB,eAAe,EACf,IAAI,EACN,GAAI,SAAS;IAEb,0BAA0B,CAAC,IAAI,EAAE,OAAO,EAAE,KAAK,CAAC;IAChD,0BAA0B,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC;IAC9C,0BAA0B,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC;IAC9C,0BAA0B,CAAC,IAAI,EAAE,UAAU,EAAE,QAAQ,CAAC;IAEtD,IAAI,CAAC,qBAAsB,2OAAE,QAAA,AAAK,EAAC,IAAI,CAAC,qBAAqB,EAAE,qBAAqB,EAAE,CAAC,CAAC;IAExF,IAAI,KAAK,EAAE;QACT,IAAI,CAAC,KAAM,GAAE,KAAK;IACtB;IAEE,IAAI,eAAe,EAAE;QACnB,IAAI,CAAC,eAAgB,GAAE,eAAe;IAC1C;IAEE,IAAI,IAAI,EAAE;QACR,IAAI,CAAC,IAAK,GAAE,IAAI;IACpB;IAEE,IAAI,WAAW,CAAC,MAAM,EAAE;QACtB,IAAI,CAAC,WAAY,GAAE,CAAC;eAAG,IAAI,CAAC,WAAW,EAAE;eAAG,WAAW;SAAC;IAC5D;IAEE,IAAI,WAAW,CAAC,MAAM,EAAE;QACtB,IAAI,CAAC,WAAY,GAAE,CAAC;eAAG,IAAI,CAAC,WAAW,EAAE;eAAG,WAAW;SAAC;IAC5D;IAEE,IAAI,eAAe,CAAC,MAAM,EAAE;QAC1B,IAAI,CAAC,eAAgB,GAAE,CAAC;eAAG,IAAI,CAAC,eAAe,EAAE;eAAG,eAAe;SAAC;IACxE;IAEE,IAAI,WAAW,CAAC,MAAM,EAAE;QACtB,IAAI,CAAC,WAAY,GAAE,CAAC;eAAG,IAAI,CAAC,WAAW,EAAE;eAAG,WAAW;SAAC;IAC5D;IAEE,IAAI,CAAC,kBAAmB,GAAE;QAAE,GAAG,IAAI,CAAC,kBAAkB;QAAE,GAAG,kBAAA;IAAA,CAAoB;AACjF;AAEA;;;CAGA,GACO,SAAS,2BAGd,IAAI,EAAQ,IAAI,EAAQ,QAAQ,EAAoB;IACpD,IAAI,CAAC,IAAI,CAAA,OAAI,4OAAA,AAAK,EAAC,IAAI,CAAC,IAAI,CAAC,EAAE,QAAQ,EAAE,CAAC,CAAC;AAC7C;AAmBA,SAAS,gBAAgB,CAAC,KAAK,EAAS,IAAI,EAAmB;IAC7D,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,eAAgB,EAAA,GAAI,IAAI;IAEpE,IAAI,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,EAAE;QAC7B,KAAK,CAAC,KAAM,GAAE;YAAE,GAAG,KAAK;YAAE,GAAG,KAAK,CAAC,KAAA;QAAA,CAAO;IAC9C;IAEE,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE;QAC5B,KAAK,CAAC,IAAK,GAAE;YAAE,GAAG,IAAI;YAAE,GAAG,KAAK,CAAC,IAAA;QAAA,CAAM;IAC3C;IAEE,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE;QAC5B,KAAK,CAAC,IAAK,GAAE;YAAE,GAAG,IAAI;YAAE,GAAG,KAAK,CAAC,IAAA;QAAA,CAAM;IAC3C;IAEE,IAAI,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,MAAM,EAAE;QAChC,KAAK,CAAC,QAAS,GAAE;YAAE,GAAG,QAAQ;YAAE,GAAG,KAAK,CAAC,QAAA;QAAA,CAAU;IACvD;IAEE,IAAI,KAAK,EAAE;QACT,KAAK,CAAC,KAAM,GAAE,KAAK;IACvB;IAEA,qEAAA;IACE,IAAI,eAAgB,IAAG,KAAK,CAAC,IAAA,KAAS,aAAa,EAAE;QACnD,KAAK,CAAC,WAAY,GAAE,eAAe;IACvC;AACA;AAEA,SAAS,uBAAuB,CAAC,KAAK,EAAS,WAAW,EAAsB;IAC9E,MAAM,iBAAkB,GAAE,CAAC;WAAI,KAAK,CAAC,WAAY,IAAG,EAAE,CAAC,EAAE;WAAG,WAAW;KAAC;IACxE,KAAK,CAAC,WAAA,GAAc,iBAAiB,CAAC,MAAO,GAAE,iBAAkB,GAAE,SAAS;AAC9E;AAEA,SAAS,uBAAuB,CAAC,KAAK,EAAS,qBAAqB,EAA4C;IAC9G,KAAK,CAAC,qBAAA,GAAwB;QAC5B,GAAG,KAAK,CAAC,qBAAqB;QAC9B,GAAG,qBAAqB;IAC5B,CAAG;AACH;AAEA,SAAS,gBAAgB,CAAC,KAAK,EAAS,IAAI,EAAc;IACxD,KAAK,CAAC,QAAA,GAAW;QACf,KAAK,8OAAE,qBAAA,AAAkB,EAAC,IAAI,CAAC;QAC/B,GAAG,KAAK,CAAC,QAAQ;IACrB,CAAG;IAED,KAAK,CAAC,qBAAA,GAAwB;QAC5B,sBAAsB,GAAE,8RAAA,AAAiC,EAAC,IAAI,CAAC;QAC/D,GAAG,KAAK,CAAC,qBAAqB;IAClC,CAAG;IAED,MAAM,QAAS,+OAAE,cAAA,AAAW,EAAC,IAAI,CAAC;IAClC,MAAM,8PAAkB,aAAA,AAAU,EAAC,QAAQ,CAAC,CAAC,WAAW;IACxD,IAAI,eAAgB,IAAG,CAAC,KAAK,CAAC,WAAY,IAAG,KAAK,CAAC,IAAK,KAAI,aAAa,EAAE;QACzE,KAAK,CAAC,WAAY,GAAE,eAAe;IACvC;AACA;AAEA;;;CAGA,GACA,SAAS,uBAAuB,CAAC,KAAK,EAAS,WAAW,EAA8C;IACxG,wEAAA;IACE,KAAK,CAAC,WAAY,GAAE,KAAK,CAAC,WAAA,GACtB,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,WAAW,IAC7B,KAAK,CAAC,WAAA,GACN;QAAC,KAAK,CAAC,WAAW;KAAA,GACpB,EAAE;IAER,8DAAA;IACE,IAAI,WAAW,EAAE;QACf,KAAK,CAAC,WAAA,GAAc,KAAK,CAAC,WAAW,CAAC,MAAM,CAAC,WAAW,CAAC;IAC7D;IAEA,wDAAA;IACE,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,MAAM,EAAE;QAC7B,OAAO,KAAK,CAAC,WAAW;IAC5B;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4292, "column": 0}, "map": {"version": 3, "file": "prepareEvent.js", "sources": ["file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/%40sentry%2Bcore%409.22.0/node_modules/%40sentry/core/src/utils/prepareEvent.ts"], "sourcesContent": ["import type { Client } from '../client';\nimport { DEFAULT_ENVIRONMENT } from '../constants';\nimport { getGlobalScope } from '../currentScopes';\nimport { notifyEventProcessors } from '../eventProcessors';\nimport type { CaptureContext, ScopeContext } from '../scope';\nimport { Scope } from '../scope';\nimport type { Event, EventHint } from '../types-hoist/event';\nimport type { ClientOptions } from '../types-hoist/options';\nimport type { StackParser } from '../types-hoist/stacktrace';\nimport { getFilenameToDebugIdMap } from '../utils-hoist/debug-ids';\nimport { addExceptionMechanism, uuid4 } from '../utils-hoist/misc';\nimport { normalize } from '../utils-hoist/normalize';\nimport { truncate } from '../utils-hoist/string';\nimport { dateTimestampInSeconds } from '../utils-hoist/time';\nimport { applyScopeDataToEvent, mergeScopeData } from './applyScopeDataToEvent';\n\n/**\n * This type makes sure that we get either a CaptureContext, OR an EventHint.\n * It does not allow mixing them, which could lead to unexpected outcomes, e.g. this is disallowed:\n * { user: { id: '123' }, mechanism: { handled: false } }\n */\nexport type ExclusiveEventHintOrCaptureContext =\n  | (CaptureContext & Partial<{ [key in keyof EventHint]: never }>)\n  | (EventHint & Partial<{ [key in keyof ScopeContext]: never }>);\n\n/**\n * Adds common information to events.\n *\n * The information includes release and environment from `options`,\n * breadcrumbs and context (extra, tags and user) from the scope.\n *\n * Information that is already present in the event is never overwritten. For\n * nested objects, such as the context, keys are merged.\n *\n * @param event The original event.\n * @param hint May contain additional information about the original exception.\n * @param scope A scope containing event metadata.\n * @returns A new event with more information.\n * @hidden\n */\nexport function prepareEvent(\n  options: ClientOptions,\n  event: Event,\n  hint: EventHint,\n  scope?: Scope,\n  client?: Client,\n  isolationScope?: Scope,\n): PromiseLike<Event | null> {\n  const { normalizeDepth = 3, normalizeMaxBreadth = 1_000 } = options;\n  const prepared: Event = {\n    ...event,\n    event_id: event.event_id || hint.event_id || uuid4(),\n    timestamp: event.timestamp || dateTimestampInSeconds(),\n  };\n  const integrations = hint.integrations || options.integrations.map(i => i.name);\n\n  applyClientOptions(prepared, options);\n  applyIntegrationsMetadata(prepared, integrations);\n\n  if (client) {\n    client.emit('applyFrameMetadata', event);\n  }\n\n  // Only put debug IDs onto frames for error events.\n  if (event.type === undefined) {\n    applyDebugIds(prepared, options.stackParser);\n  }\n\n  // If we have scope given to us, use it as the base for further modifications.\n  // This allows us to prevent unnecessary copying of data if `captureContext` is not provided.\n  const finalScope = getFinalScope(scope, hint.captureContext);\n\n  if (hint.mechanism) {\n    addExceptionMechanism(prepared, hint.mechanism);\n  }\n\n  const clientEventProcessors = client ? client.getEventProcessors() : [];\n\n  // This should be the last thing called, since we want that\n  // {@link Scope.addEventProcessor} gets the finished prepared event.\n  // Merge scope data together\n  const data = getGlobalScope().getScopeData();\n\n  if (isolationScope) {\n    const isolationData = isolationScope.getScopeData();\n    mergeScopeData(data, isolationData);\n  }\n\n  if (finalScope) {\n    const finalScopeData = finalScope.getScopeData();\n    mergeScopeData(data, finalScopeData);\n  }\n\n  const attachments = [...(hint.attachments || []), ...data.attachments];\n  if (attachments.length) {\n    hint.attachments = attachments;\n  }\n\n  applyScopeDataToEvent(prepared, data);\n\n  const eventProcessors = [\n    ...clientEventProcessors,\n    // Run scope event processors _after_ all other processors\n    ...data.eventProcessors,\n  ];\n\n  const result = notifyEventProcessors(eventProcessors, prepared, hint);\n\n  return result.then(evt => {\n    if (evt) {\n      // We apply the debug_meta field only after all event processors have ran, so that if any event processors modified\n      // file names (e.g.the RewriteFrames integration) the filename -> debug ID relationship isn't destroyed.\n      // This should not cause any PII issues, since we're only moving data that is already on the event and not adding\n      // any new data\n      applyDebugMeta(evt);\n    }\n\n    if (typeof normalizeDepth === 'number' && normalizeDepth > 0) {\n      return normalizeEvent(evt, normalizeDepth, normalizeMaxBreadth);\n    }\n    return evt;\n  });\n}\n\n/**\n * Enhances event using the client configuration.\n * It takes care of all \"static\" values like environment, release and `dist`,\n * as well as truncating overly long values.\n *\n * Only exported for tests.\n *\n * @param event event instance to be enhanced\n */\nexport function applyClientOptions(event: Event, options: ClientOptions): void {\n  const { environment, release, dist, maxValueLength = 250 } = options;\n\n  // empty strings do not make sense for environment, release, and dist\n  // so we handle them the same as if they were not provided\n  event.environment = event.environment || environment || DEFAULT_ENVIRONMENT;\n\n  if (!event.release && release) {\n    event.release = release;\n  }\n\n  if (!event.dist && dist) {\n    event.dist = dist;\n  }\n\n  const request = event.request;\n  if (request?.url) {\n    request.url = truncate(request.url, maxValueLength);\n  }\n}\n\n/**\n * Puts debug IDs into the stack frames of an error event.\n */\nexport function applyDebugIds(event: Event, stackParser: StackParser): void {\n  // Build a map of filename -> debug_id\n  const filenameDebugIdMap = getFilenameToDebugIdMap(stackParser);\n\n  event.exception?.values?.forEach(exception => {\n    exception.stacktrace?.frames?.forEach(frame => {\n      if (frame.filename) {\n        frame.debug_id = filenameDebugIdMap[frame.filename];\n      }\n    });\n  });\n}\n\n/**\n * Moves debug IDs from the stack frames of an error event into the debug_meta field.\n */\nexport function applyDebugMeta(event: Event): void {\n  // Extract debug IDs and filenames from the stack frames on the event.\n  const filenameDebugIdMap: Record<string, string> = {};\n  event.exception?.values?.forEach(exception => {\n    exception.stacktrace?.frames?.forEach(frame => {\n      if (frame.debug_id) {\n        if (frame.abs_path) {\n          filenameDebugIdMap[frame.abs_path] = frame.debug_id;\n        } else if (frame.filename) {\n          filenameDebugIdMap[frame.filename] = frame.debug_id;\n        }\n        delete frame.debug_id;\n      }\n    });\n  });\n\n  if (Object.keys(filenameDebugIdMap).length === 0) {\n    return;\n  }\n\n  // Fill debug_meta information\n  event.debug_meta = event.debug_meta || {};\n  event.debug_meta.images = event.debug_meta.images || [];\n  const images = event.debug_meta.images;\n  Object.entries(filenameDebugIdMap).forEach(([filename, debug_id]) => {\n    images.push({\n      type: 'sourcemap',\n      code_file: filename,\n      debug_id,\n    });\n  });\n}\n\n/**\n * This function adds all used integrations to the SDK info in the event.\n * @param event The event that will be filled with all integrations.\n */\nfunction applyIntegrationsMetadata(event: Event, integrationNames: string[]): void {\n  if (integrationNames.length > 0) {\n    event.sdk = event.sdk || {};\n    event.sdk.integrations = [...(event.sdk.integrations || []), ...integrationNames];\n  }\n}\n\n/**\n * Applies `normalize` function on necessary `Event` attributes to make them safe for serialization.\n * Normalized keys:\n * - `breadcrumbs.data`\n * - `user`\n * - `contexts`\n * - `extra`\n * @param event Event\n * @returns Normalized event\n */\nfunction normalizeEvent(event: Event | null, depth: number, maxBreadth: number): Event | null {\n  if (!event) {\n    return null;\n  }\n\n  const normalized: Event = {\n    ...event,\n    ...(event.breadcrumbs && {\n      breadcrumbs: event.breadcrumbs.map(b => ({\n        ...b,\n        ...(b.data && {\n          data: normalize(b.data, depth, maxBreadth),\n        }),\n      })),\n    }),\n    ...(event.user && {\n      user: normalize(event.user, depth, maxBreadth),\n    }),\n    ...(event.contexts && {\n      contexts: normalize(event.contexts, depth, maxBreadth),\n    }),\n    ...(event.extra && {\n      extra: normalize(event.extra, depth, maxBreadth),\n    }),\n  };\n\n  // event.contexts.trace stores information about a Transaction. Similarly,\n  // event.spans[] stores information about child Spans. Given that a\n  // Transaction is conceptually a Span, normalization should apply to both\n  // Transactions and Spans consistently.\n  // For now the decision is to skip normalization of Transactions and Spans,\n  // so this block overwrites the normalized event to add back the original\n  // Transaction information prior to normalization.\n  if (event.contexts?.trace && normalized.contexts) {\n    normalized.contexts.trace = event.contexts.trace;\n\n    // event.contexts.trace.data may contain circular/dangerous data so we need to normalize it\n    if (event.contexts.trace.data) {\n      normalized.contexts.trace.data = normalize(event.contexts.trace.data, depth, maxBreadth);\n    }\n  }\n\n  // event.spans[].data may contain circular/dangerous data so we need to normalize it\n  if (event.spans) {\n    normalized.spans = event.spans.map(span => {\n      return {\n        ...span,\n        ...(span.data && {\n          data: normalize(span.data, depth, maxBreadth),\n        }),\n      };\n    });\n  }\n\n  // event.contexts.flags (FeatureFlagContext) stores context for our feature\n  // flag integrations. It has a greater nesting depth than our other typed\n  // Contexts, so we re-normalize with a fixed depth of 3 here. We do not want\n  // to skip this in case of conflicting, user-provided context.\n  if (event.contexts?.flags && normalized.contexts) {\n    normalized.contexts.flags = normalize(event.contexts.flags, 3, maxBreadth);\n  }\n\n  return normalized;\n}\n\nfunction getFinalScope(scope: Scope | undefined, captureContext: CaptureContext | undefined): Scope | undefined {\n  if (!captureContext) {\n    return scope;\n  }\n\n  const finalScope = scope ? scope.clone() : new Scope();\n  finalScope.update(captureContext);\n  return finalScope;\n}\n\n/**\n * Parse either an `EventHint` directly, or convert a `CaptureContext` to an `EventHint`.\n * This is used to allow to update method signatures that used to accept a `CaptureContext` but should now accept an `EventHint`.\n */\nexport function parseEventHintOrCaptureContext(\n  hint: ExclusiveEventHintOrCaptureContext | undefined,\n): EventHint | undefined {\n  if (!hint) {\n    return undefined;\n  }\n\n  // If you pass a Scope or `() => Scope` as CaptureContext, we just return this as captureContext\n  if (hintIsScopeOrFunction(hint)) {\n    return { captureContext: hint };\n  }\n\n  if (hintIsScopeContext(hint)) {\n    return {\n      captureContext: hint,\n    };\n  }\n\n  return hint;\n}\n\nfunction hintIsScopeOrFunction(hint: CaptureContext | EventHint): hint is Scope | ((scope: Scope) => Scope) {\n  return hint instanceof Scope || typeof hint === 'function';\n}\n\ntype ScopeContextProperty = keyof ScopeContext;\nconst captureContextKeys: readonly ScopeContextProperty[] = [\n  'user',\n  'level',\n  'extra',\n  'contexts',\n  'tags',\n  'fingerprint',\n  'propagationContext',\n] as const;\n\nfunction hintIsScopeContext(hint: Partial<ScopeContext> | EventHint): hint is Partial<ScopeContext> {\n  return Object.keys(hint).some(key => captureContextKeys.includes(key as ScopeContextProperty));\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAgBA;;;;CAIA,GAKA;;;;;;;;;;;;;;CAcA,GACO,SAAS,YAAY,CAC1B,OAAO,EACP,KAAK,EACL,IAAI,EACJ,KAAK,EACL,MAAM,EACN,cAAc;IAEd,MAAM,EAAE,cAAA,GAAiB,CAAC,EAAE,mBAAA,GAAsB,IAAA,EAAQ,GAAE,OAAO;IACnE,MAAM,QAAQ,GAAU;QACtB,GAAG,KAAK;QACR,QAAQ,EAAE,KAAK,CAAC,QAAS,IAAG,IAAI,CAAC,QAAS,IAAG,wPAAA,AAAK,EAAE;QACpD,SAAS,EAAE,KAAK,CAAC,SAAA,oPAAa,yBAAA,AAAsB,EAAE;IAC1D,CAAG;IACD,MAAM,YAAa,GAAE,IAAI,CAAC,YAAA,IAAgB,OAAO,CAAC,YAAY,CAAC,GAAG,CAAC,CAAA,IAAK,CAAC,CAAC,IAAI,CAAC;IAE/E,kBAAkB,CAAC,QAAQ,EAAE,OAAO,CAAC;IACrC,yBAAyB,CAAC,QAAQ,EAAE,YAAY,CAAC;IAEjD,IAAI,MAAM,EAAE;QACV,MAAM,CAAC,IAAI,CAAC,oBAAoB,EAAE,KAAK,CAAC;IAC5C;IAEA,mDAAA;IACE,IAAI,KAAK,CAAC,IAAK,KAAI,SAAS,EAAE;QAC5B,aAAa,CAAC,QAAQ,EAAE,OAAO,CAAC,WAAW,CAAC;IAChD;IAEA,8EAAA;IACA,6FAAA;IACE,MAAM,UAAW,GAAE,aAAa,CAAC,KAAK,EAAE,IAAI,CAAC,cAAc,CAAC;IAE5D,IAAI,IAAI,CAAC,SAAS,EAAE;wPAClB,wBAAA,AAAqB,EAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC;IACnD;IAEE,MAAM,qBAAA,GAAwB,MAAA,GAAS,MAAM,CAAC,kBAAkB,EAAC,GAAI,EAAE;IAEzE,2DAAA;IACA,oEAAA;IACA,4BAAA;IACE,MAAM,6OAAO,kBAAA,AAAc,EAAE,EAAC,YAAY,EAAE;IAE5C,IAAI,cAAc,EAAE;QAClB,MAAM,aAAc,GAAE,cAAc,CAAC,YAAY,EAAE;SACnD,wQAAA,AAAc,EAAC,IAAI,EAAE,aAAa,CAAC;IACvC;IAEE,IAAI,UAAU,EAAE;QACd,MAAM,cAAe,GAAE,UAAU,CAAC,YAAY,EAAE;gQAChD,iBAAA,AAAc,EAAC,IAAI,EAAE,cAAc,CAAC;IACxC;IAEE,MAAM,WAAY,GAAE,CAAC;WAAI,IAAI,CAAC,WAAA,IAAe,EAAE,CAAC,EAAE;WAAG,IAAI,CAAC,WAAW;KAAC;IACtE,IAAI,WAAW,CAAC,MAAM,EAAE;QACtB,IAAI,CAAC,WAAY,GAAE,WAAW;IAClC;4PAEE,wBAAA,AAAqB,EAAC,QAAQ,EAAE,IAAI,CAAC;IAErC,MAAM,kBAAkB;WACnB,qBAAqB;QAC5B,0DAAA;WACO,IAAI,CAAC,eAAe;KACxB;IAED,MAAM,MAAO,4OAAE,wBAAA,AAAqB,EAAC,eAAe,EAAE,QAAQ,EAAE,IAAI,CAAC;IAErE,OAAO,MAAM,CAAC,IAAI,EAAC,OAAO;QACxB,IAAI,GAAG,EAAE;YACb,mHAAA;YACA,wGAAA;YACA,iHAAA;YACA,eAAA;YACM,cAAc,CAAC,GAAG,CAAC;QACzB;QAEI,IAAI,OAAO,cAAe,KAAI,YAAY,cAAA,GAAiB,CAAC,EAAE;YAC5D,OAAO,cAAc,CAAC,GAAG,EAAE,cAAc,EAAE,mBAAmB,CAAC;QACrE;QACI,OAAO,GAAG;IACd,CAAG,CAAC;AACJ;AAEA;;;;;;;;CAQA,GACO,SAAS,kBAAkB,CAAC,KAAK,EAAS,OAAO,EAAuB;IAC7E,MAAM,EAAE,WAAW,EAAE,OAAO,EAAE,IAAI,EAAE,cAAe,GAAE,GAAI,EAAA,GAAI,OAAO;IAEtE,qEAAA;IACA,0DAAA;IACE,KAAK,CAAC,WAAA,GAAc,KAAK,CAAC,WAAY,IAAG,WAAY,mOAAG,sBAAmB;IAE3E,IAAI,CAAC,KAAK,CAAC,OAAQ,IAAG,OAAO,EAAE;QAC7B,KAAK,CAAC,OAAQ,GAAE,OAAO;IAC3B;IAEE,IAAI,CAAC,KAAK,CAAC,IAAK,IAAG,IAAI,EAAE;QACvB,KAAK,CAAC,IAAK,GAAE,IAAI;IACrB;IAEE,MAAM,OAAA,GAAU,KAAK,CAAC,OAAO;IAC7B,IAAI,OAAO,EAAE,GAAG,EAAE;QAChB,OAAO,CAAC,GAAA,qPAAM,WAAA,AAAQ,EAAC,OAAO,CAAC,GAAG,EAAE,cAAc,CAAC;IACvD;AACA;AAEA;;CAEA,GACO,SAAS,aAAa,CAAC,KAAK,EAAS,WAAW,EAAqB;IAC5E,sCAAA;IACE,MAAM,kBAAmB,GAAE,kRAAA,AAAuB,EAAC,WAAW,CAAC;IAE/D,KAAK,CAAC,SAAS,EAAE,MAAM,EAAE,OAAO,EAAC,SAAA,IAAa;QAC5C,SAAS,CAAC,UAAU,EAAE,MAAM,EAAE,OAAO,EAAC,KAAA,IAAS;YAC7C,IAAI,KAAK,CAAC,QAAQ,EAAE;gBAClB,KAAK,CAAC,QAAS,GAAE,kBAAkB,CAAC,KAAK,CAAC,QAAQ,CAAC;YAC3D;QACA,CAAK,CAAC;IACN,CAAG,CAAC;AACJ;AAEA;;CAEA,GACO,SAAS,cAAc,CAAC,KAAK,EAAe;IACnD,sEAAA;IACE,MAAM,kBAAkB,GAA2B,CAAA,CAAE;IACrD,KAAK,CAAC,SAAS,EAAE,MAAM,EAAE,OAAO,EAAC,SAAA,IAAa;QAC5C,SAAS,CAAC,UAAU,EAAE,MAAM,EAAE,OAAO,CAAC,KAAA,IAAS;YAC7C,IAAI,KAAK,CAAC,QAAQ,EAAE;gBAClB,IAAI,KAAK,CAAC,QAAQ,EAAE;oBAClB,kBAAkB,CAAC,KAAK,CAAC,QAAQ,CAAE,GAAE,KAAK,CAAC,QAAQ;gBAC7D,OAAe,IAAI,KAAK,CAAC,QAAQ,EAAE;oBACzB,kBAAkB,CAAC,KAAK,CAAC,QAAQ,CAAE,GAAE,KAAK,CAAC,QAAQ;gBAC7D;gBACQ,OAAO,KAAK,CAAC,QAAQ;YAC7B;QACA,CAAK,CAAC;IACN,CAAG,CAAC;IAEF,IAAI,MAAM,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,MAAA,KAAW,CAAC,EAAE;QAChD;IACJ;IAEA,8BAAA;IACE,KAAK,CAAC,UAAW,GAAE,KAAK,CAAC,UAAA,IAAc,CAAA,CAAE;IACzC,KAAK,CAAC,UAAU,CAAC,MAAO,GAAE,KAAK,CAAC,UAAU,CAAC,MAAO,IAAG,EAAE;IACvD,MAAM,MAAO,GAAE,KAAK,CAAC,UAAU,CAAC,MAAM;IACtC,MAAM,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,QAAQ,EAAE,QAAQ,CAAC,KAAK;QACnE,MAAM,CAAC,IAAI,CAAC;YACV,IAAI,EAAE,WAAW;YACjB,SAAS,EAAE,QAAQ;YACnB,QAAQ;QACd,CAAK,CAAC;IACN,CAAG,CAAC;AACJ;AAEA;;;CAGA,GACA,SAAS,yBAAyB,CAAC,KAAK,EAAS,gBAAgB,EAAkB;IACjF,IAAI,gBAAgB,CAAC,MAAO,GAAE,CAAC,EAAE;QAC/B,KAAK,CAAC,GAAI,GAAE,KAAK,CAAC,GAAA,IAAO,CAAA,CAAE;QAC3B,KAAK,CAAC,GAAG,CAAC,YAAA,GAAe,CAAC;eAAI,KAAK,CAAC,GAAG,CAAC,YAAA,IAAgB,EAAE,CAAC,EAAE;eAAG,gBAAgB;SAAC;IACrF;AACA;AAEA;;;;;;;;;CASA,GACA,SAAS,cAAc,CAAC,KAAK,EAAgB,KAAK,EAAU,UAAU,EAAwB;IAC5F,IAAI,CAAC,KAAK,EAAE;QACV,OAAO,IAAI;IACf;IAEE,MAAM,UAAU,GAAU;QACxB,GAAG,KAAK;QACR,GAAI,KAAK,CAAC,WAAA,IAAe;YACvB,WAAW,EAAE,KAAK,CAAC,WAAW,CAAC,GAAG,CAAC,CAAE,IAAA,CAAI;oBACvC,GAAG,CAAC;oBACJ,GAAI,CAAC,CAAC,IAAA,IAAQ;wBACZ,IAAI,uPAAE,YAAA,AAAS,EAAC,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,UAAU,CAAC;oBACpD,CAAS,CAAC;gBACV,CAAO,CAAC,CAAC;QACT,CAAK,CAAC;QACF,GAAI,KAAK,CAAC,IAAA,IAAQ;YAChB,IAAI,uPAAE,YAAA,AAAS,EAAC,KAAK,CAAC,IAAI,EAAE,KAAK,EAAE,UAAU,CAAC;QACpD,CAAK,CAAC;QACF,GAAI,KAAK,CAAC,QAAA,IAAY;YACpB,QAAQ,GAAE,gQAAA,AAAS,EAAC,KAAK,CAAC,QAAQ,EAAE,KAAK,EAAE,UAAU,CAAC;QAC5D,CAAK,CAAC;QACF,GAAI,KAAK,CAAC,KAAA,IAAS;YACjB,KAAK,uPAAE,YAAA,AAAS,EAAC,KAAK,CAAC,KAAK,EAAE,KAAK,EAAE,UAAU,CAAC;QACtD,CAAK,CAAC;IACN,CAAG;IAEH,0EAAA;IACA,mEAAA;IACA,yEAAA;IACA,uCAAA;IACA,2EAAA;IACA,yEAAA;IACA,kDAAA;IACE,IAAI,KAAK,CAAC,QAAQ,EAAE,KAAA,IAAS,UAAU,CAAC,QAAQ,EAAE;QAChD,UAAU,CAAC,QAAQ,CAAC,KAAA,GAAQ,KAAK,CAAC,QAAQ,CAAC,KAAK;QAEpD,2FAAA;QACI,IAAI,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE;YAC7B,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAA,wPAAO,YAAA,AAAS,EAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,EAAE,UAAU,CAAC;QAC9F;IACA;IAEA,oFAAA;IACE,IAAI,KAAK,CAAC,KAAK,EAAE;QACf,UAAU,CAAC,KAAA,GAAQ,KAAK,CAAC,KAAK,CAAC,GAAG,EAAC,IAAA,IAAQ;YACzC,OAAO;gBACL,GAAG,IAAI;gBACP,GAAI,IAAI,CAAC,IAAA,IAAQ;oBACf,IAAI,uPAAE,YAAA,AAAS,EAAC,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,UAAU,CAAC;gBACvD,CAAS,CAAC;YACV,CAAO;QACP,CAAK,CAAC;IACN;IAEA,2EAAA;IACA,yEAAA;IACA,4EAAA;IACA,8DAAA;IACE,IAAI,KAAK,CAAC,QAAQ,EAAE,KAAA,IAAS,UAAU,CAAC,QAAQ,EAAE;QAChD,UAAU,CAAC,QAAQ,CAAC,KAAA,wPAAQ,YAAA,AAAS,EAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC,EAAE,UAAU,CAAC;IAC9E;IAEE,OAAO,UAAU;AACnB;AAEA,SAAS,aAAa,CAAC,KAAK,EAAqB,cAAc,EAAiD;IAC9G,IAAI,CAAC,cAAc,EAAE;QACnB,OAAO,KAAK;IAChB;IAEE,MAAM,UAAA,GAAa,KAAA,GAAQ,KAAK,CAAC,KAAK,EAAG,GAAE,+NAAI,QAAK,EAAE;IACtD,UAAU,CAAC,MAAM,CAAC,cAAc,CAAC;IACjC,OAAO,UAAU;AACnB;AAEA;;;CAGA,GACO,SAAS,8BAA8B,CAC5C,IAAI;IAEJ,IAAI,CAAC,IAAI,EAAE;QACT,OAAO,SAAS;IACpB;IAEA,gGAAA;IACE,IAAI,qBAAqB,CAAC,IAAI,CAAC,EAAE;QAC/B,OAAO;YAAE,cAAc,EAAE;QAAA,CAAM;IACnC;IAEE,IAAI,kBAAkB,CAAC,IAAI,CAAC,EAAE;QAC5B,OAAO;YACL,cAAc,EAAE,IAAI;QAC1B,CAAK;IACL;IAEE,OAAO,IAAI;AACb;AAEA,SAAS,qBAAqB,CAAC,IAAI,EAAyE;IAC1G,OAAO,0OAAgB,SAAA,IAAS,OAAO,IAAA,KAAS,UAAU;AAC5D;AAGA,MAAM,kBAAkB,GAAoC;IAC1D,MAAM;IACN,OAAO;IACP,OAAO;IACP,UAAU;IACV,MAAM;IACN,aAAa;IACb,oBAAoB;CACpB;AAEF,SAAS,kBAAkB,CAAC,IAAI,EAAoE;IAClG,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,EAAC,GAAA,GAAO,kBAAkB,CAAC,QAAQ,CAAC,GAAA,EAA4B,CAAC;AAChG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4600, "column": 0}, "map": {"version": 3, "file": "exports.js", "sources": ["file:///C:/Users/<USER>/Documents/Cubentweb/cubent/node_modules/.pnpm/%40sentry%2Bcore%409.22.0/node_modules/%40sentry/core/src/exports.ts"], "sourcesContent": ["import { getClient, getCurrentScope, getIsolationScope, withIsolationScope } from './currentScopes';\nimport { DEBUG_BUILD } from './debug-build';\nimport type { CaptureContext } from './scope';\nimport { closeSession, makeSession, updateSession } from './session';\nimport type { CheckIn, FinishedCheckIn, MonitorConfig } from './types-hoist/checkin';\nimport type { Event, EventHint } from './types-hoist/event';\nimport type { EventProcessor } from './types-hoist/eventprocessor';\nimport type { Extra, Extras } from './types-hoist/extra';\nimport type { Primitive } from './types-hoist/misc';\nimport type { Session, SessionContext } from './types-hoist/session';\nimport type { SeverityLevel } from './types-hoist/severity';\nimport type { User } from './types-hoist/user';\nimport type { ExclusiveEventHintOrCaptureContext } from './utils/prepareEvent';\nimport { parseEventHintOrCaptureContext } from './utils/prepareEvent';\nimport { isThenable } from './utils-hoist/is';\nimport { logger } from './utils-hoist/logger';\nimport { uuid4 } from './utils-hoist/misc';\nimport { timestampInSeconds } from './utils-hoist/time';\nimport { GLOBAL_OBJ } from './utils-hoist/worldwide';\n\n/**\n * Captures an exception event and sends it to Sentry.\n *\n * @param exception The exception to capture.\n * @param hint Optional additional data to attach to the Sentry event.\n * @returns the id of the captured Sentry event.\n */\nexport function captureException(exception: unknown, hint?: ExclusiveEventHintOrCaptureContext): string {\n  return getCurrentScope().captureException(exception, parseEventHintOrCaptureContext(hint));\n}\n\n/**\n * Captures a message event and sends it to Sentry.\n *\n * @param message The message to send to Sentry.\n * @param captureContext Define the level of the message or pass in additional data to attach to the message.\n * @returns the id of the captured message.\n */\nexport function captureMessage(message: string, captureContext?: CaptureContext | SeverityLevel): string {\n  // This is necessary to provide explicit scopes upgrade, without changing the original\n  // arity of the `captureMessage(message, level)` method.\n  const level = typeof captureContext === 'string' ? captureContext : undefined;\n  const context = typeof captureContext !== 'string' ? { captureContext } : undefined;\n  return getCurrentScope().captureMessage(message, level, context);\n}\n\n/**\n * Captures a manually created event and sends it to Sentry.\n *\n * @param event The event to send to Sentry.\n * @param hint Optional additional data to attach to the Sentry event.\n * @returns the id of the captured event.\n */\nexport function captureEvent(event: Event, hint?: EventHint): string {\n  return getCurrentScope().captureEvent(event, hint);\n}\n\n/**\n * Sets context data with the given name.\n * @param name of the context\n * @param context Any kind of data. This data will be normalized.\n */\nexport function setContext(name: string, context: { [key: string]: unknown } | null): void {\n  getIsolationScope().setContext(name, context);\n}\n\n/**\n * Set an object that will be merged sent as extra data with the event.\n * @param extras Extras object to merge into current context.\n */\nexport function setExtras(extras: Extras): void {\n  getIsolationScope().setExtras(extras);\n}\n\n/**\n * Set key:value that will be sent as extra data with the event.\n * @param key String of extra\n * @param extra Any kind of data. This data will be normalized.\n */\nexport function setExtra(key: string, extra: Extra): void {\n  getIsolationScope().setExtra(key, extra);\n}\n\n/**\n * Set an object that will be merged sent as tags data with the event.\n * @param tags Tags context object to merge into current context.\n */\nexport function setTags(tags: { [key: string]: Primitive }): void {\n  getIsolationScope().setTags(tags);\n}\n\n/**\n * Set key:value that will be sent as tags data with the event.\n *\n * Can also be used to unset a tag, by passing `undefined`.\n *\n * @param key String key of tag\n * @param value Value of tag\n */\nexport function setTag(key: string, value: Primitive): void {\n  getIsolationScope().setTag(key, value);\n}\n\n/**\n * Updates user context information for future events.\n *\n * @param user User context object to be set in the current context. Pass `null` to unset the user.\n */\nexport function setUser(user: User | null): void {\n  getIsolationScope().setUser(user);\n}\n\n/**\n * The last error event id of the isolation scope.\n *\n * Warning: This function really returns the last recorded error event id on the current\n * isolation scope. If you call this function after handling a certain error and another error\n * is captured in between, the last one is returned instead of the one you might expect.\n * Also, ids of events that were never sent to Sentry (for example because\n * they were dropped in `beforeSend`) could be returned.\n *\n * @returns The last event id of the isolation scope.\n */\nexport function lastEventId(): string | undefined {\n  return getIsolationScope().lastEventId();\n}\n\n/**\n * Create a cron monitor check in and send it to Sentry.\n *\n * @param checkIn An object that describes a check in.\n * @param upsertMonitorConfig An optional object that describes a monitor config. Use this if you want\n * to create a monitor automatically when sending a check in.\n */\nexport function captureCheckIn(checkIn: CheckIn, upsertMonitorConfig?: MonitorConfig): string {\n  const scope = getCurrentScope();\n  const client = getClient();\n  if (!client) {\n    DEBUG_BUILD && logger.warn('Cannot capture check-in. No client defined.');\n  } else if (!client.captureCheckIn) {\n    DEBUG_BUILD && logger.warn('Cannot capture check-in. Client does not support sending check-ins.');\n  } else {\n    return client.captureCheckIn(checkIn, upsertMonitorConfig, scope);\n  }\n\n  return uuid4();\n}\n\n/**\n * Wraps a callback with a cron monitor check in. The check in will be sent to Sentry when the callback finishes.\n *\n * @param monitorSlug The distinct slug of the monitor.\n * @param upsertMonitorConfig An optional object that describes a monitor config. Use this if you want\n * to create a monitor automatically when sending a check in.\n */\nexport function withMonitor<T>(\n  monitorSlug: CheckIn['monitorSlug'],\n  callback: () => T,\n  upsertMonitorConfig?: MonitorConfig,\n): T {\n  const checkInId = captureCheckIn({ monitorSlug, status: 'in_progress' }, upsertMonitorConfig);\n  const now = timestampInSeconds();\n\n  function finishCheckIn(status: FinishedCheckIn['status']): void {\n    captureCheckIn({ monitorSlug, status, checkInId, duration: timestampInSeconds() - now });\n  }\n\n  return withIsolationScope(() => {\n    let maybePromiseResult: T;\n    try {\n      maybePromiseResult = callback();\n    } catch (e) {\n      finishCheckIn('error');\n      throw e;\n    }\n\n    if (isThenable(maybePromiseResult)) {\n      Promise.resolve(maybePromiseResult).then(\n        () => {\n          finishCheckIn('ok');\n        },\n        e => {\n          finishCheckIn('error');\n          throw e;\n        },\n      );\n    } else {\n      finishCheckIn('ok');\n    }\n\n    return maybePromiseResult;\n  });\n}\n\n/**\n * Call `flush()` on the current client, if there is one. See {@link Client.flush}.\n *\n * @param timeout Maximum time in ms the client should wait to flush its event queue. Omitting this parameter will cause\n * the client to wait until all events are sent before resolving the promise.\n * @returns A promise which resolves to `true` if the queue successfully drains before the timeout, or `false` if it\n * doesn't (or if there's no client defined).\n */\nexport async function flush(timeout?: number): Promise<boolean> {\n  const client = getClient();\n  if (client) {\n    return client.flush(timeout);\n  }\n  DEBUG_BUILD && logger.warn('Cannot flush events. No client defined.');\n  return Promise.resolve(false);\n}\n\n/**\n * Call `close()` on the current client, if there is one. See {@link Client.close}.\n *\n * @param timeout Maximum time in ms the client should wait to flush its event queue before shutting down. Omitting this\n * parameter will cause the client to wait until all events are sent before disabling itself.\n * @returns A promise which resolves to `true` if the queue successfully drains before the timeout, or `false` if it\n * doesn't (or if there's no client defined).\n */\nexport async function close(timeout?: number): Promise<boolean> {\n  const client = getClient();\n  if (client) {\n    return client.close(timeout);\n  }\n  DEBUG_BUILD && logger.warn('Cannot flush events and disable SDK. No client defined.');\n  return Promise.resolve(false);\n}\n\n/**\n * Returns true if Sentry has been properly initialized.\n */\nexport function isInitialized(): boolean {\n  return !!getClient();\n}\n\n/** If the SDK is initialized & enabled. */\nexport function isEnabled(): boolean {\n  const client = getClient();\n  return client?.getOptions().enabled !== false && !!client?.getTransport();\n}\n\n/**\n * Add an event processor.\n * This will be added to the current isolation scope, ensuring any event that is processed in the current execution\n * context will have the processor applied.\n */\nexport function addEventProcessor(callback: EventProcessor): void {\n  getIsolationScope().addEventProcessor(callback);\n}\n\n/**\n * Start a session on the current isolation scope.\n *\n * @param context (optional) additional properties to be applied to the returned session object\n *\n * @returns the new active session\n */\nexport function startSession(context?: SessionContext): Session {\n  const isolationScope = getIsolationScope();\n  const currentScope = getCurrentScope();\n\n  // Will fetch userAgent if called from browser sdk\n  const { userAgent } = GLOBAL_OBJ.navigator || {};\n\n  const session = makeSession({\n    user: currentScope.getUser() || isolationScope.getUser(),\n    ...(userAgent && { userAgent }),\n    ...context,\n  });\n\n  // End existing session if there's one\n  const currentSession = isolationScope.getSession();\n  if (currentSession?.status === 'ok') {\n    updateSession(currentSession, { status: 'exited' });\n  }\n\n  endSession();\n\n  // Afterwards we set the new session on the scope\n  isolationScope.setSession(session);\n\n  return session;\n}\n\n/**\n * End the session on the current isolation scope.\n */\nexport function endSession(): void {\n  const isolationScope = getIsolationScope();\n  const currentScope = getCurrentScope();\n\n  const session = currentScope.getSession() || isolationScope.getSession();\n  if (session) {\n    closeSession(session);\n  }\n  _sendSessionUpdate();\n\n  // the session is over; take it off of the scope\n  isolationScope.setSession();\n}\n\n/**\n * Sends the current Session on the scope\n */\nfunction _sendSessionUpdate(): void {\n  const isolationScope = getIsolationScope();\n  const client = getClient();\n  const session = isolationScope.getSession();\n  if (session && client) {\n    client.captureSession(session);\n  }\n}\n\n/**\n * Sends the current session on the scope to Sentry\n *\n * @param end If set the session will be marked as exited and removed from the scope.\n *            Defaults to `false`.\n */\nexport function captureSession(end: boolean = false): void {\n  // both send the update and pull the session from the scope\n  if (end) {\n    endSession();\n    return;\n  }\n\n  // only send the update\n  _sendSessionUpdate();\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoBA;;;;;;CAMA,GACO,SAAS,gBAAgB,CAAC,SAAS,EAAW,IAAI,EAA+C;IACtG,8OAAO,kBAAA,AAAe,EAAE,EAAC,gBAAgB,CAAC,SAAS,iPAAE,iCAAA,AAA8B,EAAC,IAAI,CAAC,CAAC;AAC5F;AAEA;;;;;;CAMA,GACO,SAAS,cAAc,CAAC,OAAO,EAAU,cAAc,EAA2C;IACzG,sFAAA;IACA,wDAAA;IACE,MAAM,KAAM,GAAE,OAAO,cAAA,KAAmB,QAAS,GAAE,cAAe,GAAE,SAAS;IAC7E,MAAM,OAAA,GAAU,OAAO,cAAe,KAAI,QAAS,GAAE;QAAE,cAAA;IAAA,CAAiB,GAAE,SAAS;IACnF,6OAAO,mBAAA,AAAe,EAAE,EAAC,cAAc,CAAC,OAAO,EAAE,KAAK,EAAE,OAAO,CAAC;AAClE;AAEA;;;;;;CAMA,GACO,SAAS,YAAY,CAAC,KAAK,EAAS,IAAI,EAAsB;IACnE,8OAAO,kBAAA,AAAe,EAAE,EAAC,YAAY,CAAC,KAAK,EAAE,IAAI,CAAC;AACpD;AAEA;;;;CAIA,GACO,SAAS,UAAU,CAAC,IAAI,EAAU,OAAO,EAA2C;2OACzF,oBAAA,AAAiB,EAAE,EAAC,UAAU,CAAC,IAAI,EAAE,OAAO,CAAC;AAC/C;AAEA;;;CAGA,GACO,SAAS,SAAS,CAAC,MAAM,EAAgB;QAC9C,uPAAA,AAAiB,EAAE,EAAC,SAAS,CAAC,MAAM,CAAC;AACvC;AAEA;;;;CAIA,GACO,SAAS,QAAQ,CAAC,GAAG,EAAU,KAAK,EAAe;2OACxD,oBAAA,AAAiB,EAAE,EAAC,QAAQ,CAAC,GAAG,EAAE,KAAK,CAAC;AAC1C;AAEA;;;CAGA,GACO,SAAS,OAAO,CAAC,IAAI,EAAsC;2OAChE,oBAAA,AAAiB,EAAE,EAAC,OAAO,CAAC,IAAI,CAAC;AACnC;AAEA;;;;;;;CAOA,GACO,SAAS,MAAM,CAAC,GAAG,EAAU,KAAK,EAAmB;QAC1D,uPAAA,AAAiB,EAAE,EAAC,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC;AACxC;AAEA;;;;CAIA,GACO,SAAS,OAAO,CAAC,IAAI,EAAqB;2OAC/C,oBAAA,AAAiB,EAAE,EAAC,OAAO,CAAC,IAAI,CAAC;AACnC;AAEA;;;;;;;;;;CAUA,GACO,SAAS,WAAW,GAAuB;IAChD,8OAAO,oBAAA,AAAiB,EAAE,EAAC,WAAW,EAAE;AAC1C;AAEA;;;;;;CAMA,GACO,SAAS,cAAc,CAAC,OAAO,EAAW,mBAAmB,EAA0B;IAC5F,MAAM,KAAA,GAAQ,yPAAA,AAAe,EAAE;IAC/B,MAAM,MAAA,0OAAS,YAAA,AAAS,EAAE;IAC1B,IAAI,CAAC,MAAM,EAAE;4OACX,cAAA,kPAAe,SAAM,CAAC,IAAI,CAAC,6CAA6C,CAAC;IAC7E,CAAE,MAAO,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE;4OACjC,cAAA,kPAAe,SAAM,CAAC,IAAI,CAAC,qEAAqE,CAAC;IACrG,OAAS;QACL,OAAO,MAAM,CAAC,cAAc,CAAC,OAAO,EAAE,mBAAmB,EAAE,KAAK,CAAC;IACrE;IAEE,uPAAO,QAAA,AAAK,EAAE;AAChB;AAEA;;;;;;CAMA,GACO,SAAS,WAAW,CACzB,WAAW,EACX,QAAQ,EACR,mBAAmB;IAEnB,MAAM,SAAA,GAAY,cAAc,CAAC;QAAE,WAAW;QAAE,MAAM,EAAE,aAAA;IAAA,CAAe,EAAE,mBAAmB,CAAC;IAC7F,MAAM,GAAA,mPAAM,qBAAA,AAAkB,EAAE;IAEhC,SAAS,aAAa,CAAC,MAAM,EAAmC;QAC9D,cAAc,CAAC;YAAE,WAAW;YAAE,MAAM;YAAE,SAAS;YAAE,QAAQ,kPAAE,qBAAA,AAAkB,MAAK,GAAA;QAAA,CAAK,CAAC;IAC5F;IAEE,8OAAO,qBAAA,AAAkB,EAAC,MAAM;QAC9B,IAAI,kBAAkB;QACtB,IAAI;YACF,kBAAmB,GAAE,QAAQ,EAAE;QACrC,CAAM,CAAA,OAAO,CAAC,EAAE;YACV,aAAa,CAAC,OAAO,CAAC;YACtB,MAAM,CAAC;QACb;QAEI,KAAI,0PAAA,AAAU,EAAC,kBAAkB,CAAC,EAAE;YAClC,OAAO,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC,IAAI,CACtC,MAAM;gBACJ,aAAa,CAAC,IAAI,CAAC;YAC7B,CAAS,GACD,KAAK;gBACH,aAAa,CAAC,OAAO,CAAC;gBACtB,MAAM,CAAC;YACjB,CAAS;QAET,OAAW;YACL,aAAa,CAAC,IAAI,CAAC;QACzB;QAEI,OAAO,kBAAkB;IAC7B,CAAG,CAAC;AACJ;AAEA;;;;;;;CAOA,GACO,eAAe,KAAK,CAAC,OAAO,EAA6B;IAC9D,MAAM,MAAA,IAAS,kPAAA,AAAS,EAAE;IAC1B,IAAI,MAAM,EAAE;QACV,OAAO,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC;IAChC;wOACE,cAAA,kPAAe,SAAM,CAAC,IAAI,CAAC,yCAAyC,CAAC;IACrE,OAAO,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC;AAC/B;AAEA;;;;;;;CAOA,GACO,eAAe,KAAK,CAAC,OAAO,EAA6B;IAC9D,MAAM,MAAA,0OAAS,YAAA,AAAS,EAAE;IAC1B,IAAI,MAAM,EAAE;QACV,OAAO,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC;IAChC;IACE,kPAAA,kPAAe,SAAM,CAAC,IAAI,CAAC,yDAAyD,CAAC;IACrF,OAAO,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC;AAC/B;AAEA;;CAEA,GACO,SAAS,aAAa,GAAY;IACvC,OAAO,CAAC,wOAAC,YAAA,AAAS,EAAE;AACtB;AAEA,yCAAA,GACO,SAAS,SAAS,GAAY;IACnC,MAAM,MAAA,0OAAS,YAAA,AAAS,EAAE;IAC1B,OAAO,MAAM,EAAE,UAAU,EAAE,CAAC,OAAA,KAAY,KAAA,IAAS,CAAC,CAAC,MAAM,EAAE,YAAY,EAAE;AAC3E;AAEA;;;;CAIA,GACO,SAAS,iBAAiB,CAAC,QAAQ,EAAwB;2OAChE,oBAAA,AAAiB,EAAE,EAAC,iBAAiB,CAAC,QAAQ,CAAC;AACjD;AAEA;;;;;;CAMA,GACO,SAAS,YAAY,CAAC,OAAO,EAA4B;IAC9D,MAAM,cAAA,0OAAiB,oBAAA,AAAiB,EAAE;IAC1C,MAAM,YAAA,IAAe,wPAAA,AAAe,EAAE;IAExC,kDAAA;IACE,MAAM,EAAE,SAAA,EAAY,oPAAE,aAAU,CAAC,SAAA,IAAa,CAAA,CAAE;IAEhD,MAAM,OAAA,oOAAU,cAAA,AAAW,EAAC;QAC1B,IAAI,EAAE,YAAY,CAAC,OAAO,EAAG,IAAG,cAAc,CAAC,OAAO,EAAE;QACxD,GAAI,SAAA,IAAa;YAAE,SAAA;QAAA,CAAW,CAAC;QAC/B,GAAG,OAAO;IACd,CAAG,CAAC;IAEJ,sCAAA;IACE,MAAM,cAAe,GAAE,cAAc,CAAC,UAAU,EAAE;IAClD,IAAI,cAAc,EAAE,MAAO,KAAI,IAAI,EAAE;yOACnC,gBAAA,AAAa,EAAC,cAAc,EAAE;YAAE,MAAM,EAAE,QAAS;QAAA,CAAC,CAAC;IACvD;IAEE,UAAU,EAAE;IAEd,iDAAA;IACE,cAAc,CAAC,UAAU,CAAC,OAAO,CAAC;IAElC,OAAO,OAAO;AAChB;AAEA;;CAEA,GACO,SAAS,UAAU,GAAS;IACjC,MAAM,cAAA,GAAiB,2PAAA,AAAiB,EAAE;IAC1C,MAAM,YAAA,0OAAe,kBAAA,AAAe,EAAE;IAEtC,MAAM,OAAA,GAAU,YAAY,CAAC,UAAU,EAAC,IAAK,cAAc,CAAC,UAAU,EAAE;IACxE,IAAI,OAAO,EAAE;yOACX,eAAA,AAAY,EAAC,OAAO,CAAC;IACzB;IACE,kBAAkB,EAAE;IAEtB,gDAAA;IACE,cAAc,CAAC,UAAU,EAAE;AAC7B;AAEA;;CAEA,GACA,SAAS,kBAAkB,GAAS;IAClC,MAAM,cAAA,IAAiB,0PAAA,AAAiB,EAAE;IAC1C,MAAM,MAAA,0OAAS,YAAA,AAAS,EAAE;IAC1B,MAAM,OAAQ,GAAE,cAAc,CAAC,UAAU,EAAE;IAC3C,IAAI,OAAQ,IAAG,MAAM,EAAE;QACrB,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC;IAClC;AACA;AAEA;;;;;CAKA,GACO,SAAS,cAAc,CAAC,GAAG,GAAY,KAAK,EAAQ;IAC3D,2DAAA;IACE,IAAI,GAAG,EAAE;QACP,UAAU,EAAE;QACZ;IACJ;IAEA,uBAAA;IACE,kBAAkB,EAAE;AACtB", "ignoreList": [0], "debugId": null}}]}