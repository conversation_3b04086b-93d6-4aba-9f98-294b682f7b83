#!/usr/bin/env node
import{a as s,b as u,c as $,d as D,e as d,g as k,h as b,k as W}from"./chunk-OQ3Y4V4F.mjs";s();import O from"picocolors";import{Command as R}from"commander";var A={name:"@turbo/workspaces",version:"2.5.3",description:"Tools for working with package managers",homepage:"https://turborepo.com",license:"MIT",repository:{type:"git",url:"https://github.com/vercel/turborepo",directory:"packages/turbo-workspaces"},bugs:{url:"https://github.com/vercel/turborepo/issues"},bin:"dist/cli.js",module:"dist/index.mjs",main:"dist/index.js",types:"dist/index.d.ts",scripts:{build:"tsup",dev:"tsup --watch",test:"jest",lint:"eslint src/","check-types":"tsc --noEmit","lint:prettier":"prettier -c . --cache --ignore-path=../../.prettierignore"},dependencies:{commander:"^10.0.0",execa:"5.1.1","fast-glob":"^3.2.12","fs-extra":"^10.1.0","gradient-string":"^2.0.0",inquirer:"^8.0.0","js-yaml":"^4.1.0",ora:"4.1.1",picocolors:"1.0.1",semver:"7.6.2","update-check":"^1.5.4"},devDependencies:{"@jest/globals":"^29.7.0","@turbo/eslint-config":"workspace:*","@turbo/test-utils":"workspace:*","@turbo/tsconfig":"workspace:*","@turbo/utils":"workspace:*","@types/fs-extra":"^9.0.13","@types/gradient-string":"^1.1.2","@types/inquirer":"^7.3.1","@types/js-yaml":"^4.0.5","@types/node":"^18.17.2","@types/semver":"7.5.8",jest:"^29.7.0","ts-jest":"^29.2.5",tsup:"^5.10.3",typescript:"5.5.4"},files:["dist"],publishConfig:{access:"public"}};s();s();import C from"path";import T from"inquirer";import i from"picocolors";async function M(t){let r=new b;r.hero();let p=await T.prompt({type:"input",name:"directoryInput",message:"Where is the root of the repo?",when:!t,default:".",validate:e=>{let{exists:o,absolute:n}=d({directory:e});return o?!0:`Directory ${i.dim(`(${n})`)} does not exist`},filter:e=>e.trim()}),{directoryInput:c=t}=p,{exists:v,absolute:m}=d({directory:c});if(!v)return r.error(`Directory ${i.dim(`(${m})`)} does not exist`),process.exit(1);let g=await k({root:m}),l=g.workspaceData.workspaces.length,y=l>0,a={};g.workspaceData.workspaces.forEach(e=>{let n=C.relative(m,e.paths.root).split(C.sep)[0];n in a||(a[n]=[]),a[n].push(e)});let w=e=>`${e.name} (${i.italic(`./${C.relative(m,e.paths.root)}`)})`,f=({number:e,dir:o,workspaces:n})=>{r.indented(2,`${e}. ${i.bold(o)}`),n.forEach((S,q)=>{r.indented(3,`${q+1}. ${w(S)}`)})};r.header("Repository Summary"),r.indented(1,`${i.underline(g.name)}:`),r.indented(1,`Package Manager: ${i.bold(i.italic(g.packageManager))}`),y&&(r.indented(1,`Workspaces (${i.bold(l.toString())}):`),Object.keys(a).forEach((e,o)=>{f({number:o+1,workspaces:a[e],dir:e})}),r.blankLine())}s();import I from"inquirer";import E from"picocolors";function B({packageManager:t,currentWorkspaceManger:r,availablePackageManagers:p}){return r===t?"already in use":p[t]?!1:"not installed"}async function j(t,r,p){let c=new b(p);c.hero(),c.header("Welcome, let's convert your project."),c.blankLine();let v=await I.prompt({type:"input",name:"directoryInput",message:"Where is the root of your repo?",when:!t,default:".",validate:e=>{let{exists:o,absolute:n}=d({directory:e});return o?!0:`Directory ${E.dim(`(${n})`)} does not exist`},filter:e=>e.trim()}),{directoryInput:m=t}=v,{exists:g,absolute:l}=d({directory:m});if(!g)return c.error(`Directory ${E.dim(`(${l})`)} does not exist`),process.exit(1);let[y,a]=await Promise.all([k({root:l}),$()]),w=await I.prompt({name:"packageManagerInput",type:"list",message:`Convert from ${y.packageManager} to:`,when:!r||!Object.keys(a).includes(r),choices:[{pm:"npm",label:"npm"},{pm:"pnpm",label:"pnpm"},{pm:"yarn",label:"yarn"},{pm:"bun",label:"Bun (beta)"}].map(({pm:e,label:o})=>({name:o,value:e,disabled:B({packageManager:e,currentWorkspaceManger:y.packageManager,availablePackageManagers:a})}))}),{packageManagerInput:f=r}=w;await W({project:y,convertTo:{name:f,version:a[f]},logger:c,options:p})}var h=new R;h.name("@turbo/workspaces").description("Tools for working with package manager workspaces").version(A.version,"-v, --version","output the current version");h.command("convert").description("Convert project between workspace managers").argument("[path]","Project root").argument("[package-manager]","Package manager to convert to").option("--skip-install","Do not run a package manager install after conversion",!1).option("--ignore-unchanged-package-manager","Prevent script failure if the package manager is unchanged",!1).option("--dry","Dry run (no changes are made to files)",!1).option("--force","Bypass Git safety checks and forcibly run conversion",!1).action(j);h.command("summary").description("Display a summary of the specified project").argument("[path]","Project root").action(M);h.parseAsync().catch(t=>{u.log(),t instanceof D?u.log(O.red(t.message)):(u.log(O.red("Unexpected error. Please report it as a bug:")),u.log(t)),u.log(),process.exit(1)});
