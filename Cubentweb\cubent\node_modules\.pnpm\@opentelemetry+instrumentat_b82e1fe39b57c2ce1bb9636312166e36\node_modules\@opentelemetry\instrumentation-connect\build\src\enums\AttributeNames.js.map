{"version": 3, "file": "AttributeNames.js", "sourceRoot": "", "sources": ["../../../src/enums/AttributeNames.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,IAAY,cAGX;AAHD,WAAY,cAAc;IACxB,+CAA6B,CAAA;IAC7B,+CAA6B,CAAA;AAC/B,CAAC,EAHW,cAAc,GAAd,sBAAc,KAAd,sBAAc,QAGzB;AAED,IAAY,YAGX;AAHD,WAAY,YAAY;IACtB,yCAAyB,CAAA;IACzB,mDAAmC,CAAA;AACrC,CAAC,EAHW,YAAY,GAAZ,oBAAY,KAAZ,oBAAY,QAGvB;AAED,IAAY,YAGX;AAHD,WAAY,YAAY;IACtB,yCAAyB,CAAA;IACzB,mDAAmC,CAAA;AACrC,CAAC,EAHW,YAAY,GAAZ,oBAAY,KAAZ,oBAAY,QAGvB", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport enum AttributeNames {\n  CONNECT_TYPE = 'connect.type',\n  CONNECT_NAME = 'connect.name',\n}\n\nexport enum ConnectTypes {\n  MIDDLEWARE = 'middleware',\n  REQUEST_HANDLER = 'request_handler',\n}\n\nexport enum ConnectNames {\n  MIDDLEWARE = 'middleware',\n  REQUEST_HANDLER = 'request handler',\n}\n"]}