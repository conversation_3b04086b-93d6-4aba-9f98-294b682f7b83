"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
var _matches = require("../matches");
Object.keys(_matches).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (key in exports && exports[key] === _matches[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _matches[key];
    }
  });
});
var _getNodeText = require("../get-node-text");
Object.keys(_getNodeText).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (key in exports && exports[key] === _getNodeText[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _getNodeText[key];
    }
  });
});
var _queryHelpers = require("../query-helpers");
Object.keys(_queryHelpers).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (key in exports && exports[key] === _queryHelpers[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _queryHelpers[key];
    }
  });
});
var _config = require("../config");
Object.keys(_config).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (key in exports && exports[key] === _config[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _config[key];
    }
  });
});