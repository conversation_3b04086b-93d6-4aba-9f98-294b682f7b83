{"version": 3, "file": "instrumentation.js", "sourceRoot": "", "sources": ["../../src/instrumentation.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,8CAA8D;AAC9D,4CAM4B;AAG5B,+DAA4D;AAC5D,2DAAwD;AACxD,mCAMiB;AACjB,kBAAkB;AAClB,uCAA0D;AAC1D,oEAKwC;AACxC,8EAA0E;AAC1E,qDAM0B;AAE1B,gDAAgD;AAChD,MAAa,sBAAuB,SAAQ,qCAAiD;IAC3F,YAAY,SAAuC,EAAE;QACnD,KAAK,CAAC,sBAAY,EAAE,yBAAe,EAAE,MAAM,CAAC,CAAC;IAC/C,CAAC;IAED,IAAI;QACF,OAAO;YACL,IAAI,qDAAmC,CACrC,SAAS,EACT,CAAC,YAAY,CAAC,EACd,aAAa,CAAC,EAAE;gBACd,MAAM,WAAW,GAAG,aAAa,CAAC,MAAmC,CAAC;gBACtE,6BAA6B;gBAC7B,IAAI,IAAA,2BAAS,EAAC,WAAW,CAAC,KAAK,CAAC,EAAE;oBAChC,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;iBACpC;gBACD,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,OAAO,EAAE,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC;gBACxD,2BAA2B;gBAC3B,IAAI,IAAA,2BAAS,EAAC,WAAW,CAAC,GAAG,CAAC,EAAE;oBAC9B,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;iBAClC;gBACD,8DAA8D;gBAC9D,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,KAAK,EAAE,IAAI,CAAC,kBAAkB,EAAS,CAAC,CAAC;gBACjE,gCAAgC;gBAChC,IAAI,IAAA,2BAAS,EAAC,aAAa,CAAC,WAAW,CAAC,GAAG,CAAC,EAAE;oBAC5C,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;iBAChD;gBACD,IAAI,CAAC,KAAK,CACR,aAAa,CAAC,WAAW,EACzB,KAAK;gBACL,8DAA8D;gBAC9D,IAAI,CAAC,eAAe,EAAS,CAC9B,CAAC;gBACF,OAAO,aAAa,CAAC;YACvB,CAAC,EACD,aAAa,CAAC,EAAE;gBACd,IAAI,aAAa,KAAK,SAAS;oBAAE,OAAO;gBACxC,MAAM,WAAW,GAAG,aAAa,CAAC,MAAmC,CAAC;gBACtE,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;gBACnC,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;gBACjC,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;YACjD,CAAC,CACF;SACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,cAAc;QACpB,MAAM,eAAe,GAAG,IAAI,CAAC;QAC7B,OAAO,UAAU,QAAiC;YAChD,OAAO,SAAS,WAAW,CAEzB,GAAG,IAAiC;gBAEpC,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;gBACzC,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAiB,CAAC;gBAChE,eAAe,CAAC,WAAW,CAAC,KAAK,EAAE,IAAA,oBAAY,EAAC,IAAI,CAAC,CAAC,CAAC;gBACvD,OAAO,KAAK,CAAC;YACf,CAAC,CAAC;QACJ,CAAC,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,kBAAkB;QACxB,MAAM,eAAe,GAAG,IAAI,CAAC;QAC7B,OAAO,UAAU,QAA+B;YAC9C,OAAO,SAAS,GAAG,CAEjB,GAAG,IAAiC;gBAEpC,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;gBACzC,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAiB,CAAC;gBAChE,eAAe,CAAC,WAAW,CAAC,KAAK,EAAE,IAAA,oBAAY,EAAC,IAAI,CAAC,CAAC,CAAC;gBACvD,OAAO,KAAK,CAAC;YACf,CAAC,CAAC;QACJ,CAAC,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,eAAe;QACrB,MAAM,eAAe,GAAG,IAAI,CAAC;QAC7B,OAAO,UAAU,QAAoC;YACnD,OAAO,SAAS,GAAG,CAEjB,GAAG,IAAiC;gBAEpC,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;gBACzC,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBAChE,eAAe,CAAC,WAAW,CAAC,KAAK,EAAE,IAAA,oBAAY,EAAC,IAAI,CAAC,CAAC,CAAC;gBACvD,OAAO,KAAK,CAAC;YACf,CAAC,CAAC;QACJ,CAAC,CAAC;IACJ,CAAC;IAED,oEAAoE;IAC5D,WAAW,CAEjB,KAAmB,EACnB,SAAkB;QAElB,MAAM,eAAe,GAAG,IAAI,CAAC;QAC7B,+CAA+C;QAC/C,IAAI,KAAK,CAAC,8BAAa,CAAC,KAAK,IAAI;YAAE,OAAO;QAC1C,KAAK,CAAC,8BAAa,CAAC,GAAG,IAAI,CAAC;QAE5B,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,QAAQ,EAAE,QAAQ,CAAC,EAAE;YACrC,kCAAkC;YAClC,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC;gBAAE,OAAO,QAAQ,CAAC;YAE3C,MAAM,OAAO,GAAG,UAEd,GAAmB,EACnB,GAAqB;gBAErB,IAAA,sBAAc,EAAC,GAAG,EAAE,SAAS,CAAC,CAAC;gBAC/B,MAAM,KAAK,GAAI,GAAG,CAAC,uCAAsB,CAAc;qBACpD,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,KAAK,GAAG,IAAI,IAAI,KAAK,IAAI,CAAC;qBAC7C,IAAI,CAAC,EAAE,CAAC;oBACT,8CAA8C;qBAC7C,OAAO,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC;gBAE3B,MAAM,UAAU,GAAe;oBAC7B,CAAC,0CAAmB,CAAC,EAAE,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG;iBACtD,CAAC;gBACF,MAAM,QAAQ,GAAG,IAAA,wBAAgB,EAAC,KAAK,EAAE,KAAK,EAAE,SAAS,CAAC,CAAC;gBAC3D,MAAM,IAAI,GAAG,QAAQ,CAAC,UAAU,CAC9B,+BAAc,CAAC,YAAY,CACR,CAAC;gBAEtB,MAAM,WAAW,GAAG,IAAA,qBAAc,EAAC,aAAO,CAAC,MAAM,EAAE,CAAC,CAAC;gBACrD,IAAI,CAAA,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAE,IAAI,MAAK,cAAO,CAAC,IAAI,EAAE;oBACtC,WAAW,CAAC,KAAK,GAAG,KAAK,IAAI,GAAG,CAAC;iBAClC;gBAED,2DAA2D;gBAC3D,IAAI,IAAA,sBAAc,EAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,EAAE,eAAe,CAAC,SAAS,EAAE,CAAC,EAAE;oBACpE,IAAI,IAAI,KAAK,mCAAgB,CAAC,UAAU,EAAE;wBACvC,GAAG,CAAC,uCAAsB,CAAc,CAAC,GAAG,EAAE,CAAC;qBACjD;oBACD,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;iBACxC;gBAED,IAAI,WAAK,CAAC,OAAO,CAAC,aAAO,CAAC,MAAM,EAAE,CAAC,KAAK,SAAS,EAAE;oBACjD,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;iBACxC;gBAED,MAAM,QAAQ,GAAG,eAAe,CAAC,YAAY,CAC3C;oBACE,OAAO,EAAE,GAAG;oBACZ,SAAS,EAAE,IAAI;oBACf,KAAK;iBACN,EACD,QAAQ,CAAC,IAAI,CACd,CAAC;gBACF,MAAM,IAAI,GAAG,eAAe,CAAC,MAAM,CAAC,SAAS,CAAC,QAAQ,EAAE;oBACtD,UAAU,EAAE,MAAM,CAAC,MAAM,CAAC,UAAU,EAAE,QAAQ,CAAC,UAAU,CAAC;iBAC3D,CAAC,CAAC;gBAEH,MAAM,EAAE,WAAW,EAAE,GAAG,eAAe,CAAC,SAAS,EAAE,CAAC;gBACpD,IAAI,WAAW,EAAE;oBACf,IAAA,wCAAsB,EACpB,GAAG,EAAE,CACH,WAAW,CAAC,IAAI,EAAE;wBAChB,OAAO,EAAE,GAAG;wBACZ,SAAS,EAAE,IAAI;wBACf,KAAK;qBACN,CAAC,EACJ,CAAC,CAAC,EAAE;wBACF,IAAI,CAAC,EAAE;4BACL,UAAI,CAAC,KAAK,CAAC,8CAA8C,EAAE,CAAC,CAAC,CAAC;yBAC/D;oBACH,CAAC,EACD,IAAI,CACL,CAAC;iBACH;gBAED,IAAI,YAAY,GAAG,KAAK,CAAC;gBACzB,IACE,QAAQ,CAAC,UAAU,CAAC,+BAAc,CAAC,YAAY,CAAC;oBAChD,mCAAgB,CAAC,UAAU,EAC3B;oBACA,IAAI,CAAC,GAAG,EAAE,CAAC;oBACX,YAAY,GAAG,IAAI,CAAC;iBACrB;gBACD,qCAAqC;gBACrC,MAAM,gBAAgB,GAAG,GAAG,EAAE;oBAC5B,IAAI,YAAY,KAAK,KAAK,EAAE;wBAC1B,YAAY,GAAG,IAAI,CAAC;wBACpB,IAAI,CAAC,GAAG,EAAE,CAAC;qBACZ;gBACH,CAAC,CAAC;gBAEF,4BAA4B;gBAC5B,MAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;gBACnC,MAAM,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,CAAC,OAAO,GAAG,KAAK,UAAU,CAAC,CAAC;gBACrE,IAAI,WAAW,IAAI,CAAC,EAAE;oBACpB,SAAS,CAAC,WAAW,CAAC,GAAG;;wBACvB,qEAAqE;wBACrE,wCAAwC;wBACxC,MAAM,UAAU,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;wBAChC,MAAM,OAAO,GAAG,CAAC,CAAC,SAAS,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC,QAAQ,CAC5D,UAAU,CACX,CAAC;wBACF,IAAI,CAAC,YAAY,IAAI,OAAO,EAAE;4BAC5B,MAAM,CAAC,KAAK,EAAE,OAAO,CAAC,GAAG,IAAA,yBAAiB,EAAC,UAAU,CAAC,CAAC;4BACvD,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;4BAC5B,IAAI,CAAC,SAAS,CAAC;gCACb,IAAI,EAAE,oBAAc,CAAC,KAAK;gCAC1B,OAAO;6BACR,CAAC,CAAC;yBACJ;wBAED,IAAI,YAAY,KAAK,KAAK,EAAE;4BAC1B,YAAY,GAAG,IAAI,CAAC;4BACpB,MAAA,GAAG,CAAC,GAAG,0CAAE,cAAc,CAAC,QAAQ,EAAE,gBAAgB,CAAC,CAAC;4BACpD,IAAI,CAAC,GAAG,EAAE,CAAC;yBACZ;wBACD,IAAI,CAAC,CAAC,GAAG,CAAC,KAAK,IAAI,OAAO,CAAC,EAAE;4BAC1B,GAAG,CAAC,uCAAsB,CAAc,CAAC,GAAG,EAAE,CAAC;yBACjD;wBACD,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAa,CAAC;wBAC/C,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;oBACzC,CAAC,CAAC;iBACH;gBAED,IAAI;oBACF,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;iBACxC;gBAAC,OAAO,QAAQ,EAAE;oBACjB,MAAM,CAAC,KAAK,EAAE,OAAO,CAAC,GAAG,IAAA,yBAAiB,EAAC,QAAQ,CAAC,CAAC;oBACrD,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;oBAC5B,IAAI,CAAC,SAAS,CAAC;wBACb,IAAI,EAAE,oBAAc,CAAC,KAAK;wBAC1B,OAAO;qBACR,CAAC,CAAC;oBACH,MAAM,QAAQ,CAAC;iBAChB;wBAAS;oBACR;;;;;uBAKG;oBACH,IAAI,CAAC,YAAY,EAAE;wBACjB,GAAG,CAAC,IAAI,CAAC,QAAQ,EAAE,gBAAgB,CAAC,CAAC;qBACtC;iBACF;YACH,CAAC,CAAC;YAEF,yEAAyE;YACzE,sEAAsE;YACtE,mCAAmC;YACnC,8EAA8E;YAC9E,kFAAkF;YAClF,gFAAgF;YAChF,6CAA6C;YAC7C,8EAA8E;YAC9E,KAAK,MAAM,GAAG,IAAI,QAAQ,EAAE;gBAC1B,MAAM,CAAC,cAAc,CAAC,OAAO,EAAE,GAAG,EAAE;oBAClC,GAAG;wBACD,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAC;oBACvB,CAAC;oBACD,GAAG,CAAC,KAAK;wBACP,QAAQ,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;oBACxB,CAAC;iBACF,CAAC,CAAC;aACJ;YACD,OAAO,OAAO,CAAC;QACjB,CAAC,CAAC,CAAC;IACL,CAAC;IAED,YAAY,CAAC,IAAwB,EAAE,WAAmB;;QACxD,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QAE1C,IAAI,CAAC,CAAC,YAAY,YAAY,QAAQ,CAAC,EAAE;YACvC,OAAO,WAAW,CAAC;SACpB;QAED,IAAI;YACF,OAAO,MAAA,YAAY,CAAC,IAAI,EAAE,WAAW,CAAC,mCAAI,WAAW,CAAC;SACvD;QAAC,OAAO,GAAG,EAAE;YACZ,UAAI,CAAC,KAAK,CACR,+DAA+D,EAC/D,GAAG,CACJ,CAAC;YACF,OAAO,WAAW,CAAC;SACpB;IACH,CAAC;CACF;AArSD,wDAqSC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { getRPCMetadata, RPCType } from '@opentelemetry/core';\nimport {\n  trace,\n  context,\n  diag,\n  Attributes,\n  SpanStatusCode,\n} from '@opentelemetry/api';\nimport type * as express from 'express';\nimport { ExpressInstrumentationConfig, ExpressRequestInfo } from './types';\nimport { ExpressLayerType } from './enums/ExpressLayerType';\nimport { AttributeNames } from './enums/AttributeNames';\nimport {\n  asErrorAndMessage,\n  getLayerMetadata,\n  getLayerPath,\n  isLayerIgnored,\n  storeLayerPath,\n} from './utils';\n/** @knipignore */\nimport { PACKAGE_NAME, PACKAGE_VERSION } from './version';\nimport {\n  InstrumentationBase,\n  InstrumentationNodeModuleDefinition,\n  isWrapped,\n  safeExecuteInTheMiddle,\n} from '@opentelemetry/instrumentation';\nimport { SEMATTRS_HTTP_ROUTE } from '@opentelemetry/semantic-conventions';\nimport {\n  ExpressLayer,\n  ExpressRouter,\n  kLayerPatched,\n  PatchedRequest,\n  _LAYERS_STORE_PROPERTY,\n} from './internal-types';\n\n/** Express instrumentation for OpenTelemetry */\nexport class ExpressInstrumentation extends InstrumentationBase<ExpressInstrumentationConfig> {\n  constructor(config: ExpressInstrumentationConfig = {}) {\n    super(PACKAGE_NAME, PACKAGE_VERSION, config);\n  }\n\n  init() {\n    return [\n      new InstrumentationNodeModuleDefinition(\n        'express',\n        ['>=4.0.0 <5'],\n        moduleExports => {\n          const routerProto = moduleExports.Router as unknown as express.Router;\n          // patch express.Router.route\n          if (isWrapped(routerProto.route)) {\n            this._unwrap(routerProto, 'route');\n          }\n          this._wrap(routerProto, 'route', this._getRoutePatch());\n          // patch express.Router.use\n          if (isWrapped(routerProto.use)) {\n            this._unwrap(routerProto, 'use');\n          }\n          // eslint-disable-next-line @typescript-eslint/no-explicit-any\n          this._wrap(routerProto, 'use', this._getRouterUsePatch() as any);\n          // patch express.Application.use\n          if (isWrapped(moduleExports.application.use)) {\n            this._unwrap(moduleExports.application, 'use');\n          }\n          this._wrap(\n            moduleExports.application,\n            'use',\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\n            this._getAppUsePatch() as any\n          );\n          return moduleExports;\n        },\n        moduleExports => {\n          if (moduleExports === undefined) return;\n          const routerProto = moduleExports.Router as unknown as express.Router;\n          this._unwrap(routerProto, 'route');\n          this._unwrap(routerProto, 'use');\n          this._unwrap(moduleExports.application, 'use');\n        }\n      ),\n    ];\n  }\n\n  /**\n   * Get the patch for Router.route function\n   */\n  private _getRoutePatch() {\n    const instrumentation = this;\n    return function (original: express.Router['route']) {\n      return function route_trace(\n        this: ExpressRouter,\n        ...args: Parameters<typeof original>\n      ) {\n        const route = original.apply(this, args);\n        const layer = this.stack[this.stack.length - 1] as ExpressLayer;\n        instrumentation._applyPatch(layer, getLayerPath(args));\n        return route;\n      };\n    };\n  }\n\n  /**\n   * Get the patch for Router.use function\n   */\n  private _getRouterUsePatch() {\n    const instrumentation = this;\n    return function (original: express.Router['use']) {\n      return function use(\n        this: express.Application,\n        ...args: Parameters<typeof original>\n      ) {\n        const route = original.apply(this, args);\n        const layer = this.stack[this.stack.length - 1] as ExpressLayer;\n        instrumentation._applyPatch(layer, getLayerPath(args));\n        return route;\n      };\n    };\n  }\n\n  /**\n   * Get the patch for Application.use function\n   */\n  private _getAppUsePatch() {\n    const instrumentation = this;\n    return function (original: express.Application['use']) {\n      return function use(\n        this: { _router: ExpressRouter },\n        ...args: Parameters<typeof original>\n      ) {\n        const route = original.apply(this, args);\n        const layer = this._router.stack[this._router.stack.length - 1];\n        instrumentation._applyPatch(layer, getLayerPath(args));\n        return route;\n      };\n    };\n  }\n\n  /** Patch each express layer to create span and propagate context */\n  private _applyPatch(\n    this: ExpressInstrumentation,\n    layer: ExpressLayer,\n    layerPath?: string\n  ) {\n    const instrumentation = this;\n    // avoid patching multiple times the same layer\n    if (layer[kLayerPatched] === true) return;\n    layer[kLayerPatched] = true;\n\n    this._wrap(layer, 'handle', original => {\n      // TODO: instrument error handlers\n      if (original.length === 4) return original;\n\n      const patched = function (\n        this: ExpressLayer,\n        req: PatchedRequest,\n        res: express.Response\n      ) {\n        storeLayerPath(req, layerPath);\n        const route = (req[_LAYERS_STORE_PROPERTY] as string[])\n          .filter(path => path !== '/' && path !== '/*')\n          .join('')\n          // remove duplicate slashes to normalize route\n          .replace(/\\/{2,}/g, '/');\n\n        const attributes: Attributes = {\n          [SEMATTRS_HTTP_ROUTE]: route.length > 0 ? route : '/',\n        };\n        const metadata = getLayerMetadata(route, layer, layerPath);\n        const type = metadata.attributes[\n          AttributeNames.EXPRESS_TYPE\n        ] as ExpressLayerType;\n\n        const rpcMetadata = getRPCMetadata(context.active());\n        if (rpcMetadata?.type === RPCType.HTTP) {\n          rpcMetadata.route = route || '/';\n        }\n\n        // verify against the config if the layer should be ignored\n        if (isLayerIgnored(metadata.name, type, instrumentation.getConfig())) {\n          if (type === ExpressLayerType.MIDDLEWARE) {\n            (req[_LAYERS_STORE_PROPERTY] as string[]).pop();\n          }\n          return original.apply(this, arguments);\n        }\n\n        if (trace.getSpan(context.active()) === undefined) {\n          return original.apply(this, arguments);\n        }\n\n        const spanName = instrumentation._getSpanName(\n          {\n            request: req,\n            layerType: type,\n            route,\n          },\n          metadata.name\n        );\n        const span = instrumentation.tracer.startSpan(spanName, {\n          attributes: Object.assign(attributes, metadata.attributes),\n        });\n\n        const { requestHook } = instrumentation.getConfig();\n        if (requestHook) {\n          safeExecuteInTheMiddle(\n            () =>\n              requestHook(span, {\n                request: req,\n                layerType: type,\n                route,\n              }),\n            e => {\n              if (e) {\n                diag.error('express instrumentation: request hook failed', e);\n              }\n            },\n            true\n          );\n        }\n\n        let spanHasEnded = false;\n        if (\n          metadata.attributes[AttributeNames.EXPRESS_TYPE] !==\n          ExpressLayerType.MIDDLEWARE\n        ) {\n          span.end();\n          spanHasEnded = true;\n        }\n        // listener for response.on('finish')\n        const onResponseFinish = () => {\n          if (spanHasEnded === false) {\n            spanHasEnded = true;\n            span.end();\n          }\n        };\n\n        // verify we have a callback\n        const args = Array.from(arguments);\n        const callbackIdx = args.findIndex(arg => typeof arg === 'function');\n        if (callbackIdx >= 0) {\n          arguments[callbackIdx] = function () {\n            // express considers anything but an empty value, \"route\" or \"router\"\n            // passed to its callback to be an error\n            const maybeError = arguments[0];\n            const isError = ![undefined, null, 'route', 'router'].includes(\n              maybeError\n            );\n            if (!spanHasEnded && isError) {\n              const [error, message] = asErrorAndMessage(maybeError);\n              span.recordException(error);\n              span.setStatus({\n                code: SpanStatusCode.ERROR,\n                message,\n              });\n            }\n\n            if (spanHasEnded === false) {\n              spanHasEnded = true;\n              req.res?.removeListener('finish', onResponseFinish);\n              span.end();\n            }\n            if (!(req.route && isError)) {\n              (req[_LAYERS_STORE_PROPERTY] as string[]).pop();\n            }\n            const callback = args[callbackIdx] as Function;\n            return callback.apply(this, arguments);\n          };\n        }\n\n        try {\n          return original.apply(this, arguments);\n        } catch (anyError) {\n          const [error, message] = asErrorAndMessage(anyError);\n          span.recordException(error);\n          span.setStatus({\n            code: SpanStatusCode.ERROR,\n            message,\n          });\n          throw anyError;\n        } finally {\n          /**\n           * At this point if the callback wasn't called, that means either the\n           * layer is asynchronous (so it will call the callback later on) or that\n           * the layer directly end the http response, so we'll hook into the \"finish\"\n           * event to handle the later case.\n           */\n          if (!spanHasEnded) {\n            res.once('finish', onResponseFinish);\n          }\n        }\n      };\n\n      // `handle` isn't just a regular function in some cases. It also contains\n      // some properties holding metadata and state so we need to proxy them\n      // through through patched function\n      // ref: https://github.com/open-telemetry/opentelemetry-js-contrib/issues/1950\n      // Also some apps/libs do their own patching before OTEL and have these properties\n      // in the proptotype. So we use a `for...in` loop to get own properties and also\n      // any enumerable prop in the prototype chain\n      // ref: https://github.com/open-telemetry/opentelemetry-js-contrib/issues/2271\n      for (const key in original) {\n        Object.defineProperty(patched, key, {\n          get() {\n            return original[key];\n          },\n          set(value) {\n            original[key] = value;\n          },\n        });\n      }\n      return patched;\n    });\n  }\n\n  _getSpanName(info: ExpressRequestInfo, defaultName: string) {\n    const { spanNameHook } = this.getConfig();\n\n    if (!(spanNameHook instanceof Function)) {\n      return defaultName;\n    }\n\n    try {\n      return spanNameHook(info, defaultName) ?? defaultName;\n    } catch (err) {\n      diag.error(\n        'express instrumentation: error calling span name rewrite hook',\n        err\n      );\n      return defaultName;\n    }\n  }\n}\n"]}