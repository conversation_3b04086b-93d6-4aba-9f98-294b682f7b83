{"name": "env-ci", "description": "Get environment variables exposed by CI services", "version": "5.5.0", "author": "<PERSON> (https://github.com/pvdlg)", "ava": {"files": ["test/**/*.test.js"]}, "bugs": {"url": "https://github.com/semantic-release/env-ci/issues"}, "dependencies": {"execa": "^5.0.0", "fromentries": "^1.3.2", "java-properties": "^1.0.0"}, "devDependencies": {"ava": "3.15.0", "codecov": "3.8.3", "file-url": "3.0.0", "nyc": "15.1.0", "proxyquire": "2.1.3", "tempy": "0.7.1", "xo": "0.47.0"}, "engines": {"node": ">=10.17"}, "files": ["index.js", "lib", "services"], "homepage": "https://github.com/semantic-release/env-ci#readme", "keywords": ["appveyor", "bamboo", "bitbucket", "bitrise", "buddy", "buildkite", "ci", "circle", "cirrus", "cloudflare", "codebuild", "codefresh", "codeship", "drone", "environment", "git", "github", "gitlab", "jenkins", "netlify", "puppet", "sail", "scrutinizer", "semaphore", "shippable", "teamcity", "travis", "variable", "vsts", "<PERSON><PERSON><PERSON>"], "license": "MIT", "main": "index.js", "nyc": {"include": ["index.js", "lib/**/*.js", "services/**/*.js"], "reporter": ["json", "text", "html"], "all": true}, "prettier": {"printWidth": 120, "trailingComma": "es5"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "https://github.com/semantic-release/env-ci.git"}, "scripts": {"codecov": "codecov -f coverage/coverage-final.json", "lint": "xo", "semantic-release": "semantic-release", "test": "npm run lint && npm run test:ci", "test:ci": "nyc ava -v"}, "xo": {"prettier": true, "space": true, "rules": {"unicorn/string-content": "off", "unicorn/prefer-module": "off"}}, "renovate": {"extends": ["github>semantic-release/.github"]}}