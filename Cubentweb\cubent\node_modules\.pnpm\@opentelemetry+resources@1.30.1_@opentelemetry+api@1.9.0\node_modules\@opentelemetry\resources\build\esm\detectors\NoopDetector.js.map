{"version": 3, "file": "NoopDetector.js", "sourceRoot": "", "sources": ["../../../src/detectors/NoopDetector.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAIH,OAAO,EAAE,gBAAgB,EAAE,MAAM,oBAAoB,CAAC;AAEtD;IAAA;IAIA,CAAC;IAHC,6BAAM,GAAN;QACE,OAAO,OAAO,CAAC,OAAO,CAAC,gBAAgB,CAAC,MAAM,EAAE,CAAC,CAAC;IACpD,CAAC;IACH,mBAAC;AAAD,CAAC,AAJD,IAIC;;AAED,MAAM,CAAC,IAAM,YAAY,GAAG,IAAI,YAAY,EAAE,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Detector } from '../types';\nimport { IResource } from '../IResource';\nimport { noopDetectorSync } from './NoopDetectorSync';\n\nexport class NoopDetector implements Detector {\n  detect(): Promise<IResource> {\n    return Promise.resolve(noopDetectorSync.detect());\n  }\n}\n\nexport const noopDetector = new NoopDetector();\n"]}