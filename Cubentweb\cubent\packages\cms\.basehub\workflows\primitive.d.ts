// This file was generated by basehub. Do not edit directly. Read more: https://basehub.com/docs/api-reference/basehub-sdk

/* eslint-disable */
/* eslint-disable eslint-comments/no-restricted-disable */
/* tslint:disable */
// @ts-nocheck

import { Scalars } from "../schema";
type KeysStartingWith<Obj, Prefix extends string> = {
    [K in keyof Obj]: K extends `${Prefix}${string}` ? K : never;
}[keyof Obj];
type ExtractWorkflowKey<T extends string> = T extends `${infer Base}:${string}` ? Base : T;
export type WorkflowKeys = KeysStartingWith<Scalars, "bshb_workflow">;
type WorkflowSchemaMap = {
    [K in WorkflowKeys]: Scalars[`schema_${K}`];
};
export declare const authenticateWebhook: <Key extends `${WorkflowKeys}:${string}`>({ secret: _secret, body, signature, }: {
    /**
     * The body of the incoming webhook request
     * Can be:
     * - Parsed JSON from request.json()
     * - Raw string from request.text()
     * - ReadableStream from request.body
     */
    body: unknown;
    /**
     * The signature of the incoming webhook request—you get this via request.headers["x-basehub-webhook-signature"]
     * This should be a hex-encoded HMAC SHA-256 hash of the request body
     */
    signature: string | null | Headers;
    /**
     * The secret used for verifying the incoming webhook request—you get this via the BaseHub API
     * This secret should never be exposed in requests or responses
     */
    secret: Key;
}) => Promise<{
    success: true;
    payload: WorkflowSchemaMap[ExtractWorkflowKey<Key>];
} | {
    success: false;
    error: string;
}>;
export {};
