// This file was generated by basehub. Do not edit directly. Read more: https://basehub.com/docs/api-reference/basehub-sdk

/* eslint-disable */
/* eslint-disable eslint-comments/no-restricted-disable */
/* tslint:disable */
// @ts-nocheck

import * as React from "react";
export type DragHandle = {
    hasDragged: boolean;
};
export declare const DragHandle: React.ForwardRefExoticComponent<{
    onDrag: ({ x, y }: {
        x: number;
        y: number;
    }) => void;
    children: React.ReactNode;
} & React.RefAttributes<unknown>>;
