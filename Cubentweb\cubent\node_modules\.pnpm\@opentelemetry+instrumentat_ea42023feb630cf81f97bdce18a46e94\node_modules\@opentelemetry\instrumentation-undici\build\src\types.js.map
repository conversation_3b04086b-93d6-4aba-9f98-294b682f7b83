{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../../src/types.ts"], "names": [], "mappings": "", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport type { InstrumentationConfig } from '@opentelemetry/instrumentation';\nimport type { Attributes, Span } from '@opentelemetry/api';\n\nexport interface UndiciRequest {\n  origin: string;\n  method: string;\n  path: string;\n  /**\n   * Serialized string of headers in the form `name: value\\r\\n` for v5\n   * Array of strings v6\n   */\n  headers: string | string[];\n  /**\n   * Helper method to add headers (from v6)\n   */\n  addHeader: (name: string, value: string) => void;\n  throwOnError: boolean;\n  completed: boolean;\n  aborted: boolean;\n  idempotent: boolean;\n  contentLength: number | null;\n  contentType: string | null;\n  body: any;\n}\n\nexport interface UndiciResponse {\n  headers: Buffer[];\n  statusCode: number;\n  statusText: string;\n}\n\nexport interface IgnoreRequestFunction<T = UndiciRequest> {\n  (request: T): boolean;\n}\n\nexport interface RequestHookFunction<T = UndiciRequest> {\n  (span: Span, request: T): void;\n}\n\nexport interface ResponseHookFunction<\n  RequestType = UndiciRequest,\n  ResponseType = UndiciResponse\n> {\n  (span: Span, info: { request: RequestType; response: ResponseType }): void;\n}\n\nexport interface StartSpanHookFunction<T = UndiciRequest> {\n  (request: T): Attributes;\n}\n\n// This package will instrument HTTP requests made through `undici` or  `fetch` global API\n// so it seems logical to have similar options than the HTTP instrumentation\nexport interface UndiciInstrumentationConfig<\n  RequestType = UndiciRequest,\n  ResponseType = UndiciResponse\n> extends InstrumentationConfig {\n  /** Not trace all outgoing requests that matched with custom function */\n  ignoreRequestHook?: IgnoreRequestFunction<RequestType>;\n  /** Function for adding custom attributes before request is handled */\n  requestHook?: RequestHookFunction<RequestType>;\n  /** Function called once response headers have been received */\n  responseHook?: ResponseHookFunction<RequestType, ResponseType>;\n  /** Function for adding custom attributes before a span is started */\n  startSpanHook?: StartSpanHookFunction<RequestType>;\n  /** Require parent to create span for outgoing requests */\n  requireParentforSpans?: boolean;\n  /** Map the following HTTP headers to span attributes. */\n  headersToSpanAttributes?: {\n    requestHeaders?: string[];\n    responseHeaders?: string[];\n  };\n}\n"]}