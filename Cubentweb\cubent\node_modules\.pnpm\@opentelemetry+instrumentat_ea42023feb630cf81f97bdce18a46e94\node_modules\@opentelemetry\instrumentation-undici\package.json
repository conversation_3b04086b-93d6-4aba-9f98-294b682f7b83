{"name": "@opentelemetry/instrumentation-undici", "version": "0.10.1", "description": "OpenTelemetry instrumentation for `undici` http client and Node.js fetch()", "main": "build/src/index.js", "types": "build/src/index.d.ts", "repository": "open-telemetry/opentelemetry-js-contrib", "scripts": {"prepublishOnly": "npm run compile", "compile": "tsc -p .", "test": "nyc mocha test/**/*.test.ts", "test-all-versions": "tav", "tdd": "npm run test -- --watch-extensions ts --watch", "clean": "rimraf build/*", "lint": "eslint . --ext .ts", "lint:fix": "eslint . --ext .ts --fix", "watch": "tsc -w", "prewatch": "npm run precompile", "version:update": "node ../../../scripts/version-update.js"}, "keywords": ["opentelemetry", "fetch", "undici", "nodejs", "tracing", "instrumentation"], "author": "OpenTelemetry Authors", "license": "Apache-2.0", "engines": {"node": ">=14"}, "files": ["build/src/**/*.js", "build/src/**/*.js.map", "build/src/**/*.d.ts"], "publishConfig": {"access": "public"}, "devDependencies": {"@opentelemetry/api": "^1.7.0", "@opentelemetry/sdk-metrics": "^1.8.0", "@opentelemetry/sdk-trace-base": "^1.8.0", "@opentelemetry/sdk-trace-node": "^1.8.0", "@types/mocha": "7.0.2", "@types/node": "18.18.14", "nyc": "15.1.0", "rimraf": "5.0.10", "semver": "^7.6.0", "test-all-versions": "6.1.0", "typescript": "4.4.4", "undici": "6.21.1"}, "peerDependencies": {"@opentelemetry/api": "^1.7.0"}, "dependencies": {"@opentelemetry/core": "^1.8.0", "@opentelemetry/instrumentation": "^0.57.1"}, "homepage": "https://github.com/open-telemetry/opentelemetry-js-contrib/tree/main/plugins/node/instrumentation-undici", "sideEffects": false, "gitHead": "1eb77007669bae87fe5664d68ba6533b95275d52"}