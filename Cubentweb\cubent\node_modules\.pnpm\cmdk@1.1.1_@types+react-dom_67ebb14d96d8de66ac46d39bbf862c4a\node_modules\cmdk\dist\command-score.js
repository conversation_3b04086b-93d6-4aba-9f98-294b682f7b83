var p=Object.defineProperty;var H=Object.getOwnPropertyDescriptor;var J=Object.getOwnPropertyNames;var $=Object.prototype.hasOwnProperty;var k=(_,E)=>{for(var h in E)p(_,h,{get:E[h],enumerable:!0})},m=(_,E,h,C)=>{if(E&&typeof E=="object"||typeof E=="function")for(let c of J(E))!$.call(_,c)&&c!==h&&p(_,c,{get:()=>E[c],enumerable:!(C=H(E,c))||C.enumerable});return _};var B=_=>m(p({},"__esModule",{value:!0}),_);var a={};k(a,{commandScore:()=>Z});module.exports=B(a);var D=1,K=.9,W=.8,j=.17,u=.1,G=.999,y=.9999;var F=.99,q=/[\\\/_+.#"@\[\(\{&]/,Q=/[\\\/_+.#"@\[\(\{&]/g,V=/[\s-]/,Y=/[\s-]/g;function L(_,E,h,C,c,P,O){if(P===E.length)return c===_.length?D:F;var T=`${c},${P}`;if(O[T]!==void 0)return O[T];for(var U=C.charAt(P),f=h.indexOf(U,c),S=0,A,N,R,M;f>=0;)A=L(_,E,h,C,f+1,P+1,O),A>S&&(f===c?A*=D:q.test(_.charAt(f-1))?(A*=W,R=_.slice(c,f-1).match(Q),R&&c>0&&(A*=Math.pow(G,R.length))):V.test(_.charAt(f-1))?(A*=K,M=_.slice(c,f-1).match(Y),M&&c>0&&(A*=Math.pow(G,M.length))):(A*=j,c>0&&(A*=Math.pow(G,f-c))),_.charAt(f)!==E.charAt(P)&&(A*=y)),(A<u&&h.charAt(f-1)===C.charAt(P+1)||C.charAt(P+1)===C.charAt(P)&&h.charAt(f-1)!==C.charAt(P))&&(N=L(_,E,h,C,f+1,P+2,O),N*u>A&&(A=N*u)),A>S&&(S=A),f=h.indexOf(U,f+1);return O[T]=S,S}function X(_){return _.toLowerCase().replace(Y," ")}function Z(_,E,h){return _=h&&h.length>0?`${_+" "+h.join(" ")}`:_,L(_,E,X(_),X(E),0,0,{})}0&&(module.exports={commandScore});
