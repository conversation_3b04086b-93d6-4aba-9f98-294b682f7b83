{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/index.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH;;;;;;;GAOG;AACH,MAAM,oBAAoB,GAAG;IAC3B;QACE,KAAK,EAAE,QAAQ;QACf,IAAI,EAAE,CAAC;KACR;IACD;QACE,KAAK,EAAE,8DAA8D;QACrE,IAAI,EAAE,CAAC;KACR;IACD;QACE,KAAK,EAAE,6BAA6B;QACpC,IAAI,EAAE,CAAC;KACR;IACD;QACE,KAAK,EACH,kLAAkL;QACpL,IAAI,EAAE,CAAC,CAAC;KACT;CACF,CAAC;AAOF;;;;;;GAMG;AACI,MAAM,4BAA4B,GAA0B,CACjE,OAAO,EACP,OAAO,EACP,EAAE;;IACF,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,OAAO,CAAC,MAAM,EAAE;QAC5C,MAAM,gBAAgB,GACpB,MAAA,MAAA,oBAAoB,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE;YACtC,OAAO,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC7B,CAAC,CAAC,0CAAE,IAAI,mCAAI,CAAC,CAAC;QAChB,MAAM,eAAe,GACnB,gBAAgB,IAAI,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,gBAAgB,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC;QACvE,IAAI,OAAO,CAAC,MAAM,GAAG,eAAe,CAAC,MAAM,EAAE;YAC3C,eAAe,CAAC,IAAI,CAClB,IAAI,OAAO,CAAC,MAAM,GAAG,gBAAgB,mBAAmB,CACzD,CAAC;SACH;QACD,OAAO,GAAG,OAAO,IAAI,eAAe,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;KAClD;IACD,OAAO,OAAO,CAAC;AACjB,CAAC,CAAC;AAnBW,QAAA,4BAA4B,gCAmBvC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * List of regexes and the number of arguments that should be serialized for matching commands.\n * For example, HSET should serialize which key and field it's operating on, but not its value.\n * Setting the subset to -1 will serialize all arguments.\n * Commands without a match will have their first argument serialized.\n *\n * Refer to https://redis.io/commands/ for the full list.\n */\nconst serializationSubsets = [\n  {\n    regex: /^ECHO/i,\n    args: 0,\n  },\n  {\n    regex: /^(LPUSH|MSET|PFA|PUBLISH|RPUSH|SADD|SET|SPUBLISH|XADD|ZADD)/i,\n    args: 1,\n  },\n  {\n    regex: /^(HSET|HMSET|LSET|LINSERT)/i,\n    args: 2,\n  },\n  {\n    regex:\n      /^(ACL|BIT|B[LRZ]|CLIENT|CLUSTER|CONFIG|COMMAND|DECR|DEL|EVAL|EX|FUNCTION|GEO|GET|HINCR|HMGET|HSCAN|INCR|L[TRLM]|MEMORY|P[EFISTU]|RPOP|S[CDIMORSU]|XACK|X[CDGILPRT]|Z[CDILMPRS])/i,\n    args: -1,\n  },\n];\n\nexport type DbStatementSerializer = (\n  cmdName: string,\n  cmdArgs: Array<string | Buffer | number | any[]>\n) => string;\n\n/**\n * Given the redis command name and arguments, return a combination of the\n * command name + the allowed arguments according to `serializationSubsets`.\n * @param cmdName The redis command name\n * @param cmdArgs The redis command arguments\n * @returns a combination of the command name + args according to `serializationSubsets`.\n */\nexport const defaultDbStatementSerializer: DbStatementSerializer = (\n  cmdName,\n  cmdArgs\n) => {\n  if (Array.isArray(cmdArgs) && cmdArgs.length) {\n    const nArgsToSerialize =\n      serializationSubsets.find(({ regex }) => {\n        return regex.test(cmdName);\n      })?.args ?? 0;\n    const argsToSerialize =\n      nArgsToSerialize >= 0 ? cmdArgs.slice(0, nArgsToSerialize) : cmdArgs;\n    if (cmdArgs.length > argsToSerialize.length) {\n      argsToSerialize.push(\n        `[${cmdArgs.length - nArgsToSerialize} other arguments]`\n      );\n    }\n    return `${cmdName} ${argsToSerialize.join(' ')}`;\n  }\n  return cmdName;\n};\n"]}