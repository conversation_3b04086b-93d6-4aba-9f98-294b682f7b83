import { TsconfigRaw, BuildOptions, Plugin } from 'esbuild';
import ts from 'typescript';

interface DTSPluginOpts {
    outDir?: string;
    tsconfig?: string | TsconfigRaw;
    buildInfoDir?: string;
    __buildContext?: any;
}

declare function getCompilerOptions(opts: {
    tsconfig: any;
    pluginOptions: DTSPluginOpts;
    esbuildOptions: BuildOptions;
}): ts.CompilerOptions;

declare function resolveTSConfig(opts: {
    configPath?: string;
    configName?: string;
    searchPath?: string;
}): any;

declare function humanizeFileSize(size: number): string;

declare const dtsPlugin: (opts?: DTSPluginOpts) => Plugin;

export { type DTSPluginOpts, dtsPlugin as default, dtsPlugin, getCompilerOptions, humanizeFileSize, resolveTSConfig };
