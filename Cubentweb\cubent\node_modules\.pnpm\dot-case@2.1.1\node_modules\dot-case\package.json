{"name": "dot-case", "version": "2.1.1", "description": "Dot case a string", "main": "dot-case.js", "typings": "dot-case.d.ts", "files": ["dot-case.js", "dot-case.d.ts", "LICENSE"], "scripts": {"lint": "standard", "test-spec": "mocha -- -R spec --bail", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- -R spec --bail", "test": "npm run lint && npm run test-cov"}, "repository": {"type": "git", "url": "git://github.com/blakeembrey/dot-case.git"}, "keywords": ["dot", "case", "period"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blakeembrey.me"}, "license": "MIT", "bugs": {"url": "https://github.com/blakeembrey/dot-case/issues"}, "homepage": "https://github.com/blakeembrey/dot-case", "devDependencies": {"istanbul": "^0.4.3", "mocha": "^3.0.0", "standard": "^9.0.1"}, "dependencies": {"no-case": "^2.2.0"}}