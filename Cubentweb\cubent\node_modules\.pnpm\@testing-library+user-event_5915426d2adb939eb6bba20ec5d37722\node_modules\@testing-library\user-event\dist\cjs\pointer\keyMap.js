'use strict';

const defaultKeyMap = [
    {
        name: '<PERSON><PERSON>ef<PERSON>',
        pointerType: 'mouse',
        button: 'primary'
    },
    {
        name: 'MouseRight',
        pointerType: 'mouse',
        button: 'secondary'
    },
    {
        name: 'MouseM<PERSON>',
        pointerType: 'mouse',
        button: 'auxiliary'
    },
    {
        name: 'TouchA',
        pointerType: 'touch'
    },
    {
        name: 'TouchB',
        pointerType: 'touch'
    },
    {
        name: 'TouchC',
        pointerType: 'touch'
    }
];

exports.defaultKeyMap = defaultKeyMap;
