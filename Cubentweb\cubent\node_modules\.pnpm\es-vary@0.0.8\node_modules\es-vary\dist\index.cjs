"use strict";Object.defineProperty(exports, "__esModule", {value: true});const FIELD_NAME_REGEXP=/^[!#$%&'*+\-.^_`|~0-9A-Za-z]+$/;function parse(header){let end=0;let list=[];let start=0;for(var i=0,len=header.length;i<len;i++){switch(header.charCodeAt(i)){case 32:if(start===end){start=end=i+1}break;case 44:list.push(header.substring(start,end));start=end=i+1;break;default:end=i+1;break}}list.push(header.substring(start,end));return list}function append(header,field){const fields=!Array.isArray(field)?parse(String(field)):field;for(const field2 of fields){if(!FIELD_NAME_REGEXP.test(field2)){throw new TypeError("field argument contains an invalid header name")}}if(header==="*"){return header}let val=header;const vals=parse(header.toLowerCase());if(fields.indexOf("*")!==-1||vals.indexOf("*")!==-1){return"*"}for(const field2 of fields){const fld=field2.toLowerCase();if(vals.indexOf(fld)===-1){vals.push(fld);val=val?val+", "+field2:field2}}return val}function vary(res,field){if(!res||!res.getHeader||!res.setHeader){throw new TypeError("res argument is required")}let val=res.getHeader("Vary")||"";const header=Array.isArray(val)?val.join(", "):String(val);if(val=append(header,field)){res.setHeader("Vary",val)}}exports.append = append; exports.vary = vary;
