{"version": 3, "file": "memory.js", "sourceRoot": "", "sources": ["../ts/memory.ts"], "names": [], "mappings": ";;;AAOA,yCAAqC;AAGrC;;GAEG;AACH,MAAa,YAAY;IACvB,YAAmB,MAAoB;QAApB,WAAM,GAAN,MAAM,CAAc;IAAG,CAAC;IAE3C,cAAc,CAAC,WAA4B;QACzC,MAAM,UAAU,GAAG,IAAI,UAAU,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAA;QAC5E,MAAM,QAAQ,GAAG,UAAU,CAAC,MAAM,GAAG,UAAU,CAAC,iBAAiB,CAAA;QACjE,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,QAAQ,CAA+B,CAAA;QACvE,IAAI,SAAS,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,GAAG,EAAE,QAAQ,CAAC,CAAA;QACxE,SAAS,CAAC,GAAG,CAAC,IAAI,UAAU,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAA;QAChD,OAAO,IAAI,mBAAQ,CAAC,GAAG,EAAE,SAAS,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAA;IACtE,CAAC;IAED,sBAAsB,CACpB,MAAc;QAEd,MAAM,KAAK,GAAG,IAAI,UAAU,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAA;QACvD,MAAM,QAAQ,GAAG,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,iBAAiB,CAAA;QACvD,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAM,CAAA;QAC9C,MAAM,UAAU,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,GAAG,EAAE,MAAM,CAAC,CAAA;QACzE,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,CAAA;QACrB,OAAO,IAAI,mBAAQ,CAAC,EAAE,UAAU,EAAE,GAAG,EAAE,EAAE,SAAS,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAA;IAC9F,CAAC;IAED,kBAAkB,CAAC,MAAc;QAC/B,MAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,MAAM,CAAC,GAAG,CAAC,CAAA;QACxD,MAAM,GAAG,GAAyB,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAyB,CAAA;QACvF,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,MAAM,EAAE,GAAG,EAAE,QAAQ,CAAC,CAAA;QAC/C,OAAO,IAAI,mBAAQ,CAAC,GAAG,EAAE,SAAS,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAA;IAC1E,CAAC;IAED,sBAAsB,CAAC,GAAyB;QAC9C,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,GAAG,CAAC,CAAA;QACzC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;QACtB,OAAO,GAAG,CAAA;IACZ,CAAC;CACF;AAnCD,oCAmCC", "sourcesContent": ["import { EitherModule } from \"./emscripten-types\"\nimport {\n  OwnedHeapChar<PERSON>ointer,\n  JSContextPointerPointer,\n  JSValueConstPointerPointer,\n  JSValuePointerPointer,\n} from \"./types-ffi\"\nimport { Lifetime } from \"./lifetime\"\nimport { <PERSON><PERSON><PERSON>, QuickJSHandle } from \"./types\"\n\n/**\n * @private\n */\nexport class ModuleMemory {\n  constructor(public module: EitherModule) {}\n\n  toPointerArray(handleArray: QuickJSHandle[]): Lifetime<JSValueConstPointerPointer> {\n    const typedArray = new Int32Array(handleArray.map((handle) => handle.value))\n    const numBytes = typedArray.length * typedArray.BYTES_PER_ELEMENT\n    const ptr = this.module._malloc(numBytes) as JSValueConstPointerPointer\n    var heapBytes = new Uint8Array(this.module.HEAPU8.buffer, ptr, numBytes)\n    heapBytes.set(new Uint8Array(typedArray.buffer))\n    return new Lifetime(ptr, undefined, (ptr) => this.module._free(ptr))\n  }\n\n  newMutablePointerArray<T extends JSContextPointerPointer | JSValuePointerPointer>(\n    length: number\n  ): Lifetime<{ typedArray: Int32Array; ptr: T }> {\n    const zeros = new Int32Array(new Array(length).fill(0))\n    const numBytes = zeros.length * zeros.BYTES_PER_ELEMENT\n    const ptr = this.module._malloc(numBytes) as T\n    const typedArray = new Int32Array(this.module.HEAPU8.buffer, ptr, length)\n    typedArray.set(zeros)\n    return new Lifetime({ typedArray, ptr }, undefined, (value) => this.module._free(value.ptr))\n  }\n\n  newHeapCharPointer(string: string): Lifetime<OwnedHeapCharPointer> {\n    const numBytes = this.module.lengthBytesUTF8(string) + 1\n    const ptr: OwnedHeapCharPointer = this.module._malloc(numBytes) as OwnedHeapCharPointer\n    this.module.stringToUTF8(string, ptr, numBytes)\n    return new Lifetime(ptr, undefined, (value) => this.module._free(value))\n  }\n\n  consumeHeapCharPointer(ptr: OwnedHeapCharPointer): string {\n    const str = this.module.UTF8ToString(ptr)\n    this.module._free(ptr)\n    return str\n  }\n}\n"]}