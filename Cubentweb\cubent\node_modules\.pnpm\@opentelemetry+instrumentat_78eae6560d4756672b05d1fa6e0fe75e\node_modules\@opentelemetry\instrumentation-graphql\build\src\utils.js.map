{"version": 3, "file": "utils.js", "sourceRoot": "", "sources": ["../../src/utils.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAGH,0CAA0C;AAC1C,iCAAqE;AACrE,2DAAwD;AACxD,uCAA0E;AAU1E,MAAM,gBAAgB,GAAG,MAAM,CAAC,MAAM,CAAC,4BAAqB,CAAC,CAAC;AAE9D,2EAA2E;AACpE,MAAM,SAAS,GAAG,CAAC,KAAU,EAA6B,EAAE;IACjE,OAAO,OAAO,CAAA,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,IAAI,CAAA,KAAK,UAAU,CAAC;AAC3C,CAAC,CAAC;AAFW,QAAA,SAAS,aAEpB;AAEF,8EAA8E;AAC9E,MAAM,YAAY,GAAG,CAAC,KAAc,EAAuC,EAAE;IAC3E,OAAO,OAAO,KAAK,IAAI,QAAQ,IAAI,KAAK,KAAK,IAAI,CAAC;AACpD,CAAC,CAAC;AAEF,8DAA8D;AAC9D,SAAS,yBAAyB,CAAC,IAAc,EAAE,GAAW,EAAE,QAAa;IAC3E,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;QAC3B,QAAQ,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE;YAC9B,yBAAyB,CAAC,IAAI,EAAE,GAAG,GAAG,IAAI,GAAG,EAAE,EAAE,KAAK,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;KACJ;SAAM,IAAI,QAAQ,YAAY,MAAM,EAAE;QACrC,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,EAAE,KAAK,CAAC,EAAE,EAAE;YACtD,yBAAyB,CAAC,IAAI,EAAE,GAAG,GAAG,IAAI,SAAS,EAAE,EAAE,KAAK,CAAC,CAAC;QAChE,CAAC,CAAC,CAAC;KACJ;SAAM;QACL,IAAI,CAAC,YAAY,CAAC,GAAG,+BAAc,CAAC,SAAS,GAAG,MAAM,CAAC,GAAG,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;KAC1E;AACH,CAAC;AAED,8DAA8D;AAC9D,SAAgB,0BAA0B,CACxC,IAAc,EACd,cAAsC;IAEtC,MAAM,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE;QACtD,yBAAyB,CAAC,IAAI,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;IAC9C,CAAC,CAAC,CAAC;AACL,CAAC;AAPD,gEAOC;AAED,SAAgB,aAAa,CAC3B,IAAc,EACd,GAA2B,EAC3B,WAAqB,EACrB,KAAc,EACd,GAAY;IAEZ,MAAM,MAAM,GAAG,qBAAqB,CAAC,GAAG,EAAE,WAAW,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC;IACnE,IAAI,CAAC,YAAY,CAAC,+BAAc,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;AACnD,CAAC;AATD,sCASC;AAED,SAAS,sBAAsB,CAC7B,MAAkB,EAClB,SAAmD,EACnD,YAAiB,EACjB,IAAqC,EACrC,IAAc;IAKd,IAAI,KAAK,GAAG,QAAQ,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;IAEzC,IAAI,SAAS,GAAG,KAAK,CAAC;IAEtB,IAAI,CAAC,KAAK,EAAE;QACV,SAAS,GAAG,IAAI,CAAC;QACjB,MAAM,MAAM,GAAG,cAAc,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;QAElD,KAAK,GAAG;YACN,MAAM;YACN,IAAI,EAAE,kBAAkB,CACtB,MAAM,EACN,SAAS,EACT,YAAY,EACZ,IAAI,EACJ,IAAI,EACJ,MAAM,CAAC,IAAI,CACZ;YACD,KAAK,EAAE,IAAI;SACZ,CAAC;QAEF,QAAQ,CAAC,YAAY,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;KACrC;IAED,OAAO,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC;AAC9B,CAAC;AAED,SAAS,kBAAkB,CACzB,MAAkB,EAClB,SAAmD,EACnD,YAAiB,EACjB,IAAqC,EACrC,IAAc,EACd,UAAqB;;IAErB,MAAM,UAAU,GAAuB;QACrC,CAAC,+BAAc,CAAC,UAAU,CAAC,EAAE,IAAI,CAAC,SAAS;QAC3C,CAAC,+BAAc,CAAC,UAAU,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC;QAC3C,CAAC,+BAAc,CAAC,UAAU,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE;KACxD,CAAC;IAEF,MAAM,IAAI,GAAG,MAAM,CAAC,SAAS,CAC3B,GAAG,gBAAS,CAAC,OAAO,IAAI,UAAU,CAAC,+BAAc,CAAC,UAAU,CAAC,EAAE,EAC/D;QACE,UAAU;KACX,EACD,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC,SAAS,CAC7E,CAAC;IAEF,MAAM,QAAQ,GAAG,YAAY,CAAC,kCAAwB,CAAC,CAAC,MAAM,CAAC;IAC/D,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CACpC,SAAS,CAAC,EAAE,CAAC,SAAS,CAAC,IAAI,KAAK,OAAO,CACxC,CAAC;IAEF,IAAI,SAAS,EAAE;QACb,aAAa,CACX,IAAI,EACJ,QAAQ,CAAC,GAAG,EACZ,SAAS,EAAE,CAAC,WAAW,EACvB,MAAA,SAAS,CAAC,GAAG,0CAAE,KAAK,EACpB,MAAA,SAAS,CAAC,GAAG,0CAAE,GAAG,CACnB,CAAC;KACH;IAED,OAAO,IAAI,CAAC;AACd,CAAC;AAED,SAAgB,OAAO,CAAC,IAAc,EAAE,KAAa;IACnD,IAAI,KAAK,EAAE;QACT,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;KAC7B;IACD,IAAI,CAAC,GAAG,EAAE,CAAC;AACb,CAAC;AALD,0BAKC;AAED,SAAgB,YAAY,CAC1B,QAAmC,EACnC,aAA6B;IAE7B,IAAI,CAAC,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE;QACrD,OAAO,SAAS,CAAC;KAClB;IAED,IAAI,aAAa,EAAE;QACjB,OAAO,QAAQ,CAAC,WAAW;aACxB,MAAM,CACL,UAAU,CAAC,EAAE,WACX,OAAA,gBAAgB,CAAC,OAAO,CAAC,MAAC,UAAkB,0CAAE,SAAS,CAAC,KAAK,CAAC,CAAC,CAAA,EAAA,CAClE;aACA,IAAI,CAAC,UAAU,CAAC,EAAE,eAAC,OAAA,aAAa,MAAK,MAAA,MAAC,UAAkB,0CAAE,IAAI,0CAAE,KAAK,CAAA,CAAA,EAAA,CAAC,CAAC;KAC3E;SAAM;QACL,OAAO,QAAQ,CAAC,WAAW,CAAC,IAAI,CAC9B,UAAU,CAAC,EAAE,WACX,OAAA,gBAAgB,CAAC,OAAO,CAAC,MAAC,UAAkB,0CAAE,SAAS,CAAC,KAAK,CAAC,CAAC,CAAA,EAAA,CAClE,CAAC;KACH;AACH,CAAC;AArBD,oCAqBC;AAED,SAAS,QAAQ,CAAC,YAAiB,EAAE,IAAc,EAAE,KAAmB;IACtE,OAAO,CAAC,YAAY,CAAC,kCAAwB,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACnE,KAAK,CAAC,CAAC;AACX,CAAC;AAED,SAAS,QAAQ,CAAC,YAAiB,EAAE,IAAc;IACjD,OAAO,YAAY,CAAC,kCAAwB,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;AACvE,CAAC;AAED,SAAS,cAAc,CAAC,YAAiB,EAAE,IAAc;IACvD,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;QACxC,MAAM,KAAK,GAAG,QAAQ,CAAC,YAAY,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAEvD,IAAI,KAAK,EAAE;YACT,OAAO,KAAK,CAAC;SACd;KACF;IAED,OAAO;QACL,IAAI,EAAE,YAAY,CAAC,kCAAwB,CAAC,CAAC,IAAI;KAClD,CAAC;AACJ,CAAC;AAED,SAAS,WAAW,CAAC,UAAmB,EAAE,IAAiB;IACzD,MAAM,SAAS,GAAa,EAAE,CAAC;IAC/B,IAAI,IAAI,GAA4B,IAAI,CAAC;IACzC,OAAO,IAAI,EAAE;QACX,IAAI,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;QAEnB,IAAI,UAAU,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;YACzC,GAAG,GAAG,GAAG,CAAC;SACX;QACD,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;QAC5B,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;KAClB;IACD,OAAO,SAAS,CAAC,OAAO,EAAE,CAAC;AAC7B,CAAC;AAED,SAAS,WAAW,CAAC,CAAS;IAC5B,OAAO,UAAU,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;AAC7B,CAAC;AAED,SAAS,WAAW,CAAC,CAAS;IAC5B,OAAO,UAAU,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;AAC5B,CAAC;AAED,SAAS,UAAU,CAAC,IAAY,EAAE,EAAU;IAC1C,IAAI,IAAI,GAAG,EAAE,CAAC;IACd,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE;QAC3B,IAAI,IAAI,IAAI,CAAC;KACd;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAED,MAAM,gBAAgB,GAAa;IACjC,gBAAS,CAAC,KAAK;IACf,gBAAS,CAAC,MAAM;IAChB,gBAAS,CAAC,GAAG;IACb,gBAAS,CAAC,YAAY;CACvB,CAAC;AAEF,SAAgB,qBAAqB,CACnC,GAA2B,EAC3B,WAAW,GAAG,KAAK,EACnB,UAAmB,EACnB,QAAiB;;IAEjB,IAAI,MAAM,GAAG,EAAE,CAAC;IAEhB,IAAI,GAAG,aAAH,GAAG,uBAAH,GAAG,CAAE,UAAU,EAAE;QACnB,MAAM,KAAK,GAAG,OAAO,UAAU,KAAK,QAAQ,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC;QACtE,MAAM,GAAG,GAAG,OAAO,QAAQ,KAAK,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;QAE9D,IAAI,IAAI,GAA8B,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC;QAC1D,IAAI,YAAY,GAAuB,CAAC,CAAC;QACzC,OAAO,IAAI,EAAE;YACX,IAAI,IAAI,CAAC,KAAK,GAAG,KAAK,EAAE;gBACtB,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;gBACjB,YAAY,GAAG,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,IAAI,CAAC;gBAC1B,SAAS;aACV;YACD,IAAI,IAAI,CAAC,GAAG,GAAG,GAAG,EAAE;gBAClB,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;gBACjB,YAAY,GAAG,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,IAAI,CAAC;gBAC1B,SAAS;aACV;YACD,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,IAAI,CAAC;YACpC,IAAI,KAAK,GAAG,EAAE,CAAC;YACf,IAAI,CAAC,WAAW,IAAI,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;gBAC5D,yCAAyC;gBACzC,KAAK,GAAG,GAAG,CAAC;aACb;YACD,IAAI,IAAI,CAAC,IAAI,KAAK,gBAAS,CAAC,MAAM,EAAE;gBAClC,KAAK,GAAG,IAAI,KAAK,GAAG,CAAC;aACtB;YACD,IAAI,IAAI,CAAC,IAAI,KAAK,gBAAS,CAAC,GAAG,EAAE;gBAC/B,KAAK,GAAG,EAAE,CAAC;aACZ;YACD,IAAI,IAAI,CAAC,IAAI,GAAG,YAAa,EAAE;gBAC7B,MAAM,IAAI,WAAW,CAAC,IAAI,CAAC,IAAI,GAAG,YAAa,CAAC,CAAC;gBACjD,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC;gBACzB,KAAK,GAAG,WAAW,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;aACtC;iBAAM;gBACL,IAAI,IAAI,CAAC,IAAI,MAAK,MAAA,IAAI,CAAC,IAAI,0CAAE,IAAI,CAAA,EAAE;oBACjC,KAAK,GAAG,WAAW,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,CAAA,MAAA,IAAI,CAAC,IAAI,0CAAE,GAAG,KAAI,CAAC,CAAC,CAAC,CAAC;iBACzD;aACF;YACD,MAAM,IAAI,KAAK,GAAG,KAAK,CAAC;YACxB,IAAI,IAAI,EAAE;gBACR,IAAI,GAAG,IAAI,CAAC,IAAK,CAAC;aACnB;SACF;KACF;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAtDD,sDAsDC;AAED,SAAgB,UAAU,CACxB,IAAyD,EACzD,MAAkB,EAClB,SAAmD;IAEnD,IACE,CAAC,IAAI;QACL,OAAO,IAAI,CAAC,SAAS,KAAK,UAAU;QACpC,IAAI,CAAC,6BAAmB,CAAC,EACzB;QACA,OAAO;KACR;IACD,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;IAEhC,IAAI,CAAC,6BAAmB,CAAC,GAAG,IAAI,CAAC;IAEjC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;QAChC,MAAM,KAAK,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;QAE1B,IAAI,CAAC,KAAK,EAAE;YACV,OAAO;SACR;QAED,IAAI,KAAK,CAAC,OAAO,EAAE;YACjB,KAAK,CAAC,OAAO,GAAG,iBAAiB,CAAC,MAAM,EAAE,SAAS,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;SACrE;QAED,IAAI,KAAK,CAAC,IAAI,EAAE;YACd,IAAI,aAAa,GAAQ,KAAK,CAAC,IAAI,CAAC;YAEpC,OAAO,aAAa,CAAC,MAAM,EAAE;gBAC3B,aAAa,GAAG,aAAa,CAAC,MAAM,CAAC;aACtC;YACD,UAAU,CAAC,aAAa,EAAE,MAAM,EAAE,SAAS,CAAC,CAAC;SAC9C;IACH,CAAC,CAAC,CAAC;AACL,CAAC;AApCD,gCAoCC;AAED,MAAM,sBAAsB,GAAG,CAC7B,WAAqB,EACrB,GAAQ,EACR,aAAsB,EACtB,EAAE;IACF,IAAI,CAAC,aAAa,EAAE;QAClB,OAAO;KACR;IACD,WAAW,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;IACjC,WAAW,CAAC,SAAS,CAAC;QACpB,IAAI,EAAE,GAAG,CAAC,cAAc,CAAC,KAAK;QAC9B,OAAO,EAAE,GAAG,CAAC,OAAO;KACrB,CAAC,CAAC;IACH,WAAW,CAAC,GAAG,EAAE,CAAC;AACpB,CAAC,CAAC;AAEF,MAAM,wBAAwB,GAAG,CAC/B,WAAqB,EACrB,aAAsB,EACtB,EAAE;IACF,IAAI,CAAC,aAAa,EAAE;QAClB,OAAO;KACR;IACD,WAAW,CAAC,GAAG,EAAE,CAAC;AACpB,CAAC,CAAC;AAEF,SAAgB,iBAAiB,CAC/B,MAAkB,EAClB,SAAmD,EACnD,aAEC,EACD,iBAAiB,GAAG,KAAK;IAOzB,IACG,oBAAoC,CAAC,6BAAmB,CAAC;QAC1D,OAAO,aAAa,KAAK,UAAU,EACnC;QACA,OAAO,aAAc,CAAC;KACvB;IAED,SAAS,oBAAoB,CAE3B,MAAe,EACf,IAAW,EACX,YAA8C,EAC9C,IAAqC;QAErC,IAAI,CAAC,aAAa,EAAE;YAClB,OAAO,SAAS,CAAC;SAClB;QACD,MAAM,MAAM,GAAG,SAAS,EAAE,CAAC;QAE3B,wEAAwE;QACxE,mDAAmD;QACnD,IACE,MAAM,CAAC,yBAAyB;YAChC,iBAAiB;YACjB,CAAC,YAAY,CAAC,MAAM,CAAC,IAAI,OAAO,MAAM,KAAK,UAAU,CAAC,EACtD;YACA,MAAM,QAAQ,GAAI,MAAc,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YACjD,8DAA8D;YAC9D,6FAA6F;YAC7F,IAAI,OAAO,QAAQ,KAAK,UAAU,EAAE;gBAClC,OAAO,aAAa,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,CAAC,CAAC;aACnE;SACF;QAED,IAAI,CAAC,YAAY,CAAC,kCAAwB,CAAC,EAAE;YAC3C,OAAO,aAAa,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,CAAC,CAAC;SACnE;QACD,MAAM,IAAI,GAAG,WAAW,CAAC,MAAM,CAAC,UAAU,EAAE,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC;QAC/D,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,IAAS,EAAE,EAAE,CAAC,OAAO,IAAI,KAAK,QAAQ,CAAC,CAAC,MAAM,CAAC;QAE1E,IAAI,KAAU,CAAC;QACf,IAAI,aAAa,GAAG,KAAK,CAAC;QAC1B,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC,IAAI,MAAM,CAAC,KAAK,GAAG,KAAK,EAAE;YAC7C,KAAK,GAAG,cAAc,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;SAC5C;aAAM;YACL,MAAM,QAAQ,GAAG,sBAAsB,CACrC,MAAM,EACN,SAAS,EACT,YAAY,EACZ,IAAI,EACJ,IAAI,CACL,CAAC;YACF,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC;YACvB,aAAa,GAAG,QAAQ,CAAC,SAAS,CAAC;SACpC;QAED,OAAO,GAAG,CAAC,OAAO,CAAC,IAAI,CACrB,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,KAAK,CAAC,IAAI,CAAC,EACnD,GAAG,EAAE;YACH,IAAI;gBACF,MAAM,GAAG,GAAG,aAAa,CAAC,IAAI,CAC5B,IAAI,EACJ,MAAM,EACN,IAAI,EACJ,YAAY,EACZ,IAAI,CACL,CAAC;gBACF,IAAI,IAAA,iBAAS,EAAC,GAAG,CAAC,EAAE;oBAClB,OAAO,GAAG,CAAC,IAAI,CACb,CAAC,CAAM,EAAE,EAAE;wBACT,wBAAwB,CAAC,KAAK,CAAC,IAAI,EAAE,aAAa,CAAC,CAAC;wBACpD,OAAO,CAAC,CAAC;oBACX,CAAC,EACD,CAAC,GAAU,EAAE,EAAE;wBACb,sBAAsB,CAAC,KAAK,CAAC,IAAI,EAAE,GAAG,EAAE,aAAa,CAAC,CAAC;wBACvD,MAAM,GAAG,CAAC;oBACZ,CAAC,CACF,CAAC;iBACH;qBAAM;oBACL,wBAAwB,CAAC,KAAK,CAAC,IAAI,EAAE,aAAa,CAAC,CAAC;oBACpD,OAAO,GAAG,CAAC;iBACZ;aACF;YAAC,OAAO,GAAQ,EAAE;gBACjB,sBAAsB,CAAC,KAAK,CAAC,IAAI,EAAE,GAAG,EAAE,aAAa,CAAC,CAAC;gBACvD,MAAM,GAAG,CAAC;aACX;QACH,CAAC,CACF,CAAC;IACJ,CAAC;IAEA,oBAAoC,CAAC,6BAAmB,CAAC,GAAG,IAAI,CAAC;IAElE,OAAO,oBAAoB,CAAC;AAC9B,CAAC;AA1GD,8CA0GC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport type * as graphqlTypes from 'graphql';\nimport * as api from '@opentelemetry/api';\nimport { AllowedOperationTypes, SpanNames, TokenKind } from './enum';\nimport { AttributeNames } from './enums/AttributeNames';\nimport { OTEL_GRAPHQL_DATA_SYMBOL, OTEL_PATCHED_SYMBOL } from './symbols';\nimport {\n  GraphQLField,\n  GraphQLPath,\n  ObjectWithGraphQLData,\n  OtelPatched,\n  Maybe,\n} from './internal-types';\nimport { GraphQLInstrumentationParsedConfig } from './types';\n\nconst OPERATION_VALUES = Object.values(AllowedOperationTypes);\n\n// https://github.com/graphql/graphql-js/blob/main/src/jsutils/isPromise.ts\nexport const isPromise = (value: any): value is Promise<unknown> => {\n  return typeof value?.then === 'function';\n};\n\n// https://github.com/graphql/graphql-js/blob/main/src/jsutils/isObjectLike.ts\nconst isObjectLike = (value: unknown): value is { [key: string]: unknown } => {\n  return typeof value == 'object' && value !== null;\n};\n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction addInputVariableAttribute(span: api.Span, key: string, variable: any) {\n  if (Array.isArray(variable)) {\n    variable.forEach((value, idx) => {\n      addInputVariableAttribute(span, `${key}.${idx}`, value);\n    });\n  } else if (variable instanceof Object) {\n    Object.entries(variable).forEach(([nestedKey, value]) => {\n      addInputVariableAttribute(span, `${key}.${nestedKey}`, value);\n    });\n  } else {\n    span.setAttribute(`${AttributeNames.VARIABLES}${String(key)}`, variable);\n  }\n}\n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport function addInputVariableAttributes(\n  span: api.Span,\n  variableValues: { [key: string]: any }\n) {\n  Object.entries(variableValues).forEach(([key, value]) => {\n    addInputVariableAttribute(span, key, value);\n  });\n}\n\nexport function addSpanSource(\n  span: api.Span,\n  loc?: graphqlTypes.Location,\n  allowValues?: boolean,\n  start?: number,\n  end?: number\n): void {\n  const source = getSourceFromLocation(loc, allowValues, start, end);\n  span.setAttribute(AttributeNames.SOURCE, source);\n}\n\nfunction createFieldIfNotExists(\n  tracer: api.Tracer,\n  getConfig: () => GraphQLInstrumentationParsedConfig,\n  contextValue: any,\n  info: graphqlTypes.GraphQLResolveInfo,\n  path: string[]\n): {\n  field: any;\n  spanAdded: boolean;\n} {\n  let field = getField(contextValue, path);\n\n  let spanAdded = false;\n\n  if (!field) {\n    spanAdded = true;\n    const parent = getParentField(contextValue, path);\n\n    field = {\n      parent,\n      span: createResolverSpan(\n        tracer,\n        getConfig,\n        contextValue,\n        info,\n        path,\n        parent.span\n      ),\n      error: null,\n    };\n\n    addField(contextValue, path, field);\n  }\n\n  return { spanAdded, field };\n}\n\nfunction createResolverSpan(\n  tracer: api.Tracer,\n  getConfig: () => GraphQLInstrumentationParsedConfig,\n  contextValue: any,\n  info: graphqlTypes.GraphQLResolveInfo,\n  path: string[],\n  parentSpan?: api.Span\n): api.Span {\n  const attributes: api.SpanAttributes = {\n    [AttributeNames.FIELD_NAME]: info.fieldName,\n    [AttributeNames.FIELD_PATH]: path.join('.'),\n    [AttributeNames.FIELD_TYPE]: info.returnType.toString(),\n  };\n\n  const span = tracer.startSpan(\n    `${SpanNames.RESOLVE} ${attributes[AttributeNames.FIELD_PATH]}`,\n    {\n      attributes,\n    },\n    parentSpan ? api.trace.setSpan(api.context.active(), parentSpan) : undefined\n  );\n\n  const document = contextValue[OTEL_GRAPHQL_DATA_SYMBOL].source;\n  const fieldNode = info.fieldNodes.find(\n    fieldNode => fieldNode.kind === 'Field'\n  );\n\n  if (fieldNode) {\n    addSpanSource(\n      span,\n      document.loc,\n      getConfig().allowValues,\n      fieldNode.loc?.start,\n      fieldNode.loc?.end\n    );\n  }\n\n  return span;\n}\n\nexport function endSpan(span: api.Span, error?: Error): void {\n  if (error) {\n    span.recordException(error);\n  }\n  span.end();\n}\n\nexport function getOperation(\n  document: graphqlTypes.DocumentNode,\n  operationName?: Maybe<string>\n): graphqlTypes.DefinitionNode | undefined {\n  if (!document || !Array.isArray(document.definitions)) {\n    return undefined;\n  }\n\n  if (operationName) {\n    return document.definitions\n      .filter(\n        definition =>\n          OPERATION_VALUES.indexOf((definition as any)?.operation) !== -1\n      )\n      .find(definition => operationName === (definition as any)?.name?.value);\n  } else {\n    return document.definitions.find(\n      definition =>\n        OPERATION_VALUES.indexOf((definition as any)?.operation) !== -1\n    );\n  }\n}\n\nfunction addField(contextValue: any, path: string[], field: GraphQLField) {\n  return (contextValue[OTEL_GRAPHQL_DATA_SYMBOL].fields[path.join('.')] =\n    field);\n}\n\nfunction getField(contextValue: any, path: string[]) {\n  return contextValue[OTEL_GRAPHQL_DATA_SYMBOL].fields[path.join('.')];\n}\n\nfunction getParentField(contextValue: any, path: string[]) {\n  for (let i = path.length - 1; i > 0; i--) {\n    const field = getField(contextValue, path.slice(0, i));\n\n    if (field) {\n      return field;\n    }\n  }\n\n  return {\n    span: contextValue[OTEL_GRAPHQL_DATA_SYMBOL].span,\n  };\n}\n\nfunction pathToArray(mergeItems: boolean, path: GraphQLPath): string[] {\n  const flattened: string[] = [];\n  let curr: GraphQLPath | undefined = path;\n  while (curr) {\n    let key = curr.key;\n\n    if (mergeItems && typeof key === 'number') {\n      key = '*';\n    }\n    flattened.push(String(key));\n    curr = curr.prev;\n  }\n  return flattened.reverse();\n}\n\nfunction repeatBreak(i: number): string {\n  return repeatChar('\\n', i);\n}\n\nfunction repeatSpace(i: number): string {\n  return repeatChar(' ', i);\n}\n\nfunction repeatChar(char: string, to: number): string {\n  let text = '';\n  for (let i = 0; i < to; i++) {\n    text += char;\n  }\n  return text;\n}\n\nconst KindsToBeRemoved: string[] = [\n  TokenKind.FLOAT,\n  TokenKind.STRING,\n  TokenKind.INT,\n  TokenKind.BLOCK_STRING,\n];\n\nexport function getSourceFromLocation(\n  loc?: graphqlTypes.Location,\n  allowValues = false,\n  inputStart?: number,\n  inputEnd?: number\n): string {\n  let source = '';\n\n  if (loc?.startToken) {\n    const start = typeof inputStart === 'number' ? inputStart : loc.start;\n    const end = typeof inputEnd === 'number' ? inputEnd : loc.end;\n\n    let next: graphqlTypes.Token | null = loc.startToken.next;\n    let previousLine: number | undefined = 1;\n    while (next) {\n      if (next.start < start) {\n        next = next.next;\n        previousLine = next?.line;\n        continue;\n      }\n      if (next.end > end) {\n        next = next.next;\n        previousLine = next?.line;\n        continue;\n      }\n      let value = next.value || next.kind;\n      let space = '';\n      if (!allowValues && KindsToBeRemoved.indexOf(next.kind) >= 0) {\n        // value = repeatChar('*', value.length);\n        value = '*';\n      }\n      if (next.kind === TokenKind.STRING) {\n        value = `\"${value}\"`;\n      }\n      if (next.kind === TokenKind.EOF) {\n        value = '';\n      }\n      if (next.line > previousLine!) {\n        source += repeatBreak(next.line - previousLine!);\n        previousLine = next.line;\n        space = repeatSpace(next.column - 1);\n      } else {\n        if (next.line === next.prev?.line) {\n          space = repeatSpace(next.start - (next.prev?.end || 0));\n        }\n      }\n      source += space + value;\n      if (next) {\n        next = next.next!;\n      }\n    }\n  }\n\n  return source;\n}\n\nexport function wrapFields(\n  type: Maybe<graphqlTypes.GraphQLObjectType & OtelPatched>,\n  tracer: api.Tracer,\n  getConfig: () => GraphQLInstrumentationParsedConfig\n): void {\n  if (\n    !type ||\n    typeof type.getFields !== 'function' ||\n    type[OTEL_PATCHED_SYMBOL]\n  ) {\n    return;\n  }\n  const fields = type.getFields();\n\n  type[OTEL_PATCHED_SYMBOL] = true;\n\n  Object.keys(fields).forEach(key => {\n    const field = fields[key];\n\n    if (!field) {\n      return;\n    }\n\n    if (field.resolve) {\n      field.resolve = wrapFieldResolver(tracer, getConfig, field.resolve);\n    }\n\n    if (field.type) {\n      let unwrappedType: any = field.type;\n\n      while (unwrappedType.ofType) {\n        unwrappedType = unwrappedType.ofType;\n      }\n      wrapFields(unwrappedType, tracer, getConfig);\n    }\n  });\n}\n\nconst handleResolveSpanError = (\n  resolveSpan: api.Span,\n  err: any,\n  shouldEndSpan: boolean\n) => {\n  if (!shouldEndSpan) {\n    return;\n  }\n  resolveSpan.recordException(err);\n  resolveSpan.setStatus({\n    code: api.SpanStatusCode.ERROR,\n    message: err.message,\n  });\n  resolveSpan.end();\n};\n\nconst handleResolveSpanSuccess = (\n  resolveSpan: api.Span,\n  shouldEndSpan: boolean\n) => {\n  if (!shouldEndSpan) {\n    return;\n  }\n  resolveSpan.end();\n};\n\nexport function wrapFieldResolver<TSource = any, TContext = any, TArgs = any>(\n  tracer: api.Tracer,\n  getConfig: () => GraphQLInstrumentationParsedConfig,\n  fieldResolver: Maybe<\n    graphqlTypes.GraphQLFieldResolver<TSource, TContext, TArgs> & OtelPatched\n  >,\n  isDefaultResolver = false\n): graphqlTypes.GraphQLFieldResolver<\n  TSource,\n  TContext & ObjectWithGraphQLData,\n  TArgs\n> &\n  OtelPatched {\n  if (\n    (wrappedFieldResolver as OtelPatched)[OTEL_PATCHED_SYMBOL] ||\n    typeof fieldResolver !== 'function'\n  ) {\n    return fieldResolver!;\n  }\n\n  function wrappedFieldResolver(\n    this: graphqlTypes.GraphQLFieldResolver<TSource, TContext, TArgs>,\n    source: TSource,\n    args: TArgs,\n    contextValue: TContext & ObjectWithGraphQLData,\n    info: graphqlTypes.GraphQLResolveInfo\n  ) {\n    if (!fieldResolver) {\n      return undefined;\n    }\n    const config = getConfig();\n\n    // follows what graphql is doing to decide if this is a trivial resolver\n    // for which we don't need to create a resolve span\n    if (\n      config.ignoreTrivialResolveSpans &&\n      isDefaultResolver &&\n      (isObjectLike(source) || typeof source === 'function')\n    ) {\n      const property = (source as any)[info.fieldName];\n      // a function execution is not trivial and should be recorder.\n      // property which is not a function is just a value and we don't want a \"resolve\" span for it\n      if (typeof property !== 'function') {\n        return fieldResolver.call(this, source, args, contextValue, info);\n      }\n    }\n\n    if (!contextValue[OTEL_GRAPHQL_DATA_SYMBOL]) {\n      return fieldResolver.call(this, source, args, contextValue, info);\n    }\n    const path = pathToArray(config.mergeItems, info && info.path);\n    const depth = path.filter((item: any) => typeof item === 'string').length;\n\n    let field: any;\n    let shouldEndSpan = false;\n    if (config.depth >= 0 && config.depth < depth) {\n      field = getParentField(contextValue, path);\n    } else {\n      const newField = createFieldIfNotExists(\n        tracer,\n        getConfig,\n        contextValue,\n        info,\n        path\n      );\n      field = newField.field;\n      shouldEndSpan = newField.spanAdded;\n    }\n\n    return api.context.with(\n      api.trace.setSpan(api.context.active(), field.span),\n      () => {\n        try {\n          const res = fieldResolver.call(\n            this,\n            source,\n            args,\n            contextValue,\n            info\n          );\n          if (isPromise(res)) {\n            return res.then(\n              (r: any) => {\n                handleResolveSpanSuccess(field.span, shouldEndSpan);\n                return r;\n              },\n              (err: Error) => {\n                handleResolveSpanError(field.span, err, shouldEndSpan);\n                throw err;\n              }\n            );\n          } else {\n            handleResolveSpanSuccess(field.span, shouldEndSpan);\n            return res;\n          }\n        } catch (err: any) {\n          handleResolveSpanError(field.span, err, shouldEndSpan);\n          throw err;\n        }\n      }\n    );\n  }\n\n  (wrappedFieldResolver as OtelPatched)[OTEL_PATCHED_SYMBOL] = true;\n\n  return wrappedFieldResolver;\n}\n"]}