{"version": 3, "file": "runtime.js", "sourceRoot": "", "sources": ["../ts/runtime.ts"], "names": [], "mappings": ";;;AAAA,yDAAiD;AACjD,uCAA0C;AAC1C,mCAAkC;AAElC,qCAA4C;AAQ5C,yCAAwD;AACxD,qCAAuC;AAEvC,mCAOgB;AA4BhB;;;;;;;;;;;;;;;;;;;;;;;;GAwBG;AACH,MAAa,cAAc;IA8BzB,eAAe;IACf,YAAY,IAMX;QAjBD,eAAe;QACL,UAAK,GAAG,IAAI,gBAAK,EAAE,CAAA;QAE7B,eAAe;QACL,eAAU,GAAG,IAAI,GAAG,EAAoC,CAAA;QAkP1D,qBAAgB,GAAqB;YAC3C,eAAe,EAAE,CAAC,EAAE,EAAE,EAAE;gBACtB,IAAI,EAAE,KAAK,IAAI,CAAC,EAAE,CAAC,KAAK,EAAE;oBACxB,MAAM,IAAI,KAAK,CAAC,uEAAuE,CAAC,CAAA;iBACzF;gBAED,MAAM,EAAE,GAAG,IAAI,CAAC,gBAAgB,CAAA;gBAChC,IAAI,CAAC,EAAE,EAAE;oBACP,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,CAAA;iBAC3D;gBAED,OAAO,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;YACzB,CAAC;YAED,gBAAgB,EAAE,IAAA,+BAAY,EAAC,IAAI,EAAE,QAAQ,CAAC,EAAE,OAAO,EAAE,EAAE,EAAE,GAAG,EAAE,UAAU;gBAC1E,MAAM,YAAY,GAAG,IAAI,CAAC,YAAY,CAAA;gBACtC,IAAI,CAAC,YAAY,EAAE;oBACjB,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAA;iBAChD;gBAED,IAAI,EAAE,KAAK,IAAI,CAAC,EAAE,CAAC,KAAK,EAAE;oBACxB,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAA;iBAC5C;gBAED,MAAM,OAAO,GACX,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC;oBACxB,IAAI,CAAC,UAAU,CAAC;wBACd,cAAc,EAAE,GAAG;qBACpB,CAAC,CAAA;gBAEJ,IAAI;oBACF,MAAM,MAAM,GAAG,KAAK,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC,CAAA;oBAEhE,IAAI,OAAO,MAAM,KAAK,QAAQ,IAAI,OAAO,IAAI,MAAM,IAAI,MAAM,CAAC,KAAK,EAAE;wBACnE,IAAA,gBAAQ,EAAC,0CAA0C,EAAE,MAAM,CAAC,KAAK,CAAC,CAAA;wBAClE,MAAM,MAAM,CAAC,KAAK,CAAA;qBACnB;oBAED,MAAM,YAAY,GAChB,OAAO,MAAM,KAAK,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,IAAI,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAA;oBAEjF,OAAO,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,YAAY,CAAC,CAAC,KAAK,CAAA;iBAC1D;gBAAC,OAAO,KAAK,EAAE;oBACd,IAAA,gBAAQ,EAAC,iCAAiC,EAAE,KAAK,CAAC,CAAA;oBAClD,OAAO,CAAC,KAAK,CAAC,KAAY,CAAC,CAAA;oBAC3B,OAAO,CAA4B,CAAA;iBACpC;YACH,CAAC,CAAC;YAEF,eAAe,EAAE,IAAA,+BAAY,EAC3B,IAAI,EACJ,QAAQ,CAAC,EAAE,OAAO,EAAE,EAAE,EAAE,GAAG,EAAE,cAAc,EAAE,iBAAiB;gBAC5D,MAAM,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAA;gBAC9C,IAAI,CAAC,gBAAgB,EAAE;oBACrB,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAA;iBACpD;gBAED,IAAI,EAAE,KAAK,IAAI,CAAC,EAAE,CAAC,KAAK,EAAE;oBACxB,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAA;iBAC5C;gBAED,MAAM,OAAO,GACX,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC;oBACxB,IAAI,CAAC,UAAU,CAAC;wBACd,gFAAgF;wBAChF,cAAc,EAAE,GAAG;qBACpB,CAAC,CAAA;gBAEJ,IAAI;oBACF,MAAM,MAAM,GAAG,KAAK,CAAC,CAAC,OAAO,CAC3B,gBAAgB,CAAC,cAAc,EAAE,iBAAiB,EAAE,OAAO,CAAC,CAC7D,CAAA;oBAED,IAAI,OAAO,MAAM,KAAK,QAAQ,IAAI,OAAO,IAAI,MAAM,IAAI,MAAM,CAAC,KAAK,EAAE;wBACnE,IAAA,gBAAQ,EAAC,mDAAmD,EAAE,MAAM,CAAC,KAAK,CAAC,CAAA;wBAC3E,MAAM,MAAM,CAAC,KAAK,CAAA;qBACnB;oBAED,MAAM,IAAI,GAAG,OAAO,MAAM,KAAK,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAA;oBAC/D,OAAO,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC,KAAK,CAAA;iBACvE;gBAAC,OAAO,KAAK,EAAE;oBACd,IAAA,gBAAQ,EAAC,+BAA+B,EAAE,KAAK,CAAC,CAAA;oBAChD,OAAO,CAAC,KAAK,CAAC,KAAY,CAAC,CAAA;oBAC3B,OAAO,CAA4B,CAAA;iBACpC;YACH,CAAC,CACF;SACF,CAAA;QA3TC,IAAI,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAA;QACvE,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAA;QACzB,IAAI,CAAC,MAAM,GAAG,IAAI,qBAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;QAC3C,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAA;QACnB,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,CAAA;QACjB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAA;QAC/B,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;QAC1B,IAAI,CAAC,SAAS,CAAC,mBAAmB,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAA;QAExE,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;IAC9D,CAAC;IAED,IAAI,KAAK;QACP,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CAAA;IACzB,CAAC;IAED,OAAO;QACL,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAA;IAC7B,CAAC;IAED,UAAU,CAAC,UAA0B,EAAE;QACrC,IAAI,OAAO,CAAC,UAAU,IAAI,OAAO,CAAC,UAAU,KAAK,yBAAiB,EAAE;YAClE,MAAM,IAAI,KAAK,CAAC,+CAA+C,CAAC,CAAA;SACjE;QAED,MAAM,GAAG,GAAG,IAAI,mBAAQ,CACtB,OAAO,CAAC,cAAc,IAAI,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,EAChE,SAAS,EACT,CAAC,OAAO,EAAE,EAAE;YACV,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;YAC/B,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,OAAO,CAAC,CAAA;YACrC,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC,OAAO,CAAC,CAAA;QACnC,CAAC,CACF,CAAA;QAED,MAAM,OAAO,GAAG,IAAI,wBAAc,CAAC;YACjC,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,GAAG;YACH,GAAG,EAAE,IAAI,CAAC,GAAG;YACb,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,cAAc,EAAE,OAAO,CAAC,cAAc;YACtC,OAAO,EAAE,IAAI;YACb,SAAS,EAAE,IAAI,CAAC,SAAS;SAC1B,CAAC,CAAA;QACF,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,EAAE,OAAO,CAAC,CAAA;QAEvC,OAAO,OAAO,CAAA;IAChB,CAAC;IAED;;;;;OAKG;IACH,eAAe,CAAC,YAA4B,EAAE,gBAAqC;QACjF,IAAI,CAAC,YAAY,GAAG,YAAY,CAAA;QAChC,IAAI,CAAC,gBAAgB,GAAG,gBAAgB,CAAA;QACxC,IAAI,CAAC,GAAG,CAAC,6BAA6B,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;IACtF,CAAC;IAED;;OAEG;IACH,kBAAkB;QAChB,IAAI,CAAC,YAAY,GAAG,SAAS,CAAA;QAC7B,IAAI,CAAC,GAAG,CAAC,8BAA8B,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,CAAA;IACxD,CAAC;IAED,6EAA6E;IAE7E;;;;;OAKG;IACH,aAAa;QACX,OAAO,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAA;IAC1D,CAAC;IAID;;;;;;OAMG;IACH,mBAAmB,CAAC,EAAoB;QACtC,MAAM,oBAAoB,GAAG,IAAI,CAAC,gBAAgB,CAAA;QAClD,IAAI,CAAC,gBAAgB,GAAG,EAAE,CAAA;QAC1B,IAAI,CAAC,oBAAoB,EAAE;YACzB,IAAI,CAAC,GAAG,CAAC,iCAAiC,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,CAAA;SAC1D;IACH,CAAC;IAED;;;OAGG;IACH,sBAAsB;QACpB,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACzB,IAAI,CAAC,GAAG,CAAC,kCAAkC,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,CAAA;YAC1D,IAAI,CAAC,gBAAgB,GAAG,SAAS,CAAA;SAClC;IACH,CAAC;IAED;;;;;;;;;;;;;;;;OAgBG;IACH,kBAAkB,CAAC,mBAAkC,CAAC,CAAC;QACrD,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAA0B,CAAC,CAAC,CAAA;QAChF,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,qBAAqB,CAC7C,IAAI,CAAC,EAAE,CAAC,KAAK,EACb,gBAAgB,IAAI,CAAC,CAAC,EACtB,SAAS,CAAC,KAAK,CAAC,GAAG,CACpB,CAAA;QAED,MAAM,MAAM,GAAG,SAAS,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAqB,CAAA;QAChE,SAAS,CAAC,OAAO,EAAE,CAAA;QACnB,IAAI,MAAM,KAAK,CAAC,EAAE;YAChB,oBAAoB;YACpB,IAAI,CAAC,GAAG,CAAC,2BAA2B,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAA;YAC7D,OAAO,EAAE,KAAK,EAAE,CAAC,EAAE,CAAA;SACpB;QAED,MAAM,OAAO,GACX,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC;YAC3B,IAAI,CAAC,UAAU,CAAC;gBACd,cAAc,EAAE,MAAM;aACvB,CAAC,CAAA;QAEJ,MAAM,WAAW,GAAG,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAA;QAC9E,MAAM,SAAS,GAAG,OAAO,CAAC,MAAM,CAAC,WAAW,CAAC,CAAA;QAC7C,IAAI,SAAS,KAAK,QAAQ,EAAE;YAC1B,MAAM,YAAY,GAAG,OAAO,CAAC,SAAS,CAAC,WAAW,CAAC,CAAA;YACnD,WAAW,CAAC,OAAO,EAAE,CAAA;YACrB,OAAO,EAAE,KAAK,EAAE,YAAY,EAAE,CAAA;SAC/B;aAAM;YACL,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,WAAW,EAAE,EAAE,OAAO,EAAE,CAAC,CAAA;YACrD,OAAO;gBACL,KAAK;aACN,CAAA;SACF;IACH,CAAC;IAED;;;OAGG;IACH,cAAc,CAAC,UAAkB;QAC/B,IAAI,UAAU,GAAG,CAAC,IAAI,UAAU,KAAK,CAAC,CAAC,EAAE;YACvC,MAAM,IAAI,KAAK,CAAC,+DAA+D,CAAC,CAAA;SACjF;QAED,IAAI,CAAC,GAAG,CAAC,yBAAyB,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,EAAE,UAAU,CAAC,CAAA;IAC/D,CAAC;IAED;;;;;;OAMG;IACH,kBAAkB;QAChB,MAAM,oBAAoB,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,CAAA;QAC7E,OAAO,oBAAoB,CAAC,eAAe,CACzC,IAAI,CAAC,GAAG,CAAC,6BAA6B,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,EAAE,oBAAoB,CAAC,GAAG,CAAC,KAAK,CAAC,CACtF,CAAA;IACH,CAAC;IAED;;;OAGG;IACH,eAAe;QACb,OAAO,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC,IAAI,CAAC,GAAG,CAAC,0BAA0B,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAA;IAC/F,CAAC;IAED;;;OAGG;IACH,eAAe,CAAC,SAAiB;QAC/B,IAAI,SAAS,GAAG,CAAC,EAAE;YACjB,MAAM,IAAI,KAAK,CAAC,+DAA+D,CAAC,CAAA;SACjF;QAED,IAAI,CAAC,GAAG,CAAC,0BAA0B,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,EAAE,SAAS,CAAC,CAAA;IAC/D,CAAC;IAED;;;OAGG;IACH,WAAW,CAAC,MAAqB;QAC/B,IAAI,MAAM,CAAC,KAAK,IAAI,MAAM,CAAC,KAAK,CAAC,EAAE,KAAK,IAAI,CAAC,EAAE,EAAE;YAC/C,MAAM,IAAI,0BAAiB,CACzB,wCAAwC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,OAAO,IAAI,CAAC,EAAE,CAAC,KAAK,EAAE,CACpF,CAAA;SACF;IACH,CAAC;IAEO,gBAAgB;QACtB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACjB,gDAAgD;YAChD,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,CAAA;SACpD;QACD,OAAO,IAAI,CAAC,OAAO,CAAA;IACrB,CAAC;CA0FF;AAlWD,wCAkWC", "sourcesContent": ["import { maybeAsyncFn } from \"./asyncify-helpers\"\nimport { QuickJSContext } from \"./context\"\nimport { debugLog } from \"./debug\"\nimport { EitherModule } from \"./emscripten-types\"\nimport { <PERSON>JSWrongOwner } from \"./errors\"\nimport {\n  <PERSON><PERSON><PERSON><PERSON><PERSON>p<PERSON>har<PERSON>ointer,\n  JSContextPointer,\n  JSContextPointerPointer,\n  JSModuleDefPointer,\n  JSRuntimePointer,\n} from \"./types-ffi\"\nimport { Disposable, Lifetime, Scope } from \"./lifetime\"\nimport { ModuleMemory } from \"./memory\"\nimport { QuickJSModuleCallbacks, RuntimeCallbacks } from \"./module\"\nimport {\n  ContextOptions,\n  DefaultIntrinsics,\n  EitherFFI,\n  JSModuleLoader,\n  JSModuleNormalizer,\n  QuickJSHandle,\n} from \"./types\"\nimport { SuccessOrFail } from \"./vm-interface\"\n\n/**\n * Callback called regularly while the VM executes code.\n * Determines if a VM's execution should be interrupted.\n *\n * @returns `true` to interrupt JS execution inside the VM.\n * @returns `false` or `undefined` to continue JS execution inside the VM.\n */\nexport type InterruptHandler = (runtime: QuickJSRuntime) => boolean | undefined\n\n/**\n * Used as an optional for the results of executing pendingJobs.\n * On success, `value` contains the number of async jobs executed\n * by the runtime.\n * @source\n */\nexport type ExecutePendingJobsResult = SuccessOrFail<\n  /** Number of jobs successfully executed. */\n  number,\n  /** The error that occurred. */\n  QuickJSHandle & {\n    /** The context where the error occurred. */\n    context: QuickJSContext\n  }\n>\n\n/**\n * A runtime represents a Javascript runtime corresponding to an object heap.\n * Several runtimes can exist at the same time but they cannot exchange objects.\n * Inside a given runtime, no multi-threading is supported.\n *\n * You can think of separate runtimes like different domains in a browser, and\n * the contexts within a runtime like the different windows open to the same\n * domain.\n *\n * Create a runtime via {@link QuickJSWASMModule.newRuntime}.\n *\n * You should create separate runtime instances for untrusted code from\n * different sources for isolation. However, stronger isolation is also\n * available (at the cost of memory usage), by creating separate WebAssembly\n * modules to further isolate untrusted code.\n * See {@link newQuickJSWASMModule}.\n *\n * Implement memory and CPU constraints with [[setInterruptHandler]]\n * (called regularly while the interpreter runs), [[setMemoryLimit]], and\n * [[setMaxStackSize]].\n * Use [[computeMemoryUsage]] or [[dumpMemoryUsage]] to guide memory limit\n * tuning.\n *\n * Configure ES module loading with [[setModuleLoader]].\n */\nexport class QuickJSRuntime implements Disposable {\n  /**\n   * If this runtime was created as as part of a context, points to the context\n   * associated with the runtime.\n   *\n   * If this runtime was created stand-alone, this may or may not contain a context.\n   * A context here may be allocated if one is needed by the runtime, eg for [[computeMemoryUsage]].\n   */\n  public context: QuickJSContext | undefined\n\n  /** @private */\n  protected module: EitherModule\n  /** @private */\n  protected memory: ModuleMemory\n  /** @private */\n  protected ffi: EitherFFI\n  /** @private */\n  protected rt: Lifetime<JSRuntimePointer>\n  /** @private */\n  protected callbacks: QuickJSModuleCallbacks\n  /** @private */\n  protected scope = new Scope()\n\n  /** @private */\n  protected contextMap = new Map<JSContextPointer, QuickJSContext>()\n  /** @private */\n  protected moduleLoader: JSModuleLoader | undefined\n  /** @private */\n  protected moduleNormalizer: JSModuleNormalizer | undefined\n\n  /** @private */\n  constructor(args: {\n    module: EitherModule\n    ffi: EitherFFI\n    rt: Lifetime<JSRuntimePointer>\n    callbacks: QuickJSModuleCallbacks\n    ownedLifetimes?: Disposable[]\n  }) {\n    args.ownedLifetimes?.forEach((lifetime) => this.scope.manage(lifetime))\n    this.module = args.module\n    this.memory = new ModuleMemory(this.module)\n    this.ffi = args.ffi\n    this.rt = args.rt\n    this.callbacks = args.callbacks\n    this.scope.manage(this.rt)\n    this.callbacks.setRuntimeCallbacks(this.rt.value, this.cToHostCallbacks)\n\n    this.executePendingJobs = this.executePendingJobs.bind(this)\n  }\n\n  get alive() {\n    return this.scope.alive\n  }\n\n  dispose() {\n    return this.scope.dispose()\n  }\n\n  newContext(options: ContextOptions = {}): QuickJSContext {\n    if (options.intrinsics && options.intrinsics !== DefaultIntrinsics) {\n      throw new Error(\"TODO: Custom intrinsics are not supported yet\")\n    }\n\n    const ctx = new Lifetime(\n      options.contextPointer || this.ffi.QTS_NewContext(this.rt.value),\n      undefined,\n      (ctx_ptr) => {\n        this.contextMap.delete(ctx_ptr)\n        this.callbacks.deleteContext(ctx_ptr)\n        this.ffi.QTS_FreeContext(ctx_ptr)\n      }\n    )\n\n    const context = new QuickJSContext({\n      module: this.module,\n      ctx,\n      ffi: this.ffi,\n      rt: this.rt,\n      ownedLifetimes: options.ownedLifetimes,\n      runtime: this,\n      callbacks: this.callbacks,\n    })\n    this.contextMap.set(ctx.value, context)\n\n    return context\n  }\n\n  /**\n   * Set the loader for EcmaScript modules requested by any context in this\n   * runtime.\n   *\n   * The loader can be removed with [[removeModuleLoader]].\n   */\n  setModuleLoader(moduleLoader: JSModuleLoader, moduleNormalizer?: JSModuleNormalizer): void {\n    this.moduleLoader = moduleLoader\n    this.moduleNormalizer = moduleNormalizer\n    this.ffi.QTS_RuntimeEnableModuleLoader(this.rt.value, this.moduleNormalizer ? 1 : 0)\n  }\n\n  /**\n   * Remove the the loader set by [[setModuleLoader]]. This disables module loading.\n   */\n  removeModuleLoader(): void {\n    this.moduleLoader = undefined\n    this.ffi.QTS_RuntimeDisableModuleLoader(this.rt.value)\n  }\n\n  // Runtime management -------------------------------------------------------\n\n  /**\n   * In QuickJS, promises and async functions create pendingJobs. These do not execute\n   * immediately and need to be run by calling [[executePendingJobs]].\n   *\n   * @return true if there is at least one pendingJob queued up.\n   */\n  hasPendingJob(): boolean {\n    return Boolean(this.ffi.QTS_IsJobPending(this.rt.value))\n  }\n\n  private interruptHandler: InterruptHandler | undefined\n\n  /**\n   * Set a callback which is regularly called by the QuickJS engine when it is\n   * executing code. This callback can be used to implement an execution\n   * timeout.\n   *\n   * The interrupt handler can be removed with [[removeInterruptHandler]].\n   */\n  setInterruptHandler(cb: InterruptHandler) {\n    const prevInterruptHandler = this.interruptHandler\n    this.interruptHandler = cb\n    if (!prevInterruptHandler) {\n      this.ffi.QTS_RuntimeEnableInterruptHandler(this.rt.value)\n    }\n  }\n\n  /**\n   * Remove the interrupt handler, if any.\n   * See [[setInterruptHandler]].\n   */\n  removeInterruptHandler() {\n    if (this.interruptHandler) {\n      this.ffi.QTS_RuntimeDisableInterruptHandler(this.rt.value)\n      this.interruptHandler = undefined\n    }\n  }\n\n  /**\n   * Execute pendingJobs on the runtime until `maxJobsToExecute` jobs are\n   * executed (default all pendingJobs), the queue is exhausted, or the runtime\n   * encounters an exception.\n   *\n   * In QuickJS, promises and async functions *inside the runtime* create\n   * pendingJobs. These do not execute immediately and need to triggered to run.\n   *\n   * @param maxJobsToExecute - When negative, run all pending jobs. Otherwise execute\n   * at most `maxJobsToExecute` before returning.\n   *\n   * @return On success, the number of executed jobs. On error, the exception\n   * that stopped execution, and the context it occurred in. Note that\n   * executePendingJobs will not normally return errors thrown inside async\n   * functions or rejected promises. Those errors are available by calling\n   * [[resolvePromise]] on the promise handle returned by the async function.\n   */\n  executePendingJobs(maxJobsToExecute: number | void = -1): ExecutePendingJobsResult {\n    const ctxPtrOut = this.memory.newMutablePointerArray<JSContextPointerPointer>(1)\n    const valuePtr = this.ffi.QTS_ExecutePendingJob(\n      this.rt.value,\n      maxJobsToExecute ?? -1,\n      ctxPtrOut.value.ptr\n    )\n\n    const ctxPtr = ctxPtrOut.value.typedArray[0] as JSContextPointer\n    ctxPtrOut.dispose()\n    if (ctxPtr === 0) {\n      // No jobs executed.\n      this.ffi.QTS_FreeValuePointerRuntime(this.rt.value, valuePtr)\n      return { value: 0 }\n    }\n\n    const context =\n      this.contextMap.get(ctxPtr) ??\n      this.newContext({\n        contextPointer: ctxPtr,\n      })\n\n    const resultValue = context.getMemory(this.rt.value).heapValueHandle(valuePtr)\n    const typeOfRet = context.typeof(resultValue)\n    if (typeOfRet === \"number\") {\n      const executedJobs = context.getNumber(resultValue)\n      resultValue.dispose()\n      return { value: executedJobs }\n    } else {\n      const error = Object.assign(resultValue, { context })\n      return {\n        error,\n      }\n    }\n  }\n\n  /**\n   * Set the max memory this runtime can allocate.\n   * To remove the limit, set to `-1`.\n   */\n  setMemoryLimit(limitBytes: number) {\n    if (limitBytes < 0 && limitBytes !== -1) {\n      throw new Error(\"Cannot set memory limit to negative number. To unset, pass -1\")\n    }\n\n    this.ffi.QTS_RuntimeSetMemoryLimit(this.rt.value, limitBytes)\n  }\n\n  /**\n   * Compute memory usage for this runtime. Returns the result as a handle to a\n   * JSValue object. Use [[QuickJSContext.dump]] to convert to a native object.\n   * Calling this method will allocate more memory inside the runtime. The information\n   * is accurate as of just before the call to `computeMemoryUsage`.\n   * For a human-digestible representation, see [[dumpMemoryUsage]].\n   */\n  computeMemoryUsage(): QuickJSHandle {\n    const serviceContextMemory = this.getSystemContext().getMemory(this.rt.value)\n    return serviceContextMemory.heapValueHandle(\n      this.ffi.QTS_RuntimeComputeMemoryUsage(this.rt.value, serviceContextMemory.ctx.value)\n    )\n  }\n\n  /**\n   * @returns a human-readable description of memory usage in this runtime.\n   * For programmatic access to this information, see [[computeMemoryUsage]].\n   */\n  dumpMemoryUsage(): string {\n    return this.memory.consumeHeapCharPointer(this.ffi.QTS_RuntimeDumpMemoryUsage(this.rt.value))\n  }\n\n  /**\n   * Set the max stack size for this runtime, in bytes.\n   * To remove the limit, set to `0`.\n   */\n  setMaxStackSize(stackSize: number) {\n    if (stackSize < 0) {\n      throw new Error(\"Cannot set memory limit to negative number. To unset, pass 0.\")\n    }\n\n    this.ffi.QTS_RuntimeSetMaxStackSize(this.rt.value, stackSize)\n  }\n\n  /**\n   * Assert that `handle` is owned by this runtime.\n   * @throws QuickJSWrongOwner if owned by a different runtime.\n   */\n  assertOwned(handle: QuickJSHandle) {\n    if (handle.owner && handle.owner.rt !== this.rt) {\n      throw new QuickJSWrongOwner(\n        `Handle is not owned by this runtime: ${handle.owner.rt.value} != ${this.rt.value}`\n      )\n    }\n  }\n\n  private getSystemContext() {\n    if (!this.context) {\n      // We own this context and should dispose of it.\n      this.context = this.scope.manage(this.newContext())\n    }\n    return this.context\n  }\n\n  private cToHostCallbacks: RuntimeCallbacks = {\n    shouldInterrupt: (rt) => {\n      if (rt !== this.rt.value) {\n        throw new Error(\"QuickJSContext instance received C -> JS interrupt with mismatched rt\")\n      }\n\n      const fn = this.interruptHandler\n      if (!fn) {\n        throw new Error(\"QuickJSContext had no interrupt handler\")\n      }\n\n      return fn(this) ? 1 : 0\n    },\n\n    loadModuleSource: maybeAsyncFn(this, function* (awaited, rt, ctx, moduleName) {\n      const moduleLoader = this.moduleLoader\n      if (!moduleLoader) {\n        throw new Error(\"Runtime has no module loader\")\n      }\n\n      if (rt !== this.rt.value) {\n        throw new Error(\"Runtime pointer mismatch\")\n      }\n\n      const context =\n        this.contextMap.get(ctx) ??\n        this.newContext({\n          contextPointer: ctx,\n        })\n\n      try {\n        const result = yield* awaited(moduleLoader(moduleName, context))\n\n        if (typeof result === \"object\" && \"error\" in result && result.error) {\n          debugLog(\"cToHostLoadModule: loader returned error\", result.error)\n          throw result.error\n        }\n\n        const moduleSource =\n          typeof result === \"string\" ? result : \"value\" in result ? result.value : result\n\n        return this.memory.newHeapCharPointer(moduleSource).value\n      } catch (error) {\n        debugLog(\"cToHostLoadModule: caught error\", error)\n        context.throw(error as any)\n        return 0 as BorrowedHeapCharPointer\n      }\n    }),\n\n    normalizeModule: maybeAsyncFn(\n      this,\n      function* (awaited, rt, ctx, baseModuleName, moduleNameRequest) {\n        const moduleNormalizer = this.moduleNormalizer\n        if (!moduleNormalizer) {\n          throw new Error(\"Runtime has no module normalizer\")\n        }\n\n        if (rt !== this.rt.value) {\n          throw new Error(\"Runtime pointer mismatch\")\n        }\n\n        const context =\n          this.contextMap.get(ctx) ??\n          this.newContext({\n            /* TODO: Does this happen? Are we responsible for disposing? I don't think so */\n            contextPointer: ctx,\n          })\n\n        try {\n          const result = yield* awaited(\n            moduleNormalizer(baseModuleName, moduleNameRequest, context)\n          )\n\n          if (typeof result === \"object\" && \"error\" in result && result.error) {\n            debugLog(\"cToHostNormalizeModule: normalizer returned error\", result.error)\n            throw result.error\n          }\n\n          const name = typeof result === \"string\" ? result : result.value\n          return context.getMemory(this.rt.value).newHeapCharPointer(name).value\n        } catch (error) {\n          debugLog(\"normalizeModule: caught error\", error)\n          context.throw(error as any)\n          return 0 as BorrowedHeapCharPointer\n        }\n      }\n    ),\n  }\n}\n"]}