var p=(t=>typeof require<"u"?require:typeof Proxy<"u"?new Proxy(t,{get:(e,o)=>(typeof require<"u"?require:e)[o]}):t)(function(t){if(typeof require<"u")return require.apply(this,arguments);throw Error('Dynamic require of "'+t+'" is not supported')});import{createHash as T}from"crypto";import{resolve as P}from"path";import L from"typescript";function O(t){var o,f,i,s;let e=L.convertCompilerOptionsFromJson(t.tsconfig.compilerOptions,process.cwd()).options;if(e.declaration=!0,e.emitDeclarationOnly=!0,e.declarationDir||(e.declarationDir=(f=(o=e.declarationDir)!=null?o:t.esbuildOptions.outdir)!=null?f:e.outDir),e.incremental&&!e.tsBuildInfoFile){let a=T("sha256").update(JSON.stringify({compilerOptions:e,__buildContext:(i=t.pluginOptions)==null?void 0:i.__buildContext})).digest("hex"),l=P(p.resolve("esbuild/package.json"),"../../.cache/esbuild-plugin-d.ts");e.tsBuildInfoFile=P((s=t.pluginOptions.buildInfoDir)!=null?s:l,`esbuild-plugin-dts-${a}.tsbuildinfo`)}return e.listEmittedFiles=!0,e}import{readFileSync as M}from"fs";import N from"lodash.merge";import{dirname as E,resolve as I}from"path";import h from"typescript";function _(t){try{return p.resolve(t)}catch{return}}function x(t){var f,i,s;let e=(i=t.configPath)!=null?i:h.findConfigFile((f=t.searchPath)!=null?f:process.cwd(),h.sys.fileExists,t.configName);if(!e)throw new Error("No config file found");e.startsWith(".")&&(e=p.resolve(e));let o=h.readConfigFile(e,a=>M(a,"utf-8"));if(o.config.extends){let a=x({...t,configPath:(s=_(o.config.extends))!=null?s:I(E(e),o.config.extends)});o.config=N(a,o.config)}if(o.error)throw o.error;return o.config}function F(t){let e=Math.floor(Math.log(t)/Math.log(1024));return Math.round(t/Math.pow(1024,e)*100)/100+["b","kb","mb","gb","tb"][e]}import y from"chalk";import{existsSync as k,lstatSync as z}from"fs";import{resolve as D}from"path";import c from"typescript";function b(t){let e=["verbose","debug","info","warning","error","silent"];for(let o of e){if(o===t)break;e.splice(e.indexOf(o),1)}return{info:(...o)=>{e.includes("info")&&console.log(...o)}}}var B=(t={})=>({name:"dts-plugin",async setup(e){let o=b(e.initialOptions.logLevel),f=t.tsconfig&&typeof t.tsconfig!="string"?t.tsconfig:x({configPath:t.tsconfig}),i=O({tsconfig:f,pluginOptions:t,esbuildOptions:e.initialOptions}),s=i.incremental?c.createIncrementalCompilerHost(i):c.createCompilerHost(i),a=[];e.onLoad({filter:/(\.tsx|\.ts)$/},async l=>{var m;a.push(l.path);let g=[];return s.getSourceFile(l.path,(m=i.target)!=null?m:c.ScriptTarget.Latest,u=>{g.push({detail:u})},!0),{errors:g}}),e.onEnd(()=>{let l;i.incremental?l=c.createIncrementalProgram({options:i,host:s,rootNames:a}):l=c.createProgram(a,i,s);let g=c.getPreEmitDiagnostics(l).map(r=>{var n;return{text:typeof r.messageText=="string"?r.messageText:r.messageText.messageText,detail:r,location:{file:(n=r.file)==null?void 0:n.fileName,namespace:"file"},category:r.category}}),m=g.filter(r=>r.category===c.DiagnosticCategory.Error).map(({category:r,...n})=>n),u=g.filter(r=>r.category===c.DiagnosticCategory.Warning).map(({category:r,...n})=>n);if(m.length>0)return{errors:m,warnings:u};let v=Date.now(),d=l.emit();if(d.emitSkipped||typeof d.emittedFiles>"u")o.info(y`{yellow No declarations emitted}`);else for(let r of d.emittedFiles){let n=D(r);if(k(n)&&n!==i.tsBuildInfoFile){let C=z(n),S=n.replace(D(process.cwd()),"").replace(/^[\\/]/,""),w=F(C.size);o.info(y`  {bold ${S}} {cyan ${w}}`)}}return o.info(y`{green Finished compiling declarations in ${Date.now()-v}ms}`),{warnings:u}})}});export{B as default,B as dtsPlugin,O as getCompilerOptions,F as humanizeFileSize,x as resolveTSConfig};
//# sourceMappingURL=index.mjs.map