{"version": 3, "file": "utils.js", "sourceRoot": "", "sources": ["../../src/utils.ts"], "names": [], "mappings": ";;;AAAA;;;;;;;;;;;;;;GAcG;AACH,mCAAiE;AAEjE,2DAAwD;AAExD,8EAA0E;AAEnE,MAAM,qBAAqB,GAAG,CACnC,OAAmB,EACnB,KAAoB,EACpB,QAAiB,EACjB,SAA2B,EAI3B,EAAE;;IACF,IAAI,QAAQ,EAAE;QACZ,OAAO;YACL,UAAU,EAAE;gBACV,CAAC,+BAAc,CAAC,QAAQ,CAAC,EAAE,SAAS,aAAT,SAAS,uBAAT,SAAS,CAAE,QAAQ,EAAE;gBAChD,CAAC,+BAAc,CAAC,QAAQ,CAAC,EAAE,oBAAY,CAAC,MAAM;gBAC9C,CAAC,0CAAmB,CAAC,EAAE,SAAS,aAAT,SAAS,uBAAT,SAAS,CAAE,QAAQ,EAAE;aAC7C;YACD,IAAI,EAAE,OAAO,CAAC,iBAAiB,IAAI,YAAY,SAAS,EAAE;SAC3D,CAAC;KACH;SAAM;QACL,OAAO;YACL,UAAU,EAAE;gBACV,CAAC,+BAAc,CAAC,QAAQ,CAAC,EAAE,MAAA,KAAK,CAAC,IAAI,mCAAI,YAAY;gBACrD,CAAC,+BAAc,CAAC,QAAQ,CAAC,EAAE,oBAAY,CAAC,UAAU;aACnD;YACD,IAAI,EAAE,gBAAgB,KAAK,CAAC,IAAI,EAAE;SACnC,CAAC;KACH;AACH,CAAC,CAAC;AA3BW,QAAA,qBAAqB,yBA2BhC;AAEF;;;;;GAKG;AACI,MAAM,cAAc,GAAG,CAC5B,IAAkB,EAClB,MAAiC,EACxB,EAAE;;IACX,OAAO,CAAC,CAAC,CACP,KAAK,CAAC,OAAO,CAAC,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,gBAAgB,CAAC;SACvC,MAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,gBAAgB,0CAAE,QAAQ,CAAC,IAAI,CAAC,CAAA,CACzC,CAAC;AACJ,CAAC,CAAC;AARW,QAAA,cAAc,kBAQzB", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { KoaLayerType, KoaInstrumentationConfig } from './types';\nimport { KoaContext, KoaMiddleware } from './internal-types';\nimport { AttributeNames } from './enums/AttributeNames';\nimport { Attributes } from '@opentelemetry/api';\nimport { SEMATTRS_HTTP_ROUTE } from '@opentelemetry/semantic-conventions';\n\nexport const getMiddlewareMetadata = (\n  context: KoaContext,\n  layer: KoaMiddleware,\n  isRouter: boolean,\n  layerPath?: string | RegExp\n): {\n  attributes: Attributes;\n  name: string;\n} => {\n  if (isRouter) {\n    return {\n      attributes: {\n        [AttributeNames.KOA_NAME]: layerPath?.toString(),\n        [AttributeNames.KOA_TYPE]: KoaLayerType.ROUTER,\n        [SEMATTRS_HTTP_ROUTE]: layerPath?.toString(),\n      },\n      name: context._matchedRouteName || `router - ${layerPath}`,\n    };\n  } else {\n    return {\n      attributes: {\n        [AttributeNames.KOA_NAME]: layer.name ?? 'middleware',\n        [AttributeNames.KOA_TYPE]: KoaLayerType.MIDDLEWARE,\n      },\n      name: `middleware - ${layer.name}`,\n    };\n  }\n};\n\n/**\n * Check whether the given request is ignored by configuration\n * @param [list] List of ignore patterns\n * @param [onException] callback for doing something when an exception has\n *     occurred\n */\nexport const isLayerIgnored = (\n  type: KoaLayerType,\n  config?: KoaInstrumentationConfig\n): boolean => {\n  return !!(\n    Array.isArray(config?.ignoreLayersType) &&\n    config?.ignoreLayersType?.includes(type)\n  );\n};\n"]}