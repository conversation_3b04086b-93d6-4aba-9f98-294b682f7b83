{"version": 3, "file": "index.d.ts", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": ";AAGA,OAAe,EAAC,kBAAkB,EAAE,iBAAiB,EAAC,MAAM,QAAQ,CAAC;AAGrE;;;GAGG;AACH,MAAM,MAAM,UAAU,GAAG,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;AAE7C;;;;GAIG;AACH,MAAM,MAAM,gBAAgB,GAAG;IAAC,CAAC,GAAG,EAAE,MAAM,GAAG,MAAM,CAAA;CAAC,CAAC;AAEvD;;;GAGG;AACH,MAAM,MAAM,mBAAmB,GAAG;IACjC,SAAS,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;IACrC,WAAW,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;CACvC,CAAC;AAEF;;GAEG;AACH,MAAM,MAAM,aAAa,GAAG,CAAC,MAAM,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,EAAE,GAAG,EAAE,MAAM,KAAK,mBAAmB,CAAC;AAEpG;;GAEG;AACH,MAAM,MAAM,YAAY,GAAG;IAC1B;;;;OAIG;IACH,GAAG,CAAC,EAAE,MAAM,CAAC;IACb;;;;OAIG;IACH,KAAK,CAAC,EAAE,OAAO,CAAC;IAChB;;;;OAIG;IACH,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB;;;;OAIG;IACH,KAAK,CAAC,EAAE,MAAM,CAAC;IACf;;;;OAIG;IACH,QAAQ,CAAC,EAAE,cAAc,GAAG,MAAM,CAAC;IACnC;;;;OAIG;IACH,MAAM,CAAC,EAAE,OAAO,CAAC;IACjB;;;OAGG;IACH,SAAS,CAAC,EAAE,MAAM,CAAC;IACnB;;;;OAIG;IACH,QAAQ,CAAC,EAAE,OAAO,CAAC;IACnB;;;OAGG;IACH,IAAI,CAAC,EAAE,MAAM,CAAC;IACd;;;;;OAKG;IACH,UAAU,CAAC,EAAE,gBAAgB,CAAC;CAC9B,CAAC;AAEF;;GAEG;AACH,qBAAa,MAAM;;IAEX,MAAM,EAAE,kBAAkB,CAAM;IAChC,GAAG,EAAE,UAAU,CAAM;IACrB,KAAK,EAAE,MAAM,CAAM;IAc1B;;;;;;;;;;;OAWG;gBACS,EACX,GAAG,EACH,KAAK,EACL,QAAQ,EACR,KAAK,EACL,QAAQ,EACR,MAAM,EACN,SAAS,EACT,QAAQ,EACR,IAAI,EACJ,UAAU,GACV,GAAE,YAAiB;IAgBpB;;OAEG;IACH,IAAI,KAAK,IAAI,OAAO,CAEnB;IAED;;;OAGG;IACH,IAAW,KAAK,CAAC,KAAK,EAAE,OAAO,GAAG,SAAS,EAE1C;IAED;;OAEG;IACH,IAAI,QAAQ,IAAI,MAAM,CAErB;IAED;;;OAGG;IACH,IAAW,QAAQ,CAAC,KAAK,EAAE,MAAM,GAAG,SAAS,EAE5C;IAED;;OAEG;IACH,IAAW,QAAQ,IAAI,cAAc,CAEpC;IAED;;;OAGG;IACH,IAAW,QAAQ,CAAC,KAAK,EAAE,cAAc,GAAG,MAAM,GAAG,SAAS,EAE7D;IAED;;OAEG;IACH,IAAW,MAAM,IAAI,OAAO,CAE3B;IAED;;OAEG;IACH,IAAW,MAAM,CAAC,KAAK,EAAE,OAAO,GAAG,SAAS,EAE3C;IAED;;OAEG;IACH,IAAW,SAAS,IAAI,MAAM,CAE7B;IAED;;OAEG;IACH,IAAW,SAAS,CAAC,KAAK,EAAE,MAAM,GAAG,SAAS,EAE7C;IAED;;OAEG;IACH,IAAW,GAAG,IAAI,MAAM,CAGvB;IAED;;;OAGG;IACH,IAAW,GAAG,CAAC,KAAK,EAAE,MAAM,GAAG,SAAS,EAEvC;IAED;;OAEG;IACH,IAAW,KAAK,IAAI,MAAM,CAEzB;IAED;;;OAGG;IACH,IAAW,KAAK,CAAC,KAAK,EAAE,MAAM,GAAG,SAAS,EAEzC;IAED;;OAEG;IACH,IAAW,QAAQ,IAAI,OAAO,CAE7B;IAED;;;OAGG;IACH,IAAW,QAAQ,CAAC,KAAK,EAAE,OAAO,GAAG,SAAS,EAE7C;IAED;;OAEG;IACH,IAAW,IAAI,IAAI,MAAM,CAExB;IAED;;OAEG;IACH,IAAW,IAAI,CAAC,KAAK,EAAE,MAAM,GAAG,SAAS,EAExC;IAED;;OAEG;IACH,IAAW,UAAU,IAAI,gBAAgB,CAaxC;IAED;;;OAGG;IACH,IAAW,UAAU,CAAC,KAAK,EAAE,gBAAgB,GAAG,SAAS,EAExD;IAED;;;;OAIG;IACI,KAAK,CAAC,GAAG,EAAE,MAAM,GAAG,MAAM,GAAG,iBAAiB;IAIrD;;;;OAIG;IACI,IAAI,CAAC,aAAa,GAAE,OAAc,GAAG,IAAI;IAahD;;;;;;OAMG;IACH,OAAO,CAAC,UAAU;IAwBlB;;;;OAIG;IACH,OAAO,CAAC,iBAAiB;IAQzB;;;OAGG;IACI,QAAQ,IAAI,IAAI;IAKvB;;;OAGG;IACI,IAAI,CAAC,OAAO,CAAC,EAAE,aAAa,GAAG,MAAM,GAAG,IAAI,GAAG,SAAS;IAmB/D;;;;;;OAMG;IACH,OAAO,CAAC,aAAa;IAiBrB;;;;;;OAMG;IACH,OAAO,CAAC,qBAAqB;IAiB7B;;;;OAIG;IACI,IAAI,CAAC,OAAO,EAAE,UAAU,GAAG,IAAI;IAsDtC;;;;OAIG;IACH,OAAO,CAAC,YAAY;CAGpB;AAED;;;;GAIG;AACH,wBAAgB,UAAU,CAAC,KAAK,CAAC,EAAE,YAAY,GAAG,MAAM,CAGvD;AAED;;GAEG;AACH,eAAO,MAAM,IAAI,EAAE,CAAC,KAAK,CAAC,EAAE,YAAY,KAAK,MAAmB,CAAC;AAEjE;;;;GAIG;AACH,wBAAgB,YAAY,CAAC,KAAK,CAAC,EAAE,YAAY,GAAG,kBAAkB,CAGrE;AAED;;GAEG;AACH,eAAO,MAAM,MAAM,EAAE,CAAC,KAAK,CAAC,EAAE,YAAY,KAAK,kBAAiC,CAAC;AAEjF,eAAe,MAAM,CAAC"}