/// <reference types="node" />
import { IncomingMessage, ServerResponse } from 'http';

/**
 * Class to represent a content type.
 */
declare class ContentType {
    parameters: any;
    type: string;
    constructor(type: string);
}
/**
 * Format object to media type.
 */
declare function format(obj: any): any;
/**
 * Parse media type to object.
 */
declare function parse(string: string | IncomingMessage | ServerResponse): ContentType;

export { format, parse };
