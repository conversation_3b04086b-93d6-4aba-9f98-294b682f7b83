import { Exception } from '@opentelemetry/api';
declare type KnexError = Error & {
    code?: string;
};
export declare const getFormatter: (runner: any) => any;
export declare function otelExceptionFromKnexError(err: KnexError, message: string): Exception;
export declare const mapSystem: (knexSystem: string) => string;
export declare const getName: (db: string, operation?: string | undefined, table?: string | undefined) => string;
export declare const limitLength: (str: string, maxLength: number) => string;
export declare const extractTableName: (builder: any) => string;
export {};
//# sourceMappingURL=utils.d.ts.map