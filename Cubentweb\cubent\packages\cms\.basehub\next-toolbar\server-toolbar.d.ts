// This file was generated by basehub. Do not edit directly. Read more: https://basehub.com/docs/api-reference/basehub-sdk

/* eslint-disable */
/* eslint-disable eslint-comments/no-restricted-disable */
/* tslint:disable */
// @ts-nocheck

import { basehub } from "../index";
type ServerToolbarProps = Parameters<typeof basehub>[0];
export declare const ServerToolbar: ({ ...basehubProps }: ServerToolbarProps) => Promise<import("react/jsx-runtime").JSX.Element>;
export {};
