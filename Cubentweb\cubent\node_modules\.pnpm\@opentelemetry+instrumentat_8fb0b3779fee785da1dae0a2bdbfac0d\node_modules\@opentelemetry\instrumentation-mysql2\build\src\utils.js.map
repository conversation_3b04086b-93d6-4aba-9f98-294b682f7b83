{"version": 3, "file": "utils.js", "sourceRoot": "", "sources": ["../../src/utils.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAGH,8EAM6C;AA6B7C;;;;GAIG;AACH,SAAgB,uBAAuB,CAAC,MAAc;IACpD,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC;IACzD,MAAM,UAAU,GAAG,QAAQ,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;IACtC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE;QACtB,OAAO;YACL,CAAC,6CAAsB,CAAC,EAAE,IAAI;YAC9B,CAAC,6CAAsB,CAAC,EAAE,UAAU;YACpC,CAAC,oDAA6B,CAAC,EAAE,aAAa,CAAC,IAAI,EAAE,IAAI,EAAE,QAAQ,CAAC;YACpE,CAAC,uCAAgB,CAAC,EAAE,QAAQ;YAC5B,CAAC,uCAAgB,CAAC,EAAE,IAAI;SACzB,CAAC;KACH;IACD,OAAO;QACL,CAAC,6CAAsB,CAAC,EAAE,IAAI;QAC9B,CAAC,oDAA6B,CAAC,EAAE,aAAa,CAAC,IAAI,EAAE,IAAI,EAAE,QAAQ,CAAC;QACpE,CAAC,uCAAgB,CAAC,EAAE,QAAQ;QAC5B,CAAC,uCAAgB,CAAC,EAAE,IAAI;KACzB,CAAC;AACJ,CAAC;AAlBD,0DAkBC;AAED,SAAS,SAAS,CAAC,MAAW;IAC5B,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,GAClC,CAAC,MAAM,IAAI,MAAM,CAAC,gBAAgB,CAAC,IAAI,MAAM,IAAI,EAAE,CAAC;IACtD,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;AACxC,CAAC;AAED,SAAS,aAAa,CACpB,IAAwB,EACxB,IAAwB,EACxB,QAA4B;IAE5B,IAAI,UAAU,GAAG,gBAAgB,IAAI,IAAI,WAAW,EAAE,CAAC;IAEvD,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;QAC5B,UAAU,IAAI,IAAI,IAAI,EAAE,CAAC;KAC1B;IAED,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE;QAChC,UAAU,IAAI,IAAI,QAAQ,EAAE,CAAC;KAC9B;IAED,OAAO,UAAU,CAAC;AACpB,CAAC;AAED;;;;GAIG;AACH,SAAgB,cAAc,CAC5B,KAAoC,EACpC,MAAmB,EACnB,MAAc;IAEd,IAAI,CAAC,MAAM,EAAE;QACX,OAAO,OAAO,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC;KACtD;IACD,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;QAC7B,OAAO,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;KAC/C;SAAM;QACL,mEAAmE;QACnE,qEAAqE;QACrE,OAAO,MAAM,IAAK,KAAsB,CAAC,MAAM;YAC7C,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE,MAAM,IAAK,KAAsB,CAAC,MAAM,CAAC;YAC7D,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC;KACf;AACH,CAAC;AAjBD,wCAiBC;AAED;;;;;GAKG;AACH,SAAgB,WAAW,CAAC,KAAoC;IAC9D,MAAM,QAAQ,GAAG,OAAO,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC;IAC/D,uBAAuB;IACvB,MAAM,UAAU,GAAG,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,OAAO,CAAC,GAAG,CAAC,CAAC;IAC1C,IAAI,OAAO,UAAU,KAAK,QAAQ,IAAI,UAAU,KAAK,CAAC,CAAC,EAAE;QACvD,OAAO,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,SAAS,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC;KAC3C;IACD,OAAO,QAAQ,CAAC;AAClB,CAAC;AARD,kCAQC;AAEM,MAAM,IAAI,GAAG,CAAC,EAAY,EAAE,EAAE;IACnC,IAAI,MAAM,GAAG,KAAK,CAAC;IACnB,OAAO,CAAC,GAAG,IAAe,EAAE,EAAE;QAC5B,IAAI,MAAM;YAAE,OAAO;QACnB,MAAM,GAAG,IAAI,CAAC;QACd,OAAO,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC;IACrB,CAAC,CAAC;AACJ,CAAC,CAAC;AAPW,QAAA,IAAI,QAOf;AAEF,SAAgB,kCAAkC,CAAC,UAAe;IAChE,MAAM,mBAAmB,GAAG,UAAU,CAAC,SAAS,CAAC;IACjD,MAAM,aAAa,GAAG,MAAM,CAAC,cAAc,CAAC,mBAAmB,CAAC,CAAC;IAEjE,uHAAuH;IACvH,gGAAgG;IAChG,gHAAgH;IAChH,+DAA+D;IAC/D,IACE,OAAO,CAAA,aAAa,aAAb,aAAa,uBAAb,aAAa,CAAE,KAAK,CAAA,KAAK,UAAU;QAC1C,OAAO,CAAA,aAAa,aAAb,aAAa,uBAAb,aAAa,CAAE,OAAO,CAAA,KAAK,UAAU,EAC5C;QACA,OAAO,aAAa,CAAC;KACtB;IAED,gDAAgD;IAChD,OAAO,mBAAmB,CAAC;AAC7B,CAAC;AAjBD,gFAiBC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Attributes } from '@opentelemetry/api';\nimport {\n  SEMATTRS_DB_CONNECTION_STRING,\n  SEMATTRS_DB_NAME,\n  SEMATTRS_DB_USER,\n  SEMATTRS_NET_PEER_NAME,\n  SEMATTRS_NET_PEER_PORT,\n} from '@opentelemetry/semantic-conventions';\nimport type * as mysqlTypes from 'mysql2';\n\ntype formatType = typeof mysqlTypes.format;\n\n/*\n  Following types declare an expectation on mysql2 types and define a subset we\n  use in the instrumentation of the types actually defined in mysql2 package\n\n  We need to import them here so that the installing party of the instrumentation\n  doesn't have to absolutely install the mysql2 package as well - specially\n  important for auto-loaders and meta-packages.\n*/\ninterface QueryOptions {\n  sql: string;\n  values?: any | any[] | { [param: string]: any };\n}\n\ninterface Query {\n  sql: string;\n}\n\ninterface Config {\n  host?: string;\n  port?: number;\n  database?: string;\n  user?: string;\n  connectionConfig?: Config;\n}\n/**\n * Get an Attributes map from a mysql connection config object\n *\n * @param config ConnectionConfig\n */\nexport function getConnectionAttributes(config: Config): Attributes {\n  const { host, port, database, user } = getConfig(config);\n  const portNumber = parseInt(port, 10);\n  if (!isNaN(portNumber)) {\n    return {\n      [SEMATTRS_NET_PEER_NAME]: host,\n      [SEMATTRS_NET_PEER_PORT]: portNumber,\n      [SEMATTRS_DB_CONNECTION_STRING]: getJDBCString(host, port, database),\n      [SEMATTRS_DB_NAME]: database,\n      [SEMATTRS_DB_USER]: user,\n    };\n  }\n  return {\n    [SEMATTRS_NET_PEER_NAME]: host,\n    [SEMATTRS_DB_CONNECTION_STRING]: getJDBCString(host, port, database),\n    [SEMATTRS_DB_NAME]: database,\n    [SEMATTRS_DB_USER]: user,\n  };\n}\n\nfunction getConfig(config: any) {\n  const { host, port, database, user } =\n    (config && config.connectionConfig) || config || {};\n  return { host, port, database, user };\n}\n\nfunction getJDBCString(\n  host: string | undefined,\n  port: number | undefined,\n  database: string | undefined\n) {\n  let jdbcString = `jdbc:mysql://${host || 'localhost'}`;\n\n  if (typeof port === 'number') {\n    jdbcString += `:${port}`;\n  }\n\n  if (typeof database === 'string') {\n    jdbcString += `/${database}`;\n  }\n\n  return jdbcString;\n}\n\n/**\n * Conjures up the value for the db.statement attribute by formatting a SQL query.\n *\n * @returns the database statement being executed.\n */\nexport function getDbStatement(\n  query: string | Query | QueryOptions,\n  format?: formatType,\n  values?: any[]\n): string {\n  if (!format) {\n    return typeof query === 'string' ? query : query.sql;\n  }\n  if (typeof query === 'string') {\n    return values ? format(query, values) : query;\n  } else {\n    // According to https://github.com/mysqljs/mysql#performing-queries\n    // The values argument will override the values in the option object.\n    return values || (query as QueryOptions).values\n      ? format(query.sql, values || (query as QueryOptions).values)\n      : query.sql;\n  }\n}\n\n/**\n * The span name SHOULD be set to a low cardinality value\n * representing the statement executed on the database.\n *\n * @returns SQL statement without variable arguments or SQL verb\n */\nexport function getSpanName(query: string | Query | QueryOptions): string {\n  const rawQuery = typeof query === 'object' ? query.sql : query;\n  // Extract the SQL verb\n  const firstSpace = rawQuery?.indexOf(' ');\n  if (typeof firstSpace === 'number' && firstSpace !== -1) {\n    return rawQuery?.substring(0, firstSpace);\n  }\n  return rawQuery;\n}\n\nexport const once = (fn: Function) => {\n  let called = false;\n  return (...args: unknown[]) => {\n    if (called) return;\n    called = true;\n    return fn(...args);\n  };\n};\n\nexport function getConnectionPrototypeToInstrument(connection: any) {\n  const connectionPrototype = connection.prototype;\n  const basePrototype = Object.getPrototypeOf(connectionPrototype);\n\n  // mysql2@3.11.5 included a refactoring, where most code was moved out of the `Connection` class and into a shared base\n  // so we need to instrument that instead, see https://github.com/sidorares/node-mysql2/pull/3081\n  // This checks if the functions we're instrumenting are there on the base - we cannot use the presence of a base\n  // prototype since EventEmitter is the base for mysql2@<=3.11.4\n  if (\n    typeof basePrototype?.query === 'function' &&\n    typeof basePrototype?.execute === 'function'\n  ) {\n    return basePrototype;\n  }\n\n  // otherwise instrument the connection directly.\n  return connectionPrototype;\n}\n"]}