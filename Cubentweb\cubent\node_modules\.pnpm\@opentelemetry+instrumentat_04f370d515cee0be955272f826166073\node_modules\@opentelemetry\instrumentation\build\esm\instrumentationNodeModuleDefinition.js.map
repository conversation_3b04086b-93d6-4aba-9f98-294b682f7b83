{"version": 3, "file": "instrumentationNodeModuleDefinition.js", "sourceRoot": "", "sources": ["../../src/instrumentationNodeModuleDefinition.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAOH;IAIE,6CACS,IAAY,EACZ,iBAA2B;IAClC,8DAA8D;IACvD,KAAqD;IAC5D,8DAA8D;IACvD,OAAwD,EAC/D,KAAmC;QAN5B,SAAI,GAAJ,IAAI,CAAQ;QACZ,sBAAiB,GAAjB,iBAAiB,CAAU;QAE3B,UAAK,GAAL,KAAK,CAAgD;QAErD,YAAO,GAAP,OAAO,CAAiD;QAG/D,IAAI,CAAC,KAAK,GAAG,KAAK,IAAI,EAAE,CAAC;IAC3B,CAAC;IACH,0CAAC;AAAD,CAAC,AAfD,IAeC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  InstrumentationModuleDefinition,\n  InstrumentationModuleFile,\n} from './types';\n\nexport class InstrumentationNodeModuleDefinition\n  implements InstrumentationModuleDefinition\n{\n  files: InstrumentationModuleFile[];\n  constructor(\n    public name: string,\n    public supportedVersions: string[],\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    public patch?: (exports: any, moduleVersion?: string) => any,\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    public unpatch?: (exports: any, moduleVersion?: string) => void,\n    files?: InstrumentationModuleFile[]\n  ) {\n    this.files = files || [];\n  }\n}\n"]}