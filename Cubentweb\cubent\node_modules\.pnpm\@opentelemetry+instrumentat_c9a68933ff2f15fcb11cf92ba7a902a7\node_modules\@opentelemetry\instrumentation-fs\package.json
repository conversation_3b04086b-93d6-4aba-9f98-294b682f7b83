{"name": "@opentelemetry/instrumentation-fs", "version": "0.19.1", "description": "OpenTelemetry instrumentation for `node:fs` file system interactions module", "main": "build/src/index.js", "types": "build/src/index.d.ts", "repository": "open-telemetry/opentelemetry-js-contrib", "scripts": {"test": "mocha 'test/**/*.test.ts'", "tdd": "npm run test -- --watch-extensions ts --watch", "clean": "rimraf build/*", "lint": "eslint . --ext .ts", "lint:fix": "eslint . --ext .ts --fix", "lint:readme": "node ../../../scripts/lint-readme.js", "prewatch": "npm run precompile", "prepublishOnly": "npm run compile", "version:update": "node ../../../scripts/version-update.js", "compile": "tsc -p ."}, "keywords": ["fs", "instrumentation", "nodejs", "opentelemetry", "profiling", "tracing"], "author": "OpenTelemetry Authors", "license": "Apache-2.0", "engines": {"node": ">=14"}, "files": ["build/src/**/*.js", "build/src/**/*.js.map", "build/src/**/*.d.ts"], "publishConfig": {"access": "public"}, "peerDependencies": {"@opentelemetry/api": "^1.3.0"}, "devDependencies": {"@opentelemetry/api": "^1.3.0", "@opentelemetry/context-async-hooks": "^1.8.0", "@opentelemetry/resources": "^1.8.0", "@opentelemetry/sdk-trace-base": "^1.8.0", "@opentelemetry/sdk-trace-node": "^1.8.0", "@types/mocha": "7.0.2", "@types/node": "18.18.14", "@types/sinon": "^10.0.11", "nyc": "15.1.0", "rimraf": "5.0.10", "sinon": "15.2.0", "typescript": "4.4.4"}, "dependencies": {"@opentelemetry/core": "^1.8.0", "@opentelemetry/instrumentation": "^0.57.1"}, "homepage": "https://github.com/open-telemetry/opentelemetry-js-contrib/tree/main/plugins/node/instrumentation-fs#readme", "gitHead": "1eb77007669bae87fe5664d68ba6533b95275d52"}