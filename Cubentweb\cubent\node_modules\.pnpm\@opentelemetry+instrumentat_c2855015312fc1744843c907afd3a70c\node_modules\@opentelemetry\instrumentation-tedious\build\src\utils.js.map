{"version": 3, "file": "utils.js", "sourceRoot": "", "sources": ["../../src/utils.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH;;;;;GAKG;AACH,SAAgB,WAAW,CACzB,SAAiB,EACjB,EAAsB,EACtB,GAAuB,EACvB,aAAiC;IAEjC,IAAI,SAAS,KAAK,cAAc,IAAI,aAAa,IAAI,EAAE,EAAE;QACvD,OAAO,GAAG,SAAS,IAAI,aAAa,IAAI,EAAE,EAAE,CAAC;KAC9C;IACD,IAAI,SAAS,KAAK,eAAe,EAAE;QACjC,sDAAsD;QACtD,IAAI,EAAE,EAAE;YACN,OAAO,GAAG,SAAS,IAAI,GAAG,IAAI,EAAE,EAAE,CAAC;SACpC;QACD,OAAO,GAAG,SAAS,IAAI,GAAG,EAAE,CAAC;KAC9B;IACD,+DAA+D;IAC/D,IAAI,EAAE,EAAE;QACN,OAAO,GAAG,SAAS,IAAI,EAAE,EAAE,CAAC;KAC7B;IACD,OAAO,GAAG,SAAS,EAAE,CAAC;AACxB,CAAC;AArBD,kCAqBC;AAEM,MAAM,IAAI,GAAG,CAAC,EAAY,EAAE,EAAE;IACnC,IAAI,MAAM,GAAG,KAAK,CAAC;IACnB,OAAO,CAAC,GAAG,IAAe,EAAE,EAAE;QAC5B,IAAI,MAAM;YAAE,OAAO;QACnB,MAAM,GAAG,IAAI,CAAC;QACd,OAAO,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC;IACrB,CAAC,CAAC;AACJ,CAAC,CAAC;AAPW,QAAA,IAAI,QAOf", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * The span name SHOULD be set to a low cardinality value\n * representing the statement executed on the database.\n *\n * @returns Operation executed on Tedious Connection. Does not map to SQL statement in any way.\n */\nexport function getSpanName(\n  operation: string,\n  db: string | undefined,\n  sql: string | undefined,\n  bulkLoadTable: string | undefined\n): string {\n  if (operation === 'execBulkLoad' && bulkLoadTable && db) {\n    return `${operation} ${bulkLoadTable} ${db}`;\n  }\n  if (operation === 'callProcedure') {\n    // `sql` refers to procedure name with `callProcedure`\n    if (db) {\n      return `${operation} ${sql} ${db}`;\n    }\n    return `${operation} ${sql}`;\n  }\n  // do not use `sql` in general case because of high-cardinality\n  if (db) {\n    return `${operation} ${db}`;\n  }\n  return `${operation}`;\n}\n\nexport const once = (fn: Function) => {\n  let called = false;\n  return (...args: unknown[]) => {\n    if (called) return;\n    called = true;\n    return fn(...args);\n  };\n};\n"]}