{"name": "es-fresh", "description": "fresh rewrite in TypeScript with ESM and CommonJS targets", "version": "0.0.8", "repository": "https://github.com/talentlessguy/es-fresh.git", "engines": {"node": ">=12.x"}, "files": ["dist", "src"], "author": "talentlessguy <<EMAIL>>", "license": "MIT", "type": "module", "main": "dist/index.cjs", "module": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}, "./package.json": "./package.json", "./": "./"}, "devDependencies": {"@types/node": "^14.6.0", "tsup": "^3.6.1", "typescript": "^4.0.2"}, "scripts": {"prepare": "pnpm build", "build": "tsup src/index.ts --minify-whitespace --format cjs,esm --dts"}}