{"name": "@opentelemetry/instrumentation-amqplib", "version": "0.46.1", "description": "OpenTelemetry instrumentation for the `amqplib` messaging client for RabbitMQ", "keywords": ["amqplib", "opentelemetry", "rabbitmq", "AMQP 0-9-1"], "homepage": "https://github.com/open-telemetry/opentelemetry-js-contrib/tree/main/plugins/node/instrumentation-amqplib#readme", "license": "Apache-2.0", "author": "OpenTelemetry Authors", "bugs": {"url": "https://github.com/open-telemetry/opentelemetry-js-contrib/issues"}, "main": "build/src/index.js", "types": "build/src/index.d.ts", "files": ["build/src/**/*.js", "build/src/**/*.js.map", "build/src/**/*.d.ts"], "publishConfig": {"access": "public"}, "repository": "open-telemetry/opentelemetry-js-contrib", "scripts": {"clean": "rimraf build/*", "compile": "tsc -p .", "lint": "eslint . --ext .ts", "lint:fix": "eslint . --ext .ts --fix", "lint:readme": "node ../../../scripts/lint-readme.js", "prewatch": "npm run precompile", "prepublishOnly": "npm run compile", "tdd": "npm run test -- --watch-extensions ts --watch", "test": "nyc mocha --require '@opentelemetry/contrib-test-utils' 'test/**/*.test.ts'", "test-all-versions": "tav", "version:update": "node ../../../scripts/version-update.js", "watch": "tsc -w", "test:docker:run": "docker run -d --hostname demo-amqplib-rabbit --name amqplib-unittests -p 22221:5672 --env RABBITMQ_DEFAULT_USER=username --env RABBITMQ_DEFAULT_PASS=password rabbitmq:3"}, "peerDependencies": {"@opentelemetry/api": "^1.3.0"}, "dependencies": {"@opentelemetry/core": "^1.8.0", "@opentelemetry/instrumentation": "^0.57.1", "@opentelemetry/semantic-conventions": "^1.27.0"}, "devDependencies": {"@opentelemetry/api": "^1.3.0", "@opentelemetry/contrib-test-utils": "^0.45.1", "@types/amqplib": "^0.5.17", "@types/lodash": "4.14.199", "@types/mocha": "8.2.3", "@types/node": "18.18.14", "@types/sinon": "10.0.20", "amqplib": "0.8.0", "expect": "29.2.0", "lodash": "4.17.21", "nyc": "15.1.0", "sinon": "15.2.0", "test-all-versions": "6.1.0", "typescript": "4.4.4"}, "engines": {"node": ">=14"}, "gitHead": "1eb77007669bae87fe5664d68ba6533b95275d52"}