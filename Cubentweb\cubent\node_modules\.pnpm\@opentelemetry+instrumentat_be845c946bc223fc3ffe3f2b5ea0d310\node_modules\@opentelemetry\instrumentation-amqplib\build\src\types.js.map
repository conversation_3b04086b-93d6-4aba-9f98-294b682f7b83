{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../../src/types.ts"], "names": [], "mappings": ";;;AA0DA,IAAY,YAUX;AAVD,WAAY,YAAY;IACtB,oCAAoB,CAAA;IACpB,2BAAW,CAAA;IACX,iCAAiB,CAAA;IACjB,iCAAiB,CAAA;IACjB,6BAAa,CAAA;IACb,mCAAmB,CAAA;IACnB,gDAAgC,CAAA;IAChC,8CAA8B,CAAA;IAC9B,kEAAkD,CAAA;AACpD,CAAC,EAVW,YAAY,GAAZ,oBAAY,KAAZ,oBAAY,QAUvB;AAmCY,QAAA,cAAc,GAAiC;IAC1D,gBAAgB,EAAE,IAAI,GAAG,EAAE;IAC3B,kBAAkB,EAAE,KAAK;CAC1B,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { Span } from '@opentelemetry/api';\nimport { InstrumentationConfig } from '@opentelemetry/instrumentation';\n\nexport interface PublishInfo {\n  moduleVersion: string | undefined;\n  exchange: string;\n  routingKey: string;\n  content: Buffer;\n  options?: AmqplibPublishOptions;\n  isConfirmChannel?: boolean;\n}\n\nexport interface PublishConfirmedInfo extends PublishInfo {\n  confirmError?: any;\n}\n\nexport interface ConsumeInfo {\n  moduleVersion: string | undefined;\n  msg: ConsumeMessage;\n}\n\nexport interface ConsumeEndInfo {\n  msg: ConsumeMessage;\n  rejected: boolean | null;\n  endOperation: EndOperation;\n}\n\nexport interface AmqplibPublishCustomAttributeFunction {\n  (span: Span, publishInfo: PublishInfo): void;\n}\n\nexport interface AmqplibPublishConfirmCustomAttributeFunction {\n  (span: Span, publishConfirmedInto: PublishConfirmedInfo): void;\n}\n\nexport interface AmqplibConsumeCustomAttributeFunction {\n  (span: Span, consumeInfo: ConsumeInfo): void;\n}\n\nexport interface AmqplibConsumeEndCustomAttributeFunction {\n  (span: Span, consumeEndInfo: ConsumeEndInfo): void;\n}\n\nexport enum EndOperation {\n  AutoAck = 'auto ack',\n  Ack = 'ack',\n  AckAll = 'ackAll',\n  Reject = 'reject',\n  Nack = 'nack',\n  NackAll = 'nackAll',\n  ChannelClosed = 'channel closed',\n  ChannelError = 'channel error',\n  InstrumentationTimeout = 'instrumentation timeout',\n}\n\nexport interface AmqplibInstrumentationConfig extends InstrumentationConfig {\n  /** hook for adding custom attributes before publish message is sent */\n  publishHook?: AmqplibPublishCustomAttributeFunction;\n\n  /** hook for adding custom attributes after publish message is confirmed by the broker */\n  publishConfirmHook?: AmqplibPublishConfirmCustomAttributeFunction;\n\n  /** hook for adding custom attributes before consumer message is processed */\n  consumeHook?: AmqplibConsumeCustomAttributeFunction;\n\n  /** hook for adding custom attributes after consumer message is acked to server */\n  consumeEndHook?: AmqplibConsumeEndCustomAttributeFunction;\n\n  /**\n   * When user is setting up consume callback, it is user's responsibility to call\n   * ack/nack etc on the msg to resolve it in the server.\n   * If user is not calling the ack, the message will stay in the queue until\n   * channel is closed, or until server timeout expires (if configured).\n   * While we wait for the ack, a reference to the message is stored in plugin, which\n   * will never be garbage collected.\n   * To prevent memory leak, plugin has it's own configuration of timeout, which\n   * will close the span if user did not call ack after this timeout.\n   * If timeout is not big enough, span might be closed with 'InstrumentationTimeout',\n   * and then received valid ack from the user later which will not be instrumented.\n   *\n   * Default is 1 minute\n   */\n  consumeTimeoutMs?: number;\n\n  /** option to use a span link for the consume message instead of continuing a trace */\n  useLinksForConsume?: boolean;\n}\n\nexport const DEFAULT_CONFIG: AmqplibInstrumentationConfig = {\n  consumeTimeoutMs: 1000 * 60, // 1 minute\n  useLinksForConsume: false,\n};\n\n// The following types are vendored from `@types/amqplib@0.10.1` - commit SHA: 4205e03127692a40b4871709a7134fe4e2ed5510\n\n// Vendored from: https://github.com/DefinitelyTyped/DefinitelyTyped/blob/4205e03127692a40b4871709a7134fe4e2ed5510/types/amqplib/properties.d.ts#L108\n// This exists in `@types/amqplib` as `Options.Publish`. We're renaming things\n// here to avoid importing the whole Options namespace.\nexport interface AmqplibPublishOptions {\n  expiration?: string | number;\n  userId?: string;\n  CC?: string | string[];\n\n  mandatory?: boolean;\n  persistent?: boolean;\n  deliveryMode?: boolean | number;\n  BCC?: string | string[];\n\n  contentType?: string;\n  contentEncoding?: string;\n  headers?: any;\n  priority?: number;\n  correlationId?: string;\n  replyTo?: string;\n  messageId?: string;\n  timestamp?: number;\n  type?: string;\n  appId?: string;\n}\n\n// Vendored from: https://github.com/DefinitelyTyped/DefinitelyTyped/blob/4205e03127692a40b4871709a7134fe4e2ed5510/types/amqplib/properties.d.ts#L142\nexport interface Message {\n  content: Buffer;\n  fields: MessageFields;\n  properties: MessageProperties;\n}\n\nexport interface ConsumeMessage extends Message {\n  fields: ConsumeMessageFields;\n}\n\nexport interface CommonMessageFields {\n  deliveryTag: number;\n  redelivered: boolean;\n  exchange: string;\n  routingKey: string;\n}\n\nexport interface MessageFields extends CommonMessageFields {\n  messageCount?: number;\n  consumerTag?: string;\n}\n\nexport interface ConsumeMessageFields extends CommonMessageFields {\n  deliveryTag: number;\n}\n\nexport interface MessageProperties {\n  contentType: any | undefined;\n  contentEncoding: any | undefined;\n  headers: any;\n  deliveryMode: any | undefined;\n  priority: any | undefined;\n  correlationId: any | undefined;\n  replyTo: any | undefined;\n  expiration: any | undefined;\n  messageId: any | undefined;\n  timestamp: any | undefined;\n  type: any | undefined;\n  userId: any | undefined;\n  appId: any | undefined;\n  clusterId: any | undefined;\n}\n"]}