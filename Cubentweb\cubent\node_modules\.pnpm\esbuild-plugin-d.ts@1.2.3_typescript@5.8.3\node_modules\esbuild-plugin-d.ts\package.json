{"name": "esbuild-plugin-d.ts", "version": "1.2.3", "main": "./dist/index.js", "exports": "./dist/index.mjs", "types": "./dist/index.d.ts", "files": ["dist", "LICENSE"], "repository": "https://github.com/Floffah/esbuild-plugin-d.ts.git", "author": "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "license": "MIT", "engines": {"node": ">=12.0.0"}, "scripts": {"prepare": "yarn build --minify", "build": "tsup", "test": "jest", "deps": "yarn ncu -u --reject chalk", "publish:local": "export $(cat .env | xargs) && yarn semantic-release --dry-run"}, "publishConfig": {"access": "public", "provenance": true}, "peerDependencies": {"typescript": "*"}, "dependencies": {"chalk": "4.1.2", "lodash.merge": "^4.6.2"}, "devDependencies": {"@jest/globals": "^29.7.0", "@trivago/prettier-plugin-sort-imports": "^4.3.0", "@tsconfig/node21": "^21.0.0", "@types/jest": "^29.5.11", "@types/lodash.merge": "^4", "@types/node": "^20.10.4", "@types/tmp": "^0.2.6", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "esbuild": "^0.19.9", "eslint": "^8.55.0", "eslint-config-prettier": "^9.1.0", "jest": "^29.7.0", "npm-check-updates": "^16.14.11", "prettier": "^3.1.1", "semantic-release": "^22.0.12", "ts-jest": "^29.1.1", "tsup": "^8.0.1", "typescript": "^5.3.3"}, "packageManager": "yarn@4.0.2"}