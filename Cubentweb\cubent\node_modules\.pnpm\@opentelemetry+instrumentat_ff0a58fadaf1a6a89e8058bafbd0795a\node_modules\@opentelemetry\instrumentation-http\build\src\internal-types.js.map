{"version": 3, "file": "internal-types.js", "sourceRoot": "", "sources": ["../../src/internal-types.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport type * as http from 'http';\nimport type * as https from 'https';\nimport { get, IncomingMessage, request } from 'http';\nimport * as url from 'url';\n\nexport type IgnoreMatcher = string | RegExp | ((url: string) => boolean);\nexport type HttpCallback = (res: IncomingMessage) => void;\nexport type RequestFunction = typeof request;\nexport type GetFunction = typeof get;\n\nexport type HttpCallbackOptional = HttpCallback | undefined;\n\n// from node 10+\nexport type RequestSignature = [http.RequestOptions, HttpCallbackOptional] &\n  HttpCallback;\n\nexport type HttpRequestArgs = Array<HttpCallbackOptional | RequestSignature>;\n\nexport type ParsedRequestOptions =\n  | (http.RequestOptions & Partial<url.UrlWithParsedQuery>)\n  | http.RequestOptions;\nexport type Http = typeof http;\nexport type Https = typeof https;\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport type Func<T> = (...args: any[]) => T;\n\nexport interface Err extends Error {\n  errno?: number;\n  code?: string;\n  path?: string;\n  syscall?: string;\n  stack?: string;\n}\n\n/**\n * Tracks whether this instrumentation emits old experimental,\n * new stable, or both semantic conventions.\n *\n * Enum values chosen such that the enum may be used as a bitmask.\n */\nexport const enum SemconvStability {\n  /** Emit only stable semantic conventions */\n  STABLE = 0x1,\n  /** Emit only old semantic conventions*/\n  OLD = 0x2,\n  /** Emit both stable and old semantic conventions*/\n  DUPLICATE = 0x1 | 0x2,\n}\n"]}