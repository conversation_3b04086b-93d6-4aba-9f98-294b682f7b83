'use strict';

!function(){try{var e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},n=(new e.Error).stack;n&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[n]="473becf7-39bd-560d-a682-a0b61c44ed43")}catch(e){}}();

var chunkTKGT252T_js = require('./chunk-TKGT252T.js');
require('fs');
require('path');

var Dn=chunkTKGT252T_js.c((Ct,jn)=>{(function(m,s){typeof Ct=="object"&&typeof jn<"u"?s(Ct):typeof define=="function"&&define.amd?define(["exports"],s):(m=typeof globalThis<"u"?globalThis:m||self,s(m.WebStreamsPolyfill={}));})(Ct,function(m){function s(){}function l(e){return typeof e=="object"&&e!==null||typeof e=="function"}let h=s;function f(e,t){try{Object.defineProperty(e,"name",{value:t,configurable:!0});}catch{}}let R=Promise,C=Promise.prototype.then,ue=Promise.reject.bind(R);function P(e){return new R(e)}function S(e){return P(t=>t(e))}function d(e){return ue(e)}function W(e,t,r){return C.call(e,t,r)}function v(e,t,r){W(W(e,t,r),void 0,h);}function M(e,t){v(e,t);}function N(e,t){v(e,void 0,t);}function Q(e,t,r){return W(e,t,r)}function Re(e){W(e,void 0,h);}let fe=e=>{if(typeof queueMicrotask=="function")fe=queueMicrotask;else {let t=S(void 0);fe=r=>W(t,r);}return fe(e)};function de(e,t,r){if(typeof e!="function")throw new TypeError("Argument is not a function");return Function.prototype.apply.call(e,t,r)}function J(e,t,r){try{return S(de(e,t,r))}catch(n){return d(n)}}let pr=16384;class k{constructor(){this._cursor=0,this._size=0,this._front={_elements:[],_next:void 0},this._back=this._front,this._cursor=0,this._size=0;}get length(){return this._size}push(t){let r=this._back,n=r;r._elements.length===pr-1&&(n={_elements:[],_next:void 0}),r._elements.push(t),n!==r&&(this._back=n,r._next=n),++this._size;}shift(){let t=this._front,r=t,n=this._cursor,o=n+1,a=t._elements,i=a[n];return o===pr&&(r=t._next,o=0),--this._size,this._cursor=o,t!==r&&(this._front=r),a[n]=void 0,i}forEach(t){let r=this._cursor,n=this._front,o=n._elements;for(;(r!==o.length||n._next!==void 0)&&!(r===o.length&&(n=n._next,o=n._elements,r=0,o.length===0));)t(o[r]),++r;}peek(){let t=this._front,r=this._cursor;return t._elements[r]}}let yr=Symbol("[[AbortSteps]]"),_r=Symbol("[[ErrorSteps]]"),Tt=Symbol("[[CancelSteps]]"),Pt=Symbol("[[PullSteps]]"),vt=Symbol("[[ReleaseSteps]]");function Sr(e,t){e._ownerReadableStream=t,t._reader=e,t._state==="readable"?qt(e):t._state==="closed"?Gn(e):gr(e,t._storedError);}function Et(e,t){let r=e._ownerReadableStream;return j(r,t)}function U(e){let t=e._ownerReadableStream;t._state==="readable"?At(e,new TypeError("Reader was released and can no longer be used to monitor the stream's closedness")):xn(e,new TypeError("Reader was released and can no longer be used to monitor the stream's closedness")),t._readableStreamController[vt](),t._reader=void 0,e._ownerReadableStream=void 0;}function Ve(e){return new TypeError("Cannot "+e+" a stream using a released reader")}function qt(e){e._closedPromise=P((t,r)=>{e._closedPromise_resolve=t,e._closedPromise_reject=r;});}function gr(e,t){qt(e),At(e,t);}function Gn(e){qt(e),Rr(e);}function At(e,t){e._closedPromise_reject!==void 0&&(Re(e._closedPromise),e._closedPromise_reject(t),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0);}function xn(e,t){gr(e,t);}function Rr(e){e._closedPromise_resolve!==void 0&&(e._closedPromise_resolve(void 0),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0);}let wr=Number.isFinite||function(e){return typeof e=="number"&&isFinite(e)},Zn=Math.trunc||function(e){return e<0?Math.ceil(e):Math.floor(e)};function Xn(e){return typeof e=="object"||typeof e=="function"}function L(e,t){if(e!==void 0&&!Xn(e))throw new TypeError(`${t} is not an object.`)}function O(e,t){if(typeof e!="function")throw new TypeError(`${t} is not a function.`)}function Jn(e){return typeof e=="object"&&e!==null||typeof e=="function"}function Cr(e,t){if(!Jn(e))throw new TypeError(`${t} is not an object.`)}function Y(e,t,r){if(e===void 0)throw new TypeError(`Parameter ${t} is required in '${r}'.`)}function Wt(e,t,r){if(e===void 0)throw new TypeError(`${t} is required in '${r}'.`)}function Bt(e){return Number(e)}function Tr(e){return e===0?0:e}function Kn(e){return Tr(Zn(e))}function kt(e,t){let n=Number.MAX_SAFE_INTEGER,o=Number(e);if(o=Tr(o),!wr(o))throw new TypeError(`${t} is not a finite number`);if(o=Kn(o),o<0||o>n)throw new TypeError(`${t} is outside the accepted range of 0 to ${n}, inclusive`);return !wr(o)||o===0?0:o}function Ot(e,t){if(!ae(e))throw new TypeError(`${t} is not a ReadableStream.`)}function we(e){return new K(e)}function Pr(e,t){e._reader._readRequests.push(t);}function Ft(e,t,r){let o=e._reader._readRequests.shift();r?o._closeSteps():o._chunkSteps(t);}function He(e){return e._reader._readRequests.length}function vr(e){let t=e._reader;return !(t===void 0||!ee(t))}class K{constructor(t){if(Y(t,1,"ReadableStreamDefaultReader"),Ot(t,"First parameter"),ie(t))throw new TypeError("This stream has already been locked for exclusive reading by another reader");Sr(this,t),this._readRequests=new k;}get closed(){return ee(this)?this._closedPromise:d(Ge("closed"))}cancel(t=void 0){return ee(this)?this._ownerReadableStream===void 0?d(Ve("cancel")):Et(this,t):d(Ge("cancel"))}read(){if(!ee(this))return d(Ge("read"));if(this._ownerReadableStream===void 0)return d(Ve("read from"));let t,r,n=P((a,i)=>{t=a,r=i;});return ze(this,{_chunkSteps:a=>t({value:a,done:!1}),_closeSteps:()=>t({value:void 0,done:!0}),_errorSteps:a=>r(a)}),n}releaseLock(){if(!ee(this))throw Ge("releaseLock");this._ownerReadableStream!==void 0&&eo(this);}}Object.defineProperties(K.prototype,{cancel:{enumerable:!0},read:{enumerable:!0},releaseLock:{enumerable:!0},closed:{enumerable:!0}}),f(K.prototype.cancel,"cancel"),f(K.prototype.read,"read"),f(K.prototype.releaseLock,"releaseLock"),typeof Symbol.toStringTag=="symbol"&&Object.defineProperty(K.prototype,Symbol.toStringTag,{value:"ReadableStreamDefaultReader",configurable:!0});function ee(e){return !l(e)||!Object.prototype.hasOwnProperty.call(e,"_readRequests")?!1:e instanceof K}function ze(e,t){let r=e._ownerReadableStream;r._disturbed=!0,r._state==="closed"?t._closeSteps():r._state==="errored"?t._errorSteps(r._storedError):r._readableStreamController[Pt](t);}function eo(e){U(e);let t=new TypeError("Reader was released");Er(e,t);}function Er(e,t){let r=e._readRequests;e._readRequests=new k,r.forEach(n=>{n._errorSteps(t);});}function Ge(e){return new TypeError(`ReadableStreamDefaultReader.prototype.${e} can only be used on a ReadableStreamDefaultReader`)}let to=Object.getPrototypeOf(Object.getPrototypeOf(async function*(){}).prototype);class qr{constructor(t,r){this._ongoingPromise=void 0,this._isFinished=!1,this._reader=t,this._preventCancel=r;}next(){let t=()=>this._nextSteps();return this._ongoingPromise=this._ongoingPromise?Q(this._ongoingPromise,t,t):t(),this._ongoingPromise}return(t){let r=()=>this._returnSteps(t);return this._ongoingPromise?Q(this._ongoingPromise,r,r):r()}_nextSteps(){if(this._isFinished)return Promise.resolve({value:void 0,done:!0});let t=this._reader,r,n,o=P((i,u)=>{r=i,n=u;});return ze(t,{_chunkSteps:i=>{this._ongoingPromise=void 0,fe(()=>r({value:i,done:!1}));},_closeSteps:()=>{this._ongoingPromise=void 0,this._isFinished=!0,U(t),r({value:void 0,done:!0});},_errorSteps:i=>{this._ongoingPromise=void 0,this._isFinished=!0,U(t),n(i);}}),o}_returnSteps(t){if(this._isFinished)return Promise.resolve({value:t,done:!0});this._isFinished=!0;let r=this._reader;if(!this._preventCancel){let n=Et(r,t);return U(r),Q(n,()=>({value:t,done:!0}))}return U(r),S({value:t,done:!0})}}let Ar={next(){return Wr(this)?this._asyncIteratorImpl.next():d(Br("next"))},return(e){return Wr(this)?this._asyncIteratorImpl.return(e):d(Br("return"))}};Object.setPrototypeOf(Ar,to);function ro(e,t){let r=we(e),n=new qr(r,t),o=Object.create(Ar);return o._asyncIteratorImpl=n,o}function Wr(e){if(!l(e)||!Object.prototype.hasOwnProperty.call(e,"_asyncIteratorImpl"))return !1;try{return e._asyncIteratorImpl instanceof qr}catch{return !1}}function Br(e){return new TypeError(`ReadableStreamAsyncIterator.${e} can only be used on a ReadableSteamAsyncIterator`)}let kr=Number.isNaN||function(e){return e!==e};var zt,It,jt;function Ie(e){return e.slice()}function Or(e,t,r,n,o){new Uint8Array(e).set(new Uint8Array(r,n,o),t);}let V=e=>(typeof e.transfer=="function"?V=t=>t.transfer():typeof structuredClone=="function"?V=t=>structuredClone(t,{transfer:[t]}):V=t=>t,V(e)),te=e=>(typeof e.detached=="boolean"?te=t=>t.detached:te=t=>t.byteLength===0,te(e));function Fr(e,t,r){if(e.slice)return e.slice(t,r);let n=r-t,o=new ArrayBuffer(n);return Or(o,0,e,t,n),o}function xe(e,t){let r=e[t];if(r!=null){if(typeof r!="function")throw new TypeError(`${String(t)} is not a function`);return r}}function no(e){let t={[Symbol.iterator]:()=>e.iterator},r=async function*(){return yield*t}(),n=r.next;return {iterator:r,nextMethod:n,done:!1}}let Dt=(jt=(zt=Symbol.asyncIterator)!==null&&zt!==void 0?zt:(It=Symbol.for)===null||It===void 0?void 0:It.call(Symbol,"Symbol.asyncIterator"))!==null&&jt!==void 0?jt:"@@asyncIterator";function zr(e,t="sync",r){if(r===void 0)if(t==="async"){if(r=xe(e,Dt),r===void 0){let a=xe(e,Symbol.iterator),i=zr(e,"sync",a);return no(i)}}else r=xe(e,Symbol.iterator);if(r===void 0)throw new TypeError("The object is not iterable");let n=de(r,e,[]);if(!l(n))throw new TypeError("The iterator method must return an object");let o=n.next;return {iterator:n,nextMethod:o,done:!1}}function oo(e){let t=de(e.nextMethod,e.iterator,[]);if(!l(t))throw new TypeError("The iterator.next() method must return an object");return t}function ao(e){return !!e.done}function io(e){return e.value}function so(e){return !(typeof e!="number"||kr(e)||e<0)}function Ir(e){let t=Fr(e.buffer,e.byteOffset,e.byteOffset+e.byteLength);return new Uint8Array(t)}function Mt(e){let t=e._queue.shift();return e._queueTotalSize-=t.size,e._queueTotalSize<0&&(e._queueTotalSize=0),t.value}function Lt(e,t,r){if(!so(r)||r===1/0)throw new RangeError("Size must be a finite, non-NaN, non-negative number.");e._queue.push({value:t,size:r}),e._queueTotalSize+=r;}function lo(e){return e._queue.peek().value}function re(e){e._queue=new k,e._queueTotalSize=0;}function jr(e){return e===DataView}function uo(e){return jr(e.constructor)}function fo(e){return jr(e)?1:e.BYTES_PER_ELEMENT}class ce{constructor(){throw new TypeError("Illegal constructor")}get view(){if(!$t(this))throw Vt("view");return this._view}respond(t){if(!$t(this))throw Vt("respond");if(Y(t,1,"respond"),t=kt(t,"First parameter"),this._associatedReadableByteStreamController===void 0)throw new TypeError("This BYOB request has been invalidated");if(te(this._view.buffer))throw new TypeError("The BYOB request's buffer has been detached and so cannot be used as a response");Ke(this._associatedReadableByteStreamController,t);}respondWithNewView(t){if(!$t(this))throw Vt("respondWithNewView");if(Y(t,1,"respondWithNewView"),!ArrayBuffer.isView(t))throw new TypeError("You can only respond with array buffer views");if(this._associatedReadableByteStreamController===void 0)throw new TypeError("This BYOB request has been invalidated");if(te(t.buffer))throw new TypeError("The given view's buffer has been detached and so cannot be used as a response");et(this._associatedReadableByteStreamController,t);}}Object.defineProperties(ce.prototype,{respond:{enumerable:!0},respondWithNewView:{enumerable:!0},view:{enumerable:!0}}),f(ce.prototype.respond,"respond"),f(ce.prototype.respondWithNewView,"respondWithNewView"),typeof Symbol.toStringTag=="symbol"&&Object.defineProperty(ce.prototype,Symbol.toStringTag,{value:"ReadableStreamBYOBRequest",configurable:!0});class H{constructor(){throw new TypeError("Illegal constructor")}get byobRequest(){if(!he(this))throw De("byobRequest");return Yt(this)}get desiredSize(){if(!he(this))throw De("desiredSize");return Hr(this)}close(){if(!he(this))throw De("close");if(this._closeRequested)throw new TypeError("The stream has already been closed; do not close it again!");let t=this._controlledReadableByteStream._state;if(t!=="readable")throw new TypeError(`The stream (in ${t} state) is not in the readable state and cannot be closed`);je(this);}enqueue(t){if(!he(this))throw De("enqueue");if(Y(t,1,"enqueue"),!ArrayBuffer.isView(t))throw new TypeError("chunk must be an array buffer view");if(t.byteLength===0)throw new TypeError("chunk must have non-zero byteLength");if(t.buffer.byteLength===0)throw new TypeError("chunk's buffer must have non-zero byteLength");if(this._closeRequested)throw new TypeError("stream is closed or draining");let r=this._controlledReadableByteStream._state;if(r!=="readable")throw new TypeError(`The stream (in ${r} state) is not in the readable state and cannot be enqueued to`);Je(this,t);}error(t=void 0){if(!he(this))throw De("error");F(this,t);}[Tt](t){Dr(this),re(this);let r=this._cancelAlgorithm(t);return Xe(this),r}[Pt](t){let r=this._controlledReadableByteStream;if(this._queueTotalSize>0){Vr(this,t);return}let n=this._autoAllocateChunkSize;if(n!==void 0){let o;try{o=new ArrayBuffer(n);}catch(i){t._errorSteps(i);return}let a={buffer:o,bufferByteLength:n,byteOffset:0,byteLength:n,bytesFilled:0,minimumFill:1,elementSize:1,viewConstructor:Uint8Array,readerType:"default"};this._pendingPullIntos.push(a);}Pr(r,t),be(this);}[vt](){if(this._pendingPullIntos.length>0){let t=this._pendingPullIntos.peek();t.readerType="none",this._pendingPullIntos=new k,this._pendingPullIntos.push(t);}}}Object.defineProperties(H.prototype,{close:{enumerable:!0},enqueue:{enumerable:!0},error:{enumerable:!0},byobRequest:{enumerable:!0},desiredSize:{enumerable:!0}}),f(H.prototype.close,"close"),f(H.prototype.enqueue,"enqueue"),f(H.prototype.error,"error"),typeof Symbol.toStringTag=="symbol"&&Object.defineProperty(H.prototype,Symbol.toStringTag,{value:"ReadableByteStreamController",configurable:!0});function he(e){return !l(e)||!Object.prototype.hasOwnProperty.call(e,"_controlledReadableByteStream")?!1:e instanceof H}function $t(e){return !l(e)||!Object.prototype.hasOwnProperty.call(e,"_associatedReadableByteStreamController")?!1:e instanceof ce}function be(e){if(!po(e))return;if(e._pulling){e._pullAgain=!0;return}e._pulling=!0;let r=e._pullAlgorithm();v(r,()=>(e._pulling=!1,e._pullAgain&&(e._pullAgain=!1,be(e)),null),n=>(F(e,n),null));}function Dr(e){Qt(e),e._pendingPullIntos=new k;}function Nt(e,t){let r=!1;e._state==="closed"&&(r=!0);let n=Mr(t);t.readerType==="default"?Ft(e,n,r):wo(e,n,r);}function Mr(e){let t=e.bytesFilled,r=e.elementSize;return new e.viewConstructor(e.buffer,e.byteOffset,t/r)}function Ze(e,t,r,n){e._queue.push({buffer:t,byteOffset:r,byteLength:n}),e._queueTotalSize+=n;}function Lr(e,t,r,n){let o;try{o=Fr(t,r,r+n);}catch(a){throw F(e,a),a}Ze(e,o,0,n);}function $r(e,t){t.bytesFilled>0&&Lr(e,t.buffer,t.byteOffset,t.bytesFilled),Ce(e);}function Nr(e,t){let r=Math.min(e._queueTotalSize,t.byteLength-t.bytesFilled),n=t.bytesFilled+r,o=r,a=!1,i=n%t.elementSize,u=n-i;u>=t.minimumFill&&(o=u-t.bytesFilled,a=!0);let p=e._queue;for(;o>0;){let c=p.peek(),y=Math.min(o,c.byteLength),_=t.byteOffset+t.bytesFilled;Or(t.buffer,_,c.buffer,c.byteOffset,y),c.byteLength===y?p.shift():(c.byteOffset+=y,c.byteLength-=y),e._queueTotalSize-=y,Qr(e,y,t),o-=y;}return a}function Qr(e,t,r){r.bytesFilled+=t;}function Ur(e){e._queueTotalSize===0&&e._closeRequested?(Xe(e),Ue(e._controlledReadableByteStream)):be(e);}function Qt(e){e._byobRequest!==null&&(e._byobRequest._associatedReadableByteStreamController=void 0,e._byobRequest._view=null,e._byobRequest=null);}function Ut(e){for(;e._pendingPullIntos.length>0;){if(e._queueTotalSize===0)return;let t=e._pendingPullIntos.peek();Nr(e,t)&&(Ce(e),Nt(e._controlledReadableByteStream,t));}}function co(e){let t=e._controlledReadableByteStream._reader;for(;t._readRequests.length>0;){if(e._queueTotalSize===0)return;let r=t._readRequests.shift();Vr(e,r);}}function ho(e,t,r,n){let o=e._controlledReadableByteStream,a=t.constructor,i=fo(a),{byteOffset:u,byteLength:p}=t,c=r*i,y;try{y=V(t.buffer);}catch(w){n._errorSteps(w);return}let _={buffer:y,bufferByteLength:y.byteLength,byteOffset:u,byteLength:p,bytesFilled:0,minimumFill:c,elementSize:i,viewConstructor:a,readerType:"byob"};if(e._pendingPullIntos.length>0){e._pendingPullIntos.push(_),Zr(o,n);return}if(o._state==="closed"){let w=new a(_.buffer,_.byteOffset,0);n._closeSteps(w);return}if(e._queueTotalSize>0){if(Nr(e,_)){let w=Mr(_);Ur(e),n._chunkSteps(w);return}if(e._closeRequested){let w=new TypeError("Insufficient bytes to fill elements in the given buffer");F(e,w),n._errorSteps(w);return}}e._pendingPullIntos.push(_),Zr(o,n),be(e);}function bo(e,t){t.readerType==="none"&&Ce(e);let r=e._controlledReadableByteStream;if(Ht(r))for(;Xr(r)>0;){let n=Ce(e);Nt(r,n);}}function mo(e,t,r){if(Qr(e,t,r),r.readerType==="none"){$r(e,r),Ut(e);return}if(r.bytesFilled<r.minimumFill)return;Ce(e);let n=r.bytesFilled%r.elementSize;if(n>0){let o=r.byteOffset+r.bytesFilled;Lr(e,r.buffer,o-n,n);}r.bytesFilled-=n,Nt(e._controlledReadableByteStream,r),Ut(e);}function Yr(e,t){let r=e._pendingPullIntos.peek();Qt(e),e._controlledReadableByteStream._state==="closed"?bo(e,r):mo(e,t,r),be(e);}function Ce(e){return e._pendingPullIntos.shift()}function po(e){let t=e._controlledReadableByteStream;return t._state!=="readable"||e._closeRequested||!e._started?!1:!!(vr(t)&&He(t)>0||Ht(t)&&Xr(t)>0||Hr(e)>0)}function Xe(e){e._pullAlgorithm=void 0,e._cancelAlgorithm=void 0;}function je(e){let t=e._controlledReadableByteStream;if(!(e._closeRequested||t._state!=="readable")){if(e._queueTotalSize>0){e._closeRequested=!0;return}if(e._pendingPullIntos.length>0){let r=e._pendingPullIntos.peek();if(r.bytesFilled%r.elementSize!==0){let n=new TypeError("Insufficient bytes to fill elements in the given buffer");throw F(e,n),n}}Xe(e),Ue(t);}}function Je(e,t){let r=e._controlledReadableByteStream;if(e._closeRequested||r._state!=="readable")return;let{buffer:n,byteOffset:o,byteLength:a}=t;if(te(n))throw new TypeError("chunk's buffer is detached and so cannot be enqueued");let i=V(n);if(e._pendingPullIntos.length>0){let u=e._pendingPullIntos.peek();if(te(u.buffer))throw new TypeError("The BYOB request's buffer has been detached and so cannot be filled with an enqueued chunk");Qt(e),u.buffer=V(u.buffer),u.readerType==="none"&&$r(e,u);}if(vr(r))if(co(e),He(r)===0)Ze(e,i,o,a);else {e._pendingPullIntos.length>0&&Ce(e);let u=new Uint8Array(i,o,a);Ft(r,u,!1);}else Ht(r)?(Ze(e,i,o,a),Ut(e)):Ze(e,i,o,a);be(e);}function F(e,t){let r=e._controlledReadableByteStream;r._state==="readable"&&(Dr(e),re(e),Xe(e),wn(r,t));}function Vr(e,t){let r=e._queue.shift();e._queueTotalSize-=r.byteLength,Ur(e);let n=new Uint8Array(r.buffer,r.byteOffset,r.byteLength);t._chunkSteps(n);}function Yt(e){if(e._byobRequest===null&&e._pendingPullIntos.length>0){let t=e._pendingPullIntos.peek(),r=new Uint8Array(t.buffer,t.byteOffset+t.bytesFilled,t.byteLength-t.bytesFilled),n=Object.create(ce.prototype);_o(n,e,r),e._byobRequest=n;}return e._byobRequest}function Hr(e){let t=e._controlledReadableByteStream._state;return t==="errored"?null:t==="closed"?0:e._strategyHWM-e._queueTotalSize}function Ke(e,t){let r=e._pendingPullIntos.peek();if(e._controlledReadableByteStream._state==="closed"){if(t!==0)throw new TypeError("bytesWritten must be 0 when calling respond() on a closed stream")}else {if(t===0)throw new TypeError("bytesWritten must be greater than 0 when calling respond() on a readable stream");if(r.bytesFilled+t>r.byteLength)throw new RangeError("bytesWritten out of range")}r.buffer=V(r.buffer),Yr(e,t);}function et(e,t){let r=e._pendingPullIntos.peek();if(e._controlledReadableByteStream._state==="closed"){if(t.byteLength!==0)throw new TypeError("The view's length must be 0 when calling respondWithNewView() on a closed stream")}else if(t.byteLength===0)throw new TypeError("The view's length must be greater than 0 when calling respondWithNewView() on a readable stream");if(r.byteOffset+r.bytesFilled!==t.byteOffset)throw new RangeError("The region specified by view does not match byobRequest");if(r.bufferByteLength!==t.buffer.byteLength)throw new RangeError("The buffer of view has different capacity than byobRequest");if(r.bytesFilled+t.byteLength>r.byteLength)throw new RangeError("The region specified by view is larger than byobRequest");let o=t.byteLength;r.buffer=V(t.buffer),Yr(e,o);}function Gr(e,t,r,n,o,a,i){t._controlledReadableByteStream=e,t._pullAgain=!1,t._pulling=!1,t._byobRequest=null,t._queue=t._queueTotalSize=void 0,re(t),t._closeRequested=!1,t._started=!1,t._strategyHWM=a,t._pullAlgorithm=n,t._cancelAlgorithm=o,t._autoAllocateChunkSize=i,t._pendingPullIntos=new k,e._readableStreamController=t;let u=r();v(S(u),()=>(t._started=!0,be(t),null),p=>(F(t,p),null));}function yo(e,t,r){let n=Object.create(H.prototype),o,a,i;t.start!==void 0?o=()=>t.start(n):o=()=>{},t.pull!==void 0?a=()=>t.pull(n):a=()=>S(void 0),t.cancel!==void 0?i=p=>t.cancel(p):i=()=>S(void 0);let u=t.autoAllocateChunkSize;if(u===0)throw new TypeError("autoAllocateChunkSize must be greater than 0");Gr(e,n,o,a,i,r,u);}function _o(e,t,r){e._associatedReadableByteStreamController=t,e._view=r;}function Vt(e){return new TypeError(`ReadableStreamBYOBRequest.prototype.${e} can only be used on a ReadableStreamBYOBRequest`)}function De(e){return new TypeError(`ReadableByteStreamController.prototype.${e} can only be used on a ReadableByteStreamController`)}function So(e,t){L(e,t);let r=e==null?void 0:e.mode;return {mode:r===void 0?void 0:go(r,`${t} has member 'mode' that`)}}function go(e,t){if(e=`${e}`,e!=="byob")throw new TypeError(`${t} '${e}' is not a valid enumeration value for ReadableStreamReaderMode`);return e}function Ro(e,t){var r;L(e,t);let n=(r=e==null?void 0:e.min)!==null&&r!==void 0?r:1;return {min:kt(n,`${t} has member 'min' that`)}}function xr(e){return new ne(e)}function Zr(e,t){e._reader._readIntoRequests.push(t);}function wo(e,t,r){let o=e._reader._readIntoRequests.shift();r?o._closeSteps(t):o._chunkSteps(t);}function Xr(e){return e._reader._readIntoRequests.length}function Ht(e){let t=e._reader;return !(t===void 0||!me(t))}class ne{constructor(t){if(Y(t,1,"ReadableStreamBYOBReader"),Ot(t,"First parameter"),ie(t))throw new TypeError("This stream has already been locked for exclusive reading by another reader");if(!he(t._readableStreamController))throw new TypeError("Cannot construct a ReadableStreamBYOBReader for a stream not constructed with a byte source");Sr(this,t),this._readIntoRequests=new k;}get closed(){return me(this)?this._closedPromise:d(tt("closed"))}cancel(t=void 0){return me(this)?this._ownerReadableStream===void 0?d(Ve("cancel")):Et(this,t):d(tt("cancel"))}read(t,r={}){if(!me(this))return d(tt("read"));if(!ArrayBuffer.isView(t))return d(new TypeError("view must be an array buffer view"));if(t.byteLength===0)return d(new TypeError("view must have non-zero byteLength"));if(t.buffer.byteLength===0)return d(new TypeError("view's buffer must have non-zero byteLength"));if(te(t.buffer))return d(new TypeError("view's buffer has been detached"));let n;try{n=Ro(r,"options");}catch(c){return d(c)}let o=n.min;if(o===0)return d(new TypeError("options.min must be greater than 0"));if(uo(t)){if(o>t.byteLength)return d(new RangeError("options.min must be less than or equal to view's byteLength"))}else if(o>t.length)return d(new RangeError("options.min must be less than or equal to view's length"));if(this._ownerReadableStream===void 0)return d(Ve("read from"));let a,i,u=P((c,y)=>{a=c,i=y;});return Jr(this,t,o,{_chunkSteps:c=>a({value:c,done:!1}),_closeSteps:c=>a({value:c,done:!0}),_errorSteps:c=>i(c)}),u}releaseLock(){if(!me(this))throw tt("releaseLock");this._ownerReadableStream!==void 0&&Co(this);}}Object.defineProperties(ne.prototype,{cancel:{enumerable:!0},read:{enumerable:!0},releaseLock:{enumerable:!0},closed:{enumerable:!0}}),f(ne.prototype.cancel,"cancel"),f(ne.prototype.read,"read"),f(ne.prototype.releaseLock,"releaseLock"),typeof Symbol.toStringTag=="symbol"&&Object.defineProperty(ne.prototype,Symbol.toStringTag,{value:"ReadableStreamBYOBReader",configurable:!0});function me(e){return !l(e)||!Object.prototype.hasOwnProperty.call(e,"_readIntoRequests")?!1:e instanceof ne}function Jr(e,t,r,n){let o=e._ownerReadableStream;o._disturbed=!0,o._state==="errored"?n._errorSteps(o._storedError):ho(o._readableStreamController,t,r,n);}function Co(e){U(e);let t=new TypeError("Reader was released");Kr(e,t);}function Kr(e,t){let r=e._readIntoRequests;e._readIntoRequests=new k,r.forEach(n=>{n._errorSteps(t);});}function tt(e){return new TypeError(`ReadableStreamBYOBReader.prototype.${e} can only be used on a ReadableStreamBYOBReader`)}function Me(e,t){let{highWaterMark:r}=e;if(r===void 0)return t;if(kr(r)||r<0)throw new RangeError("Invalid highWaterMark");return r}function rt(e){let{size:t}=e;return t||(()=>1)}function nt(e,t){L(e,t);let r=e==null?void 0:e.highWaterMark,n=e==null?void 0:e.size;return {highWaterMark:r===void 0?void 0:Bt(r),size:n===void 0?void 0:To(n,`${t} has member 'size' that`)}}function To(e,t){return O(e,t),r=>Bt(e(r))}function Po(e,t){L(e,t);let r=e==null?void 0:e.abort,n=e==null?void 0:e.close,o=e==null?void 0:e.start,a=e==null?void 0:e.type,i=e==null?void 0:e.write;return {abort:r===void 0?void 0:vo(r,e,`${t} has member 'abort' that`),close:n===void 0?void 0:Eo(n,e,`${t} has member 'close' that`),start:o===void 0?void 0:qo(o,e,`${t} has member 'start' that`),write:i===void 0?void 0:Ao(i,e,`${t} has member 'write' that`),type:a}}function vo(e,t,r){return O(e,r),n=>J(e,t,[n])}function Eo(e,t,r){return O(e,r),()=>J(e,t,[])}function qo(e,t,r){return O(e,r),n=>de(e,t,[n])}function Ao(e,t,r){return O(e,r),(n,o)=>J(e,t,[n,o])}function en(e,t){if(!Te(e))throw new TypeError(`${t} is not a WritableStream.`)}function Wo(e){if(typeof e!="object"||e===null)return !1;try{return typeof e.aborted=="boolean"}catch{return !1}}let Bo=typeof AbortController=="function";function ko(){if(Bo)return new AbortController}class oe{constructor(t={},r={}){t===void 0?t=null:Cr(t,"First parameter");let n=nt(r,"Second parameter"),o=Po(t,"First parameter");if(rn(this),o.type!==void 0)throw new RangeError("Invalid type is specified");let i=rt(n),u=Me(n,1);Vo(this,o,u,i);}get locked(){if(!Te(this))throw lt("locked");return Pe(this)}abort(t=void 0){return Te(this)?Pe(this)?d(new TypeError("Cannot abort a stream that already has a writer")):ot(this,t):d(lt("abort"))}close(){return Te(this)?Pe(this)?d(new TypeError("Cannot close a stream that already has a writer")):$(this)?d(new TypeError("Cannot close an already-closing stream")):nn(this):d(lt("close"))}getWriter(){if(!Te(this))throw lt("getWriter");return tn(this)}}Object.defineProperties(oe.prototype,{abort:{enumerable:!0},close:{enumerable:!0},getWriter:{enumerable:!0},locked:{enumerable:!0}}),f(oe.prototype.abort,"abort"),f(oe.prototype.close,"close"),f(oe.prototype.getWriter,"getWriter"),typeof Symbol.toStringTag=="symbol"&&Object.defineProperty(oe.prototype,Symbol.toStringTag,{value:"WritableStream",configurable:!0});function tn(e){return new G(e)}function Oo(e,t,r,n,o=1,a=()=>1){let i=Object.create(oe.prototype);rn(i);let u=Object.create(ve.prototype);return fn(i,u,e,t,r,n,o,a),i}function rn(e){e._state="writable",e._storedError=void 0,e._writer=void 0,e._writableStreamController=void 0,e._writeRequests=new k,e._inFlightWriteRequest=void 0,e._closeRequest=void 0,e._inFlightCloseRequest=void 0,e._pendingAbortRequest=void 0,e._backpressure=!1;}function Te(e){return !l(e)||!Object.prototype.hasOwnProperty.call(e,"_writableStreamController")?!1:e instanceof oe}function Pe(e){return e._writer!==void 0}function ot(e,t){var r;if(e._state==="closed"||e._state==="errored")return S(void 0);e._writableStreamController._abortReason=t,(r=e._writableStreamController._abortController)===null||r===void 0||r.abort(t);let n=e._state;if(n==="closed"||n==="errored")return S(void 0);if(e._pendingAbortRequest!==void 0)return e._pendingAbortRequest._promise;let o=!1;n==="erroring"&&(o=!0,t=void 0);let a=P((i,u)=>{e._pendingAbortRequest={_promise:void 0,_resolve:i,_reject:u,_reason:t,_wasAlreadyErroring:o};});return e._pendingAbortRequest._promise=a,o||xt(e,t),a}function nn(e){let t=e._state;if(t==="closed"||t==="errored")return d(new TypeError(`The stream (in ${t} state) is not in the writable state and cannot be closed`));let r=P((o,a)=>{let i={_resolve:o,_reject:a};e._closeRequest=i;}),n=e._writer;return n!==void 0&&e._backpressure&&t==="writable"&&nr(n),Ho(e._writableStreamController),r}function Fo(e){return P((r,n)=>{let o={_resolve:r,_reject:n};e._writeRequests.push(o);})}function Gt(e,t){if(e._state==="writable"){xt(e,t);return}Zt(e);}function xt(e,t){let r=e._writableStreamController;e._state="erroring",e._storedError=t;let n=e._writer;n!==void 0&&an(n,t),!Mo(e)&&r._started&&Zt(e);}function Zt(e){e._state="errored",e._writableStreamController[_r]();let t=e._storedError;if(e._writeRequests.forEach(o=>{o._reject(t);}),e._writeRequests=new k,e._pendingAbortRequest===void 0){at(e);return}let r=e._pendingAbortRequest;if(e._pendingAbortRequest=void 0,r._wasAlreadyErroring){r._reject(t),at(e);return}let n=e._writableStreamController[yr](r._reason);v(n,()=>(r._resolve(),at(e),null),o=>(r._reject(o),at(e),null));}function zo(e){e._inFlightWriteRequest._resolve(void 0),e._inFlightWriteRequest=void 0;}function Io(e,t){e._inFlightWriteRequest._reject(t),e._inFlightWriteRequest=void 0,Gt(e,t);}function jo(e){e._inFlightCloseRequest._resolve(void 0),e._inFlightCloseRequest=void 0,e._state==="erroring"&&(e._storedError=void 0,e._pendingAbortRequest!==void 0&&(e._pendingAbortRequest._resolve(),e._pendingAbortRequest=void 0)),e._state="closed";let r=e._writer;r!==void 0&&bn(r);}function Do(e,t){e._inFlightCloseRequest._reject(t),e._inFlightCloseRequest=void 0,e._pendingAbortRequest!==void 0&&(e._pendingAbortRequest._reject(t),e._pendingAbortRequest=void 0),Gt(e,t);}function $(e){return !(e._closeRequest===void 0&&e._inFlightCloseRequest===void 0)}function Mo(e){return !(e._inFlightWriteRequest===void 0&&e._inFlightCloseRequest===void 0)}function Lo(e){e._inFlightCloseRequest=e._closeRequest,e._closeRequest=void 0;}function $o(e){e._inFlightWriteRequest=e._writeRequests.shift();}function at(e){e._closeRequest!==void 0&&(e._closeRequest._reject(e._storedError),e._closeRequest=void 0);let t=e._writer;t!==void 0&&tr(t,e._storedError);}function Xt(e,t){let r=e._writer;r!==void 0&&t!==e._backpressure&&(t?ea(r):nr(r)),e._backpressure=t;}class G{constructor(t){if(Y(t,1,"WritableStreamDefaultWriter"),en(t,"First parameter"),Pe(t))throw new TypeError("This stream has already been locked for exclusive writing by another writer");this._ownerWritableStream=t,t._writer=this;let r=t._state;if(r==="writable")!$(t)&&t._backpressure?ft(this):mn(this),ut(this);else if(r==="erroring")rr(this,t._storedError),ut(this);else if(r==="closed")mn(this),Jo(this);else {let n=t._storedError;rr(this,n),hn(this,n);}}get closed(){return pe(this)?this._closedPromise:d(ye("closed"))}get desiredSize(){if(!pe(this))throw ye("desiredSize");if(this._ownerWritableStream===void 0)throw $e("desiredSize");return Yo(this)}get ready(){return pe(this)?this._readyPromise:d(ye("ready"))}abort(t=void 0){return pe(this)?this._ownerWritableStream===void 0?d($e("abort")):No(this,t):d(ye("abort"))}close(){if(!pe(this))return d(ye("close"));let t=this._ownerWritableStream;return t===void 0?d($e("close")):$(t)?d(new TypeError("Cannot close an already-closing stream")):on(this)}releaseLock(){if(!pe(this))throw ye("releaseLock");this._ownerWritableStream!==void 0&&sn(this);}write(t=void 0){return pe(this)?this._ownerWritableStream===void 0?d($e("write to")):ln(this,t):d(ye("write"))}}Object.defineProperties(G.prototype,{abort:{enumerable:!0},close:{enumerable:!0},releaseLock:{enumerable:!0},write:{enumerable:!0},closed:{enumerable:!0},desiredSize:{enumerable:!0},ready:{enumerable:!0}}),f(G.prototype.abort,"abort"),f(G.prototype.close,"close"),f(G.prototype.releaseLock,"releaseLock"),f(G.prototype.write,"write"),typeof Symbol.toStringTag=="symbol"&&Object.defineProperty(G.prototype,Symbol.toStringTag,{value:"WritableStreamDefaultWriter",configurable:!0});function pe(e){return !l(e)||!Object.prototype.hasOwnProperty.call(e,"_ownerWritableStream")?!1:e instanceof G}function No(e,t){let r=e._ownerWritableStream;return ot(r,t)}function on(e){let t=e._ownerWritableStream;return nn(t)}function Qo(e){let t=e._ownerWritableStream,r=t._state;return $(t)||r==="closed"?S(void 0):r==="errored"?d(t._storedError):on(e)}function Uo(e,t){e._closedPromiseState==="pending"?tr(e,t):Ko(e,t);}function an(e,t){e._readyPromiseState==="pending"?pn(e,t):ta(e,t);}function Yo(e){let t=e._ownerWritableStream,r=t._state;return r==="errored"||r==="erroring"?null:r==="closed"?0:dn(t._writableStreamController)}function sn(e){let t=e._ownerWritableStream,r=new TypeError("Writer was released and can no longer be used to monitor the stream's closedness");an(e,r),Uo(e,r),t._writer=void 0,e._ownerWritableStream=void 0;}function ln(e,t){let r=e._ownerWritableStream,n=r._writableStreamController,o=Go(n,t);if(r!==e._ownerWritableStream)return d($e("write to"));let a=r._state;if(a==="errored")return d(r._storedError);if($(r)||a==="closed")return d(new TypeError("The stream is closing or closed and cannot be written to"));if(a==="erroring")return d(r._storedError);let i=Fo(r);return xo(n,t,o),i}let un={};class ve{constructor(){throw new TypeError("Illegal constructor")}get abortReason(){if(!Jt(this))throw er("abortReason");return this._abortReason}get signal(){if(!Jt(this))throw er("signal");if(this._abortController===void 0)throw new TypeError("WritableStreamDefaultController.prototype.signal is not supported");return this._abortController.signal}error(t=void 0){if(!Jt(this))throw er("error");this._controlledWritableStream._state==="writable"&&cn(this,t);}[yr](t){let r=this._abortAlgorithm(t);return it(this),r}[_r](){re(this);}}Object.defineProperties(ve.prototype,{abortReason:{enumerable:!0},signal:{enumerable:!0},error:{enumerable:!0}}),typeof Symbol.toStringTag=="symbol"&&Object.defineProperty(ve.prototype,Symbol.toStringTag,{value:"WritableStreamDefaultController",configurable:!0});function Jt(e){return !l(e)||!Object.prototype.hasOwnProperty.call(e,"_controlledWritableStream")?!1:e instanceof ve}function fn(e,t,r,n,o,a,i,u){t._controlledWritableStream=e,e._writableStreamController=t,t._queue=void 0,t._queueTotalSize=void 0,re(t),t._abortReason=void 0,t._abortController=ko(),t._started=!1,t._strategySizeAlgorithm=u,t._strategyHWM=i,t._writeAlgorithm=n,t._closeAlgorithm=o,t._abortAlgorithm=a;let p=Kt(t);Xt(e,p);let c=r(),y=S(c);v(y,()=>(t._started=!0,st(t),null),_=>(t._started=!0,Gt(e,_),null));}function Vo(e,t,r,n){let o=Object.create(ve.prototype),a,i,u,p;t.start!==void 0?a=()=>t.start(o):a=()=>{},t.write!==void 0?i=c=>t.write(c,o):i=()=>S(void 0),t.close!==void 0?u=()=>t.close():u=()=>S(void 0),t.abort!==void 0?p=c=>t.abort(c):p=()=>S(void 0),fn(e,o,a,i,u,p,r,n);}function it(e){e._writeAlgorithm=void 0,e._closeAlgorithm=void 0,e._abortAlgorithm=void 0,e._strategySizeAlgorithm=void 0;}function Ho(e){Lt(e,un,0),st(e);}function Go(e,t){try{return e._strategySizeAlgorithm(t)}catch(r){return Le(e,r),1}}function dn(e){return e._strategyHWM-e._queueTotalSize}function xo(e,t,r){try{Lt(e,t,r);}catch(o){Le(e,o);return}let n=e._controlledWritableStream;if(!$(n)&&n._state==="writable"){let o=Kt(e);Xt(n,o);}st(e);}function st(e){let t=e._controlledWritableStream;if(!e._started||t._inFlightWriteRequest!==void 0)return;if(t._state==="erroring"){Zt(t);return}if(e._queue.length===0)return;let n=lo(e);n===un?Zo(e):Xo(e,n);}function Le(e,t){e._controlledWritableStream._state==="writable"&&cn(e,t);}function Zo(e){let t=e._controlledWritableStream;Lo(t),Mt(e);let r=e._closeAlgorithm();it(e),v(r,()=>(jo(t),null),n=>(Do(t,n),null));}function Xo(e,t){let r=e._controlledWritableStream;$o(r);let n=e._writeAlgorithm(t);v(n,()=>{zo(r);let o=r._state;if(Mt(e),!$(r)&&o==="writable"){let a=Kt(e);Xt(r,a);}return st(e),null},o=>(r._state==="writable"&&it(e),Io(r,o),null));}function Kt(e){return dn(e)<=0}function cn(e,t){let r=e._controlledWritableStream;it(e),xt(r,t);}function lt(e){return new TypeError(`WritableStream.prototype.${e} can only be used on a WritableStream`)}function er(e){return new TypeError(`WritableStreamDefaultController.prototype.${e} can only be used on a WritableStreamDefaultController`)}function ye(e){return new TypeError(`WritableStreamDefaultWriter.prototype.${e} can only be used on a WritableStreamDefaultWriter`)}function $e(e){return new TypeError("Cannot "+e+" a stream using a released writer")}function ut(e){e._closedPromise=P((t,r)=>{e._closedPromise_resolve=t,e._closedPromise_reject=r,e._closedPromiseState="pending";});}function hn(e,t){ut(e),tr(e,t);}function Jo(e){ut(e),bn(e);}function tr(e,t){e._closedPromise_reject!==void 0&&(Re(e._closedPromise),e._closedPromise_reject(t),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0,e._closedPromiseState="rejected");}function Ko(e,t){hn(e,t);}function bn(e){e._closedPromise_resolve!==void 0&&(e._closedPromise_resolve(void 0),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0,e._closedPromiseState="resolved");}function ft(e){e._readyPromise=P((t,r)=>{e._readyPromise_resolve=t,e._readyPromise_reject=r;}),e._readyPromiseState="pending";}function rr(e,t){ft(e),pn(e,t);}function mn(e){ft(e),nr(e);}function pn(e,t){e._readyPromise_reject!==void 0&&(Re(e._readyPromise),e._readyPromise_reject(t),e._readyPromise_resolve=void 0,e._readyPromise_reject=void 0,e._readyPromiseState="rejected");}function ea(e){ft(e);}function ta(e,t){rr(e,t);}function nr(e){e._readyPromise_resolve!==void 0&&(e._readyPromise_resolve(void 0),e._readyPromise_resolve=void 0,e._readyPromise_reject=void 0,e._readyPromiseState="fulfilled");}function ra(){if(typeof globalThis<"u")return globalThis;if(typeof self<"u")return self;if(typeof global<"u")return global}let or=ra();function na(e){if(!(typeof e=="function"||typeof e=="object")||e.name!=="DOMException")return !1;try{return new e,!0}catch{return !1}}function oa(){let e=or==null?void 0:or.DOMException;return na(e)?e:void 0}function aa(){let e=function(r,n){this.message=r||"",this.name=n||"Error",Error.captureStackTrace&&Error.captureStackTrace(this,this.constructor);};return f(e,"DOMException"),e.prototype=Object.create(Error.prototype),Object.defineProperty(e.prototype,"constructor",{value:e,writable:!0,configurable:!0}),e}let ia=oa()||aa();function yn(e,t,r,n,o,a){let i=we(e),u=tn(t);e._disturbed=!0;let p=!1,c=S(void 0);return P((y,_)=>{let w;if(a!==void 0){if(w=()=>{let b=a.reason!==void 0?a.reason:new ia("Aborted","AbortError"),g=[];n||g.push(()=>t._state==="writable"?ot(t,b):S(void 0)),o||g.push(()=>e._state==="readable"?j(e,b):S(void 0)),A(()=>Promise.all(g.map(T=>T())),!0,b);},a.aborted){w();return}a.addEventListener("abort",w);}function D(){return P((b,g)=>{function T(B){B?b():W(We(),T,g);}T(!1);})}function We(){return p?S(!0):W(u._readyPromise,()=>P((b,g)=>{ze(i,{_chunkSteps:T=>{c=W(ln(u,T),void 0,s),b(!1);},_closeSteps:()=>b(!0),_errorSteps:g});}))}if(Z(e,i._closedPromise,b=>(n?z(!0,b):A(()=>ot(t,b),!0,b),null)),Z(t,u._closedPromise,b=>(o?z(!0,b):A(()=>j(e,b),!0,b),null)),q(e,i._closedPromise,()=>(r?z():A(()=>Qo(u)),null)),$(t)||t._state==="closed"){let b=new TypeError("the destination writable stream closed before all data could be piped to it");o?z(!0,b):A(()=>j(e,b),!0,b);}Re(D());function le(){let b=c;return W(c,()=>b!==c?le():void 0)}function Z(b,g,T){b._state==="errored"?T(b._storedError):N(g,T);}function q(b,g,T){b._state==="closed"?T():M(g,T);}function A(b,g,T){if(p)return;p=!0,t._state==="writable"&&!$(t)?M(le(),B):B();function B(){return v(b(),()=>X(g,T),Be=>X(!0,Be)),null}}function z(b,g){p||(p=!0,t._state==="writable"&&!$(t)?M(le(),()=>X(b,g)):X(b,g));}function X(b,g){return sn(u),U(i),a!==void 0&&a.removeEventListener("abort",w),b?_(g):y(void 0),null}})}class x{constructor(){throw new TypeError("Illegal constructor")}get desiredSize(){if(!dt(this))throw ht("desiredSize");return ar(this)}close(){if(!dt(this))throw ht("close");if(!qe(this))throw new TypeError("The stream is not in a state that permits close");_e(this);}enqueue(t=void 0){if(!dt(this))throw ht("enqueue");if(!qe(this))throw new TypeError("The stream is not in a state that permits enqueue");return Ee(this,t)}error(t=void 0){if(!dt(this))throw ht("error");I(this,t);}[Tt](t){re(this);let r=this._cancelAlgorithm(t);return ct(this),r}[Pt](t){let r=this._controlledReadableStream;if(this._queue.length>0){let n=Mt(this);this._closeRequested&&this._queue.length===0?(ct(this),Ue(r)):Ne(this),t._chunkSteps(n);}else Pr(r,t),Ne(this);}[vt](){}}Object.defineProperties(x.prototype,{close:{enumerable:!0},enqueue:{enumerable:!0},error:{enumerable:!0},desiredSize:{enumerable:!0}}),f(x.prototype.close,"close"),f(x.prototype.enqueue,"enqueue"),f(x.prototype.error,"error"),typeof Symbol.toStringTag=="symbol"&&Object.defineProperty(x.prototype,Symbol.toStringTag,{value:"ReadableStreamDefaultController",configurable:!0});function dt(e){return !l(e)||!Object.prototype.hasOwnProperty.call(e,"_controlledReadableStream")?!1:e instanceof x}function Ne(e){if(!_n(e))return;if(e._pulling){e._pullAgain=!0;return}e._pulling=!0;let r=e._pullAlgorithm();v(r,()=>(e._pulling=!1,e._pullAgain&&(e._pullAgain=!1,Ne(e)),null),n=>(I(e,n),null));}function _n(e){let t=e._controlledReadableStream;return !qe(e)||!e._started?!1:!!(ie(t)&&He(t)>0||ar(e)>0)}function ct(e){e._pullAlgorithm=void 0,e._cancelAlgorithm=void 0,e._strategySizeAlgorithm=void 0;}function _e(e){if(!qe(e))return;let t=e._controlledReadableStream;e._closeRequested=!0,e._queue.length===0&&(ct(e),Ue(t));}function Ee(e,t){if(!qe(e))return;let r=e._controlledReadableStream;if(ie(r)&&He(r)>0)Ft(r,t,!1);else {let n;try{n=e._strategySizeAlgorithm(t);}catch(o){throw I(e,o),o}try{Lt(e,t,n);}catch(o){throw I(e,o),o}}Ne(e);}function I(e,t){let r=e._controlledReadableStream;r._state==="readable"&&(re(e),ct(e),wn(r,t));}function ar(e){let t=e._controlledReadableStream._state;return t==="errored"?null:t==="closed"?0:e._strategyHWM-e._queueTotalSize}function sa(e){return !_n(e)}function qe(e){let t=e._controlledReadableStream._state;return !e._closeRequested&&t==="readable"}function Sn(e,t,r,n,o,a,i){t._controlledReadableStream=e,t._queue=void 0,t._queueTotalSize=void 0,re(t),t._started=!1,t._closeRequested=!1,t._pullAgain=!1,t._pulling=!1,t._strategySizeAlgorithm=i,t._strategyHWM=a,t._pullAlgorithm=n,t._cancelAlgorithm=o,e._readableStreamController=t;let u=r();v(S(u),()=>(t._started=!0,Ne(t),null),p=>(I(t,p),null));}function la(e,t,r,n){let o=Object.create(x.prototype),a,i,u;t.start!==void 0?a=()=>t.start(o):a=()=>{},t.pull!==void 0?i=()=>t.pull(o):i=()=>S(void 0),t.cancel!==void 0?u=p=>t.cancel(p):u=()=>S(void 0),Sn(e,o,a,i,u,r,n);}function ht(e){return new TypeError(`ReadableStreamDefaultController.prototype.${e} can only be used on a ReadableStreamDefaultController`)}function ua(e,t){return he(e._readableStreamController)?da(e):fa(e)}function fa(e,t){let r=we(e),n=!1,o=!1,a=!1,i=!1,u,p,c,y,_,w=P(q=>{_=q;});function D(){return n?(o=!0,S(void 0)):(n=!0,ze(r,{_chunkSteps:A=>{fe(()=>{o=!1;let z=A,X=A;a||Ee(c._readableStreamController,z),i||Ee(y._readableStreamController,X),n=!1,o&&D();});},_closeSteps:()=>{n=!1,a||_e(c._readableStreamController),i||_e(y._readableStreamController),(!a||!i)&&_(void 0);},_errorSteps:()=>{n=!1;}}),S(void 0))}function We(q){if(a=!0,u=q,i){let A=Ie([u,p]),z=j(e,A);_(z);}return w}function le(q){if(i=!0,p=q,a){let A=Ie([u,p]),z=j(e,A);_(z);}return w}function Z(){}return c=Qe(Z,D,We),y=Qe(Z,D,le),N(r._closedPromise,q=>(I(c._readableStreamController,q),I(y._readableStreamController,q),(!a||!i)&&_(void 0),null)),[c,y]}function da(e){let t=we(e),r=!1,n=!1,o=!1,a=!1,i=!1,u,p,c,y,_,w=P(b=>{_=b;});function D(b){N(b._closedPromise,g=>(b!==t||(F(c._readableStreamController,g),F(y._readableStreamController,g),(!a||!i)&&_(void 0)),null));}function We(){me(t)&&(U(t),t=we(e),D(t)),ze(t,{_chunkSteps:g=>{fe(()=>{n=!1,o=!1;let T=g,B=g;if(!a&&!i)try{B=Ir(g);}catch(Be){F(c._readableStreamController,Be),F(y._readableStreamController,Be),_(j(e,Be));return}a||Je(c._readableStreamController,T),i||Je(y._readableStreamController,B),r=!1,n?Z():o&&q();});},_closeSteps:()=>{r=!1,a||je(c._readableStreamController),i||je(y._readableStreamController),c._readableStreamController._pendingPullIntos.length>0&&Ke(c._readableStreamController,0),y._readableStreamController._pendingPullIntos.length>0&&Ke(y._readableStreamController,0),(!a||!i)&&_(void 0);},_errorSteps:()=>{r=!1;}});}function le(b,g){ee(t)&&(U(t),t=xr(e),D(t));let T=g?y:c,B=g?c:y;Jr(t,b,1,{_chunkSteps:ke=>{fe(()=>{n=!1,o=!1;let Oe=g?i:a;if(g?a:i)Oe||et(T._readableStreamController,ke);else {let zn;try{zn=Ir(ke);}catch(fr){F(T._readableStreamController,fr),F(B._readableStreamController,fr),_(j(e,fr));return}Oe||et(T._readableStreamController,ke),Je(B._readableStreamController,zn);}r=!1,n?Z():o&&q();});},_closeSteps:ke=>{r=!1;let Oe=g?i:a,Rt=g?a:i;Oe||je(T._readableStreamController),Rt||je(B._readableStreamController),ke!==void 0&&(Oe||et(T._readableStreamController,ke),!Rt&&B._readableStreamController._pendingPullIntos.length>0&&Ke(B._readableStreamController,0)),(!Oe||!Rt)&&_(void 0);},_errorSteps:()=>{r=!1;}});}function Z(){if(r)return n=!0,S(void 0);r=!0;let b=Yt(c._readableStreamController);return b===null?We():le(b._view,!1),S(void 0)}function q(){if(r)return o=!0,S(void 0);r=!0;let b=Yt(y._readableStreamController);return b===null?We():le(b._view,!0),S(void 0)}function A(b){if(a=!0,u=b,i){let g=Ie([u,p]),T=j(e,g);_(T);}return w}function z(b){if(i=!0,p=b,a){let g=Ie([u,p]),T=j(e,g);_(T);}return w}function X(){}return c=Rn(X,Z,A),y=Rn(X,q,z),D(t),[c,y]}function ca(e){return l(e)&&typeof e.getReader<"u"}function ha(e){return ca(e)?ma(e.getReader()):ba(e)}function ba(e){let t,r=zr(e,"async"),n=s;function o(){let i;try{i=oo(r);}catch(p){return d(p)}let u=S(i);return Q(u,p=>{if(!l(p))throw new TypeError("The promise returned by the iterator.next() method must fulfill with an object");if(ao(p))_e(t._readableStreamController);else {let y=io(p);Ee(t._readableStreamController,y);}})}function a(i){let u=r.iterator,p;try{p=xe(u,"return");}catch(_){return d(_)}if(p===void 0)return S(void 0);let c;try{c=de(p,u,[i]);}catch(_){return d(_)}let y=S(c);return Q(y,_=>{if(!l(_))throw new TypeError("The promise returned by the iterator.return() method must fulfill with an object")})}return t=Qe(n,o,a,0),t}function ma(e){let t,r=s;function n(){let a;try{a=e.read();}catch(i){return d(i)}return Q(a,i=>{if(!l(i))throw new TypeError("The promise returned by the reader.read() method must fulfill with an object");if(i.done)_e(t._readableStreamController);else {let u=i.value;Ee(t._readableStreamController,u);}})}function o(a){try{return S(e.cancel(a))}catch(i){return d(i)}}return t=Qe(r,n,o,0),t}function pa(e,t){L(e,t);let r=e,n=r==null?void 0:r.autoAllocateChunkSize,o=r==null?void 0:r.cancel,a=r==null?void 0:r.pull,i=r==null?void 0:r.start,u=r==null?void 0:r.type;return {autoAllocateChunkSize:n===void 0?void 0:kt(n,`${t} has member 'autoAllocateChunkSize' that`),cancel:o===void 0?void 0:ya(o,r,`${t} has member 'cancel' that`),pull:a===void 0?void 0:_a(a,r,`${t} has member 'pull' that`),start:i===void 0?void 0:Sa(i,r,`${t} has member 'start' that`),type:u===void 0?void 0:ga(u,`${t} has member 'type' that`)}}function ya(e,t,r){return O(e,r),n=>J(e,t,[n])}function _a(e,t,r){return O(e,r),n=>J(e,t,[n])}function Sa(e,t,r){return O(e,r),n=>de(e,t,[n])}function ga(e,t){if(e=`${e}`,e!=="bytes")throw new TypeError(`${t} '${e}' is not a valid enumeration value for ReadableStreamType`);return e}function Ra(e,t){return L(e,t),{preventCancel:!!(e==null?void 0:e.preventCancel)}}function gn(e,t){L(e,t);let r=e==null?void 0:e.preventAbort,n=e==null?void 0:e.preventCancel,o=e==null?void 0:e.preventClose,a=e==null?void 0:e.signal;return a!==void 0&&wa(a,`${t} has member 'signal' that`),{preventAbort:!!r,preventCancel:!!n,preventClose:!!o,signal:a}}function wa(e,t){if(!Wo(e))throw new TypeError(`${t} is not an AbortSignal.`)}function Ca(e,t){L(e,t);let r=e==null?void 0:e.readable;Wt(r,"readable","ReadableWritablePair"),Ot(r,`${t} has member 'readable' that`);let n=e==null?void 0:e.writable;return Wt(n,"writable","ReadableWritablePair"),en(n,`${t} has member 'writable' that`),{readable:r,writable:n}}class E{constructor(t={},r={}){t===void 0?t=null:Cr(t,"First parameter");let n=nt(r,"Second parameter"),o=pa(t,"First parameter");if(ir(this),o.type==="bytes"){if(n.size!==void 0)throw new RangeError("The strategy for a byte stream cannot have a size function");let a=Me(n,0);yo(this,o,a);}else {let a=rt(n),i=Me(n,1);la(this,o,i,a);}}get locked(){if(!ae(this))throw Se("locked");return ie(this)}cancel(t=void 0){return ae(this)?ie(this)?d(new TypeError("Cannot cancel a stream that already has a reader")):j(this,t):d(Se("cancel"))}getReader(t=void 0){if(!ae(this))throw Se("getReader");return So(t,"First parameter").mode===void 0?we(this):xr(this)}pipeThrough(t,r={}){if(!ae(this))throw Se("pipeThrough");Y(t,1,"pipeThrough");let n=Ca(t,"First parameter"),o=gn(r,"Second parameter");if(ie(this))throw new TypeError("ReadableStream.prototype.pipeThrough cannot be used on a locked ReadableStream");if(Pe(n.writable))throw new TypeError("ReadableStream.prototype.pipeThrough cannot be used on a locked WritableStream");let a=yn(this,n.writable,o.preventClose,o.preventAbort,o.preventCancel,o.signal);return Re(a),n.readable}pipeTo(t,r={}){if(!ae(this))return d(Se("pipeTo"));if(t===void 0)return d("Parameter 1 is required in 'pipeTo'.");if(!Te(t))return d(new TypeError("ReadableStream.prototype.pipeTo's first argument must be a WritableStream"));let n;try{n=gn(r,"Second parameter");}catch(o){return d(o)}return ie(this)?d(new TypeError("ReadableStream.prototype.pipeTo cannot be used on a locked ReadableStream")):Pe(t)?d(new TypeError("ReadableStream.prototype.pipeTo cannot be used on a locked WritableStream")):yn(this,t,n.preventClose,n.preventAbort,n.preventCancel,n.signal)}tee(){if(!ae(this))throw Se("tee");let t=ua(this);return Ie(t)}values(t=void 0){if(!ae(this))throw Se("values");let r=Ra(t,"First parameter");return ro(this,r.preventCancel)}[Dt](t){return this.values(t)}static from(t){return ha(t)}}Object.defineProperties(E,{from:{enumerable:!0}}),Object.defineProperties(E.prototype,{cancel:{enumerable:!0},getReader:{enumerable:!0},pipeThrough:{enumerable:!0},pipeTo:{enumerable:!0},tee:{enumerable:!0},values:{enumerable:!0},locked:{enumerable:!0}}),f(E.from,"from"),f(E.prototype.cancel,"cancel"),f(E.prototype.getReader,"getReader"),f(E.prototype.pipeThrough,"pipeThrough"),f(E.prototype.pipeTo,"pipeTo"),f(E.prototype.tee,"tee"),f(E.prototype.values,"values"),typeof Symbol.toStringTag=="symbol"&&Object.defineProperty(E.prototype,Symbol.toStringTag,{value:"ReadableStream",configurable:!0}),Object.defineProperty(E.prototype,Dt,{value:E.prototype.values,writable:!0,configurable:!0});function Qe(e,t,r,n=1,o=()=>1){let a=Object.create(E.prototype);ir(a);let i=Object.create(x.prototype);return Sn(a,i,e,t,r,n,o),a}function Rn(e,t,r){let n=Object.create(E.prototype);ir(n);let o=Object.create(H.prototype);return Gr(n,o,e,t,r,0,void 0),n}function ir(e){e._state="readable",e._reader=void 0,e._storedError=void 0,e._disturbed=!1;}function ae(e){return !l(e)||!Object.prototype.hasOwnProperty.call(e,"_readableStreamController")?!1:e instanceof E}function ie(e){return e._reader!==void 0}function j(e,t){if(e._disturbed=!0,e._state==="closed")return S(void 0);if(e._state==="errored")return d(e._storedError);Ue(e);let r=e._reader;if(r!==void 0&&me(r)){let o=r._readIntoRequests;r._readIntoRequests=new k,o.forEach(a=>{a._closeSteps(void 0);});}let n=e._readableStreamController[Tt](t);return Q(n,s)}function Ue(e){e._state="closed";let t=e._reader;if(t!==void 0&&(Rr(t),ee(t))){let r=t._readRequests;t._readRequests=new k,r.forEach(n=>{n._closeSteps();});}}function wn(e,t){e._state="errored",e._storedError=t;let r=e._reader;r!==void 0&&(At(r,t),ee(r)?Er(r,t):Kr(r,t));}function Se(e){return new TypeError(`ReadableStream.prototype.${e} can only be used on a ReadableStream`)}function Cn(e,t){L(e,t);let r=e==null?void 0:e.highWaterMark;return Wt(r,"highWaterMark","QueuingStrategyInit"),{highWaterMark:Bt(r)}}let Tn=e=>e.byteLength;f(Tn,"size");class bt{constructor(t){Y(t,1,"ByteLengthQueuingStrategy"),t=Cn(t,"First parameter"),this._byteLengthQueuingStrategyHighWaterMark=t.highWaterMark;}get highWaterMark(){if(!vn(this))throw Pn("highWaterMark");return this._byteLengthQueuingStrategyHighWaterMark}get size(){if(!vn(this))throw Pn("size");return Tn}}Object.defineProperties(bt.prototype,{highWaterMark:{enumerable:!0},size:{enumerable:!0}}),typeof Symbol.toStringTag=="symbol"&&Object.defineProperty(bt.prototype,Symbol.toStringTag,{value:"ByteLengthQueuingStrategy",configurable:!0});function Pn(e){return new TypeError(`ByteLengthQueuingStrategy.prototype.${e} can only be used on a ByteLengthQueuingStrategy`)}function vn(e){return !l(e)||!Object.prototype.hasOwnProperty.call(e,"_byteLengthQueuingStrategyHighWaterMark")?!1:e instanceof bt}let En=()=>1;f(En,"size");class mt{constructor(t){Y(t,1,"CountQueuingStrategy"),t=Cn(t,"First parameter"),this._countQueuingStrategyHighWaterMark=t.highWaterMark;}get highWaterMark(){if(!An(this))throw qn("highWaterMark");return this._countQueuingStrategyHighWaterMark}get size(){if(!An(this))throw qn("size");return En}}Object.defineProperties(mt.prototype,{highWaterMark:{enumerable:!0},size:{enumerable:!0}}),typeof Symbol.toStringTag=="symbol"&&Object.defineProperty(mt.prototype,Symbol.toStringTag,{value:"CountQueuingStrategy",configurable:!0});function qn(e){return new TypeError(`CountQueuingStrategy.prototype.${e} can only be used on a CountQueuingStrategy`)}function An(e){return !l(e)||!Object.prototype.hasOwnProperty.call(e,"_countQueuingStrategyHighWaterMark")?!1:e instanceof mt}function Ta(e,t){L(e,t);let r=e==null?void 0:e.cancel,n=e==null?void 0:e.flush,o=e==null?void 0:e.readableType,a=e==null?void 0:e.start,i=e==null?void 0:e.transform,u=e==null?void 0:e.writableType;return {cancel:r===void 0?void 0:qa(r,e,`${t} has member 'cancel' that`),flush:n===void 0?void 0:Pa(n,e,`${t} has member 'flush' that`),readableType:o,start:a===void 0?void 0:va(a,e,`${t} has member 'start' that`),transform:i===void 0?void 0:Ea(i,e,`${t} has member 'transform' that`),writableType:u}}function Pa(e,t,r){return O(e,r),n=>J(e,t,[n])}function va(e,t,r){return O(e,r),n=>de(e,t,[n])}function Ea(e,t,r){return O(e,r),(n,o)=>J(e,t,[n,o])}function qa(e,t,r){return O(e,r),n=>J(e,t,[n])}class pt{constructor(t={},r={},n={}){t===void 0&&(t=null);let o=nt(r,"Second parameter"),a=nt(n,"Third parameter"),i=Ta(t,"First parameter");if(i.readableType!==void 0)throw new RangeError("Invalid readableType specified");if(i.writableType!==void 0)throw new RangeError("Invalid writableType specified");let u=Me(a,0),p=rt(a),c=Me(o,1),y=rt(o),_,w=P(D=>{_=D;});Aa(this,w,c,y,u,p),Ba(this,i),i.start!==void 0?_(i.start(this._transformStreamController)):_(void 0);}get readable(){if(!Wn(this))throw Fn("readable");return this._readable}get writable(){if(!Wn(this))throw Fn("writable");return this._writable}}Object.defineProperties(pt.prototype,{readable:{enumerable:!0},writable:{enumerable:!0}}),typeof Symbol.toStringTag=="symbol"&&Object.defineProperty(pt.prototype,Symbol.toStringTag,{value:"TransformStream",configurable:!0});function Aa(e,t,r,n,o,a){function i(){return t}function u(w){return Fa(e,w)}function p(w){return za(e,w)}function c(){return Ia(e)}e._writable=Oo(i,u,c,p,r,n);function y(){return ja(e)}function _(w){return Da(e,w)}e._readable=Qe(i,y,_,o,a),e._backpressure=void 0,e._backpressureChangePromise=void 0,e._backpressureChangePromise_resolve=void 0,yt(e,!0),e._transformStreamController=void 0;}function Wn(e){return !l(e)||!Object.prototype.hasOwnProperty.call(e,"_transformStreamController")?!1:e instanceof pt}function Bn(e,t){I(e._readable._readableStreamController,t),sr(e,t);}function sr(e,t){St(e._transformStreamController),Le(e._writable._writableStreamController,t),lr(e);}function lr(e){e._backpressure&&yt(e,!1);}function yt(e,t){e._backpressureChangePromise!==void 0&&e._backpressureChangePromise_resolve(),e._backpressureChangePromise=P(r=>{e._backpressureChangePromise_resolve=r;}),e._backpressure=t;}class se{constructor(){throw new TypeError("Illegal constructor")}get desiredSize(){if(!_t(this))throw gt("desiredSize");let t=this._controlledTransformStream._readable._readableStreamController;return ar(t)}enqueue(t=void 0){if(!_t(this))throw gt("enqueue");kn(this,t);}error(t=void 0){if(!_t(this))throw gt("error");ka(this,t);}terminate(){if(!_t(this))throw gt("terminate");Oa(this);}}Object.defineProperties(se.prototype,{enqueue:{enumerable:!0},error:{enumerable:!0},terminate:{enumerable:!0},desiredSize:{enumerable:!0}}),f(se.prototype.enqueue,"enqueue"),f(se.prototype.error,"error"),f(se.prototype.terminate,"terminate"),typeof Symbol.toStringTag=="symbol"&&Object.defineProperty(se.prototype,Symbol.toStringTag,{value:"TransformStreamDefaultController",configurable:!0});function _t(e){return !l(e)||!Object.prototype.hasOwnProperty.call(e,"_controlledTransformStream")?!1:e instanceof se}function Wa(e,t,r,n,o){t._controlledTransformStream=e,e._transformStreamController=t,t._transformAlgorithm=r,t._flushAlgorithm=n,t._cancelAlgorithm=o,t._finishPromise=void 0,t._finishPromise_resolve=void 0,t._finishPromise_reject=void 0;}function Ba(e,t){let r=Object.create(se.prototype),n,o,a;t.transform!==void 0?n=i=>t.transform(i,r):n=i=>{try{return kn(r,i),S(void 0)}catch(u){return d(u)}},t.flush!==void 0?o=()=>t.flush(r):o=()=>S(void 0),t.cancel!==void 0?a=i=>t.cancel(i):a=()=>S(void 0),Wa(e,r,n,o,a);}function St(e){e._transformAlgorithm=void 0,e._flushAlgorithm=void 0,e._cancelAlgorithm=void 0;}function kn(e,t){let r=e._controlledTransformStream,n=r._readable._readableStreamController;if(!qe(n))throw new TypeError("Readable side is not in a state that permits enqueue");try{Ee(n,t);}catch(a){throw sr(r,a),r._readable._storedError}sa(n)!==r._backpressure&&yt(r,!0);}function ka(e,t){Bn(e._controlledTransformStream,t);}function On(e,t){let r=e._transformAlgorithm(t);return Q(r,void 0,n=>{throw Bn(e._controlledTransformStream,n),n})}function Oa(e){let t=e._controlledTransformStream,r=t._readable._readableStreamController;_e(r);let n=new TypeError("TransformStream terminated");sr(t,n);}function Fa(e,t){let r=e._transformStreamController;if(e._backpressure){let n=e._backpressureChangePromise;return Q(n,()=>{let o=e._writable;if(o._state==="erroring")throw o._storedError;return On(r,t)})}return On(r,t)}function za(e,t){let r=e._transformStreamController;if(r._finishPromise!==void 0)return r._finishPromise;let n=e._readable;r._finishPromise=P((a,i)=>{r._finishPromise_resolve=a,r._finishPromise_reject=i;});let o=r._cancelAlgorithm(t);return St(r),v(o,()=>(n._state==="errored"?Ae(r,n._storedError):(I(n._readableStreamController,t),ur(r)),null),a=>(I(n._readableStreamController,a),Ae(r,a),null)),r._finishPromise}function Ia(e){let t=e._transformStreamController;if(t._finishPromise!==void 0)return t._finishPromise;let r=e._readable;t._finishPromise=P((o,a)=>{t._finishPromise_resolve=o,t._finishPromise_reject=a;});let n=t._flushAlgorithm();return St(t),v(n,()=>(r._state==="errored"?Ae(t,r._storedError):(_e(r._readableStreamController),ur(t)),null),o=>(I(r._readableStreamController,o),Ae(t,o),null)),t._finishPromise}function ja(e){return yt(e,!1),e._backpressureChangePromise}function Da(e,t){let r=e._transformStreamController;if(r._finishPromise!==void 0)return r._finishPromise;let n=e._writable;r._finishPromise=P((a,i)=>{r._finishPromise_resolve=a,r._finishPromise_reject=i;});let o=r._cancelAlgorithm(t);return St(r),v(o,()=>(n._state==="errored"?Ae(r,n._storedError):(Le(n._writableStreamController,t),lr(e),ur(r)),null),a=>(Le(n._writableStreamController,a),lr(e),Ae(r,a),null)),r._finishPromise}function gt(e){return new TypeError(`TransformStreamDefaultController.prototype.${e} can only be used on a TransformStreamDefaultController`)}function ur(e){e._finishPromise_resolve!==void 0&&(e._finishPromise_resolve(),e._finishPromise_resolve=void 0,e._finishPromise_reject=void 0);}function Ae(e,t){e._finishPromise_reject!==void 0&&(Re(e._finishPromise),e._finishPromise_reject(t),e._finishPromise_resolve=void 0,e._finishPromise_reject=void 0);}function Fn(e){return new TypeError(`TransformStream.prototype.${e} can only be used on a TransformStream`)}m.ByteLengthQueuingStrategy=bt,m.CountQueuingStrategy=mt,m.ReadableByteStreamController=H,m.ReadableStream=E,m.ReadableStreamBYOBReader=ne,m.ReadableStreamBYOBRequest=ce,m.ReadableStreamDefaultController=x,m.ReadableStreamDefaultReader=K,m.TransformStream=pt,m.TransformStreamDefaultController=se,m.WritableStream=oe,m.WritableStreamDefaultController=ve,m.WritableStreamDefaultWriter=G;});});var Mn=chunkTKGT252T_js.c(()=>{if(!globalThis.ReadableStream)try{let m=chunkTKGT252T_js.a("process"),{emitWarning:s}=m;try{m.emitWarning=()=>{},Object.assign(globalThis,chunkTKGT252T_js.a("stream/web")),m.emitWarning=s;}catch(l){throw m.emitWarning=s,l}}catch{Object.assign(globalThis,Dn());}try{let{Blob:m}=chunkTKGT252T_js.a("buffer");m&&!m.prototype.stream&&(m.prototype.stream=function(l){let h=0,f=this;return new ReadableStream({type:"bytes",async pull(R){let ue=await f.slice(h,Math.min(f.size,h+65536)).arrayBuffer();h+=ue.byteLength,R.enqueue(new Uint8Array(ue)),h===f.size&&R.close();}})});}catch{}});var Yn=chunkTKGT252T_js.c((si,Un)=>{if(!globalThis.DOMException)try{let{MessageChannel:m}=chunkTKGT252T_js.a("worker_threads"),s=new m().port1,l=new ArrayBuffer;s.postMessage(l,[l,l]);}catch(m){m.constructor.name==="DOMException"&&(globalThis.DOMException=m.constructor);}Un.exports=globalThis.DOMException;});chunkTKGT252T_js.e(Mn(),1);var Ln=65536;async function*cr(m,s=!0){for(let l of m)if("stream"in l)yield*l.stream();else if(ArrayBuffer.isView(l))if(s){let h=l.byteOffset,f=l.byteOffset+l.byteLength;for(;h!==f;){let R=Math.min(f-h,Ln),C=l.buffer.slice(h,h+R);h+=C.byteLength,yield new Uint8Array(C);}}else yield l;else {let h=0,f=l;for(;h!==f.size;){let C=await f.slice(h,Math.min(f.size,h+Ln)).arrayBuffer();h+=C.byteLength,yield new Uint8Array(C);}}}var $n=class hr{#e=[];#t="";#r=0;#n="transparent";constructor(s=[],l={}){if(typeof s!="object"||s===null)throw new TypeError("Failed to construct 'Blob': The provided value cannot be converted to a sequence.");if(typeof s[Symbol.iterator]!="function")throw new TypeError("Failed to construct 'Blob': The object must have a callable @@iterator property.");if(typeof l!="object"&&typeof l!="function")throw new TypeError("Failed to construct 'Blob': parameter 2 cannot convert to dictionary.");l===null&&(l={});let h=new TextEncoder;for(let R of s){let C;ArrayBuffer.isView(R)?C=new Uint8Array(R.buffer.slice(R.byteOffset,R.byteOffset+R.byteLength)):R instanceof ArrayBuffer?C=new Uint8Array(R.slice(0)):R instanceof hr?C=R:C=h.encode(`${R}`),this.#r+=ArrayBuffer.isView(C)?C.byteLength:C.size,this.#e.push(C);}this.#n=`${l.endings===void 0?"transparent":l.endings}`;let f=l.type===void 0?"":String(l.type);this.#t=/^[\x20-\x7E]*$/.test(f)?f:"";}get size(){return this.#r}get type(){return this.#t}async text(){let s=new TextDecoder,l="";for await(let h of cr(this.#e,!1))l+=s.decode(h,{stream:!0});return l+=s.decode(),l}async arrayBuffer(){let s=new Uint8Array(this.size),l=0;for await(let h of cr(this.#e,!1))s.set(h,l),l+=h.length;return s.buffer}stream(){let s=cr(this.#e,!0);return new globalThis.ReadableStream({type:"bytes",async pull(l){let h=await s.next();h.done?l.close():l.enqueue(h.value);},async cancel(){await s.return();}})}slice(s=0,l=this.size,h=""){let{size:f}=this,R=s<0?Math.max(f+s,0):Math.min(s,f),C=l<0?Math.max(f+l,0):Math.min(l,f),ue=Math.max(C-R,0),P=this.#e,S=[],d=0;for(let v of P){if(d>=ue)break;let M=ArrayBuffer.isView(v)?v.byteLength:v.size;if(R&&M<=R)R-=M,C-=M;else {let N;ArrayBuffer.isView(v)?(N=v.subarray(R,Math.min(M,C)),d+=N.byteLength):(N=v.slice(R,Math.min(M,C)),d+=N.size),C-=M,S.push(N),R=0;}}let W=new hr([],{type:String(h).toLowerCase()});return W.#r=ue,W.#e=S,W}get[Symbol.toStringTag](){return "Blob"}static[Symbol.hasInstance](s){return s&&typeof s=="object"&&typeof s.constructor=="function"&&(typeof s.stream=="function"||typeof s.arrayBuffer=="function")&&/^(Blob|File)$/.test(s[Symbol.toStringTag])}};Object.defineProperties($n.prototype,{size:{enumerable:!0},type:{enumerable:!0},slice:{enumerable:!0}});var Ma=$n,Fe=Ma;var La=class extends Fe{#e=0;#t="";constructor(s,l,h={}){if(arguments.length<2)throw new TypeError(`Failed to construct 'File': 2 arguments required, but only ${arguments.length} present.`);super(s,h),h===null&&(h={});let f=h.lastModified===void 0?Date.now():Number(h.lastModified);Number.isNaN(f)||(this.#e=f),this.#t=String(l);}get name(){return this.#t}get lastModified(){return this.#e}get[Symbol.toStringTag](){return "File"}static[Symbol.hasInstance](s){return !!s&&s instanceof Fe&&/^(File)$/.test(s[Symbol.toStringTag])}},$a=La,br=$a;var{toStringTag:Ye,iterator:Na,hasInstance:Qa}=Symbol,Nn=Math.random,Ua="append,set,get,getAll,delete,keys,values,entries,forEach,constructor".split(","),Qn=(m,s,l)=>(m+="",/^(Blob|File)$/.test(s&&s[Ye])?[(l=l!==void 0?l+"":s[Ye]=="File"?s.name:"blob",m),s.name!==l||s[Ye]=="blob"?new br([s],l,s):s]:[m,s+""]),mr=(m,s)=>(s?m:m.replace(/\r?\n|\r/g,`\r
`)).replace(/\n/g,"%0A").replace(/\r/g,"%0D").replace(/"/g,"%22"),ge=(m,s,l)=>{if(s.length<l)throw new TypeError(`Failed to execute '${m}' on 'FormData': ${l} arguments required, but only ${s.length} present.`)};var ni=class{#e=[];constructor(...s){if(s.length)throw new TypeError("Failed to construct 'FormData': parameter 1 is not of type 'HTMLFormElement'.")}get[Ye](){return "FormData"}[Na](){return this.entries()}static[Qa](s){return s&&typeof s=="object"&&s[Ye]==="FormData"&&!Ua.some(l=>typeof s[l]!="function")}append(...s){ge("append",arguments,2),this.#e.push(Qn(...s));}delete(s){ge("delete",arguments,1),s+="",this.#e=this.#e.filter(([l])=>l!==s);}get(s){ge("get",arguments,1),s+="";for(var l=this.#e,h=l.length,f=0;f<h;f++)if(l[f][0]===s)return l[f][1];return null}getAll(s,l){return ge("getAll",arguments,1),l=[],s+="",this.#e.forEach(h=>h[0]===s&&l.push(h[1])),l}has(s){return ge("has",arguments,1),s+="",this.#e.some(l=>l[0]===s)}forEach(s,l){ge("forEach",arguments,1);for(var[h,f]of this)s.call(l,f,h,this);}set(...s){ge("set",arguments,2);var l=[],h=!0;s=Qn(...s),this.#e.forEach(f=>{f[0]===s[0]?h&&(h=!l.push(s)):l.push(f);}),h&&l.push(s),this.#e=l;}*entries(){yield*this.#e;}*keys(){for(var[s]of this)yield s;}*values(){for(var[,s]of this)yield s;}};function ai(m,s=Fe){var l=`${Nn()}${Nn()}`.replace(/\./g,"").slice(-28).padStart(32,"-"),h=[],f=`--${l}\r
Content-Disposition: form-data; name="`;return m.forEach((R,C)=>typeof R=="string"?h.push(f+mr(C)+`"\r
\r
${R.replace(/\r(?!\n)|(?<!\r)\n/g,`\r
`)}\r
`):h.push(f+mr(C)+`"; filename="${mr(R.name,1)}"\r
Content-Type: ${R.type||"application/octet-stream"}\r
\r
`,R,`\r
`)),h.push(`--${l}--`),new s(h,{type:"multipart/form-data; boundary="+l})}chunkTKGT252T_js.e(Yn(),1);/*! Bundled license information:

web-streams-polyfill/dist/ponyfill.es2018.js:
  (**
   * @license
   * web-streams-polyfill v3.3.3
   * Copyright 2024 Mattias Buelens, Diwank Singh Tomer and other contributors.
   * This code is released under the MIT license.
   * SPDX-License-Identifier: MIT
   *)

node-domexception/index.js:
  (*! node-domexception. MIT License. Jimmy Wärting <https://jimmy.warting.se/opensource> *)

fetch-blob/index.js:
  (*! fetch-blob. MIT License. Jimmy Wärting <https://jimmy.warting.se/opensource> *)

formdata-polyfill/esm.min.js:
  (*! formdata-polyfill. MIT License. Jimmy Wärting <https://jimmy.warting.se/opensource> *)
*/

exports.a = Fe;
exports.b = br;
exports.c = ni;
exports.d = ai;
//# sourceMappingURL=out.js.map
//# sourceMappingURL=chunk-O2POOKSN.js.map
//# debugId=473becf7-39bd-560d-a682-a0b61c44ed43
