{"name": "es-vary", "description": "vary rewrite in TypeScript with ESM and CommonJS targets", "version": "0.0.8", "type": "module", "main": "dist/index.cjs", "module": "dist/index.js", "types": "dist/index.d.ts", "engines": {"node": ">=12.x"}, "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.cjs"}, "./package.json": "./package.json", "./": "./"}, "scripts": {"prepare": "pnpm build", "build": "tsup src/index.ts --minify-whitespace --format cjs,esm --dts"}, "repository": {"type": "git", "url": "git+https://github.com/talentlessguy/es-vary.git"}, "keywords": ["http", "esm", "es", "vary", "nodejs", "javascript"], "author": "", "license": "MIT", "bugs": {"url": "https://github.com/talentlessguy/es-vary/issues"}, "homepage": "https://github.com/talentlessguy/es-vary#readme", "devDependencies": {"@types/node": "^14.11.1", "tsup": "^3.7.0", "typescript": "^4.0.3"}}