{"version": 3, "file": "instrumentation.js", "sourceRoot": "", "sources": ["../../src/instrumentation.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,4CAA6C;AAC7C,oEAIwC;AACxC,kBAAkB;AAClB,uCAA0D;AAE1D,MAAa,0BAA2B,SAAQ,qCAAmB;IACjE,YAAY,SAAgC,EAAE;QAC5C,KAAK,CAAC,sBAAY,EAAE,yBAAe,EAAE,MAAM,CAAC,CAAC;IAC/C,CAAC;IAED,IAAI;QACF,OAAO;YACL,IAAI,qDAAmC,CACrC,cAAc,EACd,CAAC,UAAU,CAAC,EACZ,aAAa,CAAC,EAAE;gBACd,gEAAgE;gBAChE,qDAAqD;gBACrD,uDAAuD;gBACvD,MAAM,aAAa,GAAG;oBACpB,kGAAkG;oBAClG,2GAA2G;oBAC3G,MAAM,YAAY,GAAG,aAAa,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;oBAC1D,OAAO;wBACL,MAAM,iBAAiB,GAAG,CAAC,GAAG,SAAS,CAAC,CAAC;wBACzC,gCAAgC;wBAChC,MAAM,YAAY,GAAG,iBAAiB,CAAC,GAAG,EAAE,CAAC;wBAC7C,MAAM,mBAAmB,GACvB,OAAO,YAAY,KAAK,UAAU;4BAChC,CAAC,CAAC,aAAO,CAAC,IAAI,CAAC,aAAO,CAAC,MAAM,EAAE,EAAE,YAAY,CAAC;4BAC9C,CAAC,CAAC,YAAY,CAAC;wBACnB,iBAAiB,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;wBAC5C,OAAO,YAAY,CAAC,KAAK,CAAC,IAAI,EAAE,iBAAiB,CAAC,CAAC;oBACrD,CAAC,CAAC;gBACJ,CAAC,CAAC;gBAEF,kEAAkE;gBAClE,mCAAmC;gBACnC,aAAa,CAAC,IAAI,GAAG,aAAa,CAAC,IAAI,CAAC;gBACxC,OAAO,aAAa,CAAC;YACvB,CAAC,EACD,SAAS,CAAC,uEAAuE;aAClF;SACF,CAAC;IACJ,CAAC;CACF;AAxCD,gEAwCC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { context } from '@opentelemetry/api';\nimport {\n  InstrumentationBase,\n  InstrumentationConfig,\n  InstrumentationNodeModuleDefinition,\n} from '@opentelemetry/instrumentation';\n/** @knipignore */\nimport { PACKAGE_NAME, PACKAGE_VERSION } from './version';\n\nexport class LruMemoizerInstrumentation extends InstrumentationBase {\n  constructor(config: InstrumentationConfig = {}) {\n    super(PACKAGE_NAME, PACKAGE_VERSION, config);\n  }\n\n  init(): InstrumentationNodeModuleDefinition[] {\n    return [\n      new InstrumentationNodeModuleDefinition(\n        'lru-memoizer',\n        ['>=1.3 <3'],\n        moduleExports => {\n          // moduleExports is a function which receives an options object,\n          // and returns a \"memoizer\" function upon invocation.\n          // We want to patch this \"memoizer's\" internal function\n          const asyncMemoizer = function (this: unknown) {\n            // This following function is invoked every time the user wants to get a (possible) memoized value\n            // We replace it with another function in which we bind the current context to the last argument (callback)\n            const origMemoizer = moduleExports.apply(this, arguments);\n            return function (this: unknown) {\n              const modifiedArguments = [...arguments];\n              // last argument is the callback\n              const origCallback = modifiedArguments.pop();\n              const callbackWithContext =\n                typeof origCallback === 'function'\n                  ? context.bind(context.active(), origCallback)\n                  : origCallback;\n              modifiedArguments.push(callbackWithContext);\n              return origMemoizer.apply(this, modifiedArguments);\n            };\n          };\n\n          // sync function preserves context, but we still need to export it\n          // as the lru-memoizer package does\n          asyncMemoizer.sync = moduleExports.sync;\n          return asyncMemoizer;\n        },\n        undefined // no need to disable as this instrumentation does not create any spans\n      ),\n    ];\n  }\n}\n"]}