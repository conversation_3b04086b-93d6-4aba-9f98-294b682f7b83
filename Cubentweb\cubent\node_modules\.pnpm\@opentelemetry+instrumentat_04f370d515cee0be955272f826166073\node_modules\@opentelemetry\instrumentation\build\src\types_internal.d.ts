import { Tracer<PERSON><PERSON><PERSON>, MeterProvider } from '@opentelemetry/api';
import { Instrumentation } from './types';
import { LoggerProvider } from '@opentelemetry/api-logs';
export interface AutoLoaderResult {
    instrumentations: Instrumentation[];
}
export interface AutoLoaderOptions {
    instrumentations?: (Instrumentation | Instrumentation[])[];
    tracerProvider?: TracerProvider;
    meterProvider?: MeterProvider;
    loggerProvider?: LoggerProvider;
}
//# sourceMappingURL=types_internal.d.ts.map