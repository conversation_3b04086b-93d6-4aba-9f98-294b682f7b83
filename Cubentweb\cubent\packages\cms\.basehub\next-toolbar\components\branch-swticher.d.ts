// This file was generated by basehub. Do not edit directly. Read more: https://basehub.com/docs/api-reference/basehub-sdk

/* eslint-disable */
/* eslint-disable eslint-comments/no-restricted-disable */
/* tslint:disable */
// @ts-nocheck

export type LatestBranch = {
    name: string;
    isDefault: boolean;
};
export declare const BranchSwitcher: ({ isForcedDraft, draft, apiRref, latestBranches, onRefChange, getAndSetLatestBranches, }: {
    isForcedDraft: boolean;
    draft: boolean;
    apiRref: string;
    latestBranches: LatestBranch[];
    onRefChange: (ref: string, opts: {
        enableDraftMode: boolean;
    }) => void;
    getAndSetLatestBranches: () => Promise<void>;
}) => import("react/jsx-runtime").JSX.Element;
