{"version": 3, "file": "cli.js", "sourceRoot": "", "sources": ["../src/cli.ts"], "names": [], "mappings": ";;;AAOA;;GAEG;AACH,IAAY,UAOX;AAPD,WAAY,UAAU;IACrB,iDAAO,CAAA;IACP,+CAAM,CAAA;IACN,+CAAM,CAAA;IACN,6CAAK,CAAA;IACL,+CAAM,CAAA;IACN,2DAAY,CAAA;AACb,CAAC,EAPW,UAAU,GAAV,kBAAU,KAAV,kBAAU,QAOrB;AAED;;GAEG;AACU,QAAA,iBAAiB,GAA8B;IAC3D,GAAG,EAAE,UAAU,CAAC,MAAM;IACtB,KAAK,EAAE,UAAU,CAAC,OAAO;IACzB,QAAQ,EAAE,UAAU,CAAC,MAAM;IAC3B,KAAK,EAAE,UAAU,CAAC,MAAM;IACxB,QAAQ,EAAE,UAAU,CAAC,MAAM;IAC3B,MAAM,EAAE,UAAU,CAAC,OAAO;IAC1B,SAAS,EAAE,UAAU,CAAC,MAAM;IAC5B,IAAI,EAAE,UAAU,CAAC,MAAM;IACvB,QAAQ,EAAE,UAAU,CAAC,OAAO;IAC5B,UAAU,EAAE,UAAU,CAAC,YAAY;CACnC,CAAC;AAEF;;GAEG;AACH,MAAM,UAAW,SAAQ,KAAK;IAC7B,YAAY,OAAe,EAAE,SAAiB,EAAE;QAC/C,KAAK,CAAC,GAAG,OAAO,kBAAkB,MAAM,EAAE,CAAC,CAAC;QAC5C,IAAI,CAAC,IAAI,GAAG,YAAY,CAAC;IAC1B,CAAC;CACD;AAED;;;;;GAKG;AACH,SAAgB,WAAW,CAAC,MAA0B,EAAE,IAAgB;IACvE,YAAY;IACZ,IAAI,MAAM,KAAK,SAAS,IAAI,MAAM,KAAK,IAAI;QAAE,OAAO,SAAS,CAAC;IAC9D,SAAS;IACT,IAAI,IAAI,KAAK,UAAU,CAAC,MAAM;QAAE,OAAO,MAAM,CAAC,MAAM,CAAC,CAAC;IACtD,UAAU;IACV,IAAI,IAAI,KAAK,UAAU,CAAC,OAAO;QAAE,OAAO,MAAM,KAAK,MAAM,CAAC;IAC1D,UAAU;IACV,IACC,IAAI,KAAK,UAAU,CAAC,KAAK;QACzB,IAAI,KAAK,UAAU,CAAC,MAAM;QAC1B,IAAI,KAAK,UAAU,CAAC,YAAY,EAC/B;QACD,IAAI;YACH,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;YAClC,wBAAwB;YACxB,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;gBAC/B,MAAM,IAAI,UAAU,CAAC,6BAA6B,EAAE,MAAM,CAAC,CAAC;aAC5D;YACD,IAAI,IAAI,KAAK,UAAU,CAAC,KAAK,EAAE;gBAC9B,QAAQ;gBACR,OAAO,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;aAC7B;YACD,+DAA+D;YAC/D,IACC,IAAI,KAAK,UAAU,CAAC,YAAY;gBAChC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,IAAI,OAAO,CAAC,KAAK,QAAQ,CAAC,EAC5D;gBACD,MAAM,IAAI,UAAU,CAAC,qCAAqC,EAAE,MAAM,CAAC,CAAC;aACpE;YACD,SAAS;YACT,OAAO,MAAM,CAAC;SACd;QAAC,OAAO,CAAC,EAAE;YACX,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,CAAC,CAAC,CAAC;YAC9C,OAAO,SAAS,CAAC;SACjB;KACD;IACD,SAAS;IACT,OAAO,MAAM,CAAC;AACf,CAAC;AAvCD,kCAuCC;AAED;;;GAGG;AACH,SAAgB,MAAM,CAAC,MAAgB;IACtC,gBAAgB;IAChB,MAAM,OAAO,GAAkB,EAAE,CAAC;IAClC,4BAA4B;IAC5B,MAAM,CAAC,IAAI,CAAC,yBAAiB,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;QACjD,MAAM,OAAO,GAAG,gBAAgB,GAAG,MAAM,CAAC,WAAW,EAAE,CAAC;QACxD,IAAI,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,IAAI,EAAE;YACjC,OAAO,CAAC,MAAM,CAAC,GAAG,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,yBAAiB,CAAC,MAAM,CAAC,CAAC,CAAC;SAC/E;IACF,CAAC,CAAC,CAAC;IACH,qCAAqC;IACrC,MAAM,IAAI,GAAa,OAAO,CAAC,IAAI,CAAC;IACpC,MAAM,IAAI,GAAW,MAAM,CAAC,IAAI,CAAC,yBAAiB,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAC9D,MAAM,EAAE,GAAG,IAAI,MAAM,CAAC,mBAAmB,IAAI,UAAU,EAAE,GAAG,CAAC,CAAC;IAC9D,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,IAAI,EAAE,GAAG;QACjD,MAAM,OAAO,GAAG,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QAC9B,IAAI,OAAO,EAAE;YACZ,MAAM,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;YACzC,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;YACxC,IAAI,CAAC,MAAM,CAAC,GAAG,WAAW,CAAC,KAAK,EAAE,yBAAiB,CAAC,MAAM,CAAC,CAAC,CAAC;SAC7D;QACD,OAAO,IAAI,CAAC;IACb,CAAC,EAAE,EAAS,CAAiB,CAAC;IAC9B,cAAc;IACd,OAAO,MAAM,iCAAK,OAAO,GAAK,UAAU,EAAE,CAAC;AAC5C,CAAC;AAzBD,wBAyBC"}