import{a as e,c as t,d as m,f as l,g as a,h as n,i as f,j as v,k as p}from"./chunk-OQ3Y4V4F.mjs";e();async function O({root:i,to:r,options:o}){let s=new n({...o,interactive:!1}),[c,g]=await Promise.all([a({root:i}),t()]);await p({project:c,convertTo:{name:r,version:g[r]},logger:s,options:o})}export{m as ConvertError,l as MANAGERS,O as convert,f as getPackageManagerMeta,a as getWorkspaceDetails,v as install};
