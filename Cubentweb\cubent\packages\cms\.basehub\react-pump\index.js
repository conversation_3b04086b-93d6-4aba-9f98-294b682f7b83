// This file was generated by basehub. Do not edit directly. Read more: https://basehub.com/docs/api-reference/basehub-sdk

/* eslint-disable */
/* eslint-disable eslint-comments/no-restricted-disable */
/* tslint:disable */

import "./chunk-F5PHAOMO.js";

// ../../node_modules/.pnpm/basehub@8.2.7_@babel+runtim_3aebfa8e064bb601162c04844d38dd02/node_modules/basehub/src/react/pump/server-pump.tsx
import * as React from "react";
import {
  basehub,
  generateQueryOp,
  getStuffFromEnv,
  resolvedRef,
  isNextjs
} from "../index";
var LazyClientPump = React.lazy(
  () => import("./client-pump-WYUPTPKD.js").then((mod) => ({ default: mod.ClientPump }))
);
var cache = /* @__PURE__ */ new Map();
var pumpToken = null;
var spaceID = null;
var pusherData = null;
var DEDUPE_TIME_MS = 32;
var Pump = async ({
  children,
  queries,
  bind,
  ...basehubProps
}) => {
  const errors = [];
  const responseHashes = [];
  if (isNextjs) {
    let isNextjsDraftMode = false;
    if (basehubProps.draft === void 0) {
      try {
        const { draftMode } = await import("next/headers");
        isNextjsDraftMode = (await draftMode()).isEnabled;
      } catch (error) {
      }
    }
    if (isNextjsDraftMode && basehubProps.draft === void 0) {
      basehubProps.draft = true;
    }
  }
  const { headers, draft } = getStuffFromEnv(basehubProps);
  const token = headers["x-basehub-token"];
  const apiVersion = headers["x-basehub-api-version"];
  const pumpEndpoint = "https://aws.basehub.com/pump";
  const noQueries = queries.length === 0;
  const queriesWithFallback = draft && noQueries ? [{ _sys: { id: true } }] : queries;
  if (draft) {
    if (isNextjs) {
      try {
        const { cookies } = await import("next/headers");
        const cookieStore = await cookies();
        const ref = cookieStore.get("bshb-preview-ref-" + resolvedRef.repoHash)?.value;
        if (ref) {
          headers["x-basehub-ref"] = ref;
        }
      } catch (error) {
      }
    }
  }
  const results = await Promise.all(
    // @ts-ignore
    queriesWithFallback.map(async (singleQuery, index) => {
      const rawQueryOp = generateQueryOp(singleQuery);
      const cacheKey = JSON.stringify({ ...rawQueryOp, ...headers }) + (draft ? "_draft" : "_prod");
      let data = void 0;
      if (cache.has(cacheKey)) {
        const cached = cache.get(cacheKey);
        if (performance.now() - cached.start < DEDUPE_TIME_MS) {
          data = await cached.data;
        }
      }
      if (!data) {
        const dataPromise = draft ? fetch(pumpEndpoint, {
          ...isNextjs ? { cache: "no-store" } : {},
          method: "POST",
          headers: {
            ...headers,
            "content-type": "application/json",
            "x-basehub-token": token,
            "x-basehub-api-version": apiVersion
          },
          body: JSON.stringify(rawQueryOp)
        }).then(async (response) => {
          const {
            data: data2 = null,
            newPumpToken,
            errors: _errors = null,
            spaceID: _spaceID,
            pusherData: _pusherData,
            responseHash: _responseHash
          } = await response.json();
          pumpToken = newPumpToken;
          pusherData = _pusherData;
          spaceID = _spaceID;
          errors.push(_errors);
          responseHashes[index] = _responseHash;
          return basehub.replaceSystemAliases(data2);
        }) : basehub(basehubProps).query(singleQuery);
        cache.set(cacheKey, {
          start: performance.now(),
          data: dataPromise
        });
        data = await dataPromise;
      }
      return { data, rawQueryOp };
    })
  );
  if (bind) {
    children = children.bind(null, bind);
  }
  let resolvedChildren;
  const childrenPromise = children(results.map((r) => r.data));
  if (childrenPromise instanceof Promise) {
    resolvedChildren = await childrenPromise?.catch((e) => {
      if (draft) {
        console.error("Error in Pump children function", e);
        return null;
      } else
        throw e;
    });
  } else {
    resolvedChildren = childrenPromise;
  }
  if (draft) {
    if (!pumpToken || !spaceID || !pusherData) {
      console.log("Results (length):", results?.length);
      console.log("Errors:", JSON.stringify(errors, null, 2));
      console.log("Pump Endpoint:", pumpEndpoint);
      console.log("Pump Token:", pumpToken);
      console.log("Space ID:", spaceID);
      console.log("Pusher Data:", pusherData);
      console.log("Response Hashes:", JSON.stringify(responseHashes, null, 2));
      throw new Error(
        "Pump did not return the necessary data. Look at the logs to see what's missing."
      );
    }
    return /* @__PURE__ */ React.createElement(
      LazyClientPump,
      {
        rawQueries: results.map((r) => r.rawQueryOp),
        initialState: {
          // @ts-ignore
          data: !noQueries ? results.map((r) => r.data ?? null) : [],
          errors,
          responseHashes,
          pusherData,
          spaceID
        },
        pumpEndpoint,
        pumpToken: pumpToken ?? void 0,
        initialResolvedChildren: resolvedChildren,
        apiVersion,
        previewRef: headers["x-basehub-ref"] || resolvedRef.ref
      },
      children
    );
  }
  return resolvedChildren;
};
var createPump = (queries) => {
  return (props) => {
    const queryResult = typeof queries === "function" ? queries(props.params) : queries;
    return /* @__PURE__ */ React.createElement(Pump, { ...props, queries: queryResult });
  };
};
export {
  Pump,
  createPump
};
