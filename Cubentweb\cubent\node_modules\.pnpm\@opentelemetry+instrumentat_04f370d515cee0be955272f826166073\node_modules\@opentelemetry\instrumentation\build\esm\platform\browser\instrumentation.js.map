{"version": 3, "file": "instrumentation.js", "sourceRoot": "", "sources": ["../../../../src/platform/browser/instrumentation.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;;;;;;;;;;;;;AAEH,OAAO,EAAE,uBAAuB,EAAE,MAAM,uBAAuB,CAAC;AAIhE;;GAEG;AACH;IAGU,uCAAmC;IAG3C,6BACE,mBAA2B,EAC3B,sBAA8B,EAC9B,MAAkB;QAHpB,YAKE,kBAAM,mBAAmB,EAAE,sBAAsB,EAAE,MAAM,CAAC,SAK3D;QAHC,IAAI,KAAI,CAAC,OAAO,CAAC,OAAO,EAAE;YACxB,KAAI,CAAC,MAAM,EAAE,CAAC;SACf;;IACH,CAAC;IACH,0BAAC;AAAD,CAAC,AAjBD,CAGU,uBAAuB,GAchC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { InstrumentationAbstract } from '../../instrumentation';\nimport * as types from '../../types';\nimport { InstrumentationConfig } from '../../types';\n\n/**\n * Base abstract class for instrumenting web plugins\n */\nexport abstract class InstrumentationBase<\n    ConfigType extends InstrumentationConfig = InstrumentationConfig,\n  >\n  extends InstrumentationAbstract<ConfigType>\n  implements types.Instrumentation<ConfigType>\n{\n  constructor(\n    instrumentationName: string,\n    instrumentationVersion: string,\n    config: ConfigType\n  ) {\n    super(instrumentationName, instrumentationVersion, config);\n\n    if (this._config.enabled) {\n      this.enable();\n    }\n  }\n}\n"]}