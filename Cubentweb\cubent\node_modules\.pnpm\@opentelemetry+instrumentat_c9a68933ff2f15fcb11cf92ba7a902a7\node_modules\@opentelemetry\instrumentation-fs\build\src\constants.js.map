{"version": 3, "file": "constants.js", "sourceRoot": "", "sources": ["../../src/constants.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAIU,QAAA,iBAAiB,GAAe;IAC3C,QAAQ;IACR,YAAY;IACZ,OAAO;IACP,OAAO;IACP,UAAU;IACV,IAAgB;IAChB,QAAQ;IACR,MAAM;IACN,OAAO;IACP,SAAS;IACT,OAAO;IACP,SAAS;IACT,MAAM;IACN,SAAS;IACT,SAAS;IACT,UAAU;IACV,UAAU;IACV,UAAU;IACV,QAAQ;IACR,IAAI;IACJ,OAAO;IACP,MAAM;IACN,SAAS;IACT,UAAU;IACV,QAAQ;IACR,QAAQ;IACR,WAAW;IACX,yCAAyC;CAC1C,CAAC;AAEW,QAAA,kBAAkB,GAAc;IAC3C,QAAQ;IACR,YAAY;IACZ,OAAO;IACP,OAAO;IACP,UAAU;IACV,IAAe;IACf,QAAQ;IACR,QAAQ;IACR,MAAM;IACN,OAAO;IACP,SAAS;IACT,OAAO;IACP,SAAS;IACT,MAAM;IACN,SAAS;IACT,SAAS;IACT,UAAU;IACV,UAAU;IACV,UAAU;IACV,iBAAiB;IACjB,QAAQ;IACR,IAAI;IACJ,OAAO;IACP,MAAM;IACN,SAAS;IACT,UAAU;IACV,QAAQ;IACR,QAAQ;IACR,WAAW;IACX,2CAA2C;IAC3C,4CAA4C;IAC5C,4CAA4C;IAC5C,+CAA+C;IAC/C,2CAA2C;IAC3C,2CAA2C;IAC3C,+CAA+C;IAC/C,6CAA6C;IAC7C,yCAAyC;IACzC,0CAA0C;IAC1C,2CAA2C;IAC3C,2CAA2C;IAC3C,4CAA4C;CAC7C,CAAC;AAEW,QAAA,cAAc,GAAc;IACvC,YAAY;IACZ,gBAAgB;IAChB,WAAW;IACX,WAAW;IACX,cAAc;IACd,QAAmB;IACnB,YAAY;IACZ,YAAY;IACZ,UAAU;IACV,WAAW;IACX,aAAa;IACb,WAAW;IACX,aAAa;IACb,aAAa;IACb,UAAU;IACV,aAAa;IACb,cAAc;IACd,cAAc;IACd,cAAc;IACd,qBAAqB;IACrB,YAAY;IACZ,WAAW;IACX,QAAQ;IACR,UAAU;IACV,aAAa;IACb,cAAc;IACd,YAAY;IACZ,YAAY;IACZ,eAAe;IACf,+CAA+C;IAC/C,gDAAgD;IAChD,gDAAgD;IAChD,mDAAmD;IACnD,+CAA+C;IAC/C,+CAA+C;IAC/C,mDAAmD;IACnD,iDAAiD;IACjD,6CAA6C;IAC7C,8CAA8C;IAC9C,+CAA+C;IAC/C,+CAA+C;IAC/C,gDAAgD;CACjD,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport type { FMember, FPMember } from './types';\n\nexport const PROMISE_FUNCTIONS: FPMember[] = [\n  'access',\n  'appendFile',\n  'chmod',\n  'chown',\n  'copyFile',\n  'cp' as FPMember, // added in v16\n  'lchown',\n  'link',\n  'lstat',\n  'lutimes', // added in v12\n  'mkdir',\n  'mkdtemp',\n  'open',\n  'opendir', // added in v12\n  'readdir',\n  'readFile',\n  'readlink',\n  'realpath',\n  'rename',\n  'rm', // added in v14\n  'rmdir',\n  'stat',\n  'symlink',\n  'truncate',\n  'unlink',\n  'utimes',\n  'writeFile',\n  // 'lchmod', // only implemented on macOS\n];\n\nexport const CALLBACK_FUNCTIONS: FMember[] = [\n  'access',\n  'appendFile',\n  'chmod',\n  'chown',\n  'copyFile',\n  'cp' as FMember, // added in v16\n  'exists', // deprecated, inconsistent cb signature, handling separately when patching\n  'lchown',\n  'link',\n  'lstat',\n  'lutimes', // added in v12\n  'mkdir',\n  'mkdtemp',\n  'open',\n  'opendir', // added in v12\n  'readdir',\n  'readFile',\n  'readlink',\n  'realpath',\n  'realpath.native',\n  'rename',\n  'rm', // added in v14\n  'rmdir',\n  'stat',\n  'symlink',\n  'truncate',\n  'unlink',\n  'utimes',\n  'writeFile',\n  // 'close', // functions on file descriptor\n  // 'fchmod', // functions on file descriptor\n  // 'fchown', // functions on file descriptor\n  // 'fdatasync', // functions on file descriptor\n  // 'fstat', // functions on file descriptor\n  // 'fsync', // functions on file descriptor\n  // 'ftruncate', // functions on file descriptor\n  // 'futimes', // functions on file descriptor\n  // 'lchmod', // only implemented on macOS\n  // 'read', // functions on file descriptor\n  // 'readv', // functions on file descriptor\n  // 'write', // functions on file descriptor\n  // 'writev', // functions on file descriptor\n];\n\nexport const SYNC_FUNCTIONS: FMember[] = [\n  'accessSync',\n  'appendFileSync',\n  'chmodSync',\n  'chownSync',\n  'copyFileSync',\n  'cpSync' as FMember, // added in v16\n  'existsSync',\n  'lchownSync',\n  'linkSync',\n  'lstatSync',\n  'lutimesSync', // added in v12\n  'mkdirSync',\n  'mkdtempSync',\n  'opendirSync', // added in v12\n  'openSync',\n  'readdirSync',\n  'readFileSync',\n  'readlinkSync',\n  'realpathSync',\n  'realpathSync.native',\n  'renameSync',\n  'rmdirSync',\n  'rmSync', // added in v14\n  'statSync',\n  'symlinkSync',\n  'truncateSync',\n  'unlinkSync',\n  'utimesSync',\n  'writeFileSync',\n  // 'closeSync', // functions on file descriptor\n  // 'fchmodSync', // functions on file descriptor\n  // 'fchownSync', // functions on file descriptor\n  // 'fdatasyncSync', // functions on file descriptor\n  // 'fstatSync', // functions on file descriptor\n  // 'fsyncSync', // functions on file descriptor\n  // 'ftruncateSync', // functions on file descriptor\n  // 'futimesSync', // functions on file descriptor\n  // 'lchmodSync', // only implemented on macOS\n  // 'readSync', // functions on file descriptor\n  // 'readvSync', // functions on file descriptor\n  // 'writeSync', // functions on file descriptor\n  // 'writevSync', // functions on file descriptor\n];\n"]}