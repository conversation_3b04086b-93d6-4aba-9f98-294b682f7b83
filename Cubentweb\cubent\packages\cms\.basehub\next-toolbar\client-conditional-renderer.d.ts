// This file was generated by basehub. Do not edit directly. Read more: https://basehub.com/docs/api-reference/basehub-sdk

/* eslint-disable */
/* eslint-disable eslint-comments/no-restricted-disable */
/* tslint:disable */
// @ts-nocheck

import * as React from "react";
import { ResolvedRef } from "../../common-types";
import { LatestBranch } from "./components/branch-swticher";
export declare const ClientConditionalRenderer: ({ draft, isForcedDraft, enableDraftMode, disableDraftMode, revalidateTags, resolvedRef, getLatestBranches, }: {
    draft: boolean;
    isForcedDraft: boolean;
    enableDraftMode: (o: {
        bshbPreviewToken: string;
    }) => Promise<{
        status: number;
        response: object;
    }>;
    disableDraftMode: () => Promise<void>;
    revalidateTags: (o: {
        bshbPreviewToken: string;
        ref?: string;
    }) => Promise<{
        success: boolean;
        message?: string;
    }>;
    getLatestBranches: (o: {
        bshbPreviewToken: string | undefined;
    }) => Promise<{
        status: number;
        response: LatestBranch[] | {
            error: string;
        };
    }>;
    resolvedRef: ResolvedRef;
}) => React.ReactPortal | null;
