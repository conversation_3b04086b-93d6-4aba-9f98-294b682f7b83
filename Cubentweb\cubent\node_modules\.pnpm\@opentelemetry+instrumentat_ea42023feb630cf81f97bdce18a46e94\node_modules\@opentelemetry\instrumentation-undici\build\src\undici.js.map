{"version": 3, "file": "undici.js", "sourceRoot": "", "sources": ["../../src/undici.ts"], "names": [], "mappings": ";;;AAAA;;;;;;;;;;;;;;GAcG;AACH,8CAA8C;AAC9C,6BAA0B;AAE1B,oEAGwC;AACxC,4CAY4B;AAE5B,kBAAkB;AAClB,uCAA0D;AAU1D,mEAAgE;AAChE,8CAI6B;AAQ7B,mEAAmE;AACnE,oIAAoI;AACpI,MAAa,qBAAsB,SAAQ,qCAAgD;IAOzF,YAAY,SAAsC,EAAE;QAClD,KAAK,CAAC,sBAAY,EAAE,yBAAe,EAAE,MAAM,CAAC,CAAC;QAJvC,mBAAc,GAAG,IAAI,OAAO,EAAwC,CAAC;IAK7E,CAAC;IAED,sCAAsC;IACnB,IAAI;QACrB,OAAO,SAAS,CAAC;IACnB,CAAC;IAEQ,OAAO;QACd,KAAK,CAAC,OAAO,EAAE,CAAC;QAChB,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC,CAAC;QACpD,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC;IAC/B,CAAC;IAEQ,MAAM;QACb,wEAAwE;QACxE,4EAA4E;QAC5E,2EAA2E;QAC3E,EAAE;QACF,qEAAqE;QACrE,6DAA6D;QAC7D,0EAA0E;QAC1E,wEAAwE;QACxE,8DAA8D;QAC9D,KAAK,CAAC,MAAM,EAAE,CAAC;QAEf,sEAAsE;QACtE,2DAA2D;QAC3D,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,IAAI,EAAE,CAAC;QAE5C,mCAAmC;QACnC,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE;YAChC,OAAO;SACR;QAED,IAAI,CAAC,kBAAkB,CACrB,uBAAuB,EACvB,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CACjC,CAAC;QACF,IAAI,CAAC,kBAAkB,CACrB,2BAA2B,EAC3B,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CACjC,CAAC;QACF,IAAI,CAAC,kBAAkB,CACrB,wBAAwB,EACxB,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAClC,CAAC;QACF,IAAI,CAAC,kBAAkB,CAAC,yBAAyB,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAC3E,IAAI,CAAC,kBAAkB,CAAC,sBAAsB,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IAC3E,CAAC;IAEkB,wBAAwB;QACzC,IAAI,CAAC,4BAA4B,GAAG,IAAI,CAAC,KAAK,CAAC,eAAe,CAC5D,8BAA8B,EAC9B;YACE,WAAW,EAAE,kDAAkD;YAC/D,IAAI,EAAE,GAAG;YACT,SAAS,EAAE,eAAS,CAAC,MAAM;YAC3B,MAAM,EAAE;gBACN,wBAAwB,EAAE;oBACxB,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC;oBAChE,GAAG,EAAE,EAAE;iBACR;aACF;SACF,CACF,CAAC;IACJ,CAAC;IAEO,kBAAkB,CACxB,iBAAyB,EACzB,SAAwD;;QAExD,+DAA+D;QAC/D,4CAA4C;QAC5C,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,GAAG,OAAO,CAAC,OAAO;aACnC,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC;aAChB,KAAK,CAAC,GAAG,CAAC;aACV,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QACvB,MAAM,eAAe,GAAG,KAAK,GAAG,EAAE,IAAI,CAAC,KAAK,KAAK,EAAE,IAAI,KAAK,IAAI,EAAE,CAAC,CAAC;QAEpE,IAAI,WAAuB,CAAC;QAC5B,IAAI,eAAe,EAAE;YACnB,MAAA,MAAM,CAAC,SAAS,+CAAhB,MAAM,EAAa,iBAAiB,EAAE,SAAS,CAAC,CAAC;YACjD,WAAW,GAAG,GAAG,EAAE,WAAC,OAAA,MAAA,MAAM,CAAC,WAAW,+CAAlB,MAAM,EAAe,iBAAiB,EAAE,SAAS,CAAC,CAAA,EAAA,CAAC;SACxE;aAAM;YACL,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC;YAClD,OAAO,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;YAC7B,WAAW,GAAG,GAAG,EAAE,CAAC,OAAO,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;SACpD;QAED,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC;YACrB,IAAI,EAAE,iBAAiB;YACvB,WAAW;SACZ,CAAC,CAAC;IACL,CAAC;IAED,mGAAmG;IACnG,+FAA+F;IAC/F,kBAAkB;IACV,gBAAgB,CAAC,EAAE,OAAO,EAAkB;QAClD,aAAa;QACb,gCAAgC;QAChC,sBAAsB;QACtB,wBAAwB;QACxB,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QAChC,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,KAAK,KAAK,CAAC;QACzC,MAAM,eAAe,GAAG,IAAA,wCAAsB,EAC5C,GAAG,EAAE;;YACH,OAAA,CAAC,OAAO;gBACR,OAAO,CAAC,MAAM,KAAK,SAAS;iBAC5B,MAAA,MAAM,CAAC,iBAAiB,+CAAxB,MAAM,EAAqB,OAAO,CAAC,CAAA,CAAA;SAAA,EACrC,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,kCAAkC,EAAE,CAAC,CAAC,EACjE,IAAI,CACL,CAAC;QAEF,IAAI,eAAe,EAAE;YACnB,OAAO;SACR;QAED,MAAM,SAAS,GAAG,IAAA,aAAM,GAAE,CAAC;QAC3B,IAAI,UAAU,CAAC;QACf,IAAI;YACF,UAAU,GAAG,IAAI,SAAG,CAAC,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;SACpD;QAAC,OAAO,GAAG,EAAE;YACZ,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,+BAA+B,EAAE,GAAG,CAAC,CAAC;YACtD,mCAAmC;YACnC,OAAO;SACR;QACD,MAAM,SAAS,GAAG,UAAU,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;QACvD,MAAM,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QAC5D,MAAM,UAAU,GAAe;YAC7B,CAAC,uCAAkB,CAAC,mBAAmB,CAAC,EAAE,aAAa;YACvD,CAAC,uCAAkB,CAAC,4BAA4B,CAAC,EAAE,OAAO,CAAC,MAAM;YACjE,CAAC,uCAAkB,CAAC,QAAQ,CAAC,EAAE,UAAU,CAAC,QAAQ,EAAE;YACpD,CAAC,uCAAkB,CAAC,QAAQ,CAAC,EAAE,UAAU,CAAC,QAAQ;YAClD,CAAC,uCAAkB,CAAC,SAAS,CAAC,EAAE,UAAU,CAAC,MAAM;YACjD,CAAC,uCAAkB,CAAC,UAAU,CAAC,EAAE,SAAS;SAC3C,CAAC;QAEF,MAAM,WAAW,GAA2B,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;QACzE,MAAM,aAAa,GAAG,UAAU,CAAC,QAAQ,CAAC;QAC1C,MAAM,UAAU,GAAG,UAAU,CAAC,IAAI,IAAI,WAAW,CAAC,SAAS,CAAC,CAAC;QAE7D,UAAU,CAAC,uCAAkB,CAAC,cAAc,CAAC,GAAG,aAAa,CAAC;QAC9D,IAAI,UAAU,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,EAAE;YAC5C,UAAU,CAAC,uCAAkB,CAAC,WAAW,CAAC,GAAG,MAAM,CAAC,UAAU,CAAC,CAAC;SACjE;QAED,8BAA8B;QAC9B,IAAI,SAAS,CAAC;QACd,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;YAClC,MAAM,GAAG,GAAG,OAAO,CAAC,OAAO,CAAC,SAAS,CACnC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,EAAE,KAAK,YAAY,CACtC,CAAC;YACF,IAAI,GAAG,IAAI,CAAC,EAAE;gBACZ,SAAS,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;aACtC;SACF;aAAM,IAAI,OAAO,OAAO,CAAC,OAAO,KAAK,QAAQ,EAAE;YAC9C,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;YAC9C,MAAM,QAAQ,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAChC,CAAC,CAAC,WAAW,EAAE,CAAC,UAAU,CAAC,YAAY,CAAC,CACzC,CAAC;YACF,SAAS;gBACP,QAAQ,IAAI,QAAQ,CAAC,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;SACpE;QAED,IAAI,SAAS,EAAE;YACb,UAAU,CAAC,uCAAkB,CAAC,mBAAmB,CAAC,GAAG,SAAS,CAAC;SAChE;QAED,0CAA0C;QAC1C,MAAM,cAAc,GAAG,IAAA,wCAAsB,EAC3C,GAAG,EAAE,WAAC,OAAA,MAAA,MAAM,CAAC,aAAa,+CAApB,MAAM,EAAiB,OAAO,CAAC,CAAA,EAAA,EACrC,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,8BAA8B,EAAE,CAAC,CAAC,EAC7D,IAAI,CACL,CAAC;QACF,IAAI,cAAc,EAAE;YAClB,MAAM,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,EAAE;gBACpD,UAAU,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC;YACxB,CAAC,CAAC,CAAC;SACJ;QAED,mDAAmD;QACnD,0EAA0E;QAC1E,4CAA4C;QAC5C,4BAA4B;QAC5B,MAAM,SAAS,GAAG,aAAO,CAAC,MAAM,EAAE,CAAC;QACnC,MAAM,WAAW,GAAG,WAAK,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QAC7C,IAAI,IAAU,CAAC;QAEf,IACE,MAAM,CAAC,qBAAqB;YAC5B,CAAC,CAAC,WAAW,IAAI,CAAC,WAAK,CAAC,kBAAkB,CAAC,WAAW,CAAC,WAAW,EAAE,CAAC,CAAC,EACtE;YACA,IAAI,GAAG,WAAK,CAAC,eAAe,CAAC,0BAAoB,CAAC,CAAC;SACpD;aAAM;YACL,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,CAC1B,aAAa,KAAK,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,aAAa,EACnD;gBACE,IAAI,EAAE,cAAQ,CAAC,MAAM;gBACrB,UAAU,EAAE,UAAU;aACvB,EACD,SAAS,CACV,CAAC;SACH;QAED,sCAAsC;QACtC,IAAA,wCAAsB,EACpB,GAAG,EAAE,WAAC,OAAA,MAAA,MAAM,CAAC,WAAW,+CAAlB,MAAM,EAAe,IAAI,EAAE,OAAO,CAAC,CAAA,EAAA,EACzC,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,4BAA4B,EAAE,CAAC,CAAC,EAC3D,IAAI,CACL,CAAC;QAEF,sDAAsD;QACtD,0BAA0B;QAC1B,MAAM,cAAc,GAAG,WAAK,CAAC,OAAO,CAAC,aAAO,CAAC,MAAM,EAAE,EAAE,IAAI,CAAC,CAAC;QAC7D,MAAM,YAAY,GAA2B,EAAE,CAAC;QAChD,iBAAW,CAAC,MAAM,CAAC,cAAc,EAAE,YAAY,CAAC,CAAC;QAEjD,MAAM,aAAa,GAAG,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;QAEnD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,aAAa,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC7C,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC;YAEhC,IAAI,OAAO,OAAO,CAAC,SAAS,KAAK,UAAU,EAAE;gBAC3C,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;aACzB;iBAAM,IAAI,OAAO,OAAO,CAAC,OAAO,KAAK,QAAQ,EAAE;gBAC9C,OAAO,CAAC,OAAO,IAAI,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC;aACrC;iBAAM,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;gBACzC,qEAAqE;gBACrE,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;aAC5B;SACF;QACD,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,OAAO,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE,SAAS,EAAE,CAAC,CAAC;IACpE,CAAC;IAED,wFAAwF;IACxF,6FAA6F;IAC7F,uFAAuF;IAC/E,gBAAgB,CAAC,EAAE,OAAO,EAAE,MAAM,EAAyB;;QACjE,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,OAAwB,CAAC,CAAC;QAEjE,IAAI,CAAC,MAAM,EAAE;YACX,OAAO;SACR;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QAChC,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,CAAC;QACxB,MAAM,EAAE,aAAa,EAAE,UAAU,EAAE,GAAG,MAAM,CAAC;QAC7C,MAAM,cAAc,GAAe;YACjC,CAAC,uCAAkB,CAAC,oBAAoB,CAAC,EAAE,aAAa;YACxD,CAAC,uCAAkB,CAAC,iBAAiB,CAAC,EAAE,UAAU;SACnD,CAAC;QAEF,qEAAqE;QACrE,wDAAwD;QACxD,IAAI,MAAA,MAAM,CAAC,uBAAuB,0CAAE,cAAc,EAAE;YAClD,MAAM,gBAAgB,GAAG,IAAI,GAAG,CAC9B,MAAM,CAAC,uBAAuB,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC,CACxE,CAAC;YAEF,2BAA2B;YAC3B,8BAA8B;YAC9B,gCAAgC;YAChC,MAAM,UAAU,GAAG,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC;gBAC/C,CAAC,CAAC,OAAO,CAAC,OAAO;gBACjB,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;YAClC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,EAAE;gBAC5B,MAAM,QAAQ,GAAG,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;gBAChC,MAAM,YAAY,GAAG,QAAQ,KAAK,CAAC,CAAC,CAAC;gBACrC,MAAM,IAAI,GAAG,CACX,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAC5C,CAAC,WAAW,EAAE,CAAC;gBAChB,MAAM,KAAK,GAAG,YAAY;oBACxB,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,QAAQ,GAAG,CAAC,CAAC;oBAC3B,CAAC,CAAC,UAAU,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;gBAExB,IAAI,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;oBAC9B,cAAc,CAAC,uBAAuB,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC;iBAC9D;YACH,CAAC,CAAC,CAAC;SACJ;QAED,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC;IACrC,CAAC;IAED,iFAAiF;IACjF,wDAAwD;IACxD,qEAAqE;IAC7D,iBAAiB,CAAC,EACxB,OAAO,EACP,QAAQ,GACe;;QACvB,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAEhD,IAAI,CAAC,MAAM,EAAE;YACX,OAAO;SACR;QAED,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,GAAG,MAAM,CAAC;QACpC,MAAM,cAAc,GAAe;YACjC,CAAC,uCAAkB,CAAC,yBAAyB,CAAC,EAAE,QAAQ,CAAC,UAAU;SACpE,CAAC;QAEF,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QAEhC,uCAAuC;QACvC,IAAA,wCAAsB,EACpB,GAAG,EAAE,WAAC,OAAA,MAAA,MAAM,CAAC,YAAY,+CAAnB,MAAM,EAAgB,IAAI,EAAE,EAAE,OAAO,EAAE,QAAQ,EAAE,CAAC,CAAA,EAAA,EACxD,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,6BAA6B,EAAE,CAAC,CAAC,EAC5D,IAAI,CACL,CAAC;QAEF,MAAM,gBAAgB,GAAG,IAAI,GAAG,EAAE,CAAC;QAEnC,IAAI,MAAA,MAAM,CAAC,uBAAuB,0CAAE,eAAe,EAAE;YACnD,MAAA,MAAM,CAAC,uBAAuB,0CAAE,eAAe,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAC7D,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CACzC,CAAC;SACH;QAED,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,QAAQ,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE;YAC9D,MAAM,IAAI,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,CAAC,WAAW,EAAE,CAAC;YAC5D,MAAM,KAAK,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;YAExC,IAAI,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;gBAC9B,cAAc,CAAC,wBAAwB,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAC;aACnE;YAED,IAAI,IAAI,KAAK,gBAAgB,EAAE;gBAC7B,MAAM,aAAa,GAAG,MAAM,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC;gBAC/C,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,EAAE;oBACzB,cAAc,CAAC,qCAAqC,CAAC,GAAG,aAAa,CAAC;iBACvE;aACF;SACF;QAED,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC;QACnC,IAAI,CAAC,SAAS,CAAC;YACb,IAAI,EACF,QAAQ,CAAC,UAAU,IAAI,GAAG;gBACxB,CAAC,CAAC,oBAAc,CAAC,KAAK;gBACtB,CAAC,CAAC,oBAAc,CAAC,KAAK;SAC3B,CAAC,CAAC;QACH,MAAM,CAAC,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC,UAAU,EAAE,cAAc,CAAC,CAAC;IAChE,CAAC;IAED,2EAA2E;IACnE,MAAM,CAAC,EAAE,OAAO,EAA0B;QAChD,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAEhD,IAAI,CAAC,MAAM,EAAE;YACX,OAAO;SACR;QAED,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,SAAS,EAAE,GAAG,MAAM,CAAC;QAE/C,eAAe;QACf,IAAI,CAAC,GAAG,EAAE,CAAC;QACX,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAEpC,iBAAiB;QACjB,IAAI,CAAC,qBAAqB,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;IACpD,CAAC;IAED,uEAAuE;IACvE,qFAAqF;IACrF,iDAAiD;IACjD,yDAAyD;IACzD,+EAA+E;IAC/E,6BAA6B;IACrB,OAAO,CAAC,EAAE,OAAO,EAAE,KAAK,EAAO;QACrC,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAEhD,IAAI,CAAC,MAAM,EAAE;YACX,OAAO;SACR;QAED,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,SAAS,EAAE,GAAG,MAAM,CAAC;QAE/C,2EAA2E;QAC3E,+EAA+E;QAC/E,oBAAoB;QACpB,gDAAgD;QAChD,sBAAsB;QACtB,4DAA4D;QAC5D,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;QAC5B,IAAI,CAAC,SAAS,CAAC;YACb,IAAI,EAAE,oBAAc,CAAC,KAAK;YAC1B,OAAO,EAAE,KAAK,CAAC,OAAO;SACvB,CAAC,CAAC;QACH,IAAI,CAAC,GAAG,EAAE,CAAC;QACX,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAEpC,kCAAkC;QAClC,UAAU,CAAC,uCAAkB,CAAC,UAAU,CAAC,GAAG,KAAK,CAAC,OAAO,CAAC;QAC1D,IAAI,CAAC,qBAAqB,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;IACpD,CAAC;IAEO,qBAAqB,CAAC,UAAsB,EAAE,SAAiB;QACrE,yBAAyB;QACzB,MAAM,iBAAiB,GAAe,EAAE,CAAC;QACzC,6CAA6C;QAC7C,MAAM,UAAU,GAAG;YACjB,uCAAkB,CAAC,yBAAyB;YAC5C,uCAAkB,CAAC,mBAAmB;YACtC,uCAAkB,CAAC,cAAc;YACjC,uCAAkB,CAAC,WAAW;YAC9B,uCAAkB,CAAC,UAAU;YAC7B,uCAAkB,CAAC,UAAU;SAC9B,CAAC;QACF,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;YACvB,IAAI,GAAG,IAAI,UAAU,EAAE;gBACrB,iBAAiB,CAAC,GAAG,CAAC,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC;aAC1C;QACH,CAAC,CAAC,CAAC;QAEH,kCAAkC;QAClC,MAAM,eAAe,GACnB,IAAA,2BAAoB,EAAC,IAAA,qBAAc,EAAC,SAAS,EAAE,IAAA,aAAM,GAAE,CAAC,CAAC,GAAG,IAAI,CAAC;QACnE,IAAI,CAAC,4BAA4B,CAAC,MAAM,CACtC,eAAe,EACf,iBAAiB,CAClB,CAAC;IACJ,CAAC;IAEO,gBAAgB,CAAC,QAAgB;QACvC,MAAM,YAAY,GAAG;YACnB,OAAO,EAAE,IAAI;YACb,OAAO,EAAE,IAAI;YACb,IAAI,EAAE,IAAI;YACV,GAAG,EAAE,IAAI;YACT,IAAI,EAAE,IAAI;YACV,GAAG,EAAE,IAAI;YACT,KAAK,EAAE,IAAI;YACX,MAAM,EAAE,IAAI;YACZ,KAAK,EAAE,IAAI;SACZ,CAAC;QAEF,IAAI,QAAQ,CAAC,WAAW,EAAE,IAAI,YAAY,EAAE;YAC1C,OAAO,QAAQ,CAAC,WAAW,EAAE,CAAC;SAC/B;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;CACF;AArcD,sDAqcC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport * as diagch from 'diagnostics_channel';\nimport { URL } from 'url';\n\nimport {\n  InstrumentationBase,\n  safeExecuteInTheMiddle,\n} from '@opentelemetry/instrumentation';\nimport {\n  Attributes,\n  context,\n  Histogram,\n  HrTime,\n  INVALID_SPAN_CONTEXT,\n  propagation,\n  Span,\n  SpanKind,\n  SpanStatusCode,\n  trace,\n  ValueType,\n} from '@opentelemetry/api';\n\n/** @knipignore */\nimport { PACKAGE_NAME, PACKAGE_VERSION } from './version';\n\nimport {\n  ListenerRecord,\n  RequestHeadersMessage,\n  RequestMessage,\n  RequestTrailersMessage,\n  ResponseHeadersMessage,\n} from './internal-types';\nimport { UndiciInstrumentationConfig, UndiciRequest } from './types';\nimport { SemanticAttributes } from './enums/SemanticAttributes';\nimport {\n  hrTime,\n  hrTimeDuration,\n  hrTimeToMilliseconds,\n} from '@opentelemetry/core';\n\ninterface InstrumentationRecord {\n  span: Span;\n  attributes: Attributes;\n  startTime: HrTime;\n}\n\n// A combination of https://github.com/elastic/apm-agent-nodejs and\n// https://github.com/gadget-inc/opentelemetry-instrumentations/blob/main/packages/opentelemetry-instrumentation-undici/src/index.ts\nexport class UndiciInstrumentation extends InstrumentationBase<UndiciInstrumentationConfig> {\n  // Keep ref to avoid https://github.com/nodejs/node/issues/42170 bug and for\n  // unsubscribing.\n  private _channelSubs!: Array<ListenerRecord>;\n  private _recordFromReq = new WeakMap<UndiciRequest, InstrumentationRecord>();\n\n  private _httpClientDurationHistogram!: Histogram;\n  constructor(config: UndiciInstrumentationConfig = {}) {\n    super(PACKAGE_NAME, PACKAGE_VERSION, config);\n  }\n\n  // No need to instrument files/modules\n  protected override init() {\n    return undefined;\n  }\n\n  override disable(): void {\n    super.disable();\n    this._channelSubs.forEach(sub => sub.unsubscribe());\n    this._channelSubs.length = 0;\n  }\n\n  override enable(): void {\n    // \"enabled\" handling is currently a bit messy with InstrumentationBase.\n    // If constructed with `{enabled: false}`, this `.enable()` is still called,\n    // and `this.getConfig().enabled !== this.isEnabled()`, creating confusion.\n    //\n    // For now, this class will setup for instrumenting if `.enable()` is\n    // called, but use `this.getConfig().enabled` to determine if\n    // instrumentation should be generated. This covers the more likely common\n    // case of config being given a construction time, rather than later via\n    // `instance.enable()`, `.disable()`, or `.setConfig()` calls.\n    super.enable();\n\n    // This method is called by the super-class constructor before ours is\n    // called. So we need to ensure the property is initalized.\n    this._channelSubs = this._channelSubs || [];\n\n    // Avoid to duplicate subscriptions\n    if (this._channelSubs.length > 0) {\n      return;\n    }\n\n    this.subscribeToChannel(\n      'undici:request:create',\n      this.onRequestCreated.bind(this)\n    );\n    this.subscribeToChannel(\n      'undici:client:sendHeaders',\n      this.onRequestHeaders.bind(this)\n    );\n    this.subscribeToChannel(\n      'undici:request:headers',\n      this.onResponseHeaders.bind(this)\n    );\n    this.subscribeToChannel('undici:request:trailers', this.onDone.bind(this));\n    this.subscribeToChannel('undici:request:error', this.onError.bind(this));\n  }\n\n  protected override _updateMetricInstruments() {\n    this._httpClientDurationHistogram = this.meter.createHistogram(\n      'http.client.request.duration',\n      {\n        description: 'Measures the duration of outbound HTTP requests.',\n        unit: 's',\n        valueType: ValueType.DOUBLE,\n        advice: {\n          explicitBucketBoundaries: [\n            0.005, 0.01, 0.025, 0.05, 0.075, 0.1, 0.25, 0.5, 0.75, 1, 2.5, 5,\n            7.5, 10,\n          ],\n        },\n      }\n    );\n  }\n\n  private subscribeToChannel(\n    diagnosticChannel: string,\n    onMessage: (message: any, name: string | symbol) => void\n  ) {\n    // `diagnostics_channel` had a ref counting bug until v18.19.0.\n    // https://github.com/nodejs/node/pull/47520\n    const [major, minor] = process.version\n      .replace('v', '')\n      .split('.')\n      .map(n => Number(n));\n    const useNewSubscribe = major > 18 || (major === 18 && minor >= 19);\n\n    let unsubscribe: () => void;\n    if (useNewSubscribe) {\n      diagch.subscribe?.(diagnosticChannel, onMessage);\n      unsubscribe = () => diagch.unsubscribe?.(diagnosticChannel, onMessage);\n    } else {\n      const channel = diagch.channel(diagnosticChannel);\n      channel.subscribe(onMessage);\n      unsubscribe = () => channel.unsubscribe(onMessage);\n    }\n\n    this._channelSubs.push({\n      name: diagnosticChannel,\n      unsubscribe,\n    });\n  }\n\n  // This is the 1st message we receive for each request (fired after request creation). Here we will\n  // create the span and populate some atttributes, then link the span to the request for further\n  // span processing\n  private onRequestCreated({ request }: RequestMessage): void {\n    // Ignore if:\n    // - instrumentation is disabled\n    // - ignored by config\n    // - method is 'CONNECT'\n    const config = this.getConfig();\n    const enabled = config.enabled !== false;\n    const shouldIgnoreReq = safeExecuteInTheMiddle(\n      () =>\n        !enabled ||\n        request.method === 'CONNECT' ||\n        config.ignoreRequestHook?.(request),\n      e => e && this._diag.error('caught ignoreRequestHook error: ', e),\n      true\n    );\n\n    if (shouldIgnoreReq) {\n      return;\n    }\n\n    const startTime = hrTime();\n    let requestUrl;\n    try {\n      requestUrl = new URL(request.path, request.origin);\n    } catch (err) {\n      this._diag.warn('could not determine url.full:', err);\n      // Skip instrumenting this request.\n      return;\n    }\n    const urlScheme = requestUrl.protocol.replace(':', '');\n    const requestMethod = this.getRequestMethod(request.method);\n    const attributes: Attributes = {\n      [SemanticAttributes.HTTP_REQUEST_METHOD]: requestMethod,\n      [SemanticAttributes.HTTP_REQUEST_METHOD_ORIGINAL]: request.method,\n      [SemanticAttributes.URL_FULL]: requestUrl.toString(),\n      [SemanticAttributes.URL_PATH]: requestUrl.pathname,\n      [SemanticAttributes.URL_QUERY]: requestUrl.search,\n      [SemanticAttributes.URL_SCHEME]: urlScheme,\n    };\n\n    const schemePorts: Record<string, string> = { https: '443', http: '80' };\n    const serverAddress = requestUrl.hostname;\n    const serverPort = requestUrl.port || schemePorts[urlScheme];\n\n    attributes[SemanticAttributes.SERVER_ADDRESS] = serverAddress;\n    if (serverPort && !isNaN(Number(serverPort))) {\n      attributes[SemanticAttributes.SERVER_PORT] = Number(serverPort);\n    }\n\n    // Get user agent from headers\n    let userAgent;\n    if (Array.isArray(request.headers)) {\n      const idx = request.headers.findIndex(\n        h => h.toLowerCase() === 'user-agent'\n      );\n      if (idx >= 0) {\n        userAgent = request.headers[idx + 1];\n      }\n    } else if (typeof request.headers === 'string') {\n      const headers = request.headers.split('\\r\\n');\n      const uaHeader = headers.find(h =>\n        h.toLowerCase().startsWith('user-agent')\n      );\n      userAgent =\n        uaHeader && uaHeader.substring(uaHeader.indexOf(':') + 1).trim();\n    }\n\n    if (userAgent) {\n      attributes[SemanticAttributes.USER_AGENT_ORIGINAL] = userAgent;\n    }\n\n    // Get attributes from the hook if present\n    const hookAttributes = safeExecuteInTheMiddle(\n      () => config.startSpanHook?.(request),\n      e => e && this._diag.error('caught startSpanHook error: ', e),\n      true\n    );\n    if (hookAttributes) {\n      Object.entries(hookAttributes).forEach(([key, val]) => {\n        attributes[key] = val;\n      });\n    }\n\n    // Check if parent span is required via config and:\n    // - if a parent is required but not present, we use a `NoopSpan` to still\n    //   propagate context without recording it.\n    // - create a span otherwise\n    const activeCtx = context.active();\n    const currentSpan = trace.getSpan(activeCtx);\n    let span: Span;\n\n    if (\n      config.requireParentforSpans &&\n      (!currentSpan || !trace.isSpanContextValid(currentSpan.spanContext()))\n    ) {\n      span = trace.wrapSpanContext(INVALID_SPAN_CONTEXT);\n    } else {\n      span = this.tracer.startSpan(\n        requestMethod === '_OTHER' ? 'HTTP' : requestMethod,\n        {\n          kind: SpanKind.CLIENT,\n          attributes: attributes,\n        },\n        activeCtx\n      );\n    }\n\n    // Execute the request hook if defined\n    safeExecuteInTheMiddle(\n      () => config.requestHook?.(span, request),\n      e => e && this._diag.error('caught requestHook error: ', e),\n      true\n    );\n\n    // Context propagation goes last so no hook can tamper\n    // the propagation headers\n    const requestContext = trace.setSpan(context.active(), span);\n    const addedHeaders: Record<string, string> = {};\n    propagation.inject(requestContext, addedHeaders);\n\n    const headerEntries = Object.entries(addedHeaders);\n\n    for (let i = 0; i < headerEntries.length; i++) {\n      const [k, v] = headerEntries[i];\n\n      if (typeof request.addHeader === 'function') {\n        request.addHeader(k, v);\n      } else if (typeof request.headers === 'string') {\n        request.headers += `${k}: ${v}\\r\\n`;\n      } else if (Array.isArray(request.headers)) {\n        // undici@6.11.0 accidentally, briefly removed `request.addHeader()`.\n        request.headers.push(k, v);\n      }\n    }\n    this._recordFromReq.set(request, { span, attributes, startTime });\n  }\n\n  // This is the 2nd message we receive for each request. It is fired when connection with\n  // the remote is established and about to send the first byte. Here we do have info about the\n  // remote address and port so we can populate some `network.*` attributes into the span\n  private onRequestHeaders({ request, socket }: RequestHeadersMessage): void {\n    const record = this._recordFromReq.get(request as UndiciRequest);\n\n    if (!record) {\n      return;\n    }\n\n    const config = this.getConfig();\n    const { span } = record;\n    const { remoteAddress, remotePort } = socket;\n    const spanAttributes: Attributes = {\n      [SemanticAttributes.NETWORK_PEER_ADDRESS]: remoteAddress,\n      [SemanticAttributes.NETWORK_PEER_PORT]: remotePort,\n    };\n\n    // After hooks have been processed (which may modify request headers)\n    // we can collect the headers based on the configuration\n    if (config.headersToSpanAttributes?.requestHeaders) {\n      const headersToAttribs = new Set(\n        config.headersToSpanAttributes.requestHeaders.map(n => n.toLowerCase())\n      );\n\n      // headers could be in form\n      // ['name: value', ...] for v5\n      // ['name', 'value', ...] for v6\n      const rawHeaders = Array.isArray(request.headers)\n        ? request.headers\n        : request.headers.split('\\r\\n');\n      rawHeaders.forEach((h, idx) => {\n        const sepIndex = h.indexOf(':');\n        const hasSeparator = sepIndex !== -1;\n        const name = (\n          hasSeparator ? h.substring(0, sepIndex) : h\n        ).toLowerCase();\n        const value = hasSeparator\n          ? h.substring(sepIndex + 1)\n          : rawHeaders[idx + 1];\n\n        if (headersToAttribs.has(name)) {\n          spanAttributes[`http.request.header.${name}`] = value.trim();\n        }\n      });\n    }\n\n    span.setAttributes(spanAttributes);\n  }\n\n  // This is the 3rd message we get for each request and it's fired when the server\n  // headers are received, body may not be accessible yet.\n  // From the response headers we can set the status and content length\n  private onResponseHeaders({\n    request,\n    response,\n  }: ResponseHeadersMessage): void {\n    const record = this._recordFromReq.get(request);\n\n    if (!record) {\n      return;\n    }\n\n    const { span, attributes } = record;\n    const spanAttributes: Attributes = {\n      [SemanticAttributes.HTTP_RESPONSE_STATUS_CODE]: response.statusCode,\n    };\n\n    const config = this.getConfig();\n\n    // Execute the response hook if defined\n    safeExecuteInTheMiddle(\n      () => config.responseHook?.(span, { request, response }),\n      e => e && this._diag.error('caught responseHook error: ', e),\n      true\n    );\n\n    const headersToAttribs = new Set();\n\n    if (config.headersToSpanAttributes?.responseHeaders) {\n      config.headersToSpanAttributes?.responseHeaders.forEach(name =>\n        headersToAttribs.add(name.toLowerCase())\n      );\n    }\n\n    for (let idx = 0; idx < response.headers.length; idx = idx + 2) {\n      const name = response.headers[idx].toString().toLowerCase();\n      const value = response.headers[idx + 1];\n\n      if (headersToAttribs.has(name)) {\n        spanAttributes[`http.response.header.${name}`] = value.toString();\n      }\n\n      if (name === 'content-length') {\n        const contentLength = Number(value.toString());\n        if (!isNaN(contentLength)) {\n          spanAttributes['http.response.header.content-length'] = contentLength;\n        }\n      }\n    }\n\n    span.setAttributes(spanAttributes);\n    span.setStatus({\n      code:\n        response.statusCode >= 400\n          ? SpanStatusCode.ERROR\n          : SpanStatusCode.UNSET,\n    });\n    record.attributes = Object.assign(attributes, spanAttributes);\n  }\n\n  // This is the last event we receive if the request went without any errors\n  private onDone({ request }: RequestTrailersMessage): void {\n    const record = this._recordFromReq.get(request);\n\n    if (!record) {\n      return;\n    }\n\n    const { span, attributes, startTime } = record;\n\n    // End the span\n    span.end();\n    this._recordFromReq.delete(request);\n\n    // Record metrics\n    this.recordRequestDuration(attributes, startTime);\n  }\n\n  // This is the event we get when something is wrong in the request like\n  // - invalid options when calling `fetch` global API or any undici method for request\n  // - connectivity errors such as unreachable host\n  // - requests aborted through an `AbortController.signal`\n  // NOTE: server errors are considered valid responses and it's the lib consumer\n  // who should deal with that.\n  private onError({ request, error }: any): void {\n    const record = this._recordFromReq.get(request);\n\n    if (!record) {\n      return;\n    }\n\n    const { span, attributes, startTime } = record;\n\n    // NOTE: in `undici@6.3.0` when request aborted the error type changes from\n    // a custom error (`RequestAbortedError`) to a built-in `DOMException` carrying\n    // some differences:\n    // - `code` is from DOMEXception (ABORT_ERR: 20)\n    // - `message` changes\n    // - stacktrace is smaller and contains node internal frames\n    span.recordException(error);\n    span.setStatus({\n      code: SpanStatusCode.ERROR,\n      message: error.message,\n    });\n    span.end();\n    this._recordFromReq.delete(request);\n\n    // Record metrics (with the error)\n    attributes[SemanticAttributes.ERROR_TYPE] = error.message;\n    this.recordRequestDuration(attributes, startTime);\n  }\n\n  private recordRequestDuration(attributes: Attributes, startTime: HrTime) {\n    // Time to record metrics\n    const metricsAttributes: Attributes = {};\n    // Get the attribs already in span attributes\n    const keysToCopy = [\n      SemanticAttributes.HTTP_RESPONSE_STATUS_CODE,\n      SemanticAttributes.HTTP_REQUEST_METHOD,\n      SemanticAttributes.SERVER_ADDRESS,\n      SemanticAttributes.SERVER_PORT,\n      SemanticAttributes.URL_SCHEME,\n      SemanticAttributes.ERROR_TYPE,\n    ];\n    keysToCopy.forEach(key => {\n      if (key in attributes) {\n        metricsAttributes[key] = attributes[key];\n      }\n    });\n\n    // Take the duration and record it\n    const durationSeconds =\n      hrTimeToMilliseconds(hrTimeDuration(startTime, hrTime())) / 1000;\n    this._httpClientDurationHistogram.record(\n      durationSeconds,\n      metricsAttributes\n    );\n  }\n\n  private getRequestMethod(original: string): string {\n    const knownMethods = {\n      CONNECT: true,\n      OPTIONS: true,\n      HEAD: true,\n      GET: true,\n      POST: true,\n      PUT: true,\n      PATCH: true,\n      DELETE: true,\n      TRACE: true,\n    };\n\n    if (original.toUpperCase() in knownMethods) {\n      return original.toUpperCase();\n    }\n\n    return '_OTHER';\n  }\n}\n"]}