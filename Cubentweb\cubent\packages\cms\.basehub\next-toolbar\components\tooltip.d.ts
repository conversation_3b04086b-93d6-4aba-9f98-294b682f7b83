// This file was generated by basehub. Do not edit directly. Read more: https://basehub.com/docs/api-reference/basehub-sdk

/* eslint-disable */
/* eslint-disable eslint-comments/no-restricted-disable */
/* tslint:disable */
// @ts-nocheck

import * as React from "react";
export type Tooltip = {
    checkOverflow: () => void;
};
export declare const Tooltip: React.ForwardRefExoticComponent<{
    content: string;
    children: React.ReactNode;
    forceVisible?: boolean;
} & React.RefAttributes<unknown>>;
