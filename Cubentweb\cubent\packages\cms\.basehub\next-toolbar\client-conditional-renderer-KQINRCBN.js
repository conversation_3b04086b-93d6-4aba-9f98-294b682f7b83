// This file was generated by basehub. Do not edit directly. Read more: https://basehub.com/docs/api-reference/basehub-sdk

/* eslint-disable */
/* eslint-disable eslint-comments/no-restricted-disable */
/* tslint:disable */

"use client";
import "./chunk-YSQDPG26.js";

// ../../node_modules/.pnpm/basehub@8.2.7_@babel+runtim_3aebfa8e064bb601162c04844d38dd02/node_modules/basehub/src/next/toolbar/client-conditional-renderer.tsx
import * as React from "react";
import { createPortal } from "react-dom";
var LazyClientToolbar = React.lazy(
  () => import("./client-toolbar-CIQDQ5LJ.js").then((mod) => ({ default: mod.ClientToolbar }))
);
var ClientConditionalRenderer = ({
  draft,
  isForcedDraft,
  enableDraftMode,
  disableDraftMode,
  revalidateTags,
  resolvedRef,
  getLatestBranches
}) => {
  const [hasRendered, setHasRendered] = React.useState(false);
  React.useEffect(() => {
    setHasRendered(true);
  }, []);
  const bshbPreviewLSName = `bshb-preview-${resolvedRef.repoHash}`;
  const seekAndStoreBshbPreviewToken = React.useCallback(
    (type) => {
      if (typeof window === "undefined")
        return;
      const urlParams = new URLSearchParams(window.location.search);
      const bshbPreviewToken2 = urlParams.get("bshb-preview");
      if (bshbPreviewToken2) {
        try {
          window.localStorage?.setItem(bshbPreviewLSName, bshbPreviewToken2);
        } catch (e) {
        }
        return bshbPreviewToken2;
      }
      if (type === "url-only")
        return;
      try {
        const fromStorage = window.localStorage?.getItem(bshbPreviewLSName);
        if (fromStorage)
          return fromStorage;
      } catch (e) {
      }
    },
    [bshbPreviewLSName]
  );
  const [bshbPreviewToken, setBshbPreviewToken] = React.useState(seekAndStoreBshbPreviewToken);
  const [shouldAutoEnableDraft, setShouldAutoEnableDraft] = React.useState();
  React.useLayoutEffect(() => {
    if (draft || isForcedDraft) {
      setShouldAutoEnableDraft(false);
      return;
    }
    const previewToken = seekAndStoreBshbPreviewToken("url-only");
    if (!previewToken) {
      setShouldAutoEnableDraft(false);
      return;
    }
    setBshbPreviewToken(previewToken);
    setShouldAutoEnableDraft(true);
  }, [draft, isForcedDraft, seekAndStoreBshbPreviewToken]);
  React.useEffect(() => {
    const url = new URL(window.location.href);
    const shouldRevalidate = url.searchParams.get("__bshb-odr") === "true";
    const odrToken = url.searchParams.get("__bshb-odr-token");
    const ref = url.searchParams.get("__bshb-odr-ref");
    if (shouldRevalidate && odrToken) {
      revalidateTags({ bshbPreviewToken: odrToken, ...ref ? { ref } : {} }).then(({ success, message }) => {
        document.documentElement.dataset.basehubOdrStatus = success ? "success" : "error";
        if (!success) {
          document.documentElement.dataset.basehubOdrErrorMessage = "Response failed";
        }
        if (message) {
          document.documentElement.dataset.basehubOdrMessage = message;
        }
      }).catch((e) => {
        document.documentElement.dataset.basehubOdrStatus = "error";
        let errorMessage = "";
        try {
          errorMessage = e.message;
        } catch (err) {
          console.error(err);
          errorMessage = "Unknown error";
        }
        document.documentElement.dataset.basehubOdrErrorMessage = errorMessage;
      });
    }
  }, [revalidateTags]);
  if (!bshbPreviewToken && !isForcedDraft || !hasRendered || typeof document === "undefined") {
    return null;
  }
  const Portal = createPortal(
    /* @__PURE__ */ React.createElement(
      LazyClientToolbar,
      {
        disableDraftMode,
        enableDraftMode,
        draft,
        isForcedDraft,
        bshbPreviewToken,
        shouldAutoEnableDraft,
        seekAndStoreBshbPreviewToken,
        resolvedRef,
        getLatestBranches,
        bshbPreviewLSName
      }
    ),
    document.body
  );
  return Portal;
};
export {
  ClientConditionalRenderer
};
