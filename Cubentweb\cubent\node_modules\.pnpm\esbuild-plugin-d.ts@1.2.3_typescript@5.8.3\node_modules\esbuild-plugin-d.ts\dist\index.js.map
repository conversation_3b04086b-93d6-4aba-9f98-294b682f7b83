{"version": 3, "sources": ["../src/index.ts", "../src/lib/getCompilerOptions.ts", "../src/lib/resolveTSConfig.ts", "../src/lib/humanizeFileSize.ts", "../src/plugin.ts", "../src/lib/logger.ts"], "sourcesContent": ["export * from \"./lib\";\nexport * from \"./types\";\nexport * from \"./plugin\";\nexport { dtsPlugin as default } from \"./plugin\";\n", "import { createHash } from \"crypto\";\nimport { BuildOptions } from \"esbuild\";\nimport { resolve } from \"path\";\nimport ts from \"typescript\";\n\nimport { DTSPluginOpts } from \"@/types\";\n\nexport function getCompilerOptions(opts: {\n    tsconfig: any;\n    pluginOptions: DTSPluginOpts;\n    esbuildOptions: BuildOptions;\n}) {\n    const compilerOptions = ts.convertCompilerOptionsFromJson(\n        opts.tsconfig.compilerOptions,\n        process.cwd(),\n    ).options;\n\n    compilerOptions.declaration = true;\n    compilerOptions.emitDeclarationOnly = true;\n\n    if (!compilerOptions.declarationDir) {\n        compilerOptions.declarationDir =\n            compilerOptions.declarationDir ??\n            opts.esbuildOptions.outdir ??\n            compilerOptions.outDir;\n    }\n\n    if (compilerOptions.incremental && !compilerOptions.tsBuildInfoFile) {\n        const configHash = createHash(\"sha256\")\n            .update(\n                JSON.stringify({\n                    compilerOptions,\n                    __buildContext: opts.pluginOptions?.__buildContext,\n                }),\n            )\n            .digest(\"hex\");\n\n        const cacheDir = resolve(\n            require.resolve(\"esbuild/package.json\"),\n            \"../../.cache/esbuild-plugin-d.ts\",\n        );\n\n        compilerOptions.tsBuildInfoFile = resolve(\n            opts.pluginOptions.buildInfoDir ?? cacheDir,\n            `esbuild-plugin-dts-${configHash}.tsbuildinfo`,\n        );\n    }\n\n    compilerOptions.listEmittedFiles = true;\n\n    return compilerOptions;\n}\n", "import { readFileSync } from \"fs\";\nimport merge from \"lodash.merge\";\nimport { dirname, resolve } from \"path\";\nimport ts from \"typescript\";\n\nfunction resolveModulePath(path: string) {\n    try {\n        return require.resolve(path);\n    } catch (e) {\n        return undefined;\n    }\n}\n\nexport function resolveTSConfig(opts: {\n    configPath?: string;\n    configName?: string;\n    searchPath?: string;\n}) {\n    let configPath =\n        opts.configPath ??\n        ts.findConfigFile(\n            opts.searchPath ?? process.cwd(),\n            ts.sys.fileExists,\n            opts.configName,\n        );\n    if (!configPath) {\n        throw new Error(\"No config file found\");\n    }\n\n    if (configPath.startsWith(\".\")) {\n        configPath = require.resolve(configPath);\n    }\n\n    const config = ts.readConfigFile(configPath, (path) =>\n        readFileSync(path, \"utf-8\"),\n    );\n\n    if (config.config.extends) {\n        const parentConfig = resolveTSConfig({\n            ...opts,\n            configPath:\n                resolveModulePath(config.config.extends) ??\n                resolve(dirname(configPath), config.config.extends),\n        });\n\n        config.config = merge(parentConfig, config.config);\n    }\n\n    if (config.error) {\n        throw config.error;\n    } else {\n        return config.config;\n    }\n}\n", "export function humanizeFileSize(size: number): string {\n    const i = Math.floor(Math.log(size) / Math.log(1024));\n    return (\n        Math.round((size / Math.pow(1024, i)) * 100) / 100 +\n        [\"b\", \"kb\", \"mb\", \"gb\", \"tb\"][i]\n    );\n}\n", "import chalk from \"chalk\";\nimport { PartialMessage, Plugin } from \"esbuild\";\nimport { existsSync, lstatSync } from \"fs\";\nimport { resolve } from \"path\";\nimport ts from \"typescript\";\n\nimport { humanizeFileSize } from \"@/lib\";\nimport { getCompilerOptions } from \"@/lib/getCompilerOptions\";\nimport { createLogger } from \"@/lib/logger\";\nimport { resolveTSConfig } from \"@/lib/resolveTSConfig\";\nimport { DTSPluginOpts } from \"@/types/options\";\n\nexport const dtsPlugin = (opts: DTSPluginOpts = {}) =>\n    ({\n        name: \"dts-plugin\",\n        async setup(build) {\n            const log = createLogger(build.initialOptions.logLevel);\n\n            const config =\n                opts.tsconfig && typeof opts.tsconfig !== \"string\"\n                    ? opts.tsconfig\n                    : resolveTSConfig({\n                          configPath: opts.tsconfig,\n                      });\n\n            const compilerOptions = getCompilerOptions({\n                tsconfig: config,\n                pluginOptions: opts,\n                esbuildOptions: build.initialOptions,\n            });\n\n            const compilerHost = compilerOptions.incremental\n                ? ts.createIncrementalCompilerHost(compilerOptions)\n                : ts.createCompilerHost(compilerOptions);\n\n            const inputFiles: string[] = [];\n\n            build.onLoad({ filter: /(\\.tsx|\\.ts)$/ }, async (args) => {\n                inputFiles.push(args.path);\n\n                const errors: PartialMessage[] = [];\n\n                compilerHost.getSourceFile(\n                    args.path,\n                    compilerOptions.target ?? ts.ScriptTarget.Latest,\n                    (m) => {\n                        errors.push({\n                            detail: m,\n                        });\n                    },\n                    true,\n                );\n\n                return {\n                    errors,\n                };\n            });\n\n            build.onEnd(() => {\n                let compilerProgram;\n\n                if (compilerOptions.incremental) {\n                    compilerProgram = ts.createIncrementalProgram({\n                        options: compilerOptions,\n                        host: compilerHost,\n                        rootNames: inputFiles,\n                    });\n                } else {\n                    compilerProgram = ts.createProgram(\n                        inputFiles,\n                        compilerOptions,\n                        compilerHost,\n                    );\n                }\n\n                const diagnostics = ts\n                    .getPreEmitDiagnostics(compilerProgram as ts.Program)\n                    .map(\n                        (d) =>\n                            ({\n                                text:\n                                    typeof d.messageText === \"string\"\n                                        ? d.messageText\n                                        : d.messageText.messageText,\n                                detail: d,\n                                location: {\n                                    file: d.file?.fileName,\n                                    namespace: \"file\",\n                                },\n                                category: d.category,\n                            }) satisfies PartialMessage & {\n                                category: ts.DiagnosticCategory;\n                            },\n                    );\n\n                const errors = diagnostics\n                    .filter((d) => d.category === ts.DiagnosticCategory.Error)\n                    .map(({ category: _, ...message }) => message);\n\n                const warnings = diagnostics\n                    .filter((d) => d.category === ts.DiagnosticCategory.Warning)\n                    .map(({ category: _, ...message }) => message);\n\n                if (errors.length > 0) {\n                    return {\n                        errors,\n                        warnings,\n                    };\n                }\n\n                const startTime = Date.now();\n                const emitResult = compilerProgram.emit();\n\n                if (\n                    emitResult.emitSkipped ||\n                    typeof emitResult.emittedFiles === \"undefined\"\n                ) {\n                    log.info(chalk`{yellow No declarations emitted}`);\n                } else {\n                    for (const emittedFile of emitResult.emittedFiles) {\n                        const emittedPath = resolve(emittedFile);\n\n                        if (\n                            existsSync(emittedPath) &&\n                            emittedPath !== compilerOptions.tsBuildInfoFile\n                        ) {\n                            const stat = lstatSync(emittedPath);\n\n                            const pathFromContentRoot = emittedPath\n                                .replace(resolve(process.cwd()), \"\")\n                                .replace(/^[\\\\/]/, \"\");\n                            const humanFileSize = humanizeFileSize(stat.size);\n\n                            log.info(\n                                chalk`  {bold ${pathFromContentRoot}} {cyan ${humanFileSize}}`,\n                            );\n                        }\n                    }\n                }\n\n                log.info(\n                    chalk`{green Finished compiling declarations in ${\n                        Date.now() - startTime\n                    }ms}`,\n                );\n\n                return {\n                    warnings,\n                };\n            });\n        },\n    }) as Plugin;\n", "import { LogLevel } from \"esbuild\";\n\nexport function createLogger(logLevel?: LogLevel) {\n    const levels: LogLevel[] = [\n        \"verbose\",\n        \"debug\",\n        \"info\",\n        \"warning\",\n        \"error\",\n        \"silent\",\n    ];\n\n    for (const l of levels) {\n        if (l === logLevel) {\n            break;\n        } else {\n            levels.splice(levels.indexOf(l), 1);\n        }\n    }\n\n    return {\n        info: (...msg: string[]) => {\n            if (levels.includes(\"info\")) console.log(...msg);\n        },\n    };\n}\n"], "mappings": "6iBAAA,IAAAA,EAAA,GAAAC,EAAAD,EAAA,aAAAE,EAAA,cAAAA,EAAA,uBAAAC,EAAA,qBAAAC,EAAA,oBAAAC,IAAA,eAAAC,EAAAN,GCAA,IAAAO,EAA2B,kBAE3BC,EAAwB,gBACxBC,EAAe,yBAIR,SAASC,EAAmBC,EAIhC,CAXH,IAAAC,EAAAC,EAAAC,EAAAC,EAYI,IAAMC,EAAkB,EAAAC,QAAG,+BACvBN,EAAK,SAAS,gBACd,QAAQ,IAAI,CAChB,EAAE,QAYF,GAVAK,EAAgB,YAAc,GAC9BA,EAAgB,oBAAsB,GAEjCA,EAAgB,iBACjBA,EAAgB,gBACZH,GAAAD,EAAAI,EAAgB,iBAAhB,KAAAJ,EACAD,EAAK,eAAe,SADpB,KAAAE,EAEAG,EAAgB,QAGpBA,EAAgB,aAAe,CAACA,EAAgB,gBAAiB,CACjE,IAAME,KAAa,cAAW,QAAQ,EACjC,OACG,KAAK,UAAU,CACX,gBAAAF,EACA,gBAAgBF,EAAAH,EAAK,gBAAL,YAAAG,EAAoB,cACxC,CAAC,CACL,EACC,OAAO,KAAK,EAEXK,KAAW,WACb,gBAAgB,sBAAsB,EACtC,kCACJ,EAEAH,EAAgB,mBAAkB,YAC9BD,EAAAJ,EAAK,cAAc,eAAnB,KAAAI,EAAmCI,EACnC,sBAAsBD,CAAU,cACpC,CACJ,CAEA,OAAAF,EAAgB,iBAAmB,GAE5BA,CACX,CCnDA,IAAAI,EAA6B,cAC7BC,EAAkB,2BAClBC,EAAiC,gBACjCC,EAAe,yBAEf,SAASC,EAAkBC,EAAc,CACrC,GAAI,CACA,OAAO,QAAQ,QAAQA,CAAI,CAC/B,MAAY,CACR,MACJ,CACJ,CAEO,SAASC,EAAgBC,EAI7B,CAjBH,IAAAC,EAAAC,EAAAC,EAkBI,IAAIC,GACAF,EAAAF,EAAK,aAAL,KAAAE,EACA,EAAAG,QAAG,gBACCJ,EAAAD,EAAK,aAAL,KAAAC,EAAmB,QAAQ,IAAI,EAC/B,EAAAI,QAAG,IAAI,WACPL,EAAK,UACT,EACJ,GAAI,CAACI,EACD,MAAM,IAAI,MAAM,sBAAsB,EAGtCA,EAAW,WAAW,GAAG,IACzBA,EAAa,QAAQ,QAAQA,CAAU,GAG3C,IAAME,EAAS,EAAAD,QAAG,eAAeD,EAAaN,MAC1C,gBAAaA,EAAM,OAAO,CAC9B,EAEA,GAAIQ,EAAO,OAAO,QAAS,CACvB,IAAMC,EAAeR,EAAgB,CACjC,GAAGC,EACH,YACIG,EAAAN,EAAkBS,EAAO,OAAO,OAAO,IAAvC,KAAAH,KACA,cAAQ,WAAQC,CAAU,EAAGE,EAAO,OAAO,OAAO,CAC1D,CAAC,EAEDA,EAAO,UAAS,EAAAE,SAAMD,EAAcD,EAAO,MAAM,CACrD,CAEA,GAAIA,EAAO,MACP,MAAMA,EAAO,MAEb,OAAOA,EAAO,MAEtB,CCrDO,SAASG,EAAiBC,EAAsB,CACnD,IAAMC,EAAI,KAAK,MAAM,KAAK,IAAID,CAAI,EAAI,KAAK,IAAI,IAAI,CAAC,EACpD,OACI,KAAK,MAAOA,EAAO,KAAK,IAAI,KAAMC,CAAC,EAAK,GAAG,EAAI,IAC/C,CAAC,IAAK,KAAM,KAAM,KAAM,IAAI,EAAEA,CAAC,CAEvC,CCNA,IAAAC,EAAkB,oBAElBC,EAAsC,cACtCC,EAAwB,gBACxBC,EAAe,yBCFR,SAASC,EAAaC,EAAqB,CAC9C,IAAMC,EAAqB,CACvB,UACA,QACA,OACA,UACA,QACA,QACJ,EAEA,QAAWC,KAAKD,EAAQ,CACpB,GAAIC,IAAMF,EACN,MAEAC,EAAO,OAAOA,EAAO,QAAQC,CAAC,EAAG,CAAC,CAE1C,CAEA,MAAO,CACH,KAAM,IAAIC,IAAkB,CACpBF,EAAO,SAAS,MAAM,GAAG,QAAQ,IAAI,GAAGE,CAAG,CACnD,CACJ,CACJ,CDbO,IAAMC,EAAY,CAACC,EAAsB,CAAC,KAC5C,CACG,KAAM,aACN,MAAM,MAAMC,EAAO,CACf,IAAMC,EAAMC,EAAaF,EAAM,eAAe,QAAQ,EAEhDG,EACFJ,EAAK,UAAY,OAAOA,EAAK,UAAa,SACpCA,EAAK,SACLK,EAAgB,CACZ,WAAYL,EAAK,QACrB,CAAC,EAELM,EAAkBC,EAAmB,CACvC,SAAUH,EACV,cAAeJ,EACf,eAAgBC,EAAM,cAC1B,CAAC,EAEKO,EAAeF,EAAgB,YAC/B,EAAAG,QAAG,8BAA8BH,CAAe,EAChD,EAAAG,QAAG,mBAAmBH,CAAe,EAErCI,EAAuB,CAAC,EAE9BT,EAAM,OAAO,CAAE,OAAQ,eAAgB,EAAG,MAAOU,GAAS,CArCtE,IAAAC,EAsCgBF,EAAW,KAAKC,EAAK,IAAI,EAEzB,IAAME,EAA2B,CAAC,EAElC,OAAAL,EAAa,cACTG,EAAK,MACLC,EAAAN,EAAgB,SAAhB,KAAAM,EAA0B,EAAAH,QAAG,aAAa,OACzCK,GAAM,CACHD,EAAO,KAAK,CACR,OAAQC,CACZ,CAAC,CACL,EACA,EACJ,EAEO,CACH,OAAAD,CACJ,CACJ,CAAC,EAEDZ,EAAM,MAAM,IAAM,CACd,IAAIc,EAEAT,EAAgB,YAChBS,EAAkB,EAAAN,QAAG,yBAAyB,CAC1C,QAASH,EACT,KAAME,EACN,UAAWE,CACf,CAAC,EAEDK,EAAkB,EAAAN,QAAG,cACjBC,EACAJ,EACAE,CACJ,EAGJ,IAAMQ,EAAc,EAAAP,QACf,sBAAsBM,CAA6B,EACnD,IACIE,GAAG,CA9E5B,IAAAL,EA+E6B,OACG,KACI,OAAOK,EAAE,aAAgB,SACnBA,EAAE,YACFA,EAAE,YAAY,YACxB,OAAQA,EACR,SAAU,CACN,MAAML,EAAAK,EAAE,OAAF,YAAAL,EAAQ,SACd,UAAW,MACf,EACA,SAAUK,EAAE,QAChB,EAGR,EAEEJ,EAASG,EACV,OAAQC,GAAMA,EAAE,WAAa,EAAAR,QAAG,mBAAmB,KAAK,EACxD,IAAI,CAAC,CAAE,SAAUS,EAAG,GAAGC,CAAQ,IAAMA,CAAO,EAE3CC,EAAWJ,EACZ,OAAQC,GAAMA,EAAE,WAAa,EAAAR,QAAG,mBAAmB,OAAO,EAC1D,IAAI,CAAC,CAAE,SAAUS,EAAG,GAAGC,CAAQ,IAAMA,CAAO,EAEjD,GAAIN,EAAO,OAAS,EAChB,MAAO,CACH,OAAAA,EACA,SAAAO,CACJ,EAGJ,IAAMC,EAAY,KAAK,IAAI,EACrBC,EAAaP,EAAgB,KAAK,EAExC,GACIO,EAAW,aACX,OAAOA,EAAW,aAAiB,IAEnCpB,EAAI,KAAK,EAAAqB,yCAAuC,MAEhD,SAAWC,KAAeF,EAAW,aAAc,CAC/C,IAAMG,KAAc,WAAQD,CAAW,EAEvC,MACI,cAAWC,CAAW,GACtBA,IAAgBnB,EAAgB,gBAClC,CACE,IAAMoB,KAAO,aAAUD,CAAW,EAE5BE,EAAsBF,EACvB,WAAQ,WAAQ,QAAQ,IAAI,CAAC,EAAG,EAAE,EAClC,QAAQ,SAAU,EAAE,EACnBG,EAAgBC,EAAiBH,EAAK,IAAI,EAEhDxB,EAAI,KACA,EAAAqB,kBAAgBI,CAAmB,WAAWC,CAAa,GAC/D,CACJ,CACJ,CAGJ,OAAA1B,EAAI,KACA,EAAAqB,oDACI,KAAK,IAAI,EAAIF,CACjB,KACJ,EAEO,CACH,SAAAD,CACJ,CACJ,CAAC,CACL,CACJ", "names": ["src_exports", "__export", "dtsPlugin", "getCompilerOptions", "humanizeFileSize", "resolveTSConfig", "__toCommonJS", "import_crypto", "import_path", "import_typescript", "getCompilerOptions", "opts", "_a", "_b", "_c", "_d", "compilerOptions", "ts", "config<PERSON><PERSON>", "cacheDir", "import_fs", "import_lodash", "import_path", "import_typescript", "resolveModulePath", "path", "resolveTSConfig", "opts", "_a", "_b", "_c", "config<PERSON><PERSON>", "ts", "config", "parentConfig", "merge", "humanizeFileSize", "size", "i", "import_chalk", "import_fs", "import_path", "import_typescript", "createLogger", "logLevel", "levels", "l", "msg", "dtsPlugin", "opts", "build", "log", "createLogger", "config", "resolveTSConfig", "compilerOptions", "getCompilerOptions", "compilerHost", "ts", "inputFiles", "args", "_a", "errors", "m", "compilerProgram", "diagnostics", "d", "_", "message", "warnings", "startTime", "emitResult", "chalk", "emittedFile", "emittedPath", "stat", "pathFromContentRoot", "humanFileSize", "humanizeFileSize"]}