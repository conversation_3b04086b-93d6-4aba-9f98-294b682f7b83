// This file was generated by basehub. Do not edit directly. Read more: https://basehub.com/docs/api-reference/basehub-sdk

/* eslint-disable */
/* eslint-disable eslint-comments/no-restricted-disable */
/* tslint:disable */
// @ts-nocheck

import { LatestBranch } from "./components/branch-swticher";
import { ResolvedRef } from "../../common-types";
export declare const ClientToolbar: ({ draft, isForcedDraft, enableDraftMode, disableDraftMode, bshbPreviewToken, shouldAutoEnableDraft, seekAndStoreBshbPreviewToken, resolvedRef, getLatestBranches, }: {
    draft: boolean;
    isForcedDraft: boolean;
    enableDraftMode: (o: {
        bshbPreviewToken: string;
    }) => Promise<{
        status: number;
        response: {
            ref?: string;
            error?: string;
            latestBranches?: LatestBranch[];
        };
    }>;
    disableDraftMode: () => Promise<void>;
    bshbPreviewToken: string | undefined;
    bshbPreviewLSName: string;
    shouldAutoEnableDraft: boolean | undefined;
    seekAndStoreBshbPreviewToken: (type?: "url-only") => string | undefined;
    resolvedRef: ResolvedRef;
    getLatestBranches: (o: {
        bshbPreviewToken: string | undefined;
    }) => Promise<{
        status: number;
        response: LatestBranch[] | {
            error: string;
        };
    }>;
}) => import("react/jsx-runtime").JSX.Element;
