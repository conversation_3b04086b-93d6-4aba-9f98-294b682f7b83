// This file was generated by basehub. Do not edit directly. Read more: https://basehub.com/docs/api-reference/basehub-sdk

/* eslint-disable */
/* eslint-disable eslint-comments/no-restricted-disable */
/* tslint:disable */

// ../../node_modules/.pnpm/basehub@8.2.7_@babel+runtim_3aebfa8e064bb601162c04844d38dd02/node_modules/basehub/src/events/primitive.tsx
import {
  resolvedRef
} from "../index";
var EVENTS_V2_ENDPOINT_URL = "https://basehub.com/api/v2/events";
var QUERY_EVENTS_ENDPOINT_URL = "https://basehub.com/api/v2/events/query";
if (typeof process !== "undefined") {
  if (process?.env?.NEXT_PUBLIC_BASEHUB_ANALYTICS_V2_ENDPOINT) {
    EVENTS_V2_ENDPOINT_URL = process.env.NEXT_PUBLIC_BASEHUB_ANALYTICS_V2_ENDPOINT;
  } else if (process?.env?.BASEHUB_ANALYTICS_V2_ENDPOINT) {
    EVENTS_V2_ENDPOINT_URL = process.env.BASEHUB_ANALYTICS_V2_ENDPOINT;
  }
  if (process?.env?.NEXT_PUBLIC_BASEHUB_QUERY_EVENTS_V2_ENDPOINT) {
    QUERY_EVENTS_ENDPOINT_URL = process.env.NEXT_PUBLIC_BASEHUB_QUERY_EVENTS_V2_ENDPOINT;
  } else if (process?.env?.BASEHUB_QUERY_EVENTS_V2_ENDPOINT) {
    QUERY_EVENTS_ENDPOINT_URL = process.env.BASEHUB_QUERY_EVENTS_V2_ENDPOINT;
  }
}
var sendEvent = async (...args) => {
  const [key, data] = args;
  const parsedResolvedRef = resolvedRef;
  let formDataOrJson;
  if (data && Object.values(data).some((value) => value instanceof File)) {
    formDataOrJson = new FormData();
    formDataOrJson.append("_system_key", key);
    formDataOrJson.append("_system_type", "create");
    formDataOrJson.append(
      "_system_commitId",
      (parsedResolvedRef.type === "commit" ? parsedResolvedRef.id : parsedResolvedRef.headCommitId) ?? ""
    );
    if (parsedResolvedRef.type === "branch") {
      formDataOrJson.append("_system_branch", parsedResolvedRef.name);
    }
    if (data) {
      Object.entries(data).forEach(([field, value]) => {
        if (typeof formDataOrJson === "string")
          return;
        if (value instanceof File) {
          formDataOrJson.append(field, value);
        } else if (value !== null && value !== void 0) {
          formDataOrJson.append(`${typeof field}__${field}`, String(value));
        }
      });
    }
  } else {
    formDataOrJson = JSON.stringify({
      key,
      data,
      type: "create",
      commitId: parsedResolvedRef.type === "commit" ? parsedResolvedRef.id : parsedResolvedRef.headCommitId,
      branch: parsedResolvedRef.type === "branch" ? parsedResolvedRef.name : void 0
    });
  }
  const response = await fetch(EVENTS_V2_ENDPOINT_URL, {
    method: "POST",
    headers: {
      ...typeof formDataOrJson === "string" ? { "Content-Type": "application/json" } : {},
      Accept: "application/json"
    },
    body: formDataOrJson
  });
  return await response.json();
};
async function getEvents(key, options) {
  if (options.type === "table") {
    const response = await fetch(QUERY_EVENTS_ENDPOINT_URL, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ key, ...options })
    });
    const parsed = await response.json();
    if (parsed.success) {
      const data = parsed.data;
      return {
        success: true,
        data: data.map(({ sys_date, id, ...rest }) => ({
          date: sys_date,
          id,
          ..."value" in rest && typeof rest.value === "string" ? JSON.parse(rest.value) : rest
        }))
      };
    }
    return parsed;
  } else {
    const url = new URL(QUERY_EVENTS_ENDPOINT_URL);
    url.searchParams.append("key", key);
    options.range && url.searchParams.append("range", options.range);
    const response = await fetch(url.toString(), {
      method: "GET",
      headers: { "Content-Type": "application/json" }
    });
    return await response.json();
  }
}
async function updateEvent(key, id, data) {
  let formDataOrJson;
  if (data && Object.values(data).some((value) => value instanceof File)) {
    formDataOrJson = new FormData();
    formDataOrJson.append("_system_key", key);
    formDataOrJson.append("_system_type", "update");
    formDataOrJson.append("_system_id", id);
    Object.entries(data).forEach(([field, value]) => {
      if (typeof formDataOrJson === "string")
        return;
      if (value instanceof File) {
        formDataOrJson.append(field, value);
      } else if (value !== null && value !== void 0) {
        formDataOrJson.append(`${typeof field}__${field}`, String(value));
      }
    });
  } else {
    formDataOrJson = JSON.stringify({
      key,
      data,
      type: "update",
      id
    });
  }
  const response = await fetch(EVENTS_V2_ENDPOINT_URL, {
    method: "POST",
    headers: {
      ...typeof formDataOrJson === "string" ? { "Content-Type": "application/json" } : {},
      Accept: "application/json"
    },
    body: formDataOrJson
  });
  return await response.json();
}
async function deleteEvent(key, ids) {
  const url = new URL(EVENTS_V2_ENDPOINT_URL);
  url.searchParams.append("key", key);
  url.searchParams.append("ids", JSON.stringify(ids));
  const response = await fetch(url.toString(), {
    method: "DELETE",
    headers: { "Content-Type": "application/json" }
  });
  return await response.json();
}
function parseFormData(key, schema, formData) {
  const formattedData = {};
  const errors = {};
  schema.forEach((field) => {
    const key2 = field.name;
    if ((field.type === "select" || field.type === "radio") && field.multiple) {
      const values = formData.getAll(key2).filter(Boolean);
      if (field.required && values.length === 0) {
        errors[key2] = `${field.label || key2} is required`;
      }
      formattedData[key2] = values.map(String);
      return;
    }
    const value = formData.get(key2);
    if (field.required && (value === null || value === "")) {
      errors[key2] = `${field.label || key2} is required`;
      return;
    }
    if (value === null || value === "") {
      formattedData[key2] = field.defaultValue ?? null;
      return;
    }
    try {
      switch (field.type) {
        case "checkbox":
          formattedData[key2] = value === "on" || value === "true";
          break;
        case "email": {
          const email = String(value);
          const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
          if (!emailRegex.test(email)) {
            errors[key2] = `${field.label || key2} must be a valid email address`;
          }
          formattedData[key2] = email;
          break;
        }
        case "select":
        case "radio": {
          const stringValue = String(value);
          if (field.options.length && !field.options.includes(stringValue)) {
            errors[key2] = `${field.label || key2} must be one of the available options`;
          }
          formattedData[key2] = stringValue;
          break;
        }
        case "date":
        case "datetime": {
          const date = new Date(value);
          if (isNaN(date.getTime())) {
            errors[key2] = `${field.label || key2} must be a valid date`;
            break;
          }
          formattedData[key2] = date.toISOString();
          break;
        }
        case "number": {
          const num = Number(value);
          if (isNaN(num)) {
            errors[key2] = `${field.label || key2} must be a valid number`;
            break;
          }
          formattedData[key2] = num;
          break;
        }
        case "file": {
          if (!(value instanceof File)) {
            errors[key2] = `${field.label || key2} must be a valid file`;
            break;
          }
          if (!value.size && field.required) {
            errors[key2] = `${field.label || key2} is required`;
          } else if (!value.size) {
            formattedData[key2] = null;
          } else {
            formattedData[key2] = value;
          }
          break;
        }
        default:
          formattedData[key2] = String(value);
      }
    } catch (error) {
      errors[key2] = `Invalid value for ${field.label || key2}`;
    }
  });
  if (Object.keys(errors).length > 0) {
    return { success: false, errors };
  }
  return {
    data: formattedData,
    success: true
  };
}
export {
  deleteEvent,
  getEvents,
  parseFormData,
  sendEvent,
  updateEvent
};
