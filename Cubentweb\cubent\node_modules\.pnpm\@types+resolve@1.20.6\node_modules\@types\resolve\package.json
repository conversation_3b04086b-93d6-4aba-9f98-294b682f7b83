{"name": "@types/resolve", "version": "1.20.6", "description": "TypeScript definitions for resolve", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/resolve", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "marion<PERSON>l", "url": "https://github.com/marionebl"}, {"name": "<PERSON>", "githubUsername": "a<PERSON><PERSON><PERSON>", "url": "https://github.com/ajafff"}, {"name": "<PERSON>", "githubUsername": "lj<PERSON>b", "url": "https://github.com/ljharb"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/resolve"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "57ff7a1ad4d92d33ec0cf84533e429b281f942e40611034f3a3bf510bc275c2d", "typeScriptVersion": "4.5"}