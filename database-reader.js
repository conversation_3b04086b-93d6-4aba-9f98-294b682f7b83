#!/usr/bin/env node

/**
 * Cubent Database Reader
 * Connects to your Neon PostgreSQL database and displays current data
 */

const { Pool } = require('pg');
require('dotenv').config({ path: '.env.vscode' });

// Database connection configuration
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  ssl: {
    rejectUnauthorized: false
  }
});

// ANSI color codes for better output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m'
};

function log(message, color = colors.white) {
  console.log(`${color}${message}${colors.reset}`);
}

function logHeader(message) {
  console.log('\n' + '='.repeat(60));
  log(message, colors.bright + colors.cyan);
  console.log('='.repeat(60));
}

function logSubHeader(message) {
  console.log('\n' + '-'.repeat(40));
  log(message, colors.bright + colors.yellow);
  console.log('-'.repeat(40));
}

function logSuccess(message) {
  log(`✅ ${message}`, colors.green);
}

function logError(message) {
  log(`❌ ${message}`, colors.red);
}

function logInfo(message) {
  log(`ℹ️  ${message}`, colors.blue);
}

// Function to format and display query results
function displayResults(tableName, results) {
  if (results.rows.length === 0) {
    log(`No data found in ${tableName}`, colors.yellow);
    return;
  }

  logSuccess(`Found ${results.rows.length} records in ${tableName}`);
  
  // Display first few records with formatted output
  const maxRecords = Math.min(5, results.rows.length);
  
  for (let i = 0; i < maxRecords; i++) {
    const row = results.rows[i];
    console.log(`\n${colors.bright}Record ${i + 1}:${colors.reset}`);
    
    Object.entries(row).forEach(([key, value]) => {
      let displayValue = value;
      
      // Format different data types
      if (value === null) {
        displayValue = colors.yellow + 'NULL' + colors.reset;
      } else if (typeof value === 'object') {
        displayValue = colors.magenta + JSON.stringify(value, null, 2) + colors.reset;
      } else if (typeof value === 'boolean') {
        displayValue = value ? colors.green + 'true' + colors.reset : colors.red + 'false' + colors.reset;
      } else if (value instanceof Date) {
        displayValue = colors.cyan + value.toISOString() + colors.reset;
      }
      
      console.log(`  ${colors.bright}${key}:${colors.reset} ${displayValue}`);
    });
  }
  
  if (results.rows.length > maxRecords) {
    log(`... and ${results.rows.length - maxRecords} more records`, colors.yellow);
  }
}

// Function to get table information
async function getTableInfo() {
  try {
    const query = `
      SELECT 
        table_name,
        column_name,
        data_type,
        is_nullable,
        column_default
      FROM information_schema.columns 
      WHERE table_schema = 'public' 
      ORDER BY table_name, ordinal_position;
    `;
    
    const result = await pool.query(query);
    const tables = {};
    
    result.rows.forEach(row => {
      if (!tables[row.table_name]) {
        tables[row.table_name] = [];
      }
      tables[row.table_name].push({
        column: row.column_name,
        type: row.data_type,
        nullable: row.is_nullable === 'YES',
        default: row.column_default
      });
    });
    
    return tables;
  } catch (error) {
    logError(`Failed to get table info: ${error.message}`);
    return {};
  }
}

// Function to get row counts for all tables
async function getTableCounts() {
  try {
    const tablesQuery = `
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' AND table_type = 'BASE TABLE';
    `;
    
    const tablesResult = await pool.query(tablesQuery);
    const counts = {};
    
    for (const table of tablesResult.rows) {
      try {
        const countQuery = `SELECT COUNT(*) as count FROM "${table.table_name}";`;
        const countResult = await pool.query(countQuery);
        counts[table.table_name] = parseInt(countResult.rows[0].count);
      } catch (error) {
        counts[table.table_name] = `Error: ${error.message}`;
      }
    }
    
    return counts;
  } catch (error) {
    logError(`Failed to get table counts: ${error.message}`);
    return {};
  }
}

// Main function to read and display database contents
async function readDatabase() {
  try {
    logHeader('🗄️  CUBENT DATABASE READER');
    logInfo('Connecting to Neon PostgreSQL database...');
    
    // Test connection
    await pool.query('SELECT NOW()');
    logSuccess('Connected to database successfully!');
    
    // Get table information
    logSubHeader('📊 Database Schema');
    const tableInfo = await getTableInfo();
    const tableCounts = await getTableCounts();
    
    Object.entries(tableInfo).forEach(([tableName, columns]) => {
      const count = tableCounts[tableName] || 0;
      console.log(`\n${colors.bright}${tableName}${colors.reset} (${count} records)`);
      columns.forEach(col => {
        const nullable = col.nullable ? colors.yellow + 'nullable' + colors.reset : colors.red + 'required' + colors.reset;
        const defaultVal = col.default ? ` default: ${colors.cyan}${col.default}${colors.reset}` : '';
        console.log(`  ${col.column}: ${colors.magenta}${col.type}${colors.reset} (${nullable})${defaultVal}`);
      });
    });
    
    // Read data from main tables
    const mainTables = ['User', 'ExtensionSession', 'UsageMetrics', 'UsageAnalytics', 'ApiKey', 'UserProfile'];
    
    for (const tableName of mainTables) {
      if (tableInfo[tableName]) {
        logSubHeader(`📋 ${tableName} Data`);
        try {
          const query = `SELECT * FROM "${tableName}" ORDER BY "createdAt" DESC LIMIT 10;`;
          const result = await pool.query(query);
          displayResults(tableName, result);
        } catch (error) {
          logError(`Failed to read ${tableName}: ${error.message}`);
        }
      }
    }
    
    // Summary statistics
    logSubHeader('📈 Database Summary');
    const totalUsers = tableCounts['User'] || 0;
    const totalSessions = tableCounts['ExtensionSession'] || 0;
    const totalUsage = tableCounts['UsageMetrics'] || 0;
    
    logInfo(`Total Users: ${totalUsers}`);
    logInfo(`Active Sessions: ${totalSessions}`);
    logInfo(`Usage Records: ${totalUsage}`);
    
    // Recent activity
    if (totalUsers > 0) {
      logSubHeader('🕒 Recent Activity');
      try {
        const recentQuery = `
          SELECT 
            u.email,
            u.name,
            u."subscriptionTier",
            u."lastActiveAt",
            u."extensionEnabled"
          FROM "User" u 
          WHERE u."lastActiveAt" IS NOT NULL 
          ORDER BY u."lastActiveAt" DESC 
          LIMIT 5;
        `;
        const recentResult = await pool.query(recentQuery);
        displayResults('Recent User Activity', recentResult);
      } catch (error) {
        logError(`Failed to get recent activity: ${error.message}`);
      }
    }
    
  } catch (error) {
    logError(`Database connection failed: ${error.message}`);
    logInfo('Make sure your .env.vscode file is in the current directory with the correct DATABASE_URL');
  } finally {
    await pool.end();
    logInfo('Database connection closed.');
  }
}

// Run the database reader
if (require.main === module) {
  readDatabase().catch(console.error);
}

module.exports = { readDatabase };
