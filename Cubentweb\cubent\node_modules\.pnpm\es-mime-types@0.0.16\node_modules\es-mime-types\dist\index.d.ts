declare const extensions: any;
declare const types: any;
export { extensions, types };
/**
 * Get the default charset for a MIME type.
 */
export declare function charset(type: string): boolean | string;
/**
 * Create a full Content-Type header given a MIME type or extension.
 */
export declare function contentType(str: string): any;
/**
 * Get the default extension for a MIME type.
 */
export declare function extension(type: string | unknown): any;
/**
 * Lookup the MIME type for a file path/extension.
 */
export declare function lookup(path: string | unknown): any;
