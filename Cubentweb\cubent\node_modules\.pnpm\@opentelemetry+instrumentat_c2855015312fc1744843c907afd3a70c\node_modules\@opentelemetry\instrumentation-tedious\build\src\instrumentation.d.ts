import { InstrumentationBase, InstrumentationNodeModuleDefinition } from '@opentelemetry/instrumentation';
import { TediousInstrumentationConfig } from './types';
export declare class TediousInstrumentation extends InstrumentationBase<TediousInstrumentationConfig> {
    static readonly COMPONENT = "tedious";
    constructor(config?: TediousInstrumentationConfig);
    protected init(): InstrumentationNodeModuleDefinition[];
    private _patchConnect;
    private _patchQuery;
    private _patchCallbackQuery;
}
//# sourceMappingURL=instrumentation.d.ts.map