// This file was generated by basehub. Do not edit directly. Read more: https://basehub.com/docs/api-reference/basehub-sdk

/* eslint-disable */
/* eslint-disable eslint-comments/no-restricted-disable */
/* tslint:disable */

// @ts-nocheck
export const aliasSeparator = '__alias__'

export function replaceSystemAliases(obj) {
    if (typeof obj !== 'object' || obj === null) {
        return obj
    }

    if (Array.isArray(obj)) {
        return obj.map((item) => replaceSystemAliases(item))
    }

    const newObj = {}
    for (const [key, value] of Object.entries(obj)) {
        if (key.includes(aliasSeparator)) {
            const [_prefix, ...rest] = key.split(aliasSeparator)
            const newKey = rest.join(aliasSeparator) // In case there are multiple __alias__ in the key
            newObj[newKey] = replaceSystemAliases(value)
        } else {
            newObj[key] = replaceSystemAliases(value)
        }
    }

    return newObj
}
