{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../src/detectors/index.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,uCAQoB;AAPlB,wGAAA,YAAY,OAAA;AACZ,4GAAA,gBAAgB,OAAA;AAChB,sGAAA,UAAU,OAAA;AACV,0GAAA,cAAc,OAAA;AACd,2GAAA,eAAe,OAAA;AACf,+GAAA,mBAAmB,OAAA;AACnB,yHAAA,6BAA6B,OAAA;AAE/B,qDAAoD;AAA3C,kHAAA,eAAe,OAAA;AACxB,6CAA4C;AAAnC,0GAAA,WAAW,OAAA;AACpB,6DAA4D;AAAnD,0HAAA,mBAAmB,OAAA;AAC5B,qDAAoD;AAA3C,kHAAA,eAAe,OAAA", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport {\n  hostDetector,\n  hostDetectorSync,\n  osDetector,\n  osDetectorSync,\n  processDetector,\n  processDetectorSync,\n  serviceInstanceIdDetectorSync,\n} from './platform';\nexport { browserDetector } from './BrowserDetector';\nexport { envDetector } from './EnvDetector';\nexport { browserDetectorSync } from './BrowserDetectorSync';\nexport { envDetectorSync } from './EnvDetectorSync';\n"]}