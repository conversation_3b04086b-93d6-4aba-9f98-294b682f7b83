import { SuccessCondition } from './completion-listener';
export declare const defaultInputTarget = 0;
/**
 * Whether process.stdin should be forwarded to child processes.
 */
export declare const handleInput = false;
/**
 * How many processes to run at once.
 */
export declare const maxProcesses = 0;
/**
 * Indices and names of commands whose output are not to be logged.
 */
export declare const hide = "";
/**
 * The character to split <names> on.
 */
export declare const nameSeparator = ",";
/**
 * Which prefix style to use when logging processes output.
 */
export declare const prefix = "";
/**
 * Default prefix color.
 * @see https://www.npmjs.com/package/chalk
 */
export declare const prefixColors = "reset";
/**
 * How many bytes we'll show on the command prefix.
 */
export declare const prefixLength = 10;
export declare const raw = false;
/**
 * Number of attempts of restarting a process, if it exits with non-0 code.
 */
export declare const restartTries = 0;
/**
 * How many milliseconds concurrently should wait before restarting a process.
 */
export declare const restartDelay = 0;
/**
 * Condition of success for concurrently itself.
 */
export declare const success: SuccessCondition;
/**
 * Date format used when logging date/time.
 * @see https://www.unicode.org/reports/tr35/tr35-dates.html#Date_Field_Symbol_Table
 */
export declare const timestampFormat = "yyyy-MM-dd HH:mm:ss.SSS";
/**
 * Current working dir passed as option to spawn command.
 * Defaults to process.cwd()
 */
export declare const cwd: string | undefined;
/**
 * Whether to show timing information for processes in console output.
 */
export declare const timings = false;
/**
 * Passthrough additional arguments to commands (accessible via placeholders) instead of treating them as commands.
 */
export declare const passthroughArguments = false;
/**
 * Signal to send to other processes if one exits or dies.
 *
 * Defaults to OS specific signal. (SIGTERM on Linux/MacOS)
 */
export declare const killSignal: string | undefined;
