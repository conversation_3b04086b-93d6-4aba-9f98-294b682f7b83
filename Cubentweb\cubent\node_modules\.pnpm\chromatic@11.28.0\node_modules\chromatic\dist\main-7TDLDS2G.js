'use strict';

!function(){try{var e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},n=(new e.Error).stack;n&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[n]="f7add558-036f-5241-b000-043844b07585")}catch(e){}}();

var chunkROXNYPFF_js = require('./chunk-ROXNYPFF.js');
var chunkJ4YDBNCB_js = require('./chunk-J4YDBNCB.js');
require('./chunk-O2POOKSN.js');
require('./chunk-IM5VGDJQ.js');
require('./chunk-LTE3MQL2.js');
require('./chunk-XEU6YYLS.js');
require('./chunk-7UHX5T7X.js');
require('./chunk-LZXDNZPW.js');
require('./chunk-TKGT252T.js');

function f(e){var s;if(e.message&&(e.message=chunkJ4YDBNCB_js.e(e.message)),(s=e.exception)!=null&&s.values)for(let[p,n]of e.exception.values.entries())n.value&&(e.exception.values[p].value=chunkJ4YDBNCB_js.e(n.value));return e}function l(e){if(e.category==="console"){if(e.message==="")return null;e.message&&(e.message=chunkJ4YDBNCB_js.e(e.message));}return e}chunkROXNYPFF_js.c({dsn:"https://<EMAIL>/4507930289373184",release:"11.28.0",dist:"cli",sampleRate:1,environment:"production",enabled:process.env.DISABLE_ERROR_MONITORING!=="true"&&!0,enableTracing:!1,integrations:[],initialScope:{tags:{version:process.env.npm_package_version,index_url:process.env.CHROMATIC_INDEX_URL}},beforeSend:f,beforeBreadcrumb:l});async function g(e){try{let{code:s}=await chunkROXNYPFF_js.e({argv:e});process.exitCode=s;}catch(s){chunkROXNYPFF_js.a(s);}finally{await chunkROXNYPFF_js.b(2500),process.exit();}}

exports.main = g;
//# sourceMappingURL=out.js.map
//# sourceMappingURL=main-7TDLDS2G.js.map
//# debugId=f7add558-036f-5241-b000-043844b07585
