{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/index.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,mCAAkC;AAAzB,gGAAA,MAAM,OAAA;AACf,6DAK+B;AAJ7B,0HAAA,mBAAmB,OAAA;AAEnB,sHAAA,eAAe,OAAA;AAGjB,uCAAmE;AAA1D,8GAAA,kBAAkB,OAAA;AAAE,6GAAA,iBAAiB,OAAA;AAC9C,oEAAmE;AAA1D,0HAAA,mBAAmB,OAAA;AAC5B,sEAAqE;AAA5D,4HAAA,oBAAoB,OAAA;AAE7B,oEAAmE;AAA1D,0HAAA,mBAAmB,OAAA;AAE5B,gEAA+D;AAAtD,sHAAA,iBAAiB,OAAA;AAC1B,+DAA8D;AAArD,oHAAA,gBAAgB,OAAA;AACzB,6DAA4D;AAAnD,kHAAA,eAAe,OAAA;AACxB,mEAAkE;AAAzD,wHAAA,kBAAkB,OAAA;AAC3B,+EAA8E;AAArE,oIAAA,wBAAwB,OAAA;AACjC,qCAAsE;AAApD,2GAAA,gBAAgB,OAAA;AAClC,+BAA8B;AAArB,4FAAA,IAAI,OAAA", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport { Tracer } from './Tracer';\nexport {\n  BasicTracerProvider,\n  EXPORTER_FACTORY,\n  ForceFlushState,\n  PROPAGATOR_FACTORY,\n} from './BasicTracerProvider';\nexport { BatchSpanProcessor, RandomIdGenerator } from './platform';\nexport { ConsoleSpanExporter } from './export/ConsoleSpanExporter';\nexport { InMemorySpanExporter } from './export/InMemorySpanExporter';\nexport { ReadableSpan } from './export/ReadableSpan';\nexport { SimpleSpanProcessor } from './export/SimpleSpanProcessor';\nexport { SpanExporter } from './export/SpanExporter';\nexport { NoopSpanProcessor } from './export/NoopSpanProcessor';\nexport { AlwaysOffSampler } from './sampler/AlwaysOffSampler';\nexport { AlwaysOnSampler } from './sampler/AlwaysOnSampler';\nexport { ParentBasedSampler } from './sampler/ParentBasedSampler';\nexport { TraceIdRatioBasedSampler } from './sampler/TraceIdRatioBasedSampler';\nexport { Sampler, SamplingDecision, SamplingResult } from './Sampler';\nexport { Span } from './Span';\nexport { SpanProcessor } from './SpanProcessor';\nexport { TimedEvent } from './TimedEvent';\nexport {\n  BatchSpanProcessorBrowserConfig,\n  BufferConfig,\n  GeneralLimits,\n  SDKRegistrationConfig,\n  SpanLimits,\n  TracerConfig,\n} from './types';\nexport { IdGenerator } from './IdGenerator';\n"]}