{"version": 3, "file": "instrumentation.js", "sourceRoot": "", "sources": ["../../src/instrumentation.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,4CAU4B;AAC5B,sDAAuE;AACvE,mCAAmC;AAQnC;;GAEG;AACH,MAAsB,uBAAuB;IAW3C,YACkB,mBAA2B,EAC3B,sBAA8B,EAC9C,MAAkB;QAFF,wBAAmB,GAAnB,mBAAmB,CAAQ;QAC3B,2BAAsB,GAAtB,sBAAsB,CAAQ;QATtC,YAAO,GAAe,EAAgB,CAAC;QAwBjD,qCAAqC;QAC3B,UAAK,GAAG,OAAO,CAAC,IAAI,CAAC;QAC/B,wCAAwC;QAC9B,YAAO,GAAG,OAAO,CAAC,MAAM,CAAC;QACnC,0CAA0C;QAChC,cAAS,GAAG,OAAO,CAAC,QAAQ,CAAC;QACvC,6CAA6C;QACnC,gBAAW,GAAG,OAAO,CAAC,UAAU,CAAC;QAnBzC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;QAEvB,IAAI,CAAC,KAAK,GAAG,UAAI,CAAC,qBAAqB,CAAC;YACtC,SAAS,EAAE,mBAAmB;SAC/B,CAAC,CAAC;QAEH,IAAI,CAAC,OAAO,GAAG,WAAK,CAAC,SAAS,CAAC,mBAAmB,EAAE,sBAAsB,CAAC,CAAC;QAC5E,IAAI,CAAC,MAAM,GAAG,aAAO,CAAC,QAAQ,CAAC,mBAAmB,EAAE,sBAAsB,CAAC,CAAC;QAC5E,IAAI,CAAC,OAAO,GAAG,eAAI,CAAC,SAAS,CAAC,mBAAmB,EAAE,sBAAsB,CAAC,CAAC;QAC3E,IAAI,CAAC,wBAAwB,EAAE,CAAC;IAClC,CAAC;IAWD,mBAAmB;IACnB,IAAc,KAAK;QACjB,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC;IAED;;;OAGG;IACI,gBAAgB,CAAC,aAA4B;QAClD,IAAI,CAAC,MAAM,GAAG,aAAa,CAAC,QAAQ,CAClC,IAAI,CAAC,mBAAmB,EACxB,IAAI,CAAC,sBAAsB,CAC5B,CAAC;QAEF,IAAI,CAAC,wBAAwB,EAAE,CAAC;IAClC,CAAC;IAED,oBAAoB;IACpB,IAAc,MAAM;QAClB,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB,CAAC;IAED;;;OAGG;IACI,iBAAiB,CAAC,cAA8B;QACrD,IAAI,CAAC,OAAO,GAAG,cAAc,CAAC,SAAS,CACrC,IAAI,CAAC,mBAAmB,EACxB,IAAI,CAAC,sBAAsB,CAC5B,CAAC;IACJ,CAAC;IAED;;;;;;;OAOG;IACI,oBAAoB;;QACzB,MAAM,UAAU,GAAG,MAAA,IAAI,CAAC,IAAI,EAAE,mCAAI,EAAE,CAAC;QACrC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE;YAC9B,OAAO,CAAC,UAAU,CAAC,CAAC;SACrB;QAED,OAAO,UAAU,CAAC;IACpB,CAAC;IAED;;OAEG;IACO,wBAAwB;QAChC,OAAO;IACT,CAAC;IAED,mCAAmC;IAC5B,SAAS;QACd,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB,CAAC;IAED;;;OAGG;IACI,SAAS,CAAC,MAAkB;QACjC,mEAAmE;QACnE,uEAAuE;QACvE,IAAI,CAAC,OAAO,mBACV,OAAO,EAAE,IAAI,IACV,MAAM,CACV,CAAC;IACJ,CAAC;IAED;;;OAGG;IACI,iBAAiB,CAAC,cAA8B;QACrD,IAAI,CAAC,OAAO,GAAG,cAAc,CAAC,SAAS,CACrC,IAAI,CAAC,mBAAmB,EACxB,IAAI,CAAC,sBAAsB,CAC5B,CAAC;IACJ,CAAC;IAED,oBAAoB;IACpB,IAAc,MAAM;QAClB,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB,CAAC;IAiBD;;;;;;;OAOG;IACO,yBAAyB,CACjC,WAAyE,EACzE,WAAmB,EACnB,IAAU,EACV,IAA+B;QAE/B,IAAI,CAAC,WAAW,EAAE;YAChB,OAAO;SACR;QAED,IAAI;YACF,WAAW,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;SACzB;QAAC,OAAO,CAAC,EAAE;YACV,IAAI,CAAC,KAAK,CAAC,KAAK,CACd,mEAAmE,EACnE,EAAE,WAAW,EAAE,EACf,CAAC,CACF,CAAC;SACH;IACH,CAAC;CACF;AA5KD,0DA4KC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  diag,\n  DiagLogger,\n  metrics,\n  Meter,\n  MeterProvider,\n  trace,\n  Tracer,\n  TracerProvider,\n  Span,\n} from '@opentelemetry/api';\nimport { Logger, LoggerProvider, logs } from '@opentelemetry/api-logs';\nimport * as shimmer from 'shimmer';\nimport {\n  InstrumentationModuleDefinition,\n  Instrumentation,\n  InstrumentationConfig,\n  SpanCustomizationHook,\n} from './types';\n\n/**\n * Base abstract internal class for instrumenting node and web plugins\n */\nexport abstract class InstrumentationAbstract<\n  ConfigType extends InstrumentationConfig = InstrumentationConfig,\n> implements Instrumentation<ConfigType>\n{\n  protected _config: ConfigType = {} as ConfigType;\n\n  private _tracer: Tracer;\n  private _meter: Meter;\n  private _logger: Logger;\n  protected _diag: DiagLogger;\n\n  constructor(\n    public readonly instrumentationName: string,\n    public readonly instrumentationVersion: string,\n    config: ConfigType\n  ) {\n    this.setConfig(config);\n\n    this._diag = diag.createComponentLogger({\n      namespace: instrumentationName,\n    });\n\n    this._tracer = trace.getTracer(instrumentationName, instrumentationVersion);\n    this._meter = metrics.getMeter(instrumentationName, instrumentationVersion);\n    this._logger = logs.getLogger(instrumentationName, instrumentationVersion);\n    this._updateMetricInstruments();\n  }\n\n  /* Api to wrap instrumented method */\n  protected _wrap = shimmer.wrap;\n  /* Api to unwrap instrumented methods */\n  protected _unwrap = shimmer.unwrap;\n  /* Api to mass wrap instrumented method */\n  protected _massWrap = shimmer.massWrap;\n  /* Api to mass unwrap instrumented methods */\n  protected _massUnwrap = shimmer.massUnwrap;\n\n  /* Returns meter */\n  protected get meter(): Meter {\n    return this._meter;\n  }\n\n  /**\n   * Sets MeterProvider to this plugin\n   * @param meterProvider\n   */\n  public setMeterProvider(meterProvider: MeterProvider): void {\n    this._meter = meterProvider.getMeter(\n      this.instrumentationName,\n      this.instrumentationVersion\n    );\n\n    this._updateMetricInstruments();\n  }\n\n  /* Returns logger */\n  protected get logger(): Logger {\n    return this._logger;\n  }\n\n  /**\n   * Sets LoggerProvider to this plugin\n   * @param loggerProvider\n   */\n  public setLoggerProvider(loggerProvider: LoggerProvider): void {\n    this._logger = loggerProvider.getLogger(\n      this.instrumentationName,\n      this.instrumentationVersion\n    );\n  }\n\n  /**\n   * @experimental\n   *\n   * Get module definitions defined by {@link init}.\n   * This can be used for experimental compile-time instrumentation.\n   *\n   * @returns an array of {@link InstrumentationModuleDefinition}\n   */\n  public getModuleDefinitions(): InstrumentationModuleDefinition[] {\n    const initResult = this.init() ?? [];\n    if (!Array.isArray(initResult)) {\n      return [initResult];\n    }\n\n    return initResult;\n  }\n\n  /**\n   * Sets the new metric instruments with the current Meter.\n   */\n  protected _updateMetricInstruments(): void {\n    return;\n  }\n\n  /* Returns InstrumentationConfig */\n  public getConfig(): ConfigType {\n    return this._config;\n  }\n\n  /**\n   * Sets InstrumentationConfig to this plugin\n   * @param config\n   */\n  public setConfig(config: ConfigType): void {\n    // copy config first level properties to ensure they are immutable.\n    // nested properties are not copied, thus are mutable from the outside.\n    this._config = {\n      enabled: true,\n      ...config,\n    };\n  }\n\n  /**\n   * Sets TraceProvider to this plugin\n   * @param tracerProvider\n   */\n  public setTracerProvider(tracerProvider: TracerProvider): void {\n    this._tracer = tracerProvider.getTracer(\n      this.instrumentationName,\n      this.instrumentationVersion\n    );\n  }\n\n  /* Returns tracer */\n  protected get tracer(): Tracer {\n    return this._tracer;\n  }\n\n  /* Enable plugin */\n  public abstract enable(): void;\n\n  /* Disable plugin */\n  public abstract disable(): void;\n\n  /**\n   * Init method in which plugin should define _modules and patches for\n   * methods.\n   */\n  protected abstract init():\n    | InstrumentationModuleDefinition\n    | InstrumentationModuleDefinition[]\n    | void;\n\n  /**\n   * Execute span customization hook, if configured, and log any errors.\n   * Any semantics of the trigger and info are defined by the specific instrumentation.\n   * @param hookHandler The optional hook handler which the user has configured via instrumentation config\n   * @param triggerName The name of the trigger for executing the hook for logging purposes\n   * @param span The span to which the hook should be applied\n   * @param info The info object to be passed to the hook, with useful data the hook may use\n   */\n  protected _runSpanCustomizationHook<SpanCustomizationInfoType>(\n    hookHandler: SpanCustomizationHook<SpanCustomizationInfoType> | undefined,\n    triggerName: string,\n    span: Span,\n    info: SpanCustomizationInfoType\n  ) {\n    if (!hookHandler) {\n      return;\n    }\n\n    try {\n      hookHandler(span, info);\n    } catch (e) {\n      this._diag.error(\n        `Error running span customization hook due to exception in handler`,\n        { triggerName },\n        e\n      );\n    }\n  }\n}\n"]}