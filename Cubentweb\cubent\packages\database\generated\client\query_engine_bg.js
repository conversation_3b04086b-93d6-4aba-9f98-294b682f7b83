"use strict";var q=Object.defineProperty;var U=Object.getOwnPropertyDescriptor;var B=Object.getOwnPropertyNames;var N=Object.prototype.hasOwnProperty;var C=(n,e)=>{for(var t in e)q(n,t,{get:e[t],enumerable:!0})},$=(n,e,t,_)=>{if(e&&typeof e=="object"||typeof e=="function")for(let c of B(e))!N.call(n,c)&&c!==t&&q(n,c,{get:()=>e[c],enumerable:!(_=U(e,c))||_.enumerable});return n};var V=n=>$(q({},"__esModule",{value:!0}),n);var De={};C(De,{QueryEngine:()=>Y,__wbg_String_88810dfeb4021902:()=>Cn,__wbg_buffer_b7b08af79b0b0974:()=>Vn,__wbg_call_1084a111329e68ce:()=>ee,__wbg_call_89af060b4e1523f2:()=>de,__wbg_crypto_58f13aa23ffcb166:()=>Qn,__wbg_done_bfda7aa8f252b39f:()=>oe,__wbg_entries_7a0e06255456ebcd:()=>je,__wbg_exec_a29a4ce5544bd3be:()=>Bn,__wbg_getRandomValues_504510b5564925af:()=>Wn,__wbg_getTime_91058879093a1589:()=>fn,__wbg_get_224d16597dbbfd96:()=>ue,__wbg_get_3baa728f9d58d3f6:()=>te,__wbg_get_94990005bd6ca07c:()=>Nn,__wbg_getwithrefkey_5e6d9547403deab8:()=>Dn,__wbg_globalThis_86b222e13bdf32ed:()=>ae,__wbg_global_e5a3fe56f8be9485:()=>be,__wbg_has_4bfbc01db38743f7:()=>cn,__wbg_instanceof_ArrayBuffer_61dfc3198373c902:()=>Se,__wbg_instanceof_Promise_ae8c7ffdec83f2ae:()=>wn,__wbg_instanceof_Uint8Array_247a91427532499e:()=>Ie,__wbg_isArray_8364a5371e9737d8:()=>le,__wbg_isSafeInteger_7f1ed56200d90674:()=>we,__wbg_iterator_888179a48810a9fe:()=>xn,__wbg_keys_7840ae453e408eab:()=>ln,__wbg_length_8339fcf5d8ecd12e:()=>ye,__wbg_length_ae22078168b726f5:()=>dn,__wbg_msCrypto_abcb1295e768d1f2:()=>Yn,__wbg_new0_65387337a95cf44d:()=>sn,__wbg_new_13847c66f41dda63:()=>Un,__wbg_new_525245e2b9901204:()=>En,__wbg_new_8608a2b51a5f6737:()=>kn,__wbg_new_a220cf903aa02ca2:()=>On,__wbg_new_b85e72ed1bfd57f9:()=>nn,__wbg_new_ea1883e1e5e86686:()=>Ln,__wbg_newnoargs_76313bd6ff35d0f2:()=>ge,__wbg_newwithbyteoffsetandlength_8a2cb9ca96b27ec9:()=>zn,__wbg_newwithlength_ec548f448387c968:()=>Zn,__wbg_next_de3e9db4440638b2:()=>ie,__wbg_next_f9cb570345655b9a:()=>_e,__wbg_node_523d7bd03ef69fba:()=>Kn,__wbg_now_28a6b413aca4a96a:()=>he,__wbg_now_8ed1a4454e40ecd1:()=>bn,__wbg_now_b7a162010a9e75b4:()=>gn,__wbg_parse_52202f117ec9ecfa:()=>un,__wbg_process_5b786e71d465a513:()=>Hn,__wbg_push_37c89022f34c01ca:()=>Mn,__wbg_randomFillSync_a0d98aa11c81fe89:()=>Jn,__wbg_require_2784e593a4674877:()=>Xn,__wbg_resolve_570458cb99d56a43:()=>Fe,__wbg_self_3093d5d1f7bcb682:()=>se,__wbg_setTimeout_631fe61f31fa2fad:()=>en,__wbg_set_49185437f0ab06f8:()=>vn,__wbg_set_673dda6c73d19609:()=>qn,__wbg_set_841ac57cff3d672b:()=>Rn,__wbg_set_d1e79e2388520f18:()=>me,__wbg_set_eacc7d73fefaafdf:()=>pe,__wbg_set_wasm:()=>z,__wbg_stringify_bbf45426c92a6bf5:()=>xe,__wbg_subarray_7c2e3576afe181d1:()=>Pn,__wbg_then_876bb3c633745cc6:()=>ve,__wbg_then_95e6edc0f89b73b1:()=>Ee,__wbg_valueOf_c759749a331da0c0:()=>re,__wbg_value_6d39332ab4788d86:()=>ce,__wbg_versions_c2ab80650590b6a2:()=>Gn,__wbg_window_3bcfc4d31bc012f8:()=>fe,__wbindgen_bigint_from_i64:()=>Tn,__wbindgen_bigint_from_u64:()=>Sn,__wbindgen_bigint_get_as_i64:()=>Oe,__wbindgen_boolean_get:()=>mn,__wbindgen_cb_drop:()=>ke,__wbindgen_closure_wrapper7298:()=>Re,__wbindgen_debug_string:()=>qe,__wbindgen_error_new:()=>rn,__wbindgen_in:()=>In,__wbindgen_is_bigint:()=>yn,__wbindgen_is_function:()=>ne,__wbindgen_is_object:()=>pn,__wbindgen_is_string:()=>Fn,__wbindgen_is_undefined:()=>on,__wbindgen_jsval_eq:()=>jn,__wbindgen_jsval_loose_eq:()=>Te,__wbindgen_memory:()=>$n,__wbindgen_number_get:()=>hn,__wbindgen_number_new:()=>An,__wbindgen_object_clone_ref:()=>_n,__wbindgen_object_drop_ref:()=>an,__wbindgen_string_get:()=>Z,__wbindgen_string_new:()=>tn,__wbindgen_throw:()=>Ae,debug_panic:()=>K,getBuildTimeInfo:()=>G});module.exports=V(De);var S=()=>{};S.prototype=S;let o;function z(n){o=n}const p=new Array(128).fill(void 0);p.push(void 0,null,!0,!1);function r(n){return p[n]}let a=0,j=null;function A(){return(j===null||j.byteLength===0)&&(j=new Uint8Array(o.memory.buffer)),j}const L=typeof TextEncoder>"u"?(0,module.require)("util").TextEncoder:TextEncoder;let O=new L("utf-8");const P=typeof O.encodeInto=="function"?function(n,e){return O.encodeInto(n,e)}:function(n,e){const t=O.encode(n);return e.set(t),{read:n.length,written:t.length}};function b(n,e,t){if(t===void 0){const s=O.encode(n),w=e(s.length,1)>>>0;return A().subarray(w,w+s.length).set(s),a=s.length,w}let _=n.length,c=e(_,1)>>>0;const f=A();let u=0;for(;u<_;u++){const s=n.charCodeAt(u);if(s>127)break;f[c+u]=s}if(u!==_){u!==0&&(n=n.slice(u)),c=t(c,_,_=u+n.length*3,1)>>>0;const s=A().subarray(c+u,c+_),w=P(n,s);u+=w.written,c=t(c,_,u,1)>>>0}return a=u,c}function x(n){return n==null}let T=null;function d(){return(T===null||T.buffer.detached===!0||T.buffer.detached===void 0&&T.buffer!==o.memory.buffer)&&(T=new DataView(o.memory.buffer)),T}const W=typeof TextDecoder>"u"?(0,module.require)("util").TextDecoder:TextDecoder;let E=new W("utf-8",{ignoreBOM:!0,fatal:!0});E.decode();function m(n,e){return n=n>>>0,E.decode(A().subarray(n,n+e))}let I=p.length;function i(n){I===p.length&&p.push(p.length+1);const e=I;return I=p[e],p[e]=n,e}function J(n){n<132||(p[n]=I,I=n)}function g(n){const e=r(n);return J(n),e}function k(n){const e=typeof n;if(e=="number"||e=="boolean"||n==null)return`${n}`;if(e=="string")return`"${n}"`;if(e=="symbol"){const c=n.description;return c==null?"Symbol":`Symbol(${c})`}if(e=="function"){const c=n.name;return typeof c=="string"&&c.length>0?`Function(${c})`:"Function"}if(Array.isArray(n)){const c=n.length;let f="[";c>0&&(f+=k(n[0]));for(let u=1;u<c;u++)f+=", "+k(n[u]);return f+="]",f}const t=/\[object ([^\]]+)\]/.exec(toString.call(n));let _;if(t.length>1)_=t[1];else return toString.call(n);if(_=="Object")try{return"Object("+JSON.stringify(n)+")"}catch{return"Object"}return n instanceof Error?`${n.name}: ${n.message}
${n.stack}`:_}const v=typeof FinalizationRegistry>"u"?{register:()=>{},unregister:()=>{}}:new FinalizationRegistry(n=>{o.__wbindgen_export_2.get(n.dtor)(n.a,n.b)});function Q(n,e,t,_){const c={a:n,b:e,cnt:1,dtor:t},f=(...u)=>{c.cnt++;const s=c.a;c.a=0;try{return _(s,c.b,...u)}finally{--c.cnt===0?(o.__wbindgen_export_2.get(c.dtor)(s,c.b),v.unregister(c)):c.a=s}};return f.original=c,v.register(f,c,c),f}function H(n,e,t){o._dyn_core__ops__function__FnMut__A____Output___R_as_wasm_bindgen__closure__WasmClosure___describe__invoke__h92ff5ab9e3740637(n,e,i(t))}function G(){const n=o.getBuildTimeInfo();return g(n)}function K(n){try{const f=o.__wbindgen_add_to_stack_pointer(-16);var e=x(n)?0:b(n,o.__wbindgen_malloc,o.__wbindgen_realloc),t=a;o.debug_panic(f,e,t);var _=d().getInt32(f+4*0,!0),c=d().getInt32(f+4*1,!0);if(c)throw g(_)}finally{o.__wbindgen_add_to_stack_pointer(16)}}function l(n,e){try{return n.apply(this,e)}catch(t){o.__wbindgen_exn_store(i(t))}}function X(n,e,t,_){o.wasm_bindgen__convert__closures__invoke2_mut__h2dd59201d446cc9a(n,e,i(t),i(_))}const F=typeof FinalizationRegistry>"u"?{register:()=>{},unregister:()=>{}}:new FinalizationRegistry(n=>o.__wbg_queryengine_free(n>>>0,1));class Y{__destroy_into_raw(){const e=this.__wbg_ptr;return this.__wbg_ptr=0,F.unregister(this),e}free(){const e=this.__destroy_into_raw();o.__wbg_queryengine_free(e,0)}constructor(e,t,_){try{const s=o.__wbindgen_add_to_stack_pointer(-16);o.queryengine_new(s,i(e),i(t),i(_));var c=d().getInt32(s+4*0,!0),f=d().getInt32(s+4*1,!0),u=d().getInt32(s+4*2,!0);if(u)throw g(f);return this.__wbg_ptr=c>>>0,F.register(this,this.__wbg_ptr,this),this}finally{o.__wbindgen_add_to_stack_pointer(16)}}connect(e,t){const _=b(e,o.__wbindgen_malloc,o.__wbindgen_realloc),c=a,f=b(t,o.__wbindgen_malloc,o.__wbindgen_realloc),u=a,s=o.queryengine_connect(this.__wbg_ptr,_,c,f,u);return g(s)}disconnect(e,t){const _=b(e,o.__wbindgen_malloc,o.__wbindgen_realloc),c=a,f=b(t,o.__wbindgen_malloc,o.__wbindgen_realloc),u=a,s=o.queryengine_disconnect(this.__wbg_ptr,_,c,f,u);return g(s)}query(e,t,_,c){const f=b(e,o.__wbindgen_malloc,o.__wbindgen_realloc),u=a,s=b(t,o.__wbindgen_malloc,o.__wbindgen_realloc),w=a;var y=x(_)?0:b(_,o.__wbindgen_malloc,o.__wbindgen_realloc),h=a;const R=b(c,o.__wbindgen_malloc,o.__wbindgen_realloc),D=a,M=o.queryengine_query(this.__wbg_ptr,f,u,s,w,y,h,R,D);return g(M)}startTransaction(e,t,_){const c=b(e,o.__wbindgen_malloc,o.__wbindgen_realloc),f=a,u=b(t,o.__wbindgen_malloc,o.__wbindgen_realloc),s=a,w=b(_,o.__wbindgen_malloc,o.__wbindgen_realloc),y=a,h=o.queryengine_startTransaction(this.__wbg_ptr,c,f,u,s,w,y);return g(h)}commitTransaction(e,t,_){const c=b(e,o.__wbindgen_malloc,o.__wbindgen_realloc),f=a,u=b(t,o.__wbindgen_malloc,o.__wbindgen_realloc),s=a,w=b(_,o.__wbindgen_malloc,o.__wbindgen_realloc),y=a,h=o.queryengine_commitTransaction(this.__wbg_ptr,c,f,u,s,w,y);return g(h)}rollbackTransaction(e,t,_){const c=b(e,o.__wbindgen_malloc,o.__wbindgen_realloc),f=a,u=b(t,o.__wbindgen_malloc,o.__wbindgen_realloc),s=a,w=b(_,o.__wbindgen_malloc,o.__wbindgen_realloc),y=a,h=o.queryengine_rollbackTransaction(this.__wbg_ptr,c,f,u,s,w,y);return g(h)}metrics(e){const t=b(e,o.__wbindgen_malloc,o.__wbindgen_realloc),_=a,c=o.queryengine_metrics(this.__wbg_ptr,t,_);return g(c)}trace(e){const t=b(e,o.__wbindgen_malloc,o.__wbindgen_realloc),_=a,c=o.queryengine_trace(this.__wbg_ptr,t,_);return g(c)}}function Z(n,e){const t=r(e),_=typeof t=="string"?t:void 0;var c=x(_)?0:b(_,o.__wbindgen_malloc,o.__wbindgen_realloc),f=a;d().setInt32(n+4*1,f,!0),d().setInt32(n+4*0,c,!0)}function nn(n,e){try{var t={a:n,b:e},_=(f,u)=>{const s=t.a;t.a=0;try{return X(s,t.b,f,u)}finally{t.a=s}};const c=new Promise(_);return i(c)}finally{t.a=t.b=0}}function en(n,e){return setTimeout(r(n),e>>>0)}function tn(n,e){const t=m(n,e);return i(t)}function rn(n,e){const t=new Error(m(n,e));return i(t)}function _n(n){const e=r(n);return i(e)}function on(n){return r(n)===void 0}function cn(){return l(function(n,e){return Reflect.has(r(n),r(e))},arguments)}function un(){return l(function(n,e){const t=JSON.parse(m(n,e));return i(t)},arguments)}function sn(){return i(new Date)}function fn(n){return r(n).getTime()}function an(n){g(n)}function bn(n){return r(n).now()}function gn(){return Date.now()}function ln(n){const e=Object.keys(r(n));return i(e)}function dn(n){return r(n).length}function wn(n){let e;try{e=r(n)instanceof Promise}catch{e=!1}return e}function pn(n){const e=r(n);return typeof e=="object"&&e!==null}function xn(){return i(Symbol.iterator)}function mn(n){const e=r(n);return typeof e=="boolean"?e?1:0:2}function yn(n){return typeof r(n)=="bigint"}function hn(n,e){const t=r(e),_=typeof t=="number"?t:void 0;d().setFloat64(n+8*1,x(_)?0:_,!0),d().setInt32(n+4*0,!x(_),!0)}function Tn(n){return i(n)}function In(n,e){return r(n)in r(e)}function Sn(n){const e=BigInt.asUintN(64,n);return i(e)}function jn(n,e){return r(n)===r(e)}function An(n){return i(n)}function On(){const n=new Array;return i(n)}function qn(n,e,t){r(n)[e>>>0]=g(t)}function kn(){return i(new Map)}function En(){const n=new Object;return i(n)}function vn(n,e,t){const _=r(n).set(r(e),r(t));return i(_)}function Fn(n){return typeof r(n)=="string"}function Rn(n,e,t){r(n)[g(e)]=g(t)}function Dn(n,e){const t=r(n)[r(e)];return i(t)}function Mn(n,e){return r(n).push(r(e))}function Un(n,e,t,_){const c=new RegExp(m(n,e),m(t,_));return i(c)}function Bn(n,e,t){const _=r(n).exec(m(e,t));return x(_)?0:i(_)}function Nn(){return l(function(n,e){const t=r(n)[g(e)];return i(t)},arguments)}function Cn(n,e){const t=String(r(e)),_=b(t,o.__wbindgen_malloc,o.__wbindgen_realloc),c=a;d().setInt32(n+4*1,c,!0),d().setInt32(n+4*0,_,!0)}function $n(){const n=o.memory;return i(n)}function Vn(n){const e=r(n).buffer;return i(e)}function zn(n,e,t){const _=new Uint8Array(r(n),e>>>0,t>>>0);return i(_)}function Ln(n){const e=new Uint8Array(r(n));return i(e)}function Pn(n,e,t){const _=r(n).subarray(e>>>0,t>>>0);return i(_)}function Wn(){return l(function(n,e){r(n).getRandomValues(r(e))},arguments)}function Jn(){return l(function(n,e){r(n).randomFillSync(g(e))},arguments)}function Qn(n){const e=r(n).crypto;return i(e)}function Hn(n){const e=r(n).process;return i(e)}function Gn(n){const e=r(n).versions;return i(e)}function Kn(n){const e=r(n).node;return i(e)}function Xn(){return l(function(){const n=module.require;return i(n)},arguments)}function Yn(n){const e=r(n).msCrypto;return i(e)}function Zn(n){const e=new Uint8Array(n>>>0);return i(e)}function ne(n){return typeof r(n)=="function"}function ee(){return l(function(n,e){const t=r(n).call(r(e));return i(t)},arguments)}function te(n,e){const t=r(n)[e>>>0];return i(t)}function re(n){return r(n).valueOf()}function _e(){return l(function(n){const e=r(n).next();return i(e)},arguments)}function oe(n){return r(n).done}function ce(n){const e=r(n).value;return i(e)}function ie(n){const e=r(n).next;return i(e)}function ue(){return l(function(n,e){const t=Reflect.get(r(n),r(e));return i(t)},arguments)}function se(){return l(function(){const n=self.self;return i(n)},arguments)}function fe(){return l(function(){const n=window.window;return i(n)},arguments)}function ae(){return l(function(){const n=globalThis.globalThis;return i(n)},arguments)}function be(){return l(function(){const n=global.global;return i(n)},arguments)}function ge(n,e){const t=new S(m(n,e));return i(t)}function le(n){return Array.isArray(r(n))}function de(){return l(function(n,e,t){const _=r(n).call(r(e),r(t));return i(_)},arguments)}function we(n){return Number.isSafeInteger(r(n))}function pe(){return l(function(n,e,t){return Reflect.set(r(n),r(e),r(t))},arguments)}function xe(){return l(function(n){const e=JSON.stringify(r(n));return i(e)},arguments)}function me(n,e,t){r(n).set(r(e),t>>>0)}function ye(n){return r(n).length}function he(){return l(function(){return Date.now()},arguments)}function Te(n,e){return r(n)==r(e)}function Ie(n){let e;try{e=r(n)instanceof Uint8Array}catch{e=!1}return e}function Se(n){let e;try{e=r(n)instanceof ArrayBuffer}catch{e=!1}return e}function je(n){const e=Object.entries(r(n));return i(e)}function Ae(n,e){throw new Error(m(n,e))}function Oe(n,e){const t=r(e),_=typeof t=="bigint"?t:void 0;d().setBigInt64(n+8*1,x(_)?BigInt(0):_,!0),d().setInt32(n+4*0,!x(_),!0)}function qe(n,e){const t=k(r(e)),_=b(t,o.__wbindgen_malloc,o.__wbindgen_realloc),c=a;d().setInt32(n+4*1,c,!0),d().setInt32(n+4*0,_,!0)}function ke(n){const e=g(n).original;return e.cnt--==1?(e.a=0,!0):!1}function Ee(n,e){const t=r(n).then(r(e));return i(t)}function ve(n,e,t){const _=r(n).then(r(e),r(t));return i(_)}function Fe(n){const e=Promise.resolve(r(n));return i(e)}function Re(n,e,t){const _=Q(n,e,552,H);return i(_)}0&&(module.exports={QueryEngine,__wbg_String_88810dfeb4021902,__wbg_buffer_b7b08af79b0b0974,__wbg_call_1084a111329e68ce,__wbg_call_89af060b4e1523f2,__wbg_crypto_58f13aa23ffcb166,__wbg_done_bfda7aa8f252b39f,__wbg_entries_7a0e06255456ebcd,__wbg_exec_a29a4ce5544bd3be,__wbg_getRandomValues_504510b5564925af,__wbg_getTime_91058879093a1589,__wbg_get_224d16597dbbfd96,__wbg_get_3baa728f9d58d3f6,__wbg_get_94990005bd6ca07c,__wbg_getwithrefkey_5e6d9547403deab8,__wbg_globalThis_86b222e13bdf32ed,__wbg_global_e5a3fe56f8be9485,__wbg_has_4bfbc01db38743f7,__wbg_instanceof_ArrayBuffer_61dfc3198373c902,__wbg_instanceof_Promise_ae8c7ffdec83f2ae,__wbg_instanceof_Uint8Array_247a91427532499e,__wbg_isArray_8364a5371e9737d8,__wbg_isSafeInteger_7f1ed56200d90674,__wbg_iterator_888179a48810a9fe,__wbg_keys_7840ae453e408eab,__wbg_length_8339fcf5d8ecd12e,__wbg_length_ae22078168b726f5,__wbg_msCrypto_abcb1295e768d1f2,__wbg_new0_65387337a95cf44d,__wbg_new_13847c66f41dda63,__wbg_new_525245e2b9901204,__wbg_new_8608a2b51a5f6737,__wbg_new_a220cf903aa02ca2,__wbg_new_b85e72ed1bfd57f9,__wbg_new_ea1883e1e5e86686,__wbg_newnoargs_76313bd6ff35d0f2,__wbg_newwithbyteoffsetandlength_8a2cb9ca96b27ec9,__wbg_newwithlength_ec548f448387c968,__wbg_next_de3e9db4440638b2,__wbg_next_f9cb570345655b9a,__wbg_node_523d7bd03ef69fba,__wbg_now_28a6b413aca4a96a,__wbg_now_8ed1a4454e40ecd1,__wbg_now_b7a162010a9e75b4,__wbg_parse_52202f117ec9ecfa,__wbg_process_5b786e71d465a513,__wbg_push_37c89022f34c01ca,__wbg_randomFillSync_a0d98aa11c81fe89,__wbg_require_2784e593a4674877,__wbg_resolve_570458cb99d56a43,__wbg_self_3093d5d1f7bcb682,__wbg_setTimeout_631fe61f31fa2fad,__wbg_set_49185437f0ab06f8,__wbg_set_673dda6c73d19609,__wbg_set_841ac57cff3d672b,__wbg_set_d1e79e2388520f18,__wbg_set_eacc7d73fefaafdf,__wbg_set_wasm,__wbg_stringify_bbf45426c92a6bf5,__wbg_subarray_7c2e3576afe181d1,__wbg_then_876bb3c633745cc6,__wbg_then_95e6edc0f89b73b1,__wbg_valueOf_c759749a331da0c0,__wbg_value_6d39332ab4788d86,__wbg_versions_c2ab80650590b6a2,__wbg_window_3bcfc4d31bc012f8,__wbindgen_bigint_from_i64,__wbindgen_bigint_from_u64,__wbindgen_bigint_get_as_i64,__wbindgen_boolean_get,__wbindgen_cb_drop,__wbindgen_closure_wrapper7298,__wbindgen_debug_string,__wbindgen_error_new,__wbindgen_in,__wbindgen_is_bigint,__wbindgen_is_function,__wbindgen_is_object,__wbindgen_is_string,__wbindgen_is_undefined,__wbindgen_jsval_eq,__wbindgen_jsval_loose_eq,__wbindgen_memory,__wbindgen_number_get,__wbindgen_number_new,__wbindgen_object_clone_ref,__wbindgen_object_drop_ref,__wbindgen_string_get,__wbindgen_string_new,__wbindgen_throw,debug_panic,getBuildTimeInfo});
