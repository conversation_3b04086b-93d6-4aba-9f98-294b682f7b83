{"version": 3, "file": "internal-types.js", "sourceRoot": "", "sources": ["../../src/internal-types.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AA2CU,QAAA,mBAAmB,GAAG,MAAM,CACvC,oDAAoD,CACrD,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport type * as pgTypes from 'pg';\nimport type * as pgPoolTypes from 'pg-pool';\n\nexport type PostgresCallback = (err: Error, res: object) => unknown;\n\n// NB: this type describes the shape of a parsed, normalized form of the\n// connection information that's stored inside each pg.Client instance. It's\n// _not_ the same as the ConnectionConfig type exported from `@types/pg`. That\n// type defines how data must be _passed in_ when creating a new `pg.Client`,\n// which doesn't necessarily match the normalized internal form. E.g., a user\n// can call `new Client({ connectionString: '...' }), but `connectionString`\n// will never show up in the type below, because only the extracted host, port,\n// etc. are recorded in this normalized config. The keys listed below are also\n// incomplete, which is fine because the type is internal and these keys are the\n// only ones our code is reading. See https://github.com/brianc/node-postgres/blob/fde5ec586e49258dfc4a2fcd861fcdecb4794fc3/lib/client.js#L25\nexport interface PgParsedConnectionParams {\n  database?: string;\n  host?: string;\n  port?: number;\n  user?: string;\n}\n\nexport interface PgClientExtended extends pgTypes.Client {\n  connectionParameters: PgParsedConnectionParams;\n}\n\nexport type PgPoolCallback = (\n  err: Error,\n  client: any,\n  done: (release?: any) => void\n) => void;\n\nexport interface PgPoolOptionsParams {\n  database: string;\n  host: string;\n  port: number;\n  user: string;\n  idleTimeoutMillis: number; // the minimum amount of time that an object may sit idle in the pool before it is eligible for eviction due to idle time\n  maxClient: number; // maximum size of the pool\n}\n\nexport const EVENT_LISTENERS_SET = Symbol(\n  'opentelemetry.instrumentation.pg.eventListenersSet'\n);\n\nexport interface PgPoolExtended extends pgPoolTypes<pgTypes.Client> {\n  options: PgPoolOptionsParams;\n  [EVENT_LISTENERS_SET]?: boolean; // flag to identify if the event listeners for instrumentation have been set\n}\n\nexport type PgClientConnect = (callback?: Function) => Promise<void> | void;\n"]}