#!/usr/bin/env node
"use strict";var Pl=Object.create;var ii=Object.defineProperty;var Tl=Object.getOwnPropertyDescriptor;var Ll=Object.getOwnPropertyNames;var Nl=Object.getPrototypeOf,Ml=Object.prototype.hasOwnProperty;var Il=(r,e)=>()=>(r&&(e=r(r=0)),e);var y=(r,e)=>()=>(e||r((e={exports:{}}).exports,e),e.exports),jl=(r,e)=>{for(var t in e)ii(r,t,{get:e[t],enumerable:!0})},ql=(r,e,t,s)=>{if(e&&typeof e=="object"||typeof e=="function")for(let i of Ll(e))!Ml.call(r,i)&&i!==t&&ii(r,i,{get:()=>e[i],enumerable:!(s=Tl(e,i))||s.enumerable});return r};var C=(r,e,t)=>(t=r!=null?Pl(Nl(r)):{},ql(e||!r||!r.__esModule?ii(t,"default",{value:r,enumerable:!0}):t,r));var u=Il(()=>{});var Do=y((Gm,Se)=>{u();function ni(r){return Se.exports=ni=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Se.exports.__esModule=!0,Se.exports.default=Se.exports,ni(r)}Se.exports=ni,Se.exports.__esModule=!0,Se.exports.default=Se.exports});var yo=y((Hm,ke)=>{u();var go=Do().default;function Eo(){"use strict";ke.exports=Eo=function(){return e},ke.exports.__esModule=!0,ke.exports.default=ke.exports;var r,e={},t=Object.prototype,s=t.hasOwnProperty,i=Object.defineProperty||function(g,m,D){g[m]=D.value},n=typeof Symbol=="function"?Symbol:{},o=n.iterator||"@@iterator",a=n.asyncIterator||"@@asyncIterator",l=n.toStringTag||"@@toStringTag";function c(g,m,D){return Object.defineProperty(g,m,{value:D,enumerable:!0,configurable:!0,writable:!0}),g[m]}try{c({},"")}catch{c=function(D,A,b){return D[A]=b}}function h(g,m,D,A){var b=m&&m.prototype instanceof x?m:x,F=Object.create(b.prototype),T=new ri(A||[]);return i(F,"_invoke",{value:xl(g,D,T)}),F}function d(g,m,D){try{return{type:"normal",arg:g.call(m,D)}}catch(A){return{type:"throw",arg:A}}}e.wrap=h;var p="suspendedStart",E="suspendedYield",w="executing",P="completed",S={};function x(){}function ft(){}function De(){}var Qs={};c(Qs,o,function(){return this});var ei=Object.getPrototypeOf,Mr=ei&&ei(ei(si([])));Mr&&Mr!==t&&s.call(Mr,o)&&(Qs=Mr);var Lt=De.prototype=x.prototype=Object.create(Qs);function po(g){["next","throw","return"].forEach(function(m){c(g,m,function(D){return this._invoke(m,D)})})}function Ir(g,m){function D(b,F,T,G){var H=d(g[b],g,F);if(H.type!=="throw"){var pt=H.arg,ze=pt.value;return ze&&go(ze)=="object"&&s.call(ze,"__await")?m.resolve(ze.__await).then(function(mt){D("next",mt,T,G)},function(mt){D("throw",mt,T,G)}):m.resolve(ze).then(function(mt){pt.value=mt,T(pt)},function(mt){return D("throw",mt,T,G)})}G(H.arg)}var A;i(this,"_invoke",{value:function(F,T){function G(){return new m(function(H,pt){D(F,T,H,pt)})}return A=A?A.then(G,G):G()}})}function xl(g,m,D){var A=p;return function(b,F){if(A===w)throw new Error("Generator is already running");if(A===P){if(b==="throw")throw F;return{value:r,done:!0}}for(D.method=b,D.arg=F;;){var T=D.delegate;if(T){var G=mo(T,D);if(G){if(G===S)continue;return G}}if(D.method==="next")D.sent=D._sent=D.arg;else if(D.method==="throw"){if(A===p)throw A=P,D.arg;D.dispatchException(D.arg)}else D.method==="return"&&D.abrupt("return",D.arg);A=w;var H=d(g,m,D);if(H.type==="normal"){if(A=D.done?P:E,H.arg===S)continue;return{value:H.arg,done:D.done}}H.type==="throw"&&(A=P,D.method="throw",D.arg=H.arg)}}}function mo(g,m){var D=m.method,A=g.iterator[D];if(A===r)return m.delegate=null,D==="throw"&&g.iterator.return&&(m.method="return",m.arg=r,mo(g,m),m.method==="throw")||D!=="return"&&(m.method="throw",m.arg=new TypeError("The iterator does not provide a '"+D+"' method")),S;var b=d(A,g.iterator,m.arg);if(b.type==="throw")return m.method="throw",m.arg=b.arg,m.delegate=null,S;var F=b.arg;return F?F.done?(m[g.resultName]=F.value,m.next=g.nextLoc,m.method!=="return"&&(m.method="next",m.arg=r),m.delegate=null,S):F:(m.method="throw",m.arg=new TypeError("iterator result is not an object"),m.delegate=null,S)}function Ol(g){var m={tryLoc:g[0]};1 in g&&(m.catchLoc=g[1]),2 in g&&(m.finallyLoc=g[2],m.afterLoc=g[3]),this.tryEntries.push(m)}function ti(g){var m=g.completion||{};m.type="normal",delete m.arg,g.completion=m}function ri(g){this.tryEntries=[{tryLoc:"root"}],g.forEach(Ol,this),this.reset(!0)}function si(g){if(g||g===""){var m=g[o];if(m)return m.call(g);if(typeof g.next=="function")return g;if(!isNaN(g.length)){var D=-1,A=function b(){for(;++D<g.length;)if(s.call(g,D))return b.value=g[D],b.done=!1,b;return b.value=r,b.done=!0,b};return A.next=A}}throw new TypeError(go(g)+" is not iterable")}return ft.prototype=De,i(Lt,"constructor",{value:De,configurable:!0}),i(De,"constructor",{value:ft,configurable:!0}),ft.displayName=c(De,l,"GeneratorFunction"),e.isGeneratorFunction=function(g){var m=typeof g=="function"&&g.constructor;return!!m&&(m===ft||(m.displayName||m.name)==="GeneratorFunction")},e.mark=function(g){return Object.setPrototypeOf?Object.setPrototypeOf(g,De):(g.__proto__=De,c(g,l,"GeneratorFunction")),g.prototype=Object.create(Lt),g},e.awrap=function(g){return{__await:g}},po(Ir.prototype),c(Ir.prototype,a,function(){return this}),e.AsyncIterator=Ir,e.async=function(g,m,D,A,b){b===void 0&&(b=Promise);var F=new Ir(h(g,m,D,A),b);return e.isGeneratorFunction(m)?F:F.next().then(function(T){return T.done?T.value:F.next()})},po(Lt),c(Lt,l,"Generator"),c(Lt,o,function(){return this}),c(Lt,"toString",function(){return"[object Generator]"}),e.keys=function(g){var m=Object(g),D=[];for(var A in m)D.push(A);return D.reverse(),function b(){for(;D.length;){var F=D.pop();if(F in m)return b.value=F,b.done=!1,b}return b.done=!0,b}},e.values=si,ri.prototype={constructor:ri,reset:function(m){if(this.prev=0,this.next=0,this.sent=this._sent=r,this.done=!1,this.delegate=null,this.method="next",this.arg=r,this.tryEntries.forEach(ti),!m)for(var D in this)D.charAt(0)==="t"&&s.call(this,D)&&!isNaN(+D.slice(1))&&(this[D]=r)},stop:function(){this.done=!0;var m=this.tryEntries[0].completion;if(m.type==="throw")throw m.arg;return this.rval},dispatchException:function(m){if(this.done)throw m;var D=this;function A(pt,ze){return T.type="throw",T.arg=m,D.next=pt,ze&&(D.method="next",D.arg=r),!!ze}for(var b=this.tryEntries.length-1;b>=0;--b){var F=this.tryEntries[b],T=F.completion;if(F.tryLoc==="root")return A("end");if(F.tryLoc<=this.prev){var G=s.call(F,"catchLoc"),H=s.call(F,"finallyLoc");if(G&&H){if(this.prev<F.catchLoc)return A(F.catchLoc,!0);if(this.prev<F.finallyLoc)return A(F.finallyLoc)}else if(G){if(this.prev<F.catchLoc)return A(F.catchLoc,!0)}else{if(!H)throw new Error("try statement without catch or finally");if(this.prev<F.finallyLoc)return A(F.finallyLoc)}}}},abrupt:function(m,D){for(var A=this.tryEntries.length-1;A>=0;--A){var b=this.tryEntries[A];if(b.tryLoc<=this.prev&&s.call(b,"finallyLoc")&&this.prev<b.finallyLoc){var F=b;break}}F&&(m==="break"||m==="continue")&&F.tryLoc<=D&&D<=F.finallyLoc&&(F=null);var T=F?F.completion:{};return T.type=m,T.arg=D,F?(this.method="next",this.next=F.finallyLoc,S):this.complete(T)},complete:function(m,D){if(m.type==="throw")throw m.arg;return m.type==="break"||m.type==="continue"?this.next=m.arg:m.type==="return"?(this.rval=this.arg=m.arg,this.method="return",this.next="end"):m.type==="normal"&&D&&(this.next=D),S},finish:function(m){for(var D=this.tryEntries.length-1;D>=0;--D){var A=this.tryEntries[D];if(A.finallyLoc===m)return this.complete(A.completion,A.afterLoc),ti(A),S}},catch:function(m){for(var D=this.tryEntries.length-1;D>=0;--D){var A=this.tryEntries[D];if(A.tryLoc===m){var b=A.completion;if(b.type==="throw"){var F=b.arg;ti(A)}return F}}throw new Error("illegal catch attempt")},delegateYield:function(m,D,A){return this.delegate={iterator:si(m),resultName:D,nextLoc:A},this.method==="next"&&(this.arg=r),S}},e}ke.exports=Eo,ke.exports.__esModule=!0,ke.exports.default=ke.exports});var Co=y((Jm,Ao)=>{u();var jr=yo()();Ao.exports=jr;try{regeneratorRuntime=jr}catch{typeof globalThis=="object"?globalThis.regeneratorRuntime=jr:Function("r","regeneratorRuntime = r")(jr)}});var Fo=y((dd,fi)=>{"use strict";u();var wo=(r,...e)=>new Promise(t=>{t(r(...e))});fi.exports=wo;fi.exports.default=wo});var So=y((Dd,pi)=>{"use strict";u();var $l=Fo(),bo=r=>{if(!((Number.isInteger(r)||r===1/0)&&r>0))return Promise.reject(new TypeError("Expected `concurrency` to be a number from 1 and up"));let e=[],t=0,s=()=>{t--,e.length>0&&e.shift()()},i=(a,l,...c)=>{t++;let h=$l(a,...c);l(h),h.then(s,s)},n=(a,l,...c)=>{t<r?i(a,l,...c):e.push(i.bind(null,a,l,...c))},o=(a,...l)=>new Promise(c=>n(a,c,...l));return Object.defineProperties(o,{activeCount:{get:()=>t},pendingCount:{get:()=>e.length},clearQueue:{value:()=>{e.length=0}}}),o};pi.exports=bo;pi.exports.default=bo});var vo=y((gd,mi)=>{"use strict";u();var ko=So(),qr=class extends Error{constructor(e){super(),this.value=e}},Ul=async(r,e)=>e(await r),Wl=async r=>{let e=await Promise.all(r);if(e[1]===!0)throw new qr(e[0]);return!1},_o=async(r,e,t)=>{t={concurrency:1/0,preserveOrder:!0,...t};let s=ko(t.concurrency),i=[...r].map(o=>[o,s(Ul,o,e)]),n=ko(t.preserveOrder?1:1/0);try{await Promise.all(i.map(o=>n(Wl,o)))}catch(o){if(o instanceof qr)return o.value;throw o}};mi.exports=_o;mi.exports.default=_o});var To=y((Ed,di)=>{"use strict";u();var Ro=require("path"),$r=require("fs"),{promisify:Bo}=require("util"),zl=vo(),Gl=Bo($r.stat),Hl=Bo($r.lstat),xo={directory:"isDirectory",file:"isFile"};function Oo({type:r}){if(!(r in xo))throw new Error(`Invalid type specified: ${r}`)}var Po=(r,e)=>r===void 0||e[xo[r]]();di.exports=async(r,e)=>{e={cwd:process.cwd(),type:"file",allowSymlinks:!0,...e},Oo(e);let t=e.allowSymlinks?Gl:Hl;return zl(r,async s=>{try{let i=await t(Ro.resolve(e.cwd,s));return Po(e.type,i)}catch{return!1}},e)};di.exports.sync=(r,e)=>{e={cwd:process.cwd(),allowSymlinks:!0,type:"file",...e},Oo(e);let t=e.allowSymlinks?$r.statSync:$r.lstatSync;for(let s of r)try{let i=t(Ro.resolve(e.cwd,s));if(Po(e.type,i))return s}catch{}}});var No=y((yd,Di)=>{"use strict";u();var Lo=require("fs"),{promisify:Jl}=require("util"),Vl=Jl(Lo.access);Di.exports=async r=>{try{return await Vl(r),!0}catch{return!1}};Di.exports.sync=r=>{try{return Lo.accessSync(r),!0}catch{return!1}}});var Io=y((Ad,Mt)=>{"use strict";u();var Ge=require("path"),Ur=To(),Mo=No(),gi=Symbol("findUp.stop");Mt.exports=async(r,e={})=>{let t=Ge.resolve(e.cwd||""),{root:s}=Ge.parse(t),i=[].concat(r),n=async o=>{if(typeof r!="function")return Ur(i,o);let a=await r(o.cwd);return typeof a=="string"?Ur([a],o):a};for(;;){let o=await n({...e,cwd:t});if(o===gi)return;if(o)return Ge.resolve(t,o);if(t===s)return;t=Ge.dirname(t)}};Mt.exports.sync=(r,e={})=>{let t=Ge.resolve(e.cwd||""),{root:s}=Ge.parse(t),i=[].concat(r),n=o=>{if(typeof r!="function")return Ur.sync(i,o);let a=r(o.cwd);return typeof a=="string"?Ur.sync([a],o):a};for(;;){let o=n({...e,cwd:t});if(o===gi)return;if(o)return Ge.resolve(t,o);if(t===s)return;t=Ge.dirname(t)}};Mt.exports.exists=Mo;Mt.exports.sync.exists=Mo.sync;Mt.exports.stop=gi});var jt=y((zd,Xo)=>{"use strict";u();var Zo=new Map([["C","cwd"],["f","file"],["z","gzip"],["P","preservePaths"],["U","unlink"],["strip-components","strip"],["stripComponents","strip"],["keep-newer","newer"],["keepNewer","newer"],["keep-newer-files","newer"],["keepNewerFiles","newer"],["k","keep"],["keep-existing","keep"],["keepExisting","keep"],["m","noMtime"],["no-mtime","noMtime"],["p","preserveOwner"],["L","follow"],["h","follow"]]);Xo.exports=r=>r?Object.keys(r).map(e=>[Zo.has(e)?Zo.get(e):e,r[e]]).reduce((e,t)=>(e[t[0]]=t[1],e),Object.create(null)):{}});var Yr=y((Gd,uu)=>{"use strict";u();var Qo=typeof process=="object"&&process?process:{stdout:null,stderr:null},yh=require("events"),eu=require("stream"),tu=require("string_decoder").StringDecoder,ve=Symbol("EOF"),Re=Symbol("maybeEmitEnd"),Je=Symbol("emittedEnd"),zr=Symbol("emittingEnd"),dr=Symbol("emittedError"),Gr=Symbol("closed"),ru=Symbol("read"),Hr=Symbol("flush"),su=Symbol("flushChunk"),V=Symbol("encoding"),Be=Symbol("decoder"),Jr=Symbol("flowing"),Dr=Symbol("paused"),qt=Symbol("resume"),L=Symbol("buffer"),Ee=Symbol("pipes"),I=Symbol("bufferLength"),Ci=Symbol("bufferPush"),wi=Symbol("bufferShift"),q=Symbol("objectMode"),$=Symbol("destroyed"),Fi=Symbol("emitData"),iu=Symbol("emitEnd"),bi=Symbol("emitEnd2"),xe=Symbol("async"),gr=r=>Promise.resolve().then(r),nu=global._MP_NO_ITERATOR_SYMBOLS_!=="1",Ah=nu&&Symbol.asyncIterator||Symbol("asyncIterator not implemented"),Ch=nu&&Symbol.iterator||Symbol("iterator not implemented"),wh=r=>r==="end"||r==="finish"||r==="prefinish",Fh=r=>r instanceof ArrayBuffer||typeof r=="object"&&r.constructor&&r.constructor.name==="ArrayBuffer"&&r.byteLength>=0,bh=r=>!Buffer.isBuffer(r)&&ArrayBuffer.isView(r),Vr=class{constructor(e,t,s){this.src=e,this.dest=t,this.opts=s,this.ondrain=()=>e[qt](),t.on("drain",this.ondrain)}unpipe(){this.dest.removeListener("drain",this.ondrain)}proxyErrors(){}end(){this.unpipe(),this.opts.end&&this.dest.end()}},Si=class extends Vr{unpipe(){this.src.removeListener("error",this.proxyErrors),super.unpipe()}constructor(e,t,s){super(e,t,s),this.proxyErrors=i=>t.emit("error",i),e.on("error",this.proxyErrors)}};uu.exports=class ou extends eu{constructor(e){super(),this[Jr]=!1,this[Dr]=!1,this[Ee]=[],this[L]=[],this[q]=e&&e.objectMode||!1,this[q]?this[V]=null:this[V]=e&&e.encoding||null,this[V]==="buffer"&&(this[V]=null),this[xe]=e&&!!e.async||!1,this[Be]=this[V]?new tu(this[V]):null,this[ve]=!1,this[Je]=!1,this[zr]=!1,this[Gr]=!1,this[dr]=null,this.writable=!0,this.readable=!0,this[I]=0,this[$]=!1,e&&e.debugExposeBuffer===!0&&Object.defineProperty(this,"buffer",{get:()=>this[L]}),e&&e.debugExposePipes===!0&&Object.defineProperty(this,"pipes",{get:()=>this[Ee]})}get bufferLength(){return this[I]}get encoding(){return this[V]}set encoding(e){if(this[q])throw new Error("cannot set encoding in objectMode");if(this[V]&&e!==this[V]&&(this[Be]&&this[Be].lastNeed||this[I]))throw new Error("cannot change encoding");this[V]!==e&&(this[Be]=e?new tu(e):null,this[L].length&&(this[L]=this[L].map(t=>this[Be].write(t)))),this[V]=e}setEncoding(e){this.encoding=e}get objectMode(){return this[q]}set objectMode(e){this[q]=this[q]||!!e}get async(){return this[xe]}set async(e){this[xe]=this[xe]||!!e}write(e,t,s){if(this[ve])throw new Error("write after end");if(this[$])return this.emit("error",Object.assign(new Error("Cannot call write after a stream was destroyed"),{code:"ERR_STREAM_DESTROYED"})),!0;typeof t=="function"&&(s=t,t="utf8"),t||(t="utf8");let i=this[xe]?gr:n=>n();return!this[q]&&!Buffer.isBuffer(e)&&(bh(e)?e=Buffer.from(e.buffer,e.byteOffset,e.byteLength):Fh(e)?e=Buffer.from(e):typeof e!="string"&&(this.objectMode=!0)),this[q]?(this.flowing&&this[I]!==0&&this[Hr](!0),this.flowing?this.emit("data",e):this[Ci](e),this[I]!==0&&this.emit("readable"),s&&i(s),this.flowing):e.length?(typeof e=="string"&&!(t===this[V]&&!this[Be].lastNeed)&&(e=Buffer.from(e,t)),Buffer.isBuffer(e)&&this[V]&&(e=this[Be].write(e)),this.flowing&&this[I]!==0&&this[Hr](!0),this.flowing?this.emit("data",e):this[Ci](e),this[I]!==0&&this.emit("readable"),s&&i(s),this.flowing):(this[I]!==0&&this.emit("readable"),s&&i(s),this.flowing)}read(e){if(this[$])return null;if(this[I]===0||e===0||e>this[I])return this[Re](),null;this[q]&&(e=null),this[L].length>1&&!this[q]&&(this.encoding?this[L]=[this[L].join("")]:this[L]=[Buffer.concat(this[L],this[I])]);let t=this[ru](e||null,this[L][0]);return this[Re](),t}[ru](e,t){return e===t.length||e===null?this[wi]():(this[L][0]=t.slice(e),t=t.slice(0,e),this[I]-=e),this.emit("data",t),!this[L].length&&!this[ve]&&this.emit("drain"),t}end(e,t,s){return typeof e=="function"&&(s=e,e=null),typeof t=="function"&&(s=t,t="utf8"),e&&this.write(e,t),s&&this.once("end",s),this[ve]=!0,this.writable=!1,(this.flowing||!this[Dr])&&this[Re](),this}[qt](){this[$]||(this[Dr]=!1,this[Jr]=!0,this.emit("resume"),this[L].length?this[Hr]():this[ve]?this[Re]():this.emit("drain"))}resume(){return this[qt]()}pause(){this[Jr]=!1,this[Dr]=!0}get destroyed(){return this[$]}get flowing(){return this[Jr]}get paused(){return this[Dr]}[Ci](e){this[q]?this[I]+=1:this[I]+=e.length,this[L].push(e)}[wi](){return this[L].length&&(this[q]?this[I]-=1:this[I]-=this[L][0].length),this[L].shift()}[Hr](e){do;while(this[su](this[wi]()));!e&&!this[L].length&&!this[ve]&&this.emit("drain")}[su](e){return e?(this.emit("data",e),this.flowing):!1}pipe(e,t){if(this[$])return;let s=this[Je];return t=t||{},e===Qo.stdout||e===Qo.stderr?t.end=!1:t.end=t.end!==!1,t.proxyErrors=!!t.proxyErrors,s?t.end&&e.end():(this[Ee].push(t.proxyErrors?new Si(this,e,t):new Vr(this,e,t)),this[xe]?gr(()=>this[qt]()):this[qt]()),e}unpipe(e){let t=this[Ee].find(s=>s.dest===e);t&&(this[Ee].splice(this[Ee].indexOf(t),1),t.unpipe())}addListener(e,t){return this.on(e,t)}on(e,t){let s=super.on(e,t);return e==="data"&&!this[Ee].length&&!this.flowing?this[qt]():e==="readable"&&this[I]!==0?super.emit("readable"):wh(e)&&this[Je]?(super.emit(e),this.removeAllListeners(e)):e==="error"&&this[dr]&&(this[xe]?gr(()=>t.call(this,this[dr])):t.call(this,this[dr])),s}get emittedEnd(){return this[Je]}[Re](){!this[zr]&&!this[Je]&&!this[$]&&this[L].length===0&&this[ve]&&(this[zr]=!0,this.emit("end"),this.emit("prefinish"),this.emit("finish"),this[Gr]&&this.emit("close"),this[zr]=!1)}emit(e,t,...s){if(e!=="error"&&e!=="close"&&e!==$&&this[$])return;if(e==="data")return t?this[xe]?gr(()=>this[Fi](t)):this[Fi](t):!1;if(e==="end")return this[iu]();if(e==="close"){if(this[Gr]=!0,!this[Je]&&!this[$])return;let n=super.emit("close");return this.removeAllListeners("close"),n}else if(e==="error"){this[dr]=t;let n=super.emit("error",t);return this[Re](),n}else if(e==="resume"){let n=super.emit("resume");return this[Re](),n}else if(e==="finish"||e==="prefinish"){let n=super.emit(e);return this.removeAllListeners(e),n}let i=super.emit(e,t,...s);return this[Re](),i}[Fi](e){for(let s of this[Ee])s.dest.write(e)===!1&&this.pause();let t=super.emit("data",e);return this[Re](),t}[iu](){this[Je]||(this[Je]=!0,this.readable=!1,this[xe]?gr(()=>this[bi]()):this[bi]())}[bi](){if(this[Be]){let t=this[Be].end();if(t){for(let s of this[Ee])s.dest.write(t);super.emit("data",t)}}for(let t of this[Ee])t.end();let e=super.emit("end");return this.removeAllListeners("end"),e}collect(){let e=[];this[q]||(e.dataLength=0);let t=this.promise();return this.on("data",s=>{e.push(s),this[q]||(e.dataLength+=s.length)}),t.then(()=>e)}concat(){return this[q]?Promise.reject(new Error("cannot concat in objectMode")):this.collect().then(e=>this[q]?Promise.reject(new Error("cannot concat in objectMode")):this[V]?e.join(""):Buffer.concat(e,e.dataLength))}promise(){return new Promise((e,t)=>{this.on($,()=>t(new Error("stream destroyed"))),this.on("error",s=>t(s)),this.on("end",()=>e())})}[Ah](){return{next:()=>{let t=this.read();if(t!==null)return Promise.resolve({done:!1,value:t});if(this[ve])return Promise.resolve({done:!0});let s=null,i=null,n=c=>{this.removeListener("data",o),this.removeListener("end",a),i(c)},o=c=>{this.removeListener("error",n),this.removeListener("end",a),this.pause(),s({value:c,done:!!this[ve]})},a=()=>{this.removeListener("error",n),this.removeListener("data",o),s({done:!0})},l=()=>n(new Error("stream destroyed"));return new Promise((c,h)=>{i=h,s=c,this.once($,l),this.once("error",n),this.once("end",a),this.once("data",o)})}}}[Ch](){return{next:()=>{let t=this.read();return{value:t,done:t===null}}}}destroy(e){return this[$]?(e?this.emit("error",e):this.emit($),this):(this[$]=!0,this[L].length=0,this[I]=0,typeof this.close=="function"&&!this[Gr]&&this.close(),e?this.emit("error",e):this.emit($),this)}static isStream(e){return!!e&&(e instanceof ou||e instanceof eu||e instanceof yh&&(typeof e.pipe=="function"||typeof e.write=="function"&&typeof e.end=="function"))}}});var cu=y((Hd,au)=>{u();var Sh=require("zlib").constants||{ZLIB_VERNUM:4736};au.exports=Object.freeze(Object.assign(Object.create(null),{Z_NO_FLUSH:0,Z_PARTIAL_FLUSH:1,Z_SYNC_FLUSH:2,Z_FULL_FLUSH:3,Z_FINISH:4,Z_BLOCK:5,Z_OK:0,Z_STREAM_END:1,Z_NEED_DICT:2,Z_ERRNO:-1,Z_STREAM_ERROR:-2,Z_DATA_ERROR:-3,Z_MEM_ERROR:-4,Z_BUF_ERROR:-5,Z_VERSION_ERROR:-6,Z_NO_COMPRESSION:0,Z_BEST_SPEED:1,Z_BEST_COMPRESSION:9,Z_DEFAULT_COMPRESSION:-1,Z_FILTERED:1,Z_HUFFMAN_ONLY:2,Z_RLE:3,Z_FIXED:4,Z_DEFAULT_STRATEGY:0,DEFLATE:1,INFLATE:2,GZIP:3,GUNZIP:4,DEFLATERAW:5,INFLATERAW:6,UNZIP:7,BROTLI_DECODE:8,BROTLI_ENCODE:9,Z_MIN_WINDOWBITS:8,Z_MAX_WINDOWBITS:15,Z_DEFAULT_WINDOWBITS:15,Z_MIN_CHUNK:64,Z_MAX_CHUNK:1/0,Z_DEFAULT_CHUNK:16384,Z_MIN_MEMLEVEL:1,Z_MAX_MEMLEVEL:9,Z_DEFAULT_MEMLEVEL:8,Z_MIN_LEVEL:-1,Z_MAX_LEVEL:9,Z_DEFAULT_LEVEL:-1,BROTLI_OPERATION_PROCESS:0,BROTLI_OPERATION_FLUSH:1,BROTLI_OPERATION_FINISH:2,BROTLI_OPERATION_EMIT_METADATA:3,BROTLI_MODE_GENERIC:0,BROTLI_MODE_TEXT:1,BROTLI_MODE_FONT:2,BROTLI_DEFAULT_MODE:0,BROTLI_MIN_QUALITY:0,BROTLI_MAX_QUALITY:11,BROTLI_DEFAULT_QUALITY:11,BROTLI_MIN_WINDOW_BITS:10,BROTLI_MAX_WINDOW_BITS:24,BROTLI_LARGE_MAX_WINDOW_BITS:30,BROTLI_DEFAULT_WINDOW:22,BROTLI_MIN_INPUT_BLOCK_BITS:16,BROTLI_MAX_INPUT_BLOCK_BITS:24,BROTLI_PARAM_MODE:0,BROTLI_PARAM_QUALITY:1,BROTLI_PARAM_LGWIN:2,BROTLI_PARAM_LGBLOCK:3,BROTLI_PARAM_DISABLE_LITERAL_CONTEXT_MODELING:4,BROTLI_PARAM_SIZE_HINT:5,BROTLI_PARAM_LARGE_WINDOW:6,BROTLI_PARAM_NPOSTFIX:7,BROTLI_PARAM_NDIRECT:8,BROTLI_DECODER_RESULT_ERROR:0,BROTLI_DECODER_RESULT_SUCCESS:1,BROTLI_DECODER_RESULT_NEEDS_MORE_INPUT:2,BROTLI_DECODER_RESULT_NEEDS_MORE_OUTPUT:3,BROTLI_DECODER_PARAM_DISABLE_RING_BUFFER_REALLOCATION:0,BROTLI_DECODER_PARAM_LARGE_WINDOW:1,BROTLI_DECODER_NO_ERROR:0,BROTLI_DECODER_SUCCESS:1,BROTLI_DECODER_NEEDS_MORE_INPUT:2,BROTLI_DECODER_NEEDS_MORE_OUTPUT:3,BROTLI_DECODER_ERROR_FORMAT_EXUBERANT_NIBBLE:-1,BROTLI_DECODER_ERROR_FORMAT_RESERVED:-2,BROTLI_DECODER_ERROR_FORMAT_EXUBERANT_META_NIBBLE:-3,BROTLI_DECODER_ERROR_FORMAT_SIMPLE_HUFFMAN_ALPHABET:-4,BROTLI_DECODER_ERROR_FORMAT_SIMPLE_HUFFMAN_SAME:-5,BROTLI_DECODER_ERROR_FORMAT_CL_SPACE:-6,BROTLI_DECODER_ERROR_FORMAT_HUFFMAN_SPACE:-7,BROTLI_DECODER_ERROR_FORMAT_CONTEXT_MAP_REPEAT:-8,BROTLI_DECODER_ERROR_FORMAT_BLOCK_LENGTH_1:-9,BROTLI_DECODER_ERROR_FORMAT_BLOCK_LENGTH_2:-10,BROTLI_DECODER_ERROR_FORMAT_TRANSFORM:-11,BROTLI_DECODER_ERROR_FORMAT_DICTIONARY:-12,BROTLI_DECODER_ERROR_FORMAT_WINDOW_BITS:-13,BROTLI_DECODER_ERROR_FORMAT_PADDING_1:-14,BROTLI_DECODER_ERROR_FORMAT_PADDING_2:-15,BROTLI_DECODER_ERROR_FORMAT_DISTANCE:-16,BROTLI_DECODER_ERROR_DICTIONARY_NOT_SET:-19,BROTLI_DECODER_ERROR_INVALID_ARGUMENTS:-20,BROTLI_DECODER_ERROR_ALLOC_CONTEXT_MODES:-21,BROTLI_DECODER_ERROR_ALLOC_TREE_GROUPS:-22,BROTLI_DECODER_ERROR_ALLOC_CONTEXT_MAP:-25,BROTLI_DECODER_ERROR_ALLOC_RING_BUFFER_1:-26,BROTLI_DECODER_ERROR_ALLOC_RING_BUFFER_2:-27,BROTLI_DECODER_ERROR_ALLOC_BLOCK_TYPE_TREES:-30,BROTLI_DECODER_ERROR_UNREACHABLE:-31},Sh))});var xi=y((Jd,Eu)=>{"use strict";u();var lu=typeof process=="object"&&process?process:{stdout:null,stderr:null},kh=require("events"),hu=require("stream"),fu=require("string_decoder").StringDecoder,Oe=Symbol("EOF"),Pe=Symbol("maybeEmitEnd"),Ve=Symbol("emittedEnd"),Kr=Symbol("emittingEnd"),Er=Symbol("emittedError"),Zr=Symbol("closed"),pu=Symbol("read"),Xr=Symbol("flush"),mu=Symbol("flushChunk"),Y=Symbol("encoding"),Te=Symbol("decoder"),Qr=Symbol("flowing"),yr=Symbol("paused"),$t=Symbol("resume"),j=Symbol("bufferLength"),ki=Symbol("bufferPush"),_i=Symbol("bufferShift"),U=Symbol("objectMode"),W=Symbol("destroyed"),vi=Symbol("emitData"),du=Symbol("emitEnd"),Ri=Symbol("emitEnd2"),Le=Symbol("async"),Ar=r=>Promise.resolve().then(r),Du=global._MP_NO_ITERATOR_SYMBOLS_!=="1",_h=Du&&Symbol.asyncIterator||Symbol("asyncIterator not implemented"),vh=Du&&Symbol.iterator||Symbol("iterator not implemented"),Rh=r=>r==="end"||r==="finish"||r==="prefinish",Bh=r=>r instanceof ArrayBuffer||typeof r=="object"&&r.constructor&&r.constructor.name==="ArrayBuffer"&&r.byteLength>=0,xh=r=>!Buffer.isBuffer(r)&&ArrayBuffer.isView(r),es=class{constructor(e,t,s){this.src=e,this.dest=t,this.opts=s,this.ondrain=()=>e[$t](),t.on("drain",this.ondrain)}unpipe(){this.dest.removeListener("drain",this.ondrain)}proxyErrors(){}end(){this.unpipe(),this.opts.end&&this.dest.end()}},Bi=class extends es{unpipe(){this.src.removeListener("error",this.proxyErrors),super.unpipe()}constructor(e,t,s){super(e,t,s),this.proxyErrors=i=>t.emit("error",i),e.on("error",this.proxyErrors)}};Eu.exports=class gu extends hu{constructor(e){super(),this[Qr]=!1,this[yr]=!1,this.pipes=[],this.buffer=[],this[U]=e&&e.objectMode||!1,this[U]?this[Y]=null:this[Y]=e&&e.encoding||null,this[Y]==="buffer"&&(this[Y]=null),this[Le]=e&&!!e.async||!1,this[Te]=this[Y]?new fu(this[Y]):null,this[Oe]=!1,this[Ve]=!1,this[Kr]=!1,this[Zr]=!1,this[Er]=null,this.writable=!0,this.readable=!0,this[j]=0,this[W]=!1}get bufferLength(){return this[j]}get encoding(){return this[Y]}set encoding(e){if(this[U])throw new Error("cannot set encoding in objectMode");if(this[Y]&&e!==this[Y]&&(this[Te]&&this[Te].lastNeed||this[j]))throw new Error("cannot change encoding");this[Y]!==e&&(this[Te]=e?new fu(e):null,this.buffer.length&&(this.buffer=this.buffer.map(t=>this[Te].write(t)))),this[Y]=e}setEncoding(e){this.encoding=e}get objectMode(){return this[U]}set objectMode(e){this[U]=this[U]||!!e}get async(){return this[Le]}set async(e){this[Le]=this[Le]||!!e}write(e,t,s){if(this[Oe])throw new Error("write after end");if(this[W])return this.emit("error",Object.assign(new Error("Cannot call write after a stream was destroyed"),{code:"ERR_STREAM_DESTROYED"})),!0;typeof t=="function"&&(s=t,t="utf8"),t||(t="utf8");let i=this[Le]?Ar:n=>n();return!this[U]&&!Buffer.isBuffer(e)&&(xh(e)?e=Buffer.from(e.buffer,e.byteOffset,e.byteLength):Bh(e)?e=Buffer.from(e):typeof e!="string"&&(this.objectMode=!0)),this[U]?(this.flowing&&this[j]!==0&&this[Xr](!0),this.flowing?this.emit("data",e):this[ki](e),this[j]!==0&&this.emit("readable"),s&&i(s),this.flowing):e.length?(typeof e=="string"&&!(t===this[Y]&&!this[Te].lastNeed)&&(e=Buffer.from(e,t)),Buffer.isBuffer(e)&&this[Y]&&(e=this[Te].write(e)),this.flowing&&this[j]!==0&&this[Xr](!0),this.flowing?this.emit("data",e):this[ki](e),this[j]!==0&&this.emit("readable"),s&&i(s),this.flowing):(this[j]!==0&&this.emit("readable"),s&&i(s),this.flowing)}read(e){if(this[W])return null;if(this[j]===0||e===0||e>this[j])return this[Pe](),null;this[U]&&(e=null),this.buffer.length>1&&!this[U]&&(this.encoding?this.buffer=[this.buffer.join("")]:this.buffer=[Buffer.concat(this.buffer,this[j])]);let t=this[pu](e||null,this.buffer[0]);return this[Pe](),t}[pu](e,t){return e===t.length||e===null?this[_i]():(this.buffer[0]=t.slice(e),t=t.slice(0,e),this[j]-=e),this.emit("data",t),!this.buffer.length&&!this[Oe]&&this.emit("drain"),t}end(e,t,s){return typeof e=="function"&&(s=e,e=null),typeof t=="function"&&(s=t,t="utf8"),e&&this.write(e,t),s&&this.once("end",s),this[Oe]=!0,this.writable=!1,(this.flowing||!this[yr])&&this[Pe](),this}[$t](){this[W]||(this[yr]=!1,this[Qr]=!0,this.emit("resume"),this.buffer.length?this[Xr]():this[Oe]?this[Pe]():this.emit("drain"))}resume(){return this[$t]()}pause(){this[Qr]=!1,this[yr]=!0}get destroyed(){return this[W]}get flowing(){return this[Qr]}get paused(){return this[yr]}[ki](e){this[U]?this[j]+=1:this[j]+=e.length,this.buffer.push(e)}[_i](){return this.buffer.length&&(this[U]?this[j]-=1:this[j]-=this.buffer[0].length),this.buffer.shift()}[Xr](e){do;while(this[mu](this[_i]()));!e&&!this.buffer.length&&!this[Oe]&&this.emit("drain")}[mu](e){return e?(this.emit("data",e),this.flowing):!1}pipe(e,t){if(this[W])return;let s=this[Ve];return t=t||{},e===lu.stdout||e===lu.stderr?t.end=!1:t.end=t.end!==!1,t.proxyErrors=!!t.proxyErrors,s?t.end&&e.end():(this.pipes.push(t.proxyErrors?new Bi(this,e,t):new es(this,e,t)),this[Le]?Ar(()=>this[$t]()):this[$t]()),e}unpipe(e){let t=this.pipes.find(s=>s.dest===e);t&&(this.pipes.splice(this.pipes.indexOf(t),1),t.unpipe())}addListener(e,t){return this.on(e,t)}on(e,t){let s=super.on(e,t);return e==="data"&&!this.pipes.length&&!this.flowing?this[$t]():e==="readable"&&this[j]!==0?super.emit("readable"):Rh(e)&&this[Ve]?(super.emit(e),this.removeAllListeners(e)):e==="error"&&this[Er]&&(this[Le]?Ar(()=>t.call(this,this[Er])):t.call(this,this[Er])),s}get emittedEnd(){return this[Ve]}[Pe](){!this[Kr]&&!this[Ve]&&!this[W]&&this.buffer.length===0&&this[Oe]&&(this[Kr]=!0,this.emit("end"),this.emit("prefinish"),this.emit("finish"),this[Zr]&&this.emit("close"),this[Kr]=!1)}emit(e,t,...s){if(e!=="error"&&e!=="close"&&e!==W&&this[W])return;if(e==="data")return t?this[Le]?Ar(()=>this[vi](t)):this[vi](t):!1;if(e==="end")return this[du]();if(e==="close"){if(this[Zr]=!0,!this[Ve]&&!this[W])return;let n=super.emit("close");return this.removeAllListeners("close"),n}else if(e==="error"){this[Er]=t;let n=super.emit("error",t);return this[Pe](),n}else if(e==="resume"){let n=super.emit("resume");return this[Pe](),n}else if(e==="finish"||e==="prefinish"){let n=super.emit(e);return this.removeAllListeners(e),n}let i=super.emit(e,t,...s);return this[Pe](),i}[vi](e){for(let s of this.pipes)s.dest.write(e)===!1&&this.pause();let t=super.emit("data",e);return this[Pe](),t}[du](){this[Ve]||(this[Ve]=!0,this.readable=!1,this[Le]?Ar(()=>this[Ri]()):this[Ri]())}[Ri](){if(this[Te]){let t=this[Te].end();if(t){for(let s of this.pipes)s.dest.write(t);super.emit("data",t)}}for(let t of this.pipes)t.end();let e=super.emit("end");return this.removeAllListeners("end"),e}collect(){let e=[];this[U]||(e.dataLength=0);let t=this.promise();return this.on("data",s=>{e.push(s),this[U]||(e.dataLength+=s.length)}),t.then(()=>e)}concat(){return this[U]?Promise.reject(new Error("cannot concat in objectMode")):this.collect().then(e=>this[U]?Promise.reject(new Error("cannot concat in objectMode")):this[Y]?e.join(""):Buffer.concat(e,e.dataLength))}promise(){return new Promise((e,t)=>{this.on(W,()=>t(new Error("stream destroyed"))),this.on("error",s=>t(s)),this.on("end",()=>e())})}[_h](){return{next:()=>{let t=this.read();if(t!==null)return Promise.resolve({done:!1,value:t});if(this[Oe])return Promise.resolve({done:!0});let s=null,i=null,n=c=>{this.removeListener("data",o),this.removeListener("end",a),i(c)},o=c=>{this.removeListener("error",n),this.removeListener("end",a),this.pause(),s({value:c,done:!!this[Oe]})},a=()=>{this.removeListener("error",n),this.removeListener("data",o),s({done:!0})},l=()=>n(new Error("stream destroyed"));return new Promise((c,h)=>{i=h,s=c,this.once(W,l),this.once("error",n),this.once("end",a),this.once("data",o)})}}}[vh](){return{next:()=>{let t=this.read();return{value:t,done:t===null}}}}destroy(e){return this[W]?(e?this.emit("error",e):this.emit(W),this):(this[W]=!0,this.buffer.length=0,this[j]=0,typeof this.close=="function"&&!this[Zr]&&this.close(),e?this.emit("error",e):this.emit(W),this)}static isStream(e){return!!e&&(e instanceof gu||e instanceof hu||e instanceof kh&&(typeof e.pipe=="function"||typeof e.write=="function"&&typeof e.end=="function"))}}});var Ji=y(X=>{"use strict";u();var Ni=require("assert"),Ye=require("buffer").Buffer,Cu=require("zlib"),Dt=X.constants=cu(),Oh=xi(),yu=Ye.concat,gt=Symbol("_superWrite"),Wt=class extends Error{constructor(e){super("zlib: "+e.message),this.code=e.code,this.errno=e.errno,this.code||(this.code="ZLIB_ERROR"),this.message="zlib: "+e.message,Error.captureStackTrace(this,this.constructor)}get name(){return"ZlibError"}},Ph=Symbol("opts"),Cr=Symbol("flushFlag"),Au=Symbol("finishFlushFlag"),Hi=Symbol("fullFlushFlag"),B=Symbol("handle"),ts=Symbol("onError"),Ut=Symbol("sawError"),Oi=Symbol("level"),Pi=Symbol("strategy"),Ti=Symbol("ended"),Vd=Symbol("_defaultFullFlush"),rs=class extends Oh{constructor(e,t){if(!e||typeof e!="object")throw new TypeError("invalid options for ZlibBase constructor");super(e),this[Ut]=!1,this[Ti]=!1,this[Ph]=e,this[Cr]=e.flush,this[Au]=e.finishFlush;try{this[B]=new Cu[t](e)}catch(s){throw new Wt(s)}this[ts]=s=>{this[Ut]||(this[Ut]=!0,this.close(),this.emit("error",s))},this[B].on("error",s=>this[ts](new Wt(s))),this.once("end",()=>this.close)}close(){this[B]&&(this[B].close(),this[B]=null,this.emit("close"))}reset(){if(!this[Ut])return Ni(this[B],"zlib binding closed"),this[B].reset()}flush(e){this.ended||(typeof e!="number"&&(e=this[Hi]),this.write(Object.assign(Ye.alloc(0),{[Cr]:e})))}end(e,t,s){return e&&this.write(e,t),this.flush(this[Au]),this[Ti]=!0,super.end(null,null,s)}get ended(){return this[Ti]}write(e,t,s){if(typeof t=="function"&&(s=t,t="utf8"),typeof e=="string"&&(e=Ye.from(e,t)),this[Ut])return;Ni(this[B],"zlib binding closed");let i=this[B]._handle,n=i.close;i.close=()=>{};let o=this[B].close;this[B].close=()=>{},Ye.concat=c=>c;let a;try{let c=typeof e[Cr]=="number"?e[Cr]:this[Cr];a=this[B]._processChunk(e,c),Ye.concat=yu}catch(c){Ye.concat=yu,this[ts](new Wt(c))}finally{this[B]&&(this[B]._handle=i,i.close=n,this[B].close=o,this[B].removeAllListeners("error"))}this[B]&&this[B].on("error",c=>this[ts](new Wt(c)));let l;if(a)if(Array.isArray(a)&&a.length>0){l=this[gt](Ye.from(a[0]));for(let c=1;c<a.length;c++)l=this[gt](a[c])}else l=this[gt](Ye.from(a));return s&&s(),l}[gt](e){return super.write(e)}},Ne=class extends rs{constructor(e,t){e=e||{},e.flush=e.flush||Dt.Z_NO_FLUSH,e.finishFlush=e.finishFlush||Dt.Z_FINISH,super(e,t),this[Hi]=Dt.Z_FULL_FLUSH,this[Oi]=e.level,this[Pi]=e.strategy}params(e,t){if(!this[Ut]){if(!this[B])throw new Error("cannot switch params when binding is closed");if(!this[B].params)throw new Error("not supported in this implementation");if(this[Oi]!==e||this[Pi]!==t){this.flush(Dt.Z_SYNC_FLUSH),Ni(this[B],"zlib binding closed");let s=this[B].flush;this[B].flush=(i,n)=>{this.flush(i),n()};try{this[B].params(e,t)}finally{this[B].flush=s}this[B]&&(this[Oi]=e,this[Pi]=t)}}}},Mi=class extends Ne{constructor(e){super(e,"Deflate")}},Ii=class extends Ne{constructor(e){super(e,"Inflate")}},Li=Symbol("_portable"),ji=class extends Ne{constructor(e){super(e,"Gzip"),this[Li]=e&&!!e.portable}[gt](e){return this[Li]?(this[Li]=!1,e[9]=255,super[gt](e)):super[gt](e)}},qi=class extends Ne{constructor(e){super(e,"Gunzip")}},$i=class extends Ne{constructor(e){super(e,"DeflateRaw")}},Ui=class extends Ne{constructor(e){super(e,"InflateRaw")}},Wi=class extends Ne{constructor(e){super(e,"Unzip")}},ss=class extends rs{constructor(e,t){e=e||{},e.flush=e.flush||Dt.BROTLI_OPERATION_PROCESS,e.finishFlush=e.finishFlush||Dt.BROTLI_OPERATION_FINISH,super(e,t),this[Hi]=Dt.BROTLI_OPERATION_FLUSH}},zi=class extends ss{constructor(e){super(e,"BrotliCompress")}},Gi=class extends ss{constructor(e){super(e,"BrotliDecompress")}};X.Deflate=Mi;X.Inflate=Ii;X.Gzip=ji;X.Gunzip=qi;X.DeflateRaw=$i;X.InflateRaw=Ui;X.Unzip=Wi;typeof Cu.BrotliCompress=="function"?(X.BrotliCompress=zi,X.BrotliDecompress=Gi):X.BrotliCompress=X.BrotliDecompress=class{constructor(){throw new Error("Brotli is not supported in this version of Node.js")}}});var zt=y((Zd,wu)=>{u();var Th=process.env.TESTING_TAR_FAKE_PLATFORM||process.platform;wu.exports=Th!=="win32"?r=>r:r=>r&&r.replace(/\\/g,"/")});var is=y((Qd,Fu)=>{"use strict";u();var Lh=Yr(),Vi=zt(),Yi=Symbol("slurp");Fu.exports=class extends Lh{constructor(e,t,s){switch(super(),this.pause(),this.extended=t,this.globalExtended=s,this.header=e,this.startBlockSize=512*Math.ceil(e.size/512),this.blockRemain=this.startBlockSize,this.remain=e.size,this.type=e.type,this.meta=!1,this.ignore=!1,this.type){case"File":case"OldFile":case"Link":case"SymbolicLink":case"CharacterDevice":case"BlockDevice":case"Directory":case"FIFO":case"ContiguousFile":case"GNUDumpDir":break;case"NextFileHasLongLinkpath":case"NextFileHasLongPath":case"OldGnuLongPath":case"GlobalExtendedHeader":case"ExtendedHeader":case"OldExtendedHeader":this.meta=!0;break;default:this.ignore=!0}this.path=Vi(e.path),this.mode=e.mode,this.mode&&(this.mode=this.mode&4095),this.uid=e.uid,this.gid=e.gid,this.uname=e.uname,this.gname=e.gname,this.size=e.size,this.mtime=e.mtime,this.atime=e.atime,this.ctime=e.ctime,this.linkpath=Vi(e.linkpath),this.uname=e.uname,this.gname=e.gname,t&&this[Yi](t),s&&this[Yi](s,!0)}write(e){let t=e.length;if(t>this.blockRemain)throw new Error("writing more to entry than is appropriate");let s=this.remain,i=this.blockRemain;return this.remain=Math.max(0,s-t),this.blockRemain=Math.max(0,i-t),this.ignore?!0:s>=t?super.write(e):super.write(e.slice(0,s))}[Yi](e,t){for(let s in e)e[s]!==null&&e[s]!==void 0&&!(t&&s==="path")&&(this[s]=s==="path"||s==="linkpath"?Vi(e[s]):e[s])}}});var Ki=y(ns=>{"use strict";u();ns.name=new Map([["0","File"],["","OldFile"],["1","Link"],["2","SymbolicLink"],["3","CharacterDevice"],["4","BlockDevice"],["5","Directory"],["6","FIFO"],["7","ContiguousFile"],["g","GlobalExtendedHeader"],["x","ExtendedHeader"],["A","SolarisACL"],["D","GNUDumpDir"],["I","Inode"],["K","NextFileHasLongLinkpath"],["L","NextFileHasLongPath"],["M","ContinuationFile"],["N","OldGnuLongPath"],["S","SparseFile"],["V","TapeVolumeHeader"],["X","OldExtendedHeader"]]);ns.code=new Map(Array.from(ns.name).map(r=>[r[1],r[0]]))});var _u=y((t0,ku)=>{"use strict";u();var Nh=(r,e)=>{if(Number.isSafeInteger(r))r<0?Ih(r,e):Mh(r,e);else throw Error("cannot encode number outside of javascript safe integer range");return e},Mh=(r,e)=>{e[0]=128;for(var t=e.length;t>1;t--)e[t-1]=r&255,r=Math.floor(r/256)},Ih=(r,e)=>{e[0]=255;var t=!1;r=r*-1;for(var s=e.length;s>1;s--){var i=r&255;r=Math.floor(r/256),t?e[s-1]=bu(i):i===0?e[s-1]=0:(t=!0,e[s-1]=Su(i))}},jh=r=>{let e=r[0],t=e===128?$h(r.slice(1,r.length)):e===255?qh(r):null;if(t===null)throw Error("invalid base256 encoding");if(!Number.isSafeInteger(t))throw Error("parsed number outside of javascript safe integer range");return t},qh=r=>{for(var e=r.length,t=0,s=!1,i=e-1;i>-1;i--){var n=r[i],o;s?o=bu(n):n===0?o=n:(s=!0,o=Su(n)),o!==0&&(t-=o*Math.pow(256,e-i-1))}return t},$h=r=>{for(var e=r.length,t=0,s=e-1;s>-1;s--){var i=r[s];i!==0&&(t+=i*Math.pow(256,e-s-1))}return t},bu=r=>(255^r)&255,Su=r=>(255^r)+1&255;ku.exports={encode:Nh,parse:jh}});var Ht=y((r0,Ru)=>{"use strict";u();var Zi=Ki(),Gt=require("path").posix,vu=_u(),Xi=Symbol("slurp"),Q=Symbol("type"),tn=class{constructor(e,t,s,i){this.cksumValid=!1,this.needPax=!1,this.nullBlock=!1,this.block=null,this.path=null,this.mode=null,this.uid=null,this.gid=null,this.size=null,this.mtime=null,this.cksum=null,this[Q]="0",this.linkpath=null,this.uname=null,this.gname=null,this.devmaj=0,this.devmin=0,this.atime=null,this.ctime=null,Buffer.isBuffer(e)?this.decode(e,t||0,s,i):e&&this.set(e)}decode(e,t,s,i){if(t||(t=0),!e||!(e.length>=t+512))throw new Error("need 512 bytes for header");if(this.path=Et(e,t,100),this.mode=Ke(e,t+100,8),this.uid=Ke(e,t+108,8),this.gid=Ke(e,t+116,8),this.size=Ke(e,t+124,12),this.mtime=Qi(e,t+136,12),this.cksum=Ke(e,t+148,12),this[Xi](s),this[Xi](i,!0),this[Q]=Et(e,t+156,1),this[Q]===""&&(this[Q]="0"),this[Q]==="0"&&this.path.slice(-1)==="/"&&(this[Q]="5"),this[Q]==="5"&&(this.size=0),this.linkpath=Et(e,t+157,100),e.slice(t+257,t+265).toString()==="ustar\x0000")if(this.uname=Et(e,t+265,32),this.gname=Et(e,t+297,32),this.devmaj=Ke(e,t+329,8),this.devmin=Ke(e,t+337,8),e[t+475]!==0){let o=Et(e,t+345,155);this.path=o+"/"+this.path}else{let o=Et(e,t+345,130);o&&(this.path=o+"/"+this.path),this.atime=Qi(e,t+476,12),this.ctime=Qi(e,t+488,12)}let n=8*32;for(let o=t;o<t+148;o++)n+=e[o];for(let o=t+156;o<t+512;o++)n+=e[o];this.cksumValid=n===this.cksum,this.cksum===null&&n===8*32&&(this.nullBlock=!0)}[Xi](e,t){for(let s in e)e[s]!==null&&e[s]!==void 0&&!(t&&s==="path")&&(this[s]=e[s])}encode(e,t){if(e||(e=this.block=Buffer.alloc(512),t=0),t||(t=0),!(e.length>=t+512))throw new Error("need 512 bytes for header");let s=this.ctime||this.atime?130:155,i=Uh(this.path||"",s),n=i[0],o=i[1];this.needPax=i[2],this.needPax=yt(e,t,100,n)||this.needPax,this.needPax=Ze(e,t+100,8,this.mode)||this.needPax,this.needPax=Ze(e,t+108,8,this.uid)||this.needPax,this.needPax=Ze(e,t+116,8,this.gid)||this.needPax,this.needPax=Ze(e,t+124,12,this.size)||this.needPax,this.needPax=en(e,t+136,12,this.mtime)||this.needPax,e[t+156]=this[Q].charCodeAt(0),this.needPax=yt(e,t+157,100,this.linkpath)||this.needPax,e.write("ustar\x0000",t+257,8),this.needPax=yt(e,t+265,32,this.uname)||this.needPax,this.needPax=yt(e,t+297,32,this.gname)||this.needPax,this.needPax=Ze(e,t+329,8,this.devmaj)||this.needPax,this.needPax=Ze(e,t+337,8,this.devmin)||this.needPax,this.needPax=yt(e,t+345,s,o)||this.needPax,e[t+475]!==0?this.needPax=yt(e,t+345,155,o)||this.needPax:(this.needPax=yt(e,t+345,130,o)||this.needPax,this.needPax=en(e,t+476,12,this.atime)||this.needPax,this.needPax=en(e,t+488,12,this.ctime)||this.needPax);let a=8*32;for(let l=t;l<t+148;l++)a+=e[l];for(let l=t+156;l<t+512;l++)a+=e[l];return this.cksum=a,Ze(e,t+148,8,this.cksum),this.cksumValid=!0,this.needPax}set(e){for(let t in e)e[t]!==null&&e[t]!==void 0&&(this[t]=e[t])}get type(){return Zi.name.get(this[Q])||this[Q]}get typeKey(){return this[Q]}set type(e){Zi.code.has(e)?this[Q]=Zi.code.get(e):this[Q]=e}},Uh=(r,e)=>{let s=r,i="",n,o=Gt.parse(r).root||".";if(Buffer.byteLength(s)<100)n=[s,i,!1];else{i=Gt.dirname(s),s=Gt.basename(s);do Buffer.byteLength(s)<=100&&Buffer.byteLength(i)<=e?n=[s,i,!1]:Buffer.byteLength(s)>100&&Buffer.byteLength(i)<=e?n=[s.slice(0,100-1),i,!0]:(s=Gt.join(Gt.basename(i),s),i=Gt.dirname(i));while(i!==o&&!n);n||(n=[r.slice(0,100-1),"",!0])}return n},Et=(r,e,t)=>r.slice(e,e+t).toString("utf8").replace(/\0.*/,""),Qi=(r,e,t)=>Wh(Ke(r,e,t)),Wh=r=>r===null?null:new Date(r*1e3),Ke=(r,e,t)=>r[e]&128?vu.parse(r.slice(e,e+t)):Gh(r,e,t),zh=r=>isNaN(r)?null:r,Gh=(r,e,t)=>zh(parseInt(r.slice(e,e+t).toString("utf8").replace(/\0.*$/,"").trim(),8)),Hh={12:8589934591,8:2097151},Ze=(r,e,t,s)=>s===null?!1:s>Hh[t]||s<0?(vu.encode(s,r.slice(e,e+t)),!0):(Jh(r,e,t,s),!1),Jh=(r,e,t,s)=>r.write(Vh(s,t),e,t,"ascii"),Vh=(r,e)=>Yh(Math.floor(r).toString(8),e),Yh=(r,e)=>(r.length===e-1?r:new Array(e-r.length-1).join("0")+r+" ")+"\0",en=(r,e,t,s)=>s===null?!1:Ze(r,e,t,s.getTime()/1e3),Kh=new Array(156).join("\0"),yt=(r,e,t,s)=>s===null?!1:(r.write(s+Kh,e,t,"utf8"),s.length!==Buffer.byteLength(s)||s.length>t);Ru.exports=tn});var os=y((s0,Bu)=>{"use strict";u();var Zh=Ht(),Xh=require("path"),wr=class{constructor(e,t){this.atime=e.atime||null,this.charset=e.charset||null,this.comment=e.comment||null,this.ctime=e.ctime||null,this.gid=e.gid||null,this.gname=e.gname||null,this.linkpath=e.linkpath||null,this.mtime=e.mtime||null,this.path=e.path||null,this.size=e.size||null,this.uid=e.uid||null,this.uname=e.uname||null,this.dev=e.dev||null,this.ino=e.ino||null,this.nlink=e.nlink||null,this.global=t||!1}encode(){let e=this.encodeBody();if(e==="")return null;let t=Buffer.byteLength(e),s=512*Math.ceil(1+t/512),i=Buffer.allocUnsafe(s);for(let n=0;n<512;n++)i[n]=0;new Zh({path:("PaxHeader/"+Xh.basename(this.path)).slice(0,99),mode:this.mode||420,uid:this.uid||null,gid:this.gid||null,size:t,mtime:this.mtime||null,type:this.global?"GlobalExtendedHeader":"ExtendedHeader",linkpath:"",uname:this.uname||"",gname:this.gname||"",devmaj:0,devmin:0,atime:this.atime||null,ctime:this.ctime||null}).encode(i),i.write(e,512,t,"utf8");for(let n=t+512;n<i.length;n++)i[n]=0;return i}encodeBody(){return this.encodeField("path")+this.encodeField("ctime")+this.encodeField("atime")+this.encodeField("dev")+this.encodeField("ino")+this.encodeField("nlink")+this.encodeField("charset")+this.encodeField("comment")+this.encodeField("gid")+this.encodeField("gname")+this.encodeField("linkpath")+this.encodeField("mtime")+this.encodeField("size")+this.encodeField("uid")+this.encodeField("uname")}encodeField(e){if(this[e]===null||this[e]===void 0)return"";let t=this[e]instanceof Date?this[e].getTime()/1e3:this[e],s=" "+(e==="dev"||e==="ino"||e==="nlink"?"SCHILY.":"")+e+"="+t+`
`,i=Buffer.byteLength(s),n=Math.floor(Math.log(i)/Math.log(10))+1;return i+n>=Math.pow(10,n)&&(n+=1),n+i+s}};wr.parse=(r,e,t)=>new wr(Qh(ef(r),e),t);var Qh=(r,e)=>e?Object.keys(r).reduce((t,s)=>(t[s]=r[s],t),e):r,ef=r=>r.replace(/\n$/,"").split(`
`).reduce(tf,Object.create(null)),tf=(r,e)=>{let t=parseInt(e,10);if(t!==Buffer.byteLength(e)+1)return r;e=e.slice((t+" ").length);let s=e.split("="),i=s.shift().replace(/^SCHILY\.(dev|ino|nlink)/,"$1");if(!i)return r;let n=s.join("=");return r[i]=/^([A-Z]+\.)?([mac]|birth|creation)time$/.test(i)?new Date(n*1e3):/^[0-9]+$/.test(n)?+n:n,r};Bu.exports=wr});var Jt=y((i0,xu)=>{u();xu.exports=r=>{let e=r.length-1,t=-1;for(;e>-1&&r.charAt(e)==="/";)t=e,e--;return t===-1?r:r.slice(0,t)}});var us=y((n0,Ou)=>{"use strict";u();Ou.exports=r=>class extends r{warn(e,t,s={}){this.file&&(s.file=this.file),this.cwd&&(s.cwd=this.cwd),s.code=t instanceof Error&&t.code||e,s.tarCode=e,!this.strict&&s.recoverable!==!1?(t instanceof Error&&(s=Object.assign(t,s),t=t.message),this.emit("warn",s.tarCode,t,s)):t instanceof Error?this.emit("error",Object.assign(t,s)):this.emit("error",Object.assign(new Error(`${e}: ${t}`),s))}}});var sn=y((u0,Pu)=>{"use strict";u();var as=["|","<",">","?",":"],rn=as.map(r=>String.fromCharCode(61440+r.charCodeAt(0))),rf=new Map(as.map((r,e)=>[r,rn[e]])),sf=new Map(rn.map((r,e)=>[r,as[e]]));Pu.exports={encode:r=>as.reduce((e,t)=>e.split(t).join(rf.get(t)),r),decode:r=>rn.reduce((e,t)=>e.split(t).join(sf.get(t)),r)}});var nn=y((a0,Lu)=>{u();var{isAbsolute:nf,parse:Tu}=require("path").win32;Lu.exports=r=>{let e="",t=Tu(r);for(;nf(r)||t.root;){let s=r.charAt(0)==="/"&&r.slice(0,4)!=="//?/"?"/":t.root;r=r.slice(s.length),e+=s,t=Tu(r)}return[e,r]}});var Mu=y((c0,Nu)=>{"use strict";u();Nu.exports=(r,e,t)=>(r&=4095,t&&(r=(r|384)&-19),e&&(r&256&&(r|=64),r&32&&(r|=8),r&4&&(r|=1)),r)});var dn=y((f0,Zu)=>{"use strict";u();var zu=Yr(),Gu=os(),Hu=Ht(),Ae=require("fs"),Iu=require("path"),ye=zt(),of=Jt(),Ju=(r,e)=>e?(r=ye(r).replace(/^\.(\/|$)/,""),of(e)+"/"+r):ye(r),uf=16*1024*1024,ju=Symbol("process"),qu=Symbol("file"),$u=Symbol("directory"),un=Symbol("symlink"),Uu=Symbol("hardlink"),Fr=Symbol("header"),cs=Symbol("read"),an=Symbol("lstat"),ls=Symbol("onlstat"),cn=Symbol("onread"),ln=Symbol("onreadlink"),hn=Symbol("openfile"),fn=Symbol("onopenfile"),Xe=Symbol("close"),hs=Symbol("mode"),pn=Symbol("awaitDrain"),on=Symbol("ondrain"),Ce=Symbol("prefix"),Wu=Symbol("hadError"),Vu=us(),af=sn(),Yu=nn(),Ku=Mu(),fs=Vu(class extends zu{constructor(e,t){if(t=t||{},super(t),typeof e!="string")throw new TypeError("path is required");this.path=ye(e),this.portable=!!t.portable,this.myuid=process.getuid&&process.getuid()||0,this.myuser=process.env.USER||"",this.maxReadSize=t.maxReadSize||uf,this.linkCache=t.linkCache||new Map,this.statCache=t.statCache||new Map,this.preservePaths=!!t.preservePaths,this.cwd=ye(t.cwd||process.cwd()),this.strict=!!t.strict,this.noPax=!!t.noPax,this.noMtime=!!t.noMtime,this.mtime=t.mtime||null,this.prefix=t.prefix?ye(t.prefix):null,this.fd=null,this.blockLen=null,this.blockRemain=null,this.buf=null,this.offset=null,this.length=null,this.pos=null,this.remain=null,typeof t.onwarn=="function"&&this.on("warn",t.onwarn);let s=!1;if(!this.preservePaths){let[i,n]=Yu(this.path);i&&(this.path=n,s=i)}this.win32=!!t.win32||process.platform==="win32",this.win32&&(this.path=af.decode(this.path.replace(/\\/g,"/")),e=e.replace(/\\/g,"/")),this.absolute=ye(t.absolute||Iu.resolve(this.cwd,e)),this.path===""&&(this.path="./"),s&&this.warn("TAR_ENTRY_INFO",`stripping ${s} from absolute path`,{entry:this,path:s+this.path}),this.statCache.has(this.absolute)?this[ls](this.statCache.get(this.absolute)):this[an]()}emit(e,...t){return e==="error"&&(this[Wu]=!0),super.emit(e,...t)}[an](){Ae.lstat(this.absolute,(e,t)=>{if(e)return this.emit("error",e);this[ls](t)})}[ls](e){this.statCache.set(this.absolute,e),this.stat=e,e.isFile()||(e.size=0),this.type=lf(e),this.emit("stat",e),this[ju]()}[ju](){switch(this.type){case"File":return this[qu]();case"Directory":return this[$u]();case"SymbolicLink":return this[un]();default:return this.end()}}[hs](e){return Ku(e,this.type==="Directory",this.portable)}[Ce](e){return Ju(e,this.prefix)}[Fr](){this.type==="Directory"&&this.portable&&(this.noMtime=!0),this.header=new Hu({path:this[Ce](this.path),linkpath:this.type==="Link"?this[Ce](this.linkpath):this.linkpath,mode:this[hs](this.stat.mode),uid:this.portable?null:this.stat.uid,gid:this.portable?null:this.stat.gid,size:this.stat.size,mtime:this.noMtime?null:this.mtime||this.stat.mtime,type:this.type,uname:this.portable?null:this.stat.uid===this.myuid?this.myuser:"",atime:this.portable?null:this.stat.atime,ctime:this.portable?null:this.stat.ctime}),this.header.encode()&&!this.noPax&&super.write(new Gu({atime:this.portable?null:this.header.atime,ctime:this.portable?null:this.header.ctime,gid:this.portable?null:this.header.gid,mtime:this.noMtime?null:this.mtime||this.header.mtime,path:this[Ce](this.path),linkpath:this.type==="Link"?this[Ce](this.linkpath):this.linkpath,size:this.header.size,uid:this.portable?null:this.header.uid,uname:this.portable?null:this.header.uname,dev:this.portable?null:this.stat.dev,ino:this.portable?null:this.stat.ino,nlink:this.portable?null:this.stat.nlink}).encode()),super.write(this.header.block)}[$u](){this.path.slice(-1)!=="/"&&(this.path+="/"),this.stat.size=0,this[Fr](),this.end()}[un](){Ae.readlink(this.absolute,(e,t)=>{if(e)return this.emit("error",e);this[ln](t)})}[ln](e){this.linkpath=ye(e),this[Fr](),this.end()}[Uu](e){this.type="Link",this.linkpath=ye(Iu.relative(this.cwd,e)),this.stat.size=0,this[Fr](),this.end()}[qu](){if(this.stat.nlink>1){let e=this.stat.dev+":"+this.stat.ino;if(this.linkCache.has(e)){let t=this.linkCache.get(e);if(t.indexOf(this.cwd)===0)return this[Uu](t)}this.linkCache.set(e,this.absolute)}if(this[Fr](),this.stat.size===0)return this.end();this[hn]()}[hn](){Ae.open(this.absolute,"r",(e,t)=>{if(e)return this.emit("error",e);this[fn](t)})}[fn](e){if(this.fd=e,this[Wu])return this[Xe]();this.blockLen=512*Math.ceil(this.stat.size/512),this.blockRemain=this.blockLen;let t=Math.min(this.blockLen,this.maxReadSize);this.buf=Buffer.allocUnsafe(t),this.offset=0,this.pos=0,this.remain=this.stat.size,this.length=this.buf.length,this[cs]()}[cs](){let{fd:e,buf:t,offset:s,length:i,pos:n}=this;Ae.read(e,t,s,i,n,(o,a)=>{if(o)return this[Xe](()=>this.emit("error",o));this[cn](a)})}[Xe](e){Ae.close(this.fd,e)}[cn](e){if(e<=0&&this.remain>0){let i=new Error("encountered unexpected EOF");return i.path=this.absolute,i.syscall="read",i.code="EOF",this[Xe](()=>this.emit("error",i))}if(e>this.remain){let i=new Error("did not encounter expected EOF");return i.path=this.absolute,i.syscall="read",i.code="EOF",this[Xe](()=>this.emit("error",i))}if(e===this.remain)for(let i=e;i<this.length&&e<this.blockRemain;i++)this.buf[i+this.offset]=0,e++,this.remain++;let t=this.offset===0&&e===this.buf.length?this.buf:this.buf.slice(this.offset,this.offset+e);this.write(t)?this[on]():this[pn](()=>this[on]())}[pn](e){this.once("drain",e)}write(e){if(this.blockRemain<e.length){let t=new Error("writing more data than expected");return t.path=this.absolute,this.emit("error",t)}return this.remain-=e.length,this.blockRemain-=e.length,this.pos+=e.length,this.offset+=e.length,super.write(e)}[on](){if(!this.remain)return this.blockRemain&&super.write(Buffer.alloc(this.blockRemain)),this[Xe](e=>e?this.emit("error",e):this.end());this.offset>=this.length&&(this.buf=Buffer.allocUnsafe(Math.min(this.blockRemain,this.buf.length)),this.offset=0),this.length=this.buf.length-this.offset,this[cs]()}}),mn=class extends fs{[an](){this[ls](Ae.lstatSync(this.absolute))}[un](){this[ln](Ae.readlinkSync(this.absolute))}[hn](){this[fn](Ae.openSync(this.absolute,"r"))}[cs](){let e=!0;try{let{fd:t,buf:s,offset:i,length:n,pos:o}=this,a=Ae.readSync(t,s,i,n,o);this[cn](a),e=!1}finally{if(e)try{this[Xe](()=>{})}catch{}}}[pn](e){e()}[Xe](e){Ae.closeSync(this.fd),e()}},cf=Vu(class extends zu{constructor(e,t){t=t||{},super(t),this.preservePaths=!!t.preservePaths,this.portable=!!t.portable,this.strict=!!t.strict,this.noPax=!!t.noPax,this.noMtime=!!t.noMtime,this.readEntry=e,this.type=e.type,this.type==="Directory"&&this.portable&&(this.noMtime=!0),this.prefix=t.prefix||null,this.path=ye(e.path),this.mode=this[hs](e.mode),this.uid=this.portable?null:e.uid,this.gid=this.portable?null:e.gid,this.uname=this.portable?null:e.uname,this.gname=this.portable?null:e.gname,this.size=e.size,this.mtime=this.noMtime?null:t.mtime||e.mtime,this.atime=this.portable?null:e.atime,this.ctime=this.portable?null:e.ctime,this.linkpath=ye(e.linkpath),typeof t.onwarn=="function"&&this.on("warn",t.onwarn);let s=!1;if(!this.preservePaths){let[i,n]=Yu(this.path);i&&(this.path=n,s=i)}this.remain=e.size,this.blockRemain=e.startBlockSize,this.header=new Hu({path:this[Ce](this.path),linkpath:this.type==="Link"?this[Ce](this.linkpath):this.linkpath,mode:this.mode,uid:this.portable?null:this.uid,gid:this.portable?null:this.gid,size:this.size,mtime:this.noMtime?null:this.mtime,type:this.type,uname:this.portable?null:this.uname,atime:this.portable?null:this.atime,ctime:this.portable?null:this.ctime}),s&&this.warn("TAR_ENTRY_INFO",`stripping ${s} from absolute path`,{entry:this,path:s+this.path}),this.header.encode()&&!this.noPax&&super.write(new Gu({atime:this.portable?null:this.atime,ctime:this.portable?null:this.ctime,gid:this.portable?null:this.gid,mtime:this.noMtime?null:this.mtime,path:this[Ce](this.path),linkpath:this.type==="Link"?this[Ce](this.linkpath):this.linkpath,size:this.size,uid:this.portable?null:this.uid,uname:this.portable?null:this.uname,dev:this.portable?null:this.readEntry.dev,ino:this.portable?null:this.readEntry.ino,nlink:this.portable?null:this.readEntry.nlink}).encode()),super.write(this.header.block),e.pipe(this)}[Ce](e){return Ju(e,this.prefix)}[hs](e){return Ku(e,this.type==="Directory",this.portable)}write(e){let t=e.length;if(t>this.blockRemain)throw new Error("writing more to entry than is appropriate");return this.blockRemain-=t,super.write(e)}end(){return this.blockRemain&&super.write(Buffer.alloc(this.blockRemain)),super.end()}});fs.Sync=mn;fs.Tar=cf;var lf=r=>r.isFile()?"File":r.isDirectory()?"Directory":r.isSymbolicLink()?"SymbolicLink":"Unsupported";Zu.exports=fs});var Qu=y((p0,Xu)=>{"use strict";u();Xu.exports=function(r){r.prototype[Symbol.iterator]=function*(){for(let e=this.head;e;e=e.next)yield e.value}}});var Dn=y((m0,ea)=>{"use strict";u();ea.exports=k;k.Node=At;k.create=k;function k(r){var e=this;if(e instanceof k||(e=new k),e.tail=null,e.head=null,e.length=0,r&&typeof r.forEach=="function")r.forEach(function(i){e.push(i)});else if(arguments.length>0)for(var t=0,s=arguments.length;t<s;t++)e.push(arguments[t]);return e}k.prototype.removeNode=function(r){if(r.list!==this)throw new Error("removing node which does not belong to this list");var e=r.next,t=r.prev;return e&&(e.prev=t),t&&(t.next=e),r===this.head&&(this.head=e),r===this.tail&&(this.tail=t),r.list.length--,r.next=null,r.prev=null,r.list=null,e};k.prototype.unshiftNode=function(r){if(r!==this.head){r.list&&r.list.removeNode(r);var e=this.head;r.list=this,r.next=e,e&&(e.prev=r),this.head=r,this.tail||(this.tail=r),this.length++}};k.prototype.pushNode=function(r){if(r!==this.tail){r.list&&r.list.removeNode(r);var e=this.tail;r.list=this,r.prev=e,e&&(e.next=r),this.tail=r,this.head||(this.head=r),this.length++}};k.prototype.push=function(){for(var r=0,e=arguments.length;r<e;r++)ff(this,arguments[r]);return this.length};k.prototype.unshift=function(){for(var r=0,e=arguments.length;r<e;r++)pf(this,arguments[r]);return this.length};k.prototype.pop=function(){if(!!this.tail){var r=this.tail.value;return this.tail=this.tail.prev,this.tail?this.tail.next=null:this.head=null,this.length--,r}};k.prototype.shift=function(){if(!!this.head){var r=this.head.value;return this.head=this.head.next,this.head?this.head.prev=null:this.tail=null,this.length--,r}};k.prototype.forEach=function(r,e){e=e||this;for(var t=this.head,s=0;t!==null;s++)r.call(e,t.value,s,this),t=t.next};k.prototype.forEachReverse=function(r,e){e=e||this;for(var t=this.tail,s=this.length-1;t!==null;s--)r.call(e,t.value,s,this),t=t.prev};k.prototype.get=function(r){for(var e=0,t=this.head;t!==null&&e<r;e++)t=t.next;if(e===r&&t!==null)return t.value};k.prototype.getReverse=function(r){for(var e=0,t=this.tail;t!==null&&e<r;e++)t=t.prev;if(e===r&&t!==null)return t.value};k.prototype.map=function(r,e){e=e||this;for(var t=new k,s=this.head;s!==null;)t.push(r.call(e,s.value,this)),s=s.next;return t};k.prototype.mapReverse=function(r,e){e=e||this;for(var t=new k,s=this.tail;s!==null;)t.push(r.call(e,s.value,this)),s=s.prev;return t};k.prototype.reduce=function(r,e){var t,s=this.head;if(arguments.length>1)t=e;else if(this.head)s=this.head.next,t=this.head.value;else throw new TypeError("Reduce of empty list with no initial value");for(var i=0;s!==null;i++)t=r(t,s.value,i),s=s.next;return t};k.prototype.reduceReverse=function(r,e){var t,s=this.tail;if(arguments.length>1)t=e;else if(this.tail)s=this.tail.prev,t=this.tail.value;else throw new TypeError("Reduce of empty list with no initial value");for(var i=this.length-1;s!==null;i--)t=r(t,s.value,i),s=s.prev;return t};k.prototype.toArray=function(){for(var r=new Array(this.length),e=0,t=this.head;t!==null;e++)r[e]=t.value,t=t.next;return r};k.prototype.toArrayReverse=function(){for(var r=new Array(this.length),e=0,t=this.tail;t!==null;e++)r[e]=t.value,t=t.prev;return r};k.prototype.slice=function(r,e){e=e||this.length,e<0&&(e+=this.length),r=r||0,r<0&&(r+=this.length);var t=new k;if(e<r||e<0)return t;r<0&&(r=0),e>this.length&&(e=this.length);for(var s=0,i=this.head;i!==null&&s<r;s++)i=i.next;for(;i!==null&&s<e;s++,i=i.next)t.push(i.value);return t};k.prototype.sliceReverse=function(r,e){e=e||this.length,e<0&&(e+=this.length),r=r||0,r<0&&(r+=this.length);var t=new k;if(e<r||e<0)return t;r<0&&(r=0),e>this.length&&(e=this.length);for(var s=this.length,i=this.tail;i!==null&&s>e;s--)i=i.prev;for(;i!==null&&s>r;s--,i=i.prev)t.push(i.value);return t};k.prototype.splice=function(r,e,...t){r>this.length&&(r=this.length-1),r<0&&(r=this.length+r);for(var s=0,i=this.head;i!==null&&s<r;s++)i=i.next;for(var n=[],s=0;i&&s<e;s++)n.push(i.value),i=this.removeNode(i);i===null&&(i=this.tail),i!==this.head&&i!==this.tail&&(i=i.prev);for(var s=0;s<t.length;s++)i=hf(this,i,t[s]);return n};k.prototype.reverse=function(){for(var r=this.head,e=this.tail,t=r;t!==null;t=t.prev){var s=t.prev;t.prev=t.next,t.next=s}return this.head=e,this.tail=r,this};function hf(r,e,t){var s=e===r.head?new At(t,null,e,r):new At(t,e,e.next,r);return s.next===null&&(r.tail=s),s.prev===null&&(r.head=s),r.length++,s}function ff(r,e){r.tail=new At(e,r.tail,null,r),r.head||(r.head=r.tail),r.length++}function pf(r,e){r.head=new At(e,null,r.head,r),r.tail||(r.tail=r.head),r.length++}function At(r,e,t,s){if(!(this instanceof At))return new At(r,e,t,s);this.list=s,this.value=r,e?(e.next=this,this.prev=e):this.prev=null,t?(t.prev=this,this.next=t):this.next=null}try{Qu()(k)}catch{}});var Cs=y((D0,ua)=>{"use strict";u();var ys=class{constructor(e,t){this.path=e||"./",this.absolute=t,this.entry=null,this.stat=null,this.readdir=null,this.pending=!1,this.ignore=!1,this.piped=!1}},mf=Yr(),df=Ji(),Df=is(),Sn=dn(),gf=Sn.Sync,Ef=Sn.Tar,yf=Dn(),ta=Buffer.alloc(1024),ds=Symbol("onStat"),ps=Symbol("ended"),we=Symbol("queue"),Vt=Symbol("current"),Ct=Symbol("process"),ms=Symbol("processing"),ra=Symbol("processJob"),Fe=Symbol("jobs"),gn=Symbol("jobDone"),Ds=Symbol("addFSEntry"),sa=Symbol("addTarEntry"),Cn=Symbol("stat"),wn=Symbol("readdir"),gs=Symbol("onreaddir"),Es=Symbol("pipe"),ia=Symbol("entry"),En=Symbol("entryOpt"),Fn=Symbol("writeEntryClass"),oa=Symbol("write"),yn=Symbol("ondrain"),As=require("fs"),na=require("path"),Af=us(),An=zt(),kn=Af(class extends mf{constructor(e){super(e),e=e||Object.create(null),this.opt=e,this.file=e.file||"",this.cwd=e.cwd||process.cwd(),this.maxReadSize=e.maxReadSize,this.preservePaths=!!e.preservePaths,this.strict=!!e.strict,this.noPax=!!e.noPax,this.prefix=An(e.prefix||""),this.linkCache=e.linkCache||new Map,this.statCache=e.statCache||new Map,this.readdirCache=e.readdirCache||new Map,this[Fn]=Sn,typeof e.onwarn=="function"&&this.on("warn",e.onwarn),this.portable=!!e.portable,this.zip=null,e.gzip?(typeof e.gzip!="object"&&(e.gzip={}),this.portable&&(e.gzip.portable=!0),this.zip=new df.Gzip(e.gzip),this.zip.on("data",t=>super.write(t)),this.zip.on("end",t=>super.end()),this.zip.on("drain",t=>this[yn]()),this.on("resume",t=>this.zip.resume())):this.on("drain",this[yn]),this.noDirRecurse=!!e.noDirRecurse,this.follow=!!e.follow,this.noMtime=!!e.noMtime,this.mtime=e.mtime||null,this.filter=typeof e.filter=="function"?e.filter:t=>!0,this[we]=new yf,this[Fe]=0,this.jobs=+e.jobs||4,this[ms]=!1,this[ps]=!1}[oa](e){return super.write(e)}add(e){return this.write(e),this}end(e){return e&&this.write(e),this[ps]=!0,this[Ct](),this}write(e){if(this[ps])throw new Error("write after end");return e instanceof Df?this[sa](e):this[Ds](e),this.flowing}[sa](e){let t=An(na.resolve(this.cwd,e.path));if(!this.filter(e.path,e))e.resume();else{let s=new ys(e.path,t,!1);s.entry=new Ef(e,this[En](s)),s.entry.on("end",i=>this[gn](s)),this[Fe]+=1,this[we].push(s)}this[Ct]()}[Ds](e){let t=An(na.resolve(this.cwd,e));this[we].push(new ys(e,t)),this[Ct]()}[Cn](e){e.pending=!0,this[Fe]+=1;let t=this.follow?"stat":"lstat";As[t](e.absolute,(s,i)=>{e.pending=!1,this[Fe]-=1,s?this.emit("error",s):this[ds](e,i)})}[ds](e,t){this.statCache.set(e.absolute,t),e.stat=t,this.filter(e.path,t)||(e.ignore=!0),this[Ct]()}[wn](e){e.pending=!0,this[Fe]+=1,As.readdir(e.absolute,(t,s)=>{if(e.pending=!1,this[Fe]-=1,t)return this.emit("error",t);this[gs](e,s)})}[gs](e,t){this.readdirCache.set(e.absolute,t),e.readdir=t,this[Ct]()}[Ct](){if(!this[ms]){this[ms]=!0;for(let e=this[we].head;e!==null&&this[Fe]<this.jobs;e=e.next)if(this[ra](e.value),e.value.ignore){let t=e.next;this[we].removeNode(e),e.next=t}this[ms]=!1,this[ps]&&!this[we].length&&this[Fe]===0&&(this.zip?this.zip.end(ta):(super.write(ta),super.end()))}}get[Vt](){return this[we]&&this[we].head&&this[we].head.value}[gn](e){this[we].shift(),this[Fe]-=1,this[Ct]()}[ra](e){if(!e.pending){if(e.entry){e===this[Vt]&&!e.piped&&this[Es](e);return}if(e.stat||(this.statCache.has(e.absolute)?this[ds](e,this.statCache.get(e.absolute)):this[Cn](e)),!!e.stat&&!e.ignore&&!(!this.noDirRecurse&&e.stat.isDirectory()&&!e.readdir&&(this.readdirCache.has(e.absolute)?this[gs](e,this.readdirCache.get(e.absolute)):this[wn](e),!e.readdir))){if(e.entry=this[ia](e),!e.entry){e.ignore=!0;return}e===this[Vt]&&!e.piped&&this[Es](e)}}}[En](e){return{onwarn:(t,s,i)=>this.warn(t,s,i),noPax:this.noPax,cwd:this.cwd,absolute:e.absolute,preservePaths:this.preservePaths,maxReadSize:this.maxReadSize,strict:this.strict,portable:this.portable,linkCache:this.linkCache,statCache:this.statCache,noMtime:this.noMtime,mtime:this.mtime,prefix:this.prefix}}[ia](e){this[Fe]+=1;try{return new this[Fn](e.path,this[En](e)).on("end",()=>this[gn](e)).on("error",t=>this.emit("error",t))}catch(t){this.emit("error",t)}}[yn](){this[Vt]&&this[Vt].entry&&this[Vt].entry.resume()}[Es](e){e.piped=!0,e.readdir&&e.readdir.forEach(i=>{let n=e.path,o=n==="./"?"":n.replace(/\/*$/,"/");this[Ds](o+i)});let t=e.entry,s=this.zip;s?t.on("data",i=>{s.write(i)||t.pause()}):t.on("data",i=>{super.write(i)||t.pause()})}pause(){return this.zip&&this.zip.pause(),super.pause()}}),bn=class extends kn{constructor(e){super(e),this[Fn]=gf}pause(){}resume(){}[Cn](e){let t=this.follow?"statSync":"lstatSync";this[ds](e,As[t](e.absolute))}[wn](e,t){this[gs](e,As.readdirSync(e.absolute))}[Es](e){let t=e.entry,s=this.zip;e.readdir&&e.readdir.forEach(i=>{let n=e.path,o=n==="./"?"":n.replace(/\/*$/,"/");this[Ds](o+i)}),s?t.on("data",i=>{s.write(i)}):t.on("data",i=>{super[oa](i)})}};kn.Sync=bn;ua.exports=kn});var rr=y(Sr=>{"use strict";u();var Cf=xi(),wf=require("events").EventEmitter,K=require("fs"),Rn=K.writev;if(!Rn){let r=process.binding("fs"),e=r.FSReqWrap||r.FSReqCallback;Rn=(t,s,i,n)=>{let o=(l,c)=>n(l,c,s),a=new e;a.oncomplete=o,r.writeBuffers(t,s,i,a)}}var er=Symbol("_autoClose"),pe=Symbol("_close"),br=Symbol("_ended"),R=Symbol("_fd"),aa=Symbol("_finished"),et=Symbol("_flags"),_n=Symbol("_flush"),Bn=Symbol("_handleChunk"),xn=Symbol("_makeBuf"),ks=Symbol("_mode"),ws=Symbol("_needDrain"),Xt=Symbol("_onerror"),tr=Symbol("_onopen"),vn=Symbol("_onread"),Kt=Symbol("_onwrite"),tt=Symbol("_open"),Me=Symbol("_path"),wt=Symbol("_pos"),be=Symbol("_queue"),Zt=Symbol("_read"),ca=Symbol("_readSize"),Qe=Symbol("_reading"),Fs=Symbol("_remain"),la=Symbol("_size"),bs=Symbol("_write"),Yt=Symbol("_writing"),Ss=Symbol("_defaultFlag"),Qt=Symbol("_errored"),_s=class extends Cf{constructor(e,t){if(t=t||{},super(t),this.readable=!0,this.writable=!1,typeof e!="string")throw new TypeError("path must be a string");this[Qt]=!1,this[R]=typeof t.fd=="number"?t.fd:null,this[Me]=e,this[ca]=t.readSize||16*1024*1024,this[Qe]=!1,this[la]=typeof t.size=="number"?t.size:1/0,this[Fs]=this[la],this[er]=typeof t.autoClose=="boolean"?t.autoClose:!0,typeof this[R]=="number"?this[Zt]():this[tt]()}get fd(){return this[R]}get path(){return this[Me]}write(){throw new TypeError("this is a readable stream")}end(){throw new TypeError("this is a readable stream")}[tt](){K.open(this[Me],"r",(e,t)=>this[tr](e,t))}[tr](e,t){e?this[Xt](e):(this[R]=t,this.emit("open",t),this[Zt]())}[xn](){return Buffer.allocUnsafe(Math.min(this[ca],this[Fs]))}[Zt](){if(!this[Qe]){this[Qe]=!0;let e=this[xn]();if(e.length===0)return process.nextTick(()=>this[vn](null,0,e));K.read(this[R],e,0,e.length,null,(t,s,i)=>this[vn](t,s,i))}}[vn](e,t,s){this[Qe]=!1,e?this[Xt](e):this[Bn](t,s)&&this[Zt]()}[pe](){if(this[er]&&typeof this[R]=="number"){let e=this[R];this[R]=null,K.close(e,t=>t?this.emit("error",t):this.emit("close"))}}[Xt](e){this[Qe]=!0,this[pe](),this.emit("error",e)}[Bn](e,t){let s=!1;return this[Fs]-=e,e>0&&(s=super.write(e<t.length?t.slice(0,e):t)),(e===0||this[Fs]<=0)&&(s=!1,this[pe](),super.end()),s}emit(e,t){switch(e){case"prefinish":case"finish":break;case"drain":typeof this[R]=="number"&&this[Zt]();break;case"error":return this[Qt]?void 0:(this[Qt]=!0,super.emit(e,t));default:return super.emit(e,t)}}},On=class extends _s{[tt](){let e=!0;try{this[tr](null,K.openSync(this[Me],"r")),e=!1}finally{e&&this[pe]()}}[Zt](){let e=!0;try{if(!this[Qe]){this[Qe]=!0;do{let t=this[xn](),s=t.length===0?0:K.readSync(this[R],t,0,t.length,null);if(!this[Bn](s,t))break}while(!0);this[Qe]=!1}e=!1}finally{e&&this[pe]()}}[pe](){if(this[er]&&typeof this[R]=="number"){let e=this[R];this[R]=null,K.closeSync(e),this.emit("close")}}},vs=class extends wf{constructor(e,t){t=t||{},super(t),this.readable=!1,this.writable=!0,this[Qt]=!1,this[Yt]=!1,this[br]=!1,this[ws]=!1,this[be]=[],this[Me]=e,this[R]=typeof t.fd=="number"?t.fd:null,this[ks]=t.mode===void 0?438:t.mode,this[wt]=typeof t.start=="number"?t.start:null,this[er]=typeof t.autoClose=="boolean"?t.autoClose:!0;let s=this[wt]!==null?"r+":"w";this[Ss]=t.flags===void 0,this[et]=this[Ss]?s:t.flags,this[R]===null&&this[tt]()}emit(e,t){if(e==="error"){if(this[Qt])return;this[Qt]=!0}return super.emit(e,t)}get fd(){return this[R]}get path(){return this[Me]}[Xt](e){this[pe](),this[Yt]=!0,this.emit("error",e)}[tt](){K.open(this[Me],this[et],this[ks],(e,t)=>this[tr](e,t))}[tr](e,t){this[Ss]&&this[et]==="r+"&&e&&e.code==="ENOENT"?(this[et]="w",this[tt]()):e?this[Xt](e):(this[R]=t,this.emit("open",t),this[_n]())}end(e,t){return e&&this.write(e,t),this[br]=!0,!this[Yt]&&!this[be].length&&typeof this[R]=="number"&&this[Kt](null,0),this}write(e,t){return typeof e=="string"&&(e=Buffer.from(e,t)),this[br]?(this.emit("error",new Error("write() after end()")),!1):this[R]===null||this[Yt]||this[be].length?(this[be].push(e),this[ws]=!0,!1):(this[Yt]=!0,this[bs](e),!0)}[bs](e){K.write(this[R],e,0,e.length,this[wt],(t,s)=>this[Kt](t,s))}[Kt](e,t){e?this[Xt](e):(this[wt]!==null&&(this[wt]+=t),this[be].length?this[_n]():(this[Yt]=!1,this[br]&&!this[aa]?(this[aa]=!0,this[pe](),this.emit("finish")):this[ws]&&(this[ws]=!1,this.emit("drain"))))}[_n](){if(this[be].length===0)this[br]&&this[Kt](null,0);else if(this[be].length===1)this[bs](this[be].pop());else{let e=this[be];this[be]=[],Rn(this[R],e,this[wt],(t,s)=>this[Kt](t,s))}}[pe](){if(this[er]&&typeof this[R]=="number"){let e=this[R];this[R]=null,K.close(e,t=>t?this.emit("error",t):this.emit("close"))}}},Pn=class extends vs{[tt](){let e;if(this[Ss]&&this[et]==="r+")try{e=K.openSync(this[Me],this[et],this[ks])}catch(t){if(t.code==="ENOENT")return this[et]="w",this[tt]();throw t}else e=K.openSync(this[Me],this[et],this[ks]);this[tr](null,e)}[pe](){if(this[er]&&typeof this[R]=="number"){let e=this[R];this[R]=null,K.closeSync(e),this.emit("close")}}[bs](e){let t=!0;try{this[Kt](null,K.writeSync(this[R],e,0,e.length,this[wt])),t=!1}finally{if(t)try{this[pe]()}catch{}}}};Sr.ReadStream=_s;Sr.ReadStreamSync=On;Sr.WriteStream=vs;Sr.WriteStreamSync=Pn});var Ls=y((y0,Ea)=>{"use strict";u();var Ff=us(),bf=Ht(),Sf=require("events"),kf=Dn(),_f=1024*1024,vf=is(),ha=os(),Rf=Ji(),{nextTick:Bf}=require("process"),Tn=Buffer.from([31,139]),ne=Symbol("state"),Ft=Symbol("writeEntry"),Ie=Symbol("readEntry"),Ln=Symbol("nextEntry"),fa=Symbol("processEntry"),oe=Symbol("extendedHeader"),kr=Symbol("globalExtendedHeader"),rt=Symbol("meta"),pa=Symbol("emitMeta"),O=Symbol("buffer"),je=Symbol("queue"),bt=Symbol("ended"),ma=Symbol("emittedEnd"),St=Symbol("emit"),Z=Symbol("unzip"),Rs=Symbol("consumeChunk"),Bs=Symbol("consumeChunkSub"),Nn=Symbol("consumeBody"),da=Symbol("consumeMeta"),Da=Symbol("consumeHeader"),xs=Symbol("consuming"),Mn=Symbol("bufferConcat"),In=Symbol("maybeEnd"),_r=Symbol("writing"),st=Symbol("aborted"),Os=Symbol("onDone"),kt=Symbol("sawValidEntry"),Ps=Symbol("sawNullBlock"),Ts=Symbol("sawEOF"),ga=Symbol("closeStream"),xf=r=>!0;Ea.exports=Ff(class extends Sf{constructor(e){e=e||{},super(e),this.file=e.file||"",this[kt]=null,this.on(Os,t=>{(this[ne]==="begin"||this[kt]===!1)&&this.warn("TAR_BAD_ARCHIVE","Unrecognized archive format")}),e.ondone?this.on(Os,e.ondone):this.on(Os,t=>{this.emit("prefinish"),this.emit("finish"),this.emit("end")}),this.strict=!!e.strict,this.maxMetaEntrySize=e.maxMetaEntrySize||_f,this.filter=typeof e.filter=="function"?e.filter:xf,this.writable=!0,this.readable=!1,this[je]=new kf,this[O]=null,this[Ie]=null,this[Ft]=null,this[ne]="begin",this[rt]="",this[oe]=null,this[kr]=null,this[bt]=!1,this[Z]=null,this[st]=!1,this[Ps]=!1,this[Ts]=!1,this.on("end",()=>this[ga]()),typeof e.onwarn=="function"&&this.on("warn",e.onwarn),typeof e.onentry=="function"&&this.on("entry",e.onentry)}[Da](e,t){this[kt]===null&&(this[kt]=!1);let s;try{s=new bf(e,t,this[oe],this[kr])}catch(i){return this.warn("TAR_ENTRY_INVALID",i)}if(s.nullBlock)this[Ps]?(this[Ts]=!0,this[ne]==="begin"&&(this[ne]="header"),this[St]("eof")):(this[Ps]=!0,this[St]("nullBlock"));else if(this[Ps]=!1,!s.cksumValid)this.warn("TAR_ENTRY_INVALID","checksum failure",{header:s});else if(!s.path)this.warn("TAR_ENTRY_INVALID","path is required",{header:s});else{let i=s.type;if(/^(Symbolic)?Link$/.test(i)&&!s.linkpath)this.warn("TAR_ENTRY_INVALID","linkpath required",{header:s});else if(!/^(Symbolic)?Link$/.test(i)&&s.linkpath)this.warn("TAR_ENTRY_INVALID","linkpath forbidden",{header:s});else{let n=this[Ft]=new vf(s,this[oe],this[kr]);if(!this[kt])if(n.remain){let o=()=>{n.invalid||(this[kt]=!0)};n.on("end",o)}else this[kt]=!0;n.meta?n.size>this.maxMetaEntrySize?(n.ignore=!0,this[St]("ignoredEntry",n),this[ne]="ignore",n.resume()):n.size>0&&(this[rt]="",n.on("data",o=>this[rt]+=o),this[ne]="meta"):(this[oe]=null,n.ignore=n.ignore||!this.filter(n.path,n),n.ignore?(this[St]("ignoredEntry",n),this[ne]=n.remain?"ignore":"header",n.resume()):(n.remain?this[ne]="body":(this[ne]="header",n.end()),this[Ie]?this[je].push(n):(this[je].push(n),this[Ln]())))}}}[ga](){Bf(()=>this.emit("close"))}[fa](e){let t=!0;return e?Array.isArray(e)?this.emit.apply(this,e):(this[Ie]=e,this.emit("entry",e),e.emittedEnd||(e.on("end",s=>this[Ln]()),t=!1)):(this[Ie]=null,t=!1),t}[Ln](){do;while(this[fa](this[je].shift()));if(!this[je].length){let e=this[Ie];!e||e.flowing||e.size===e.remain?this[_r]||this.emit("drain"):e.once("drain",s=>this.emit("drain"))}}[Nn](e,t){let s=this[Ft],i=s.blockRemain,n=i>=e.length&&t===0?e:e.slice(t,t+i);return s.write(n),s.blockRemain||(this[ne]="header",this[Ft]=null,s.end()),n.length}[da](e,t){let s=this[Ft],i=this[Nn](e,t);return this[Ft]||this[pa](s),i}[St](e,t,s){!this[je].length&&!this[Ie]?this.emit(e,t,s):this[je].push([e,t,s])}[pa](e){switch(this[St]("meta",this[rt]),e.type){case"ExtendedHeader":case"OldExtendedHeader":this[oe]=ha.parse(this[rt],this[oe],!1);break;case"GlobalExtendedHeader":this[kr]=ha.parse(this[rt],this[kr],!0);break;case"NextFileHasLongPath":case"OldGnuLongPath":this[oe]=this[oe]||Object.create(null),this[oe].path=this[rt].replace(/\0.*/,"");break;case"NextFileHasLongLinkpath":this[oe]=this[oe]||Object.create(null),this[oe].linkpath=this[rt].replace(/\0.*/,"");break;default:throw new Error("unknown meta: "+e.type)}}abort(e){this[st]=!0,this.emit("abort",e),this.warn("TAR_ABORT",e,{recoverable:!1})}write(e){if(this[st])return;if(this[Z]===null&&e){if(this[O]&&(e=Buffer.concat([this[O],e]),this[O]=null),e.length<Tn.length)return this[O]=e,!0;for(let s=0;this[Z]===null&&s<Tn.length;s++)e[s]!==Tn[s]&&(this[Z]=!1);if(this[Z]===null){let s=this[bt];this[bt]=!1,this[Z]=new Rf.Unzip,this[Z].on("data",n=>this[Rs](n)),this[Z].on("error",n=>this.abort(n)),this[Z].on("end",n=>{this[bt]=!0,this[Rs]()}),this[_r]=!0;let i=this[Z][s?"end":"write"](e);return this[_r]=!1,i}}this[_r]=!0,this[Z]?this[Z].write(e):this[Rs](e),this[_r]=!1;let t=this[je].length?!1:this[Ie]?this[Ie].flowing:!0;return!t&&!this[je].length&&this[Ie].once("drain",s=>this.emit("drain")),t}[Mn](e){e&&!this[st]&&(this[O]=this[O]?Buffer.concat([this[O],e]):e)}[In](){if(this[bt]&&!this[ma]&&!this[st]&&!this[xs]){this[ma]=!0;let e=this[Ft];if(e&&e.blockRemain){let t=this[O]?this[O].length:0;this.warn("TAR_BAD_ARCHIVE",`Truncated input (needed ${e.blockRemain} more bytes, only ${t} available)`,{entry:e}),this[O]&&e.write(this[O]),e.end()}this[St](Os)}}[Rs](e){if(this[xs])this[Mn](e);else if(!e&&!this[O])this[In]();else{if(this[xs]=!0,this[O]){this[Mn](e);let t=this[O];this[O]=null,this[Bs](t)}else this[Bs](e);for(;this[O]&&this[O].length>=512&&!this[st]&&!this[Ts];){let t=this[O];this[O]=null,this[Bs](t)}this[xs]=!1}(!this[O]||this[bt])&&this[In]()}[Bs](e){let t=0,s=e.length;for(;t+512<=s&&!this[st]&&!this[Ts];)switch(this[ne]){case"begin":case"header":this[Da](e,t),t+=512;break;case"ignore":case"body":t+=this[Nn](e,t);break;case"meta":t+=this[da](e,t);break;default:throw new Error("invalid state: "+this[ne])}t<s&&(this[O]?this[O]=Buffer.concat([e.slice(t),this[O]]):this[O]=e.slice(t))}end(e){this[st]||(this[Z]?this[Z].end(e):(this[bt]=!0,this.write(e)))}})});var Ns=y((A0,wa)=>{"use strict";u();var Of=jt(),Aa=Ls(),sr=require("fs"),Pf=rr(),ya=require("path"),jn=Jt();wa.exports=(r,e,t)=>{typeof r=="function"?(t=r,e=null,r={}):Array.isArray(r)&&(e=r,r={}),typeof e=="function"&&(t=e,e=null),e?e=Array.from(e):e=[];let s=Of(r);if(s.sync&&typeof t=="function")throw new TypeError("callback not supported for sync tar functions");if(!s.file&&typeof t=="function")throw new TypeError("callback only supported with file option");return e.length&&Lf(s,e),s.noResume||Tf(s),s.file&&s.sync?Nf(s):s.file?Mf(s,t):Ca(s)};var Tf=r=>{let e=r.onentry;r.onentry=e?t=>{e(t),t.resume()}:t=>t.resume()},Lf=(r,e)=>{let t=new Map(e.map(n=>[jn(n),!0])),s=r.filter,i=(n,o)=>{let a=o||ya.parse(n).root||".",l=n===a?!1:t.has(n)?t.get(n):i(ya.dirname(n),a);return t.set(n,l),l};r.filter=s?(n,o)=>s(n,o)&&i(jn(n)):n=>i(jn(n))},Nf=r=>{let e=Ca(r),t=r.file,s=!0,i;try{let n=sr.statSync(t),o=r.maxReadSize||16*1024*1024;if(n.size<o)e.end(sr.readFileSync(t));else{let a=0,l=Buffer.allocUnsafe(o);for(i=sr.openSync(t,"r");a<n.size;){let c=sr.readSync(i,l,0,o,a);a+=c,e.write(l.slice(0,c))}e.end()}s=!1}finally{if(s&&i)try{sr.closeSync(i)}catch{}}},Mf=(r,e)=>{let t=new Aa(r),s=r.maxReadSize||16*1024*1024,i=r.file,n=new Promise((o,a)=>{t.on("error",a),t.on("end",o),sr.stat(i,(l,c)=>{if(l)a(l);else{let h=new Pf.ReadStream(i,{readSize:s,size:c.size});h.on("error",a),h.pipe(t)}})});return e?n.then(e,e):n},Ca=r=>new Aa(r)});var va=y((C0,_a)=>{"use strict";u();var If=jt(),Ms=Cs(),Fa=rr(),ba=Ns(),Sa=require("path");_a.exports=(r,e,t)=>{if(typeof e=="function"&&(t=e),Array.isArray(r)&&(e=r,r={}),!e||!Array.isArray(e)||!e.length)throw new TypeError("no files or directories specified");e=Array.from(e);let s=If(r);if(s.sync&&typeof t=="function")throw new TypeError("callback not supported for sync tar functions");if(!s.file&&typeof t=="function")throw new TypeError("callback only supported with file option");return s.file&&s.sync?jf(s,e):s.file?qf(s,e,t):s.sync?$f(s,e):Uf(s,e)};var jf=(r,e)=>{let t=new Ms.Sync(r),s=new Fa.WriteStreamSync(r.file,{mode:r.mode||438});t.pipe(s),ka(t,e)},qf=(r,e,t)=>{let s=new Ms(r),i=new Fa.WriteStream(r.file,{mode:r.mode||438});s.pipe(i);let n=new Promise((o,a)=>{i.on("error",a),i.on("close",o),s.on("error",a)});return qn(s,e),t?n.then(t,t):n},ka=(r,e)=>{e.forEach(t=>{t.charAt(0)==="@"?ba({file:Sa.resolve(r.cwd,t.slice(1)),sync:!0,noResume:!0,onentry:s=>r.add(s)}):r.add(t)}),r.end()},qn=(r,e)=>{for(;e.length;){let t=e.shift();if(t.charAt(0)==="@")return ba({file:Sa.resolve(r.cwd,t.slice(1)),noResume:!0,onentry:s=>r.add(s)}).then(s=>qn(r,e));r.add(t)}r.end()},$f=(r,e)=>{let t=new Ms.Sync(r);return ka(t,e),t},Uf=(r,e)=>{let t=new Ms(r);return qn(t,e),t}});var $n=y((w0,La)=>{"use strict";u();var Wf=jt(),Ra=Cs(),ee=require("fs"),Ba=rr(),xa=Ns(),Oa=require("path"),Pa=Ht();La.exports=(r,e,t)=>{let s=Wf(r);if(!s.file)throw new TypeError("file is required");if(s.gzip)throw new TypeError("cannot append to compressed archives");if(!e||!Array.isArray(e)||!e.length)throw new TypeError("no files or directories specified");return e=Array.from(e),s.sync?zf(s,e):Hf(s,e,t)};var zf=(r,e)=>{let t=new Ra.Sync(r),s=!0,i,n;try{try{i=ee.openSync(r.file,"r+")}catch(l){if(l.code==="ENOENT")i=ee.openSync(r.file,"w+");else throw l}let o=ee.fstatSync(i),a=Buffer.alloc(512);e:for(n=0;n<o.size;n+=512){for(let h=0,d=0;h<512;h+=d){if(d=ee.readSync(i,a,h,a.length-h,n+h),n===0&&a[0]===31&&a[1]===139)throw new Error("cannot append to compressed archives");if(!d)break e}let l=new Pa(a);if(!l.cksumValid)break;let c=512*Math.ceil(l.size/512);if(n+c+512>o.size)break;n+=c,r.mtimeCache&&r.mtimeCache.set(l.path,l.mtime)}s=!1,Gf(r,t,n,i,e)}finally{if(s)try{ee.closeSync(i)}catch{}}},Gf=(r,e,t,s,i)=>{let n=new Ba.WriteStreamSync(r.file,{fd:s,start:t});e.pipe(n),Jf(e,i)},Hf=(r,e,t)=>{e=Array.from(e);let s=new Ra(r),i=(o,a,l)=>{let c=(w,P)=>{w?ee.close(o,S=>l(w)):l(null,P)},h=0;if(a===0)return c(null,0);let d=0,p=Buffer.alloc(512),E=(w,P)=>{if(w)return c(w);if(d+=P,d<512&&P)return ee.read(o,p,d,p.length-d,h+d,E);if(h===0&&p[0]===31&&p[1]===139)return c(new Error("cannot append to compressed archives"));if(d<512)return c(null,h);let S=new Pa(p);if(!S.cksumValid)return c(null,h);let x=512*Math.ceil(S.size/512);if(h+x+512>a||(h+=x+512,h>=a))return c(null,h);r.mtimeCache&&r.mtimeCache.set(S.path,S.mtime),d=0,ee.read(o,p,0,512,h,E)};ee.read(o,p,0,512,h,E)},n=new Promise((o,a)=>{s.on("error",a);let l="r+",c=(h,d)=>{if(h&&h.code==="ENOENT"&&l==="r+")return l="w+",ee.open(r.file,l,c);if(h)return a(h);ee.fstat(d,(p,E)=>{if(p)return ee.close(d,()=>a(p));i(d,E.size,(w,P)=>{if(w)return a(w);let S=new Ba.WriteStream(r.file,{fd:d,start:P});s.pipe(S),S.on("error",a),S.on("close",o),Ta(s,e)})})};ee.open(r.file,l,c)});return t?n.then(t,t):n},Jf=(r,e)=>{e.forEach(t=>{t.charAt(0)==="@"?xa({file:Oa.resolve(r.cwd,t.slice(1)),sync:!0,noResume:!0,onentry:s=>r.add(s)}):r.add(t)}),r.end()},Ta=(r,e)=>{for(;e.length;){let t=e.shift();if(t.charAt(0)==="@")return xa({file:Oa.resolve(r.cwd,t.slice(1)),noResume:!0,onentry:s=>r.add(s)}).then(s=>Ta(r,e));r.add(t)}r.end()}});var Ma=y((F0,Na)=>{"use strict";u();var Vf=jt(),Yf=$n();Na.exports=(r,e,t)=>{let s=Vf(r);if(!s.file)throw new TypeError("file is required");if(s.gzip)throw new TypeError("cannot append to compressed archives");if(!e||!Array.isArray(e)||!e.length)throw new TypeError("no files or directories specified");return e=Array.from(e),Kf(s),Yf(s,e,t)};var Kf=r=>{let e=r.filter;r.mtimeCache||(r.mtimeCache=new Map),r.filter=e?(t,s)=>e(t,s)&&!(r.mtimeCache.get(t)>s.mtime):(t,s)=>!(r.mtimeCache.get(t)>s.mtime)}});var qa=y((b0,ja)=>{u();var{promisify:Ia}=require("util"),it=require("fs"),Zf=r=>{if(!r)r={mode:511,fs:it};else if(typeof r=="object")r={mode:511,fs:it,...r};else if(typeof r=="number")r={mode:r,fs:it};else if(typeof r=="string")r={mode:parseInt(r,8),fs:it};else throw new TypeError("invalid options argument");return r.mkdir=r.mkdir||r.fs.mkdir||it.mkdir,r.mkdirAsync=Ia(r.mkdir),r.stat=r.stat||r.fs.stat||it.stat,r.statAsync=Ia(r.stat),r.statSync=r.statSync||r.fs.statSync||it.statSync,r.mkdirSync=r.mkdirSync||r.fs.mkdirSync||it.mkdirSync,r};ja.exports=Zf});var Ua=y((S0,$a)=>{u();var Xf=process.env.__TESTING_MKDIRP_PLATFORM__||process.platform,{resolve:Qf,parse:ep}=require("path"),tp=r=>{if(/\0/.test(r))throw Object.assign(new TypeError("path must be a string without null bytes"),{path:r,code:"ERR_INVALID_ARG_VALUE"});if(r=Qf(r),Xf==="win32"){let e=/[*|"<>?:]/,{root:t}=ep(r);if(e.test(r.substr(t.length)))throw Object.assign(new Error("Illegal characters in path."),{path:r,code:"EINVAL"})}return r};$a.exports=tp});var Ja=y((k0,Ha)=>{u();var{dirname:Wa}=require("path"),za=(r,e,t=void 0)=>t===e?Promise.resolve():r.statAsync(e).then(s=>s.isDirectory()?t:void 0,s=>s.code==="ENOENT"?za(r,Wa(e),e):void 0),Ga=(r,e,t=void 0)=>{if(t!==e)try{return r.statSync(e).isDirectory()?t:void 0}catch(s){return s.code==="ENOENT"?Ga(r,Wa(e),e):void 0}};Ha.exports={findMade:za,findMadeSync:Ga}});var zn=y((_0,Ya)=>{u();var{dirname:Va}=require("path"),Un=(r,e,t)=>{e.recursive=!1;let s=Va(r);return s===r?e.mkdirAsync(r,e).catch(i=>{if(i.code!=="EISDIR")throw i}):e.mkdirAsync(r,e).then(()=>t||r,i=>{if(i.code==="ENOENT")return Un(s,e).then(n=>Un(r,e,n));if(i.code!=="EEXIST"&&i.code!=="EROFS")throw i;return e.statAsync(r).then(n=>{if(n.isDirectory())return t;throw i},()=>{throw i})})},Wn=(r,e,t)=>{let s=Va(r);if(e.recursive=!1,s===r)try{return e.mkdirSync(r,e)}catch(i){if(i.code!=="EISDIR")throw i;return}try{return e.mkdirSync(r,e),t||r}catch(i){if(i.code==="ENOENT")return Wn(r,e,Wn(s,e,t));if(i.code!=="EEXIST"&&i.code!=="EROFS")throw i;try{if(!e.statSync(r).isDirectory())throw i}catch{throw i}}};Ya.exports={mkdirpManual:Un,mkdirpManualSync:Wn}});var Xa=y((v0,Za)=>{u();var{dirname:Ka}=require("path"),{findMade:rp,findMadeSync:sp}=Ja(),{mkdirpManual:ip,mkdirpManualSync:np}=zn(),op=(r,e)=>(e.recursive=!0,Ka(r)===r?e.mkdirAsync(r,e):rp(e,r).then(s=>e.mkdirAsync(r,e).then(()=>s).catch(i=>{if(i.code==="ENOENT")return ip(r,e);throw i}))),up=(r,e)=>{if(e.recursive=!0,Ka(r)===r)return e.mkdirSync(r,e);let s=sp(e,r);try{return e.mkdirSync(r,e),s}catch(i){if(i.code==="ENOENT")return np(r,e);throw i}};Za.exports={mkdirpNative:op,mkdirpNativeSync:up}});var rc=y((R0,tc)=>{u();var Qa=require("fs"),ap=process.env.__TESTING_MKDIRP_NODE_VERSION__||process.version,Gn=ap.replace(/^v/,"").split("."),ec=+Gn[0]>10||+Gn[0]==10&&+Gn[1]>=12,cp=ec?r=>r.mkdir===Qa.mkdir:()=>!1,lp=ec?r=>r.mkdirSync===Qa.mkdirSync:()=>!1;tc.exports={useNative:cp,useNativeSync:lp}});var ac=y((B0,uc)=>{u();var ir=qa(),nr=Ua(),{mkdirpNative:sc,mkdirpNativeSync:ic}=Xa(),{mkdirpManual:nc,mkdirpManualSync:oc}=zn(),{useNative:hp,useNativeSync:fp}=rc(),or=(r,e)=>(r=nr(r),e=ir(e),hp(e)?sc(r,e):nc(r,e)),pp=(r,e)=>(r=nr(r),e=ir(e),fp(e)?ic(r,e):oc(r,e));or.sync=pp;or.native=(r,e)=>sc(nr(r),ir(e));or.manual=(r,e)=>nc(nr(r),ir(e));or.nativeSync=(r,e)=>ic(nr(r),ir(e));or.manualSync=(r,e)=>oc(nr(r),ir(e));uc.exports=or});var dc=y((x0,mc)=>{"use strict";u();var ue=require("fs"),_t=require("path"),mp=ue.lchown?"lchown":"chown",dp=ue.lchownSync?"lchownSync":"chownSync",lc=ue.lchown&&!process.version.match(/v1[1-9]+\./)&&!process.version.match(/v10\.[6-9]/),cc=(r,e,t)=>{try{return ue[dp](r,e,t)}catch(s){if(s.code!=="ENOENT")throw s}},Dp=(r,e,t)=>{try{return ue.chownSync(r,e,t)}catch(s){if(s.code!=="ENOENT")throw s}},gp=lc?(r,e,t,s)=>i=>{!i||i.code!=="EISDIR"?s(i):ue.chown(r,e,t,s)}:(r,e,t,s)=>s,Hn=lc?(r,e,t)=>{try{return cc(r,e,t)}catch(s){if(s.code!=="EISDIR")throw s;Dp(r,e,t)}}:(r,e,t)=>cc(r,e,t),Ep=process.version,hc=(r,e,t)=>ue.readdir(r,e,t),yp=(r,e)=>ue.readdirSync(r,e);/^v4\./.test(Ep)&&(hc=(r,e,t)=>ue.readdir(r,t));var Is=(r,e,t,s)=>{ue[mp](r,e,t,gp(r,e,t,i=>{s(i&&i.code!=="ENOENT"?i:null)}))},fc=(r,e,t,s,i)=>{if(typeof e=="string")return ue.lstat(_t.resolve(r,e),(n,o)=>{if(n)return i(n.code!=="ENOENT"?n:null);o.name=e,fc(r,o,t,s,i)});if(e.isDirectory())Jn(_t.resolve(r,e.name),t,s,n=>{if(n)return i(n);let o=_t.resolve(r,e.name);Is(o,t,s,i)});else{let n=_t.resolve(r,e.name);Is(n,t,s,i)}},Jn=(r,e,t,s)=>{hc(r,{withFileTypes:!0},(i,n)=>{if(i){if(i.code==="ENOENT")return s();if(i.code!=="ENOTDIR"&&i.code!=="ENOTSUP")return s(i)}if(i||!n.length)return Is(r,e,t,s);let o=n.length,a=null,l=c=>{if(!a){if(c)return s(a=c);if(--o===0)return Is(r,e,t,s)}};n.forEach(c=>fc(r,c,e,t,l))})},Ap=(r,e,t,s)=>{if(typeof e=="string")try{let i=ue.lstatSync(_t.resolve(r,e));i.name=e,e=i}catch(i){if(i.code==="ENOENT")return;throw i}e.isDirectory()&&pc(_t.resolve(r,e.name),t,s),Hn(_t.resolve(r,e.name),t,s)},pc=(r,e,t)=>{let s;try{s=yp(r,{withFileTypes:!0})}catch(i){if(i.code==="ENOENT")return;if(i.code==="ENOTDIR"||i.code==="ENOTSUP")return Hn(r,e,t);throw i}return s&&s.length&&s.forEach(i=>Ap(r,i,e,t)),Hn(r,e,t)};mc.exports=Jn;Jn.sync=pc});var yc=y((O0,Vn)=>{"use strict";u();var Dc=ac(),ae=require("fs"),js=require("path"),gc=dc(),me=zt(),qs=class extends Error{constructor(e,t){super("Cannot extract through symbolic link"),this.path=t,this.symlink=e}get name(){return"SylinkError"}},$s=class extends Error{constructor(e,t){super(t+": Cannot cd into '"+e+"'"),this.path=e,this.code=t}get name(){return"CwdError"}},Us=(r,e)=>r.get(me(e)),vr=(r,e,t)=>r.set(me(e),t),Cp=(r,e)=>{ae.stat(r,(t,s)=>{(t||!s.isDirectory())&&(t=new $s(r,t&&t.code||"ENOTDIR")),e(t)})};Vn.exports=(r,e,t)=>{r=me(r);let s=e.umask,i=e.mode|448,n=(i&s)!==0,o=e.uid,a=e.gid,l=typeof o=="number"&&typeof a=="number"&&(o!==e.processUid||a!==e.processGid),c=e.preserve,h=e.unlink,d=e.cache,p=me(e.cwd),E=(S,x)=>{S?t(S):(vr(d,r,!0),x&&l?gc(x,o,a,ft=>E(ft)):n?ae.chmod(r,i,t):t())};if(d&&Us(d,r)===!0)return E();if(r===p)return Cp(r,E);if(c)return Dc(r,{mode:i}).then(S=>E(null,S),E);let P=me(js.relative(p,r)).split("/");Ws(p,P,i,d,h,p,null,E)};var Ws=(r,e,t,s,i,n,o,a)=>{if(!e.length)return a(null,o);let l=e.shift(),c=me(js.resolve(r+"/"+l));if(Us(s,c))return Ws(c,e,t,s,i,n,o,a);ae.mkdir(c,t,Ec(c,e,t,s,i,n,o,a))},Ec=(r,e,t,s,i,n,o,a)=>l=>{l?ae.lstat(r,(c,h)=>{if(c)c.path=c.path&&me(c.path),a(c);else if(h.isDirectory())Ws(r,e,t,s,i,n,o,a);else if(i)ae.unlink(r,d=>{if(d)return a(d);ae.mkdir(r,t,Ec(r,e,t,s,i,n,o,a))});else{if(h.isSymbolicLink())return a(new qs(r,r+"/"+e.join("/")));a(l)}}):(o=o||r,Ws(r,e,t,s,i,n,o,a))},wp=r=>{let e=!1,t="ENOTDIR";try{e=ae.statSync(r).isDirectory()}catch(s){t=s.code}finally{if(!e)throw new $s(r,t)}};Vn.exports.sync=(r,e)=>{r=me(r);let t=e.umask,s=e.mode|448,i=(s&t)!==0,n=e.uid,o=e.gid,a=typeof n=="number"&&typeof o=="number"&&(n!==e.processUid||o!==e.processGid),l=e.preserve,c=e.unlink,h=e.cache,d=me(e.cwd),p=S=>{vr(h,r,!0),S&&a&&gc.sync(S,n,o),i&&ae.chmodSync(r,s)};if(h&&Us(h,r)===!0)return p();if(r===d)return wp(d),p();if(l)return p(Dc.sync(r,s));let w=me(js.relative(d,r)).split("/"),P=null;for(let S=w.shift(),x=d;S&&(x+="/"+S);S=w.shift())if(x=me(js.resolve(x)),!Us(h,x))try{ae.mkdirSync(x,s),P=P||x,vr(h,x,!0)}catch{let De=ae.lstatSync(x);if(De.isDirectory()){vr(h,x,!0);continue}else if(c){ae.unlinkSync(x),ae.mkdirSync(x,s),P=P||x,vr(h,x,!0);continue}else if(De.isSymbolicLink())return new qs(x,x+"/"+w.join("/"))}return p(P)}});var Kn=y((P0,Ac)=>{u();var Yn=Object.create(null),{hasOwnProperty:Fp}=Object.prototype;Ac.exports=r=>(Fp.call(Yn,r)||(Yn[r]=r.normalize("NFKD")),Yn[r])});var bc=y((T0,Fc)=>{u();var Cc=require("assert"),bp=Kn(),Sp=Jt(),{join:wc}=require("path"),kp=process.env.TESTING_TAR_FAKE_PLATFORM||process.platform,_p=kp==="win32";Fc.exports=()=>{let r=new Map,e=new Map,t=c=>c.split("/").slice(0,-1).reduce((d,p)=>(d.length&&(p=wc(d[d.length-1],p)),d.push(p||"/"),d),[]),s=new Set,i=c=>{let h=e.get(c);if(!h)throw new Error("function does not have any path reservations");return{paths:h.paths.map(d=>r.get(d)),dirs:[...h.dirs].map(d=>r.get(d))}},n=c=>{let{paths:h,dirs:d}=i(c);return h.every(p=>p[0]===c)&&d.every(p=>p[0]instanceof Set&&p[0].has(c))},o=c=>s.has(c)||!n(c)?!1:(s.add(c),c(()=>a(c)),!0),a=c=>{if(!s.has(c))return!1;let{paths:h,dirs:d}=e.get(c),p=new Set;return h.forEach(E=>{let w=r.get(E);Cc.equal(w[0],c),w.length===1?r.delete(E):(w.shift(),typeof w[0]=="function"?p.add(w[0]):w[0].forEach(P=>p.add(P)))}),d.forEach(E=>{let w=r.get(E);Cc(w[0]instanceof Set),w[0].size===1&&w.length===1?r.delete(E):w[0].size===1?(w.shift(),p.add(w[0])):w[0].delete(c)}),s.delete(c),p.forEach(E=>o(E)),!0};return{check:n,reserve:(c,h)=>{c=_p?["win32 parallelization disabled"]:c.map(p=>bp(Sp(wc(p))).toLowerCase());let d=new Set(c.map(p=>t(p)).reduce((p,E)=>p.concat(E)));return e.set(h,{dirs:d,paths:c}),c.forEach(p=>{let E=r.get(p);E?E.push(h):r.set(p,[h])}),d.forEach(p=>{let E=r.get(p);E?E[E.length-1]instanceof Set?E[E.length-1].add(h):E.push(new Set([h])):r.set(p,[new Set([h])])}),o(h)}}}});var _c=y((L0,kc)=>{u();var vp=process.env.__FAKE_PLATFORM__||process.platform,Rp=vp==="win32",Bp=global.__FAKE_TESTING_FS__||require("fs"),{O_CREAT:xp,O_TRUNC:Op,O_WRONLY:Pp,UV_FS_O_FILEMAP:Sc=0}=Bp.constants,Tp=Rp&&!!Sc,Lp=512*1024,Np=Sc|Op|xp|Pp;kc.exports=Tp?r=>r<Lp?Np:"w":()=>"w"});var no=y((N0,$c)=>{"use strict";u();var Mp=require("assert"),Ip=Ls(),_=require("fs"),jp=rr(),qe=require("path"),Ic=yc(),vc=sn(),qp=bc(),$p=nn(),te=zt(),Up=Jt(),Wp=Kn(),Rc=Symbol("onEntry"),Qn=Symbol("checkFs"),Bc=Symbol("checkFs2"),Hs=Symbol("pruneCache"),eo=Symbol("isReusable"),ce=Symbol("makeFs"),to=Symbol("file"),ro=Symbol("directory"),Js=Symbol("link"),xc=Symbol("symlink"),Oc=Symbol("hardlink"),Pc=Symbol("unsupported"),Tc=Symbol("checkPath"),nt=Symbol("mkdir"),z=Symbol("onError"),zs=Symbol("pending"),Lc=Symbol("pend"),ur=Symbol("unpend"),Zn=Symbol("ended"),Xn=Symbol("maybeClose"),so=Symbol("skip"),Rr=Symbol("doChown"),Br=Symbol("uid"),xr=Symbol("gid"),Or=Symbol("checkedCwd"),jc=require("crypto"),qc=_c(),zp=process.env.TESTING_TAR_FAKE_PLATFORM||process.platform,Pr=zp==="win32",Gp=(r,e)=>{if(!Pr)return _.unlink(r,e);let t=r+".DELETE."+jc.randomBytes(16).toString("hex");_.rename(r,t,s=>{if(s)return e(s);_.unlink(t,e)})},Hp=r=>{if(!Pr)return _.unlinkSync(r);let e=r+".DELETE."+jc.randomBytes(16).toString("hex");_.renameSync(r,e),_.unlinkSync(e)},Nc=(r,e,t)=>r===r>>>0?r:e===e>>>0?e:t,Mc=r=>Wp(Up(te(r))).toLowerCase(),Jp=(r,e)=>{e=Mc(e);for(let t of r.keys()){let s=Mc(t);(s===e||s.indexOf(e+"/")===0)&&r.delete(t)}},Vp=r=>{for(let e of r.keys())r.delete(e)},Tr=class extends Ip{constructor(e){if(e||(e={}),e.ondone=t=>{this[Zn]=!0,this[Xn]()},super(e),this[Or]=!1,this.reservations=qp(),this.transform=typeof e.transform=="function"?e.transform:null,this.writable=!0,this.readable=!1,this[zs]=0,this[Zn]=!1,this.dirCache=e.dirCache||new Map,typeof e.uid=="number"||typeof e.gid=="number"){if(typeof e.uid!="number"||typeof e.gid!="number")throw new TypeError("cannot set owner without number uid and gid");if(e.preserveOwner)throw new TypeError("cannot preserve owner in archive and also set owner explicitly");this.uid=e.uid,this.gid=e.gid,this.setOwner=!0}else this.uid=null,this.gid=null,this.setOwner=!1;e.preserveOwner===void 0&&typeof e.uid!="number"?this.preserveOwner=process.getuid&&process.getuid()===0:this.preserveOwner=!!e.preserveOwner,this.processUid=(this.preserveOwner||this.setOwner)&&process.getuid?process.getuid():null,this.processGid=(this.preserveOwner||this.setOwner)&&process.getgid?process.getgid():null,this.forceChown=e.forceChown===!0,this.win32=!!e.win32||Pr,this.newer=!!e.newer,this.keep=!!e.keep,this.noMtime=!!e.noMtime,this.preservePaths=!!e.preservePaths,this.unlink=!!e.unlink,this.cwd=te(qe.resolve(e.cwd||process.cwd())),this.strip=+e.strip||0,this.processUmask=e.noChmod?0:process.umask(),this.umask=typeof e.umask=="number"?e.umask:this.processUmask,this.dmode=e.dmode||511&~this.umask,this.fmode=e.fmode||438&~this.umask,this.on("entry",t=>this[Rc](t))}warn(e,t,s={}){return(e==="TAR_BAD_ARCHIVE"||e==="TAR_ABORT")&&(s.recoverable=!1),super.warn(e,t,s)}[Xn](){this[Zn]&&this[zs]===0&&(this.emit("prefinish"),this.emit("finish"),this.emit("end"))}[Tc](e){if(this.strip){let t=te(e.path).split("/");if(t.length<this.strip)return!1;if(e.path=t.slice(this.strip).join("/"),e.type==="Link"){let s=te(e.linkpath).split("/");if(s.length>=this.strip)e.linkpath=s.slice(this.strip).join("/");else return!1}}if(!this.preservePaths){let t=te(e.path),s=t.split("/");if(s.includes("..")||Pr&&/^[a-z]:\.\.$/i.test(s[0]))return this.warn("TAR_ENTRY_ERROR","path contains '..'",{entry:e,path:t}),!1;let[i,n]=$p(t);i&&(e.path=n,this.warn("TAR_ENTRY_INFO",`stripping ${i} from absolute path`,{entry:e,path:t}))}if(qe.isAbsolute(e.path)?e.absolute=te(qe.resolve(e.path)):e.absolute=te(qe.resolve(this.cwd,e.path)),!this.preservePaths&&e.absolute.indexOf(this.cwd+"/")!==0&&e.absolute!==this.cwd)return this.warn("TAR_ENTRY_ERROR","path escaped extraction target",{entry:e,path:te(e.path),resolvedPath:e.absolute,cwd:this.cwd}),!1;if(e.absolute===this.cwd&&e.type!=="Directory"&&e.type!=="GNUDumpDir")return!1;if(this.win32){let{root:t}=qe.win32.parse(e.absolute);e.absolute=t+vc.encode(e.absolute.slice(t.length));let{root:s}=qe.win32.parse(e.path);e.path=s+vc.encode(e.path.slice(s.length))}return!0}[Rc](e){if(!this[Tc](e))return e.resume();switch(Mp.equal(typeof e.absolute,"string"),e.type){case"Directory":case"GNUDumpDir":e.mode&&(e.mode=e.mode|448);case"File":case"OldFile":case"ContiguousFile":case"Link":case"SymbolicLink":return this[Qn](e);case"CharacterDevice":case"BlockDevice":case"FIFO":default:return this[Pc](e)}}[z](e,t){e.name==="CwdError"?this.emit("error",e):(this.warn("TAR_ENTRY_ERROR",e,{entry:t}),this[ur](),t.resume())}[nt](e,t,s){Ic(te(e),{uid:this.uid,gid:this.gid,processUid:this.processUid,processGid:this.processGid,umask:this.processUmask,preserve:this.preservePaths,unlink:this.unlink,cache:this.dirCache,cwd:this.cwd,mode:t,noChmod:this.noChmod},s)}[Rr](e){return this.forceChown||this.preserveOwner&&(typeof e.uid=="number"&&e.uid!==this.processUid||typeof e.gid=="number"&&e.gid!==this.processGid)||typeof this.uid=="number"&&this.uid!==this.processUid||typeof this.gid=="number"&&this.gid!==this.processGid}[Br](e){return Nc(this.uid,e.uid,this.processUid)}[xr](e){return Nc(this.gid,e.gid,this.processGid)}[to](e,t){let s=e.mode&4095||this.fmode,i=new jp.WriteStream(e.absolute,{flags:qc(e.size),mode:s,autoClose:!1});i.on("error",l=>{i.fd&&_.close(i.fd,()=>{}),i.write=()=>!0,this[z](l,e),t()});let n=1,o=l=>{if(l){i.fd&&_.close(i.fd,()=>{}),this[z](l,e),t();return}--n===0&&_.close(i.fd,c=>{c?this[z](c,e):this[ur](),t()})};i.on("finish",l=>{let c=e.absolute,h=i.fd;if(e.mtime&&!this.noMtime){n++;let d=e.atime||new Date,p=e.mtime;_.futimes(h,d,p,E=>E?_.utimes(c,d,p,w=>o(w&&E)):o())}if(this[Rr](e)){n++;let d=this[Br](e),p=this[xr](e);_.fchown(h,d,p,E=>E?_.chown(c,d,p,w=>o(w&&E)):o())}o()});let a=this.transform&&this.transform(e)||e;a!==e&&(a.on("error",l=>{this[z](l,e),t()}),e.pipe(a)),a.pipe(i)}[ro](e,t){let s=e.mode&4095||this.dmode;this[nt](e.absolute,s,i=>{if(i){this[z](i,e),t();return}let n=1,o=a=>{--n===0&&(t(),this[ur](),e.resume())};e.mtime&&!this.noMtime&&(n++,_.utimes(e.absolute,e.atime||new Date,e.mtime,o)),this[Rr](e)&&(n++,_.chown(e.absolute,this[Br](e),this[xr](e),o)),o()})}[Pc](e){e.unsupported=!0,this.warn("TAR_ENTRY_UNSUPPORTED",`unsupported entry type: ${e.type}`,{entry:e}),e.resume()}[xc](e,t){this[Js](e,e.linkpath,"symlink",t)}[Oc](e,t){let s=te(qe.resolve(this.cwd,e.linkpath));this[Js](e,s,"link",t)}[Lc](){this[zs]++}[ur](){this[zs]--,this[Xn]()}[so](e){this[ur](),e.resume()}[eo](e,t){return e.type==="File"&&!this.unlink&&t.isFile()&&t.nlink<=1&&!Pr}[Qn](e){this[Lc]();let t=[e.path];e.linkpath&&t.push(e.linkpath),this.reservations.reserve(t,s=>this[Bc](e,s))}[Hs](e){e.type==="SymbolicLink"?Vp(this.dirCache):e.type!=="Directory"&&Jp(this.dirCache,e.absolute)}[Bc](e,t){this[Hs](e);let s=a=>{this[Hs](e),t(a)},i=()=>{this[nt](this.cwd,this.dmode,a=>{if(a){this[z](a,e),s();return}this[Or]=!0,n()})},n=()=>{if(e.absolute!==this.cwd){let a=te(qe.dirname(e.absolute));if(a!==this.cwd)return this[nt](a,this.dmode,l=>{if(l){this[z](l,e),s();return}o()})}o()},o=()=>{_.lstat(e.absolute,(a,l)=>{if(l&&(this.keep||this.newer&&l.mtime>e.mtime)){this[so](e),s();return}if(a||this[eo](e,l))return this[ce](null,e,s);if(l.isDirectory()){if(e.type==="Directory"){let c=!this.noChmod&&e.mode&&(l.mode&4095)!==e.mode,h=d=>this[ce](d,e,s);return c?_.chmod(e.absolute,e.mode,h):h()}if(e.absolute!==this.cwd)return _.rmdir(e.absolute,c=>this[ce](c,e,s))}if(e.absolute===this.cwd)return this[ce](null,e,s);Gp(e.absolute,c=>this[ce](c,e,s))})};this[Or]?n():i()}[ce](e,t,s){if(e){this[z](e,t),s();return}switch(t.type){case"File":case"OldFile":case"ContiguousFile":return this[to](t,s);case"Link":return this[Oc](t,s);case"SymbolicLink":return this[xc](t,s);case"Directory":case"GNUDumpDir":return this[ro](t,s)}}[Js](e,t,s,i){_[s](t,e.absolute,n=>{n?this[z](n,e):(this[ur](),e.resume()),i()})}},Gs=r=>{try{return[null,r()]}catch(e){return[e,null]}},io=class extends Tr{[ce](e,t){return super[ce](e,t,()=>{})}[Qn](e){if(this[Hs](e),!this[Or]){let n=this[nt](this.cwd,this.dmode);if(n)return this[z](n,e);this[Or]=!0}if(e.absolute!==this.cwd){let n=te(qe.dirname(e.absolute));if(n!==this.cwd){let o=this[nt](n,this.dmode);if(o)return this[z](o,e)}}let[t,s]=Gs(()=>_.lstatSync(e.absolute));if(s&&(this.keep||this.newer&&s.mtime>e.mtime))return this[so](e);if(t||this[eo](e,s))return this[ce](null,e);if(s.isDirectory()){if(e.type==="Directory"){let o=!this.noChmod&&e.mode&&(s.mode&4095)!==e.mode,[a]=o?Gs(()=>{_.chmodSync(e.absolute,e.mode)}):[];return this[ce](a,e)}let[n]=Gs(()=>_.rmdirSync(e.absolute));this[ce](n,e)}let[i]=e.absolute===this.cwd?[]:Gs(()=>Hp(e.absolute));this[ce](i,e)}[to](e,t){let s=e.mode&4095||this.fmode,i=a=>{let l;try{_.closeSync(n)}catch(c){l=c}(a||l)&&this[z](a||l,e),t()},n;try{n=_.openSync(e.absolute,qc(e.size),s)}catch(a){return i(a)}let o=this.transform&&this.transform(e)||e;o!==e&&(o.on("error",a=>this[z](a,e)),e.pipe(o)),o.on("data",a=>{try{_.writeSync(n,a,0,a.length)}catch(l){i(l)}}),o.on("end",a=>{let l=null;if(e.mtime&&!this.noMtime){let c=e.atime||new Date,h=e.mtime;try{_.futimesSync(n,c,h)}catch(d){try{_.utimesSync(e.absolute,c,h)}catch{l=d}}}if(this[Rr](e)){let c=this[Br](e),h=this[xr](e);try{_.fchownSync(n,c,h)}catch(d){try{_.chownSync(e.absolute,c,h)}catch{l=l||d}}}i(l)})}[ro](e,t){let s=e.mode&4095||this.dmode,i=this[nt](e.absolute,s);if(i){this[z](i,e),t();return}if(e.mtime&&!this.noMtime)try{_.utimesSync(e.absolute,e.atime||new Date,e.mtime)}catch{}if(this[Rr](e))try{_.chownSync(e.absolute,this[Br](e),this[xr](e))}catch{}t(),e.resume()}[nt](e,t){try{return Ic.sync(te(e),{uid:this.uid,gid:this.gid,processUid:this.processUid,processGid:this.processGid,umask:this.processUmask,preserve:this.preservePaths,unlink:this.unlink,cache:this.dirCache,cwd:this.cwd,mode:t})}catch(s){return s}}[Js](e,t,s,i){try{_[s+"Sync"](t,e.absolute),i(),e.resume()}catch(n){return this[z](n,e)}}};Tr.Sync=io;$c.exports=Tr});var Hc=y((M0,Gc)=>{"use strict";u();var Yp=jt(),Vs=no(),Wc=require("fs"),zc=rr(),Uc=require("path"),oo=Jt();Gc.exports=(r,e,t)=>{typeof r=="function"?(t=r,e=null,r={}):Array.isArray(r)&&(e=r,r={}),typeof e=="function"&&(t=e,e=null),e?e=Array.from(e):e=[];let s=Yp(r);if(s.sync&&typeof t=="function")throw new TypeError("callback not supported for sync tar functions");if(!s.file&&typeof t=="function")throw new TypeError("callback only supported with file option");return e.length&&Kp(s,e),s.file&&s.sync?Zp(s):s.file?Xp(s,t):s.sync?Qp(s):em(s)};var Kp=(r,e)=>{let t=new Map(e.map(n=>[oo(n),!0])),s=r.filter,i=(n,o)=>{let a=o||Uc.parse(n).root||".",l=n===a?!1:t.has(n)?t.get(n):i(Uc.dirname(n),a);return t.set(n,l),l};r.filter=s?(n,o)=>s(n,o)&&i(oo(n)):n=>i(oo(n))},Zp=r=>{let e=new Vs.Sync(r),t=r.file,s=Wc.statSync(t),i=r.maxReadSize||16*1024*1024;new zc.ReadStreamSync(t,{readSize:i,size:s.size}).pipe(e)},Xp=(r,e)=>{let t=new Vs(r),s=r.maxReadSize||16*1024*1024,i=r.file,n=new Promise((o,a)=>{t.on("error",a),t.on("close",o),Wc.stat(i,(l,c)=>{if(l)a(l);else{let h=new zc.ReadStream(i,{readSize:s,size:c.size});h.on("error",a),h.pipe(t)}})});return e?n.then(e,e):n},Qp=r=>new Vs.Sync(r),em=r=>new Vs(r)});var Jc=y(N=>{"use strict";u();N.c=N.create=va();N.r=N.replace=$n();N.t=N.list=Ns();N.u=N.update=Ma();N.x=N.extract=Hc();N.Pack=Cs();N.Unpack=no();N.Parse=Ls();N.ReadEntry=is();N.WriteEntry=dn();N.Header=Ht();N.Pax=os();N.types=Ki()});var Xc=y((U0,Zc)=>{u();function le(r,e){typeof e=="boolean"&&(e={forever:e}),this._originalTimeouts=JSON.parse(JSON.stringify(r)),this._timeouts=r,this._options=e||{},this._maxRetryTime=e&&e.maxRetryTime||1/0,this._fn=null,this._errors=[],this._attempts=1,this._operationTimeout=null,this._operationTimeoutCb=null,this._timeout=null,this._operationStart=null,this._timer=null,this._options.forever&&(this._cachedTimeouts=this._timeouts.slice(0))}Zc.exports=le;le.prototype.reset=function(){this._attempts=1,this._timeouts=this._originalTimeouts.slice(0)};le.prototype.stop=function(){this._timeout&&clearTimeout(this._timeout),this._timer&&clearTimeout(this._timer),this._timeouts=[],this._cachedTimeouts=null};le.prototype.retry=function(r){if(this._timeout&&clearTimeout(this._timeout),!r)return!1;var e=new Date().getTime();if(r&&e-this._operationStart>=this._maxRetryTime)return this._errors.push(r),this._errors.unshift(new Error("RetryOperation timeout occurred")),!1;this._errors.push(r);var t=this._timeouts.shift();if(t===void 0)if(this._cachedTimeouts)this._errors.splice(0,this._errors.length-1),t=this._cachedTimeouts.slice(-1);else return!1;var s=this;return this._timer=setTimeout(function(){s._attempts++,s._operationTimeoutCb&&(s._timeout=setTimeout(function(){s._operationTimeoutCb(s._attempts)},s._operationTimeout),s._options.unref&&s._timeout.unref()),s._fn(s._attempts)},t),this._options.unref&&this._timer.unref(),!0};le.prototype.attempt=function(r,e){this._fn=r,e&&(e.timeout&&(this._operationTimeout=e.timeout),e.cb&&(this._operationTimeoutCb=e.cb));var t=this;this._operationTimeoutCb&&(this._timeout=setTimeout(function(){t._operationTimeoutCb()},t._operationTimeout)),this._operationStart=new Date().getTime(),this._fn(this._attempts)};le.prototype.try=function(r){console.log("Using RetryOperation.try() is deprecated"),this.attempt(r)};le.prototype.start=function(r){console.log("Using RetryOperation.start() is deprecated"),this.attempt(r)};le.prototype.start=le.prototype.try;le.prototype.errors=function(){return this._errors};le.prototype.attempts=function(){return this._attempts};le.prototype.mainError=function(){if(this._errors.length===0)return null;for(var r={},e=null,t=0,s=0;s<this._errors.length;s++){var i=this._errors[s],n=i.message,o=(r[n]||0)+1;r[n]=o,o>=t&&(e=i,t=o)}return e}});var Qc=y(vt=>{u();var nm=Xc();vt.operation=function(r){var e=vt.timeouts(r);return new nm(e,{forever:r&&(r.forever||r.retries===1/0),unref:r&&r.unref,maxRetryTime:r&&r.maxRetryTime})};vt.timeouts=function(r){if(r instanceof Array)return[].concat(r);var e={retries:10,factor:2,minTimeout:1*1e3,maxTimeout:1/0,randomize:!1};for(var t in r)e[t]=r[t];if(e.minTimeout>e.maxTimeout)throw new Error("minTimeout is greater than maxTimeout");for(var s=[],i=0;i<e.retries;i++)s.push(this.createTimeout(i,e));return r&&r.forever&&!s.length&&s.push(this.createTimeout(i,e)),s.sort(function(n,o){return n-o}),s};vt.createTimeout=function(r,e){var t=e.randomize?Math.random()+1:1,s=Math.round(t*Math.max(e.minTimeout,1)*Math.pow(e.factor,r));return s=Math.min(s,e.maxTimeout),s};vt.wrap=function(r,e,t){if(e instanceof Array&&(t=e,e=null),!t){t=[];for(var s in r)typeof r[s]=="function"&&t.push(s)}for(var i=0;i<t.length;i++){var n=t[i],o=r[n];r[n]=function(l){var c=vt.operation(e),h=Array.prototype.slice.call(arguments,1),d=h.pop();h.push(function(p){c.retry(p)||(p&&(arguments[0]=c.mainError()),d.apply(this,arguments))}),c.attempt(function(){l.apply(r,h)})}.bind(r,o),r[n].options=e}}});var tl=y((z0,el)=>{u();el.exports=Qc()});var sl=y((G0,rl)=>{u();var om=tl();function um(r,e){function t(s,i){var n=e||{},o;"randomize"in n||(n.randomize=!0),o=om.operation(n);function a(h){i(h||new Error("Aborted"))}function l(h,d){if(h.bail){a(h);return}o.retry(h)?n.onRetry&&n.onRetry(h,d):i(o.mainError())}function c(h){var d;try{d=r(a,h)}catch(p){l(p,h);return}Promise.resolve(d).then(s).catch(function(E){l(E,h)})}o.attempt(c)}return new Promise(t)}rl.exports=um});u();var fo=C(require("picocolors")),Bl=require("commander");u();u();u();var Yl=C(Co());u();u();function oi(r,e){if(!(r instanceof e))throw new TypeError("Cannot call a class as a function")}u();u();function pr(r){return pr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},pr(r)}u();function ui(r){if(r===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return r}function ai(r,e){if(e&&(pr(e)==="object"||typeof e=="function"))return e;if(e!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return ui(r)}u();function dt(r){return dt=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},dt(r)}u();u();function _e(r,e){return _e=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(s,i){return s.__proto__=i,s},_e(r,e)}function ci(r,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function");r.prototype=Object.create(e&&e.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),e&&_e(r,e)}u();u();function li(r){return Function.toString.call(r).indexOf("[native code]")!==-1}u();u();function hi(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch{return!1}}function Nt(r,e,t){return hi()?Nt=Reflect.construct.bind():Nt=function(i,n,o){var a=[null];a.push.apply(a,n);var l=Function.bind.apply(i,a),c=new l;return o&&_e(c,o.prototype),c},Nt.apply(null,arguments)}function mr(r){var e=typeof Map=="function"?new Map:void 0;return mr=function(s){if(s===null||!li(s))return s;if(typeof s!="function")throw new TypeError("Super expression must either be null or a function");if(typeof e<"u"){if(e.has(s))return e.get(s);e.set(s,i)}function i(){return Nt(s,arguments,dt(this).constructor)}return i.prototype=Object.create(s.prototype,{constructor:{value:i,enumerable:!1,writable:!0,configurable:!0}}),_e(i,s)},mr(r)}var jo=C(Io());var Kl=C(require("fs-extra")),vd=function(r){ci(e,r);function e(t){var s;return oi(this,e),s=ai(this,dt(e).call(this,"No package.json could be found upwards from the directory ".concat(t))),s.directory=t,s}return e}(mr(Error));u();u();var Zl=C(require("fs")),Xl=C(require("path"));u();var hh=C(require("fs")),fh=C(require("path")),ph=C(require("js-yaml")),mh=require("fast-glob");var ge={};jl(ge,{bold:()=>ih,dimmed:()=>oh,error:()=>yi,grey:()=>uh,info:()=>sh,item:()=>ah,log:()=>He,turboBlue:()=>It,turboGradient:()=>rh,turboLoader:()=>Ho,turboRed:()=>zo,underline:()=>nh,warn:()=>Jo,yellow:()=>Go});u();var J=require("picocolors"),qo=C(require("ora")),$o=C(require("gradient-string")),Uo="#0099F7",Wo="#F11712",th="#FFFF00",Ei=r=>{let e=ch(r);return t=>`\x1B[38;5;${e}m${t}${(0,J.reset)("")}`},rh=(0,$o.default)(Uo,Wo),It=Ei(Uo),zo=Ei(Wo),Go=Ei(th),Ho=r=>(0,qo.default)({text:r,spinner:{frames:["   ",It(">  "),It(">> "),It(">>>")]}}),sh=(...r)=>{He(It((0,J.bold)(">>>")),r.join(" "))},ih=(...r)=>{He((0,J.bold)(r.join(" ")))},nh=(...r)=>{He((0,J.underline)(r.join(" ")))},oh=(...r)=>{He((0,J.dim)(r.join(" ")))},uh=(...r)=>{He((0,J.gray)(r.join(" ")))},ah=(...r)=>{He(It((0,J.bold)("  \u2022")),r.join(" "))},He=(...r)=>{console.log(...r)},Jo=(...r)=>{console.error(Go((0,J.bold)(">>>")),r.join(" "))},yi=(...r)=>{console.error(zo((0,J.bold)(">>>")),r.join(" "))};function ch(r){let e=parseInt(r.slice(1),16),t=Math.floor(e/(256*256))%256,s=Math.floor(e/256)%256,i=e%256;return 16+36*Math.round(t/255*5)+6*Math.round(s/255*5)+Math.round(i/255*5)}u();var Vo=C(require("os")),Yo=C(require("execa"));async function Wr(r,e=[],t){let s={cwd:Vo.default.tmpdir(),env:{COREPACK_ENABLE_STRICT:"0"},...t};try{let{stdout:i}=await(0,Yo.default)(r,e,s);return i.trim()}catch{return}}async function Ai(){let[r,e,t,s]=await Promise.all([Wr("yarnpkg",["--version"],{cwd:"."}),Wr("npm",["--version"]),Wr("pnpm",["--version"]),Wr("bun",["--version"])]);return{yarn:r,pnpm:t,npm:e,bun:s}}u();var dh=C(require("fs-extra"));u();var Dh=C(require("path")),gh=C(require("fs-extra")),Eh=C(require("picocolors"));u();var Vc=require("stream"),Yc=require("util"),tm=require("path"),rm=require("os"),Kc=require("fs"),sm=C(Jc());var j0=(0,Yc.promisify)(Vc.Stream.pipeline);u();var im=C(require("fs-extra"));u();var mm=C(require("path")),dm=C(sl()),Dm=C(require("picocolors")),gm=C(require("fs-extra"));u();u();var il={name:"@turbo/workspaces",version:"2.5.3",description:"Tools for working with package managers",homepage:"https://turborepo.com",license:"MIT",repository:{type:"git",url:"https://github.com/vercel/turborepo",directory:"packages/turbo-workspaces"},bugs:{url:"https://github.com/vercel/turborepo/issues"},bin:"dist/cli.js",module:"dist/index.mjs",main:"dist/index.js",types:"dist/index.d.ts",scripts:{build:"tsup",dev:"tsup --watch",test:"jest",lint:"eslint src/","check-types":"tsc --noEmit","lint:prettier":"prettier -c . --cache --ignore-path=../../.prettierignore"},dependencies:{commander:"^10.0.0",execa:"5.1.1","fast-glob":"^3.2.12","fs-extra":"^10.1.0","gradient-string":"^2.0.0",inquirer:"^8.0.0","js-yaml":"^4.1.0",ora:"4.1.1",picocolors:"1.0.1",semver:"7.6.2","update-check":"^1.5.4"},devDependencies:{"@jest/globals":"^29.7.0","@turbo/eslint-config":"workspace:*","@turbo/test-utils":"workspace:*","@turbo/tsconfig":"workspace:*","@turbo/utils":"workspace:*","@types/fs-extra":"^9.0.13","@types/gradient-string":"^1.1.2","@types/inquirer":"^7.3.1","@types/js-yaml":"^4.0.5","@types/node":"^18.17.2","@types/semver":"7.5.8",jest:"^29.7.0","ts-jest":"^29.2.5",tsup:"^5.10.3",typescript:"5.5.4"},files:["dist"],publishConfig:{access:"public"}};u();u();var Zs=C(require("path")),wl=C(require("inquirer")),We=C(require("picocolors"));u();var re=C(require("picocolors")),nl=C(require("gradient-string")),ar=2,ot=class{constructor({interactive:e,dry:t}={}){this.interactive=e!=null?e:!0,this.dry=t!=null?t:!1,this.step=1}logger(...e){this.interactive&&console.log(...e)}indented(e,...t){this.logger(" ".repeat(ar*e),...t)}header(e){this.blankLine(),this.logger(re.default.bold(e))}installerFrames(){let e=`${" ".repeat(ar)} - ${this.dry?re.default.yellow("SKIPPED | "):re.default.green("OK | ")}`;return[`${e}   `,`${e}>  `,`${e}>> `,`${e}>>>`]}gradient(e){return(0,nl.default)("#0099F7","#F11712")(e.toString())}hero(){this.logger(re.default.bold(this.gradient(`
>>> TURBOREPO
`)))}info(...e){this.logger(...e)}mainStep(e){this.blankLine(),this.logger(`${this.step}. ${re.default.underline(e)}`),this.step+=1}subStep(...e){this.logger(" ".repeat(ar),"-",this.dry?re.default.yellow("SKIPPED |"):re.default.green("OK |"),...e)}subStepFailure(...e){this.logger(" ".repeat(ar),"-",re.default.red("ERROR |"),...e)}rootHeader(){this.blankLine(),this.indented(2,"Root:")}rootStep(...e){this.logger(" ".repeat(ar*3),"-",this.dry?re.default.yellow("SKIPPED |"):re.default.green("OK |"),...e)}workspaceHeader(){this.blankLine(),this.indented(2,"Workspaces:")}workspaceStep(...e){this.logger(" ".repeat(ar*3),"-",this.dry?re.default.yellow("SKIPPED |"):re.default.green("OK |"),...e)}blankLine(){this.logger()}error(...e){console.error(...e)}};u();var $e=C(require("path")),ol=C(require("execa")),se=require("fs-extra"),ul=require("fast-glob"),al=C(require("js-yaml"));u();var v=class extends Error{constructor(t,s){var i;super(t);this.name="ConvertError",this.type=(i=s==null?void 0:s.type)!=null?i:"unknown",Error.captureStackTrace(this,v)}};var ym=/^(?!_)(?<manager>.+)@(?<version>.+)$/;function M({workspaceRoot:r}){let e=$e.default.join(r,"package.json");try{return(0,se.readJsonSync)(e,"utf8")}catch(t){if(t&&typeof t=="object"&&"code"in t){if(t.code==="ENOENT")throw new v(`no "package.json" found at ${r}`,{type:"package_json-missing"});if(t.code==="EJSONPARSE")throw new v(`failed to parse "package.json" at ${r}`,{type:"package_json-parse_error"})}throw new Error(`unexpected error reading "package.json" at ${r}`)}}function ut({workspaceRoot:r}){let{packageManager:e}=M({workspaceRoot:r});if(e)try{let t=ym.exec(e);if(t){let[s,i]=t;return i}}catch{}}function Ue({workspaceRoot:r}){let e=M({workspaceRoot:r}),t=$e.default.basename(r),{name:s=t,description:i}=e;return{name:s,description:i}}function uo({workspaceRoot:r}){let e=$e.default.join(r,"pnpm-workspace.yaml");if((0,se.existsSync)(e))try{let t=al.default.load((0,se.readFileSync)(e,"utf8"));if(t instanceof Object&&"packages"in t&&Array.isArray(t.packages))return t.packages}catch{throw new v(`failed to parse ${e}`,{type:"pnpm-workspace_parse_error"})}return[]}function at({root:r,lockFile:e,workspaceConfig:t}){let s=n=>$e.default.join(r,n),i={root:r,lockfile:s(e),packageJson:s("package.json"),nodeModules:s("node_modules")};return t&&(i.workspaceConfig=s(t)),i}function cr({workspaces:r}){var e;return r?Array.isArray(r)?r:"packages"in r?(e=r.packages)!=null?e:[]:[]:[]}function ct({workspaceRoot:r,workspaceGlobs:e}){if(!e)return[];let t=e.filter(s=>s.startsWith("!")).map(s=>s.slice(1));return e.filter(s=>!s.startsWith("!")).flatMap(s=>{let i=[`${s}/package.json`];return(0,ul.sync)(i,{onlyFiles:!0,absolute:!0,cwd:r,ignore:["**/node_modules/**",...t]})}).map(s=>{let i=$e.default.dirname(s),{name:n,description:o}=Ue({workspaceRoot:i});return{name:n,description:o,paths:{root:i,packageJson:s,nodeModules:$e.default.join(i,"node_modules")}}})}function lt({directory:r}){let e=$e.default.resolve(process.cwd(),r);return{exists:(0,se.existsSync)(e),absolute:e}}function he({packageManager:r,action:e,project:t}){let s=t.workspaceData.globs.length>0;return`${e==="remove"?"Removing":"Adding"} ${r} ${s?"workspaces":""} ${e==="remove"?"from":"to"} ${t.name}`}function cl({project:r}){let e=t=>!(t.includes("*")&&(t.includes("**")||t.split("/").slice(0,-1).join("/").includes("*"))||["!","[","]","{","}"].some(s=>t.includes(s)));return r.workspaceData.globs.every(e)}function ie({project:r,options:e}){e!=null&&e.dry||(0,se.rmSync)(r.paths.lockfile,{force:!0})}async function Ys({project:r,options:e}){if(!(e!=null&&e.dry)&&(0,se.existsSync)(r.paths.lockfile))try{let{stdout:t}=await(0,ol.default)("bun",["bun.lockb"],{stdin:"ignore",cwd:r.paths.root});await(0,se.writeFile)($e.default.join(r.paths.root,"yarn.lock"),t)}catch{}finally{(0,se.rmSync)(r.paths.lockfile,{force:!0})}}u();u();u();var Rt=C(require("path")),de=C(require("fs-extra")),pl=C(require("execa"));u();var ll=C(require("path")),hl=C(require("fs-extra")),fl=C(require("picocolors"));function Am({dependencyList:r,project:e,to:t}){let s=[];return e.workspaceData.workspaces.forEach(i=>{let{name:n}=i;if(r[n]){let o=r[n],a=o.startsWith("workspace:")?o.slice(10):o;r[n]=t.name==="pnpm"?`workspace:${a}`:a,s.push(n)}}),{dependencyList:r,updated:s}}function fe({project:r,workspace:e,to:t,logger:s,options:i}){if(["yarn","npm","bun"].includes(t.name)&&["yarn","npm","bun"].includes(r.packageManager))return;let n=M({workspaceRoot:e.paths.root}),o={dependencies:[],devDependencies:[],peerDependencies:[],optionalDependencies:[]},a=["dependencies","devDependencies","peerDependencies","optionalDependencies"];a.forEach(d=>{let p=n[d];if(p){let{updated:E,dependencyList:w}=Am({dependencyList:p,project:r,to:t});n[d]=w,o[d]=E}});let l=d=>{let p=o[d].length;if(p>0)return`${fl.default.green(p.toString())} ${d}`},c=a.map(l).filter(Boolean),h=`./${ll.default.relative(r.paths.root,e.paths.packageJson)}`;if(c.length>=1){let d="updating";c.forEach((p,E)=>{c.length===1?d+=` ${p} in ${h}`:E===c.length-1?d+=`and ${p} in ${h}`:d+=` ${p}, `}),s.workspaceStep(d)}else s.workspaceStep(`no workspace dependencies found in ${h}`);i!=null&&i.dry||hl.default.writeJSONSync(e.paths.packageJson,n,{spaces:2})}var ht={name:"pnpm",lock:"pnpm-lock.yaml"};async function ml(r){let e=Rt.default.join(r.workspaceRoot,ht.lock),t=Rt.default.join(r.workspaceRoot,"pnpm-workspace.yaml"),s=ut({workspaceRoot:r.workspaceRoot});return de.default.existsSync(e)||de.default.existsSync(t)||s===ht.name}async function Cm(r){if(!await ml(r))throw new v("Not a pnpm project",{type:"package_manager-unexpected"});let{name:t,description:s}=Ue(r);return{name:t,description:s,packageManager:ht.name,paths:at({root:r.workspaceRoot,lockFile:ht.lock,workspaceConfig:"pnpm-workspace.yaml"}),workspaceData:{globs:uo(r),workspaces:ct({workspaceGlobs:uo(r),...r})}}}async function wm(r){let{project:e,to:t,logger:s,options:i}=r,n=e.workspaceData.globs.length>0;s.mainStep(he({action:"create",packageManager:ht.name,project:e}));let o=M({workspaceRoot:e.paths.root});s.rootHeader(),o.packageManager=`${t.name}@${t.version}`,s.rootStep(`adding "packageManager" field to ${e.name} root "package.json"`),i!=null&&i.dry||(de.default.writeJSONSync(e.paths.packageJson,o,{spaces:2}),n&&(s.rootStep('adding "pnpm-workspace.yaml"'),de.default.writeFileSync(Rt.default.join(e.paths.root,"pnpm-workspace.yaml"),`packages:
${e.workspaceData.globs.map(a=>`  - "${a}"`).join(`
`)}`))),n&&(fe({workspace:{name:"root",paths:e.paths},project:e,to:t,logger:s,options:i}),s.workspaceHeader(),e.workspaceData.workspaces.forEach(a=>{fe({workspace:a,project:e,to:t,logger:s,options:i})}))}async function Fm(r){let{project:e,logger:t,options:s}=r,i=e.workspaceData.globs.length>0;t.mainStep(he({action:"remove",packageManager:ht.name,project:e}));let n=M({workspaceRoot:e.paths.root});if(e.paths.workspaceConfig&&i&&(t.subStep('removing "pnpm-workspace.yaml"'),s!=null&&s.dry||de.default.rmSync(e.paths.workspaceConfig,{force:!0})),t.subStep(`removing "packageManager" field in ${e.name} root "package.json"`),delete n.packageManager,!(s!=null&&s.dry)){de.default.writeJSONSync(e.paths.packageJson,n,{spaces:2});let o=[e.paths.nodeModules,...e.workspaceData.workspaces.map(a=>a.paths.nodeModules)];try{t.subStep('removing "node_modules"'),await Promise.all(o.map(a=>de.default.rm(a,{recursive:!0,force:!0})))}catch{throw new v("Failed to remove node_modules",{type:"error_removing_node_modules"})}}}async function bm(r){let{project:e,logger:t,options:s}=r;t.subStep(`removing ${Rt.default.relative(e.paths.root,e.paths.lockfile)}`),s!=null&&s.dry||de.default.rmSync(e.paths.lockfile,{force:!0})}async function Sm(r){let{project:e,options:t,logger:s}=r,i=()=>{s.subStep(`converting ${Rt.default.relative(e.paths.root,e.paths.lockfile)} to ${ht.lock}`)},n=async()=>{if(!(t!=null&&t.dry)&&de.default.existsSync(e.paths.lockfile))try{await(0,pl.default)(ht.name,["import"],{stdio:"ignore",cwd:e.paths.root})}catch{}finally{ie({project:e,options:t})}};switch(e.packageManager){case"pnpm":break;case"bun":i(),await Ys({project:e,options:t}),await n(),de.default.rmSync(Rt.default.join(e.paths.root,"yarn.lock"),{force:!0});break;case"npm":i(),await n();break;case"yarn":i(),await n();break}}var dl={detect:ml,read:Cm,create:wm,remove:Fm,clean:bm,convertLock:Sm};u();var Lr=C(require("path")),Bt=C(require("fs-extra"));var lr={name:"npm",lock:"package-lock.json"};async function Dl(r){let e=Lr.default.join(r.workspaceRoot,lr.lock),t=ut({workspaceRoot:r.workspaceRoot});return Bt.default.existsSync(e)||t===lr.name}async function km(r){if(!await Dl(r))throw new v("Not an npm project",{type:"package_manager-unexpected"});let t=M(r),{name:s,description:i}=Ue(r),n=cr({workspaces:t.workspaces});return{name:s,description:i,packageManager:lr.name,paths:at({root:r.workspaceRoot,lockFile:lr.lock}),workspaceData:{globs:n,workspaces:ct({workspaceGlobs:n,...r})}}}async function _m(r){let{project:e,options:t,to:s,logger:i}=r,n=e.workspaceData.globs.length>0;i.mainStep(he({packageManager:lr.name,action:"create",project:e}));let o=M({workspaceRoot:e.paths.root});i.rootHeader(),i.rootStep(`adding "packageManager" field to ${Lr.default.relative(e.paths.root,e.paths.packageJson)}`),o.packageManager=`${s.name}@${s.version}`,n?(i.rootStep(`adding "workspaces" field to ${Lr.default.relative(e.paths.root,e.paths.packageJson)}`),o.workspaces=e.workspaceData.globs,t!=null&&t.dry||Bt.default.writeJSONSync(e.paths.packageJson,o,{spaces:2}),fe({workspace:{name:"root",paths:e.paths},project:e,to:s,logger:i,options:t}),i.workspaceHeader(),e.workspaceData.workspaces.forEach(a=>{fe({workspace:a,project:e,to:s,logger:i,options:t})})):t!=null&&t.dry||Bt.default.writeJSONSync(e.paths.packageJson,o,{spaces:2})}async function vm(r){let{project:e,logger:t,options:s}=r,i=e.workspaceData.globs.length>0;t.mainStep(he({packageManager:lr.name,action:"remove",project:e}));let n=M({workspaceRoot:e.paths.root});if(i&&(t.subStep(`removing "workspaces" field in ${e.name} root "package.json"`),delete n.workspaces),t.subStep(`removing "packageManager" field in ${e.name} root "package.json"`),delete n.packageManager,!(s!=null&&s.dry)){Bt.default.writeJSONSync(e.paths.packageJson,n,{spaces:2});let o=[e.paths.nodeModules,...e.workspaceData.workspaces.map(a=>a.paths.nodeModules)];try{t.subStep('removing "node_modules"'),await Promise.all(o.map(a=>Bt.default.rm(a,{recursive:!0,force:!0})))}catch{throw new v("Failed to remove node_modules",{type:"error_removing_node_modules"})}}}async function Rm(r){let{project:e,logger:t,options:s}=r;t.subStep(`removing ${Lr.default.relative(e.paths.root,e.paths.lockfile)}`),s!=null&&s.dry||Bt.default.rmSync(e.paths.lockfile,{force:!0})}async function Bm(r){let{project:e,options:t}=r;switch(e.packageManager){case"pnpm":ie({project:e,options:t});break;case"bun":ie({project:e,options:t});break;case"npm":break;case"yarn":ie({project:e,options:t});break}}var gl={detect:Dl,read:km,create:_m,remove:vm,clean:Rm,convertLock:Bm};u();var hr=C(require("path")),xt=C(require("fs-extra"));var Ot={name:"yarn",lock:"yarn.lock"};async function El(r){let e=hr.default.join(r.workspaceRoot,Ot.lock),t=ut({workspaceRoot:r.workspaceRoot});return xt.default.existsSync(e)||t===Ot.name}async function xm(r){if(!await El(r))throw new v("Not a yarn project",{type:"package_manager-unexpected"});let t=M(r),{name:s,description:i}=Ue(r),n=cr({workspaces:t.workspaces});return{name:s,description:i,packageManager:Ot.name,paths:at({root:r.workspaceRoot,lockFile:Ot.lock}),workspaceData:{globs:n,workspaces:ct({workspaceGlobs:n,...r})}}}async function Om(r){let{project:e,to:t,logger:s,options:i}=r,n=e.workspaceData.globs.length>0;s.mainStep(he({packageManager:Ot.name,action:"create",project:e}));let o=M({workspaceRoot:e.paths.root});s.rootHeader(),s.rootStep(`adding "packageManager" field to ${hr.default.relative(e.paths.root,e.paths.packageJson)}`),o.packageManager=`${t.name}@${t.version}`,n?(s.rootStep(`adding "workspaces" field to ${hr.default.relative(e.paths.root,e.paths.packageJson)}`),o.workspaces=e.workspaceData.globs,i!=null&&i.dry||xt.default.writeJSONSync(e.paths.packageJson,o,{spaces:2}),fe({workspace:{name:"root",paths:e.paths},project:e,to:t,logger:s,options:i}),s.workspaceHeader(),e.workspaceData.workspaces.forEach(a=>{fe({workspace:a,project:e,to:t,logger:s,options:i})})):i!=null&&i.dry||xt.default.writeJSONSync(e.paths.packageJson,o,{spaces:2})}async function Pm(r){let{project:e,logger:t,options:s}=r,i=e.workspaceData.globs.length>0;t.mainStep(he({packageManager:Ot.name,action:"remove",project:e}));let n=M({workspaceRoot:e.paths.root});if(i&&(t.subStep(`removing "workspaces" field in ${e.name} root "package.json"`),delete n.workspaces),t.subStep(`removing "packageManager" field in ${e.name} root "package.json"`),delete n.packageManager,!(s!=null&&s.dry)){xt.default.writeJSONSync(e.paths.packageJson,n,{spaces:2});let o=[e.paths.nodeModules,...e.workspaceData.workspaces.map(a=>a.paths.nodeModules)];try{t.subStep('removing "node_modules"'),await Promise.all(o.map(a=>xt.default.rm(a,{recursive:!0,force:!0})))}catch{throw new v("Failed to remove node_modules",{type:"error_removing_node_modules"})}}}async function Tm(r){let{project:e,logger:t,options:s}=r;t.subStep(`removing ${hr.default.relative(e.paths.root,e.paths.lockfile)}`),s!=null&&s.dry||xt.default.rmSync(e.paths.lockfile,{force:!0})}async function Lm(r){let{project:e,options:t,logger:s}=r,i=()=>{s.subStep(`converting ${hr.default.relative(e.paths.root,e.paths.lockfile)} to ${Ot.lock}`)};switch(e.packageManager){case"pnpm":ie({project:e,options:t});break;case"bun":i(),await Ys({project:e,options:t});break;case"npm":ie({project:e,options:t});break;case"yarn":break}}var yl={detect:El,read:xm,create:Om,remove:Pm,clean:Tm,convertLock:Lm};u();var Nr=C(require("path")),Pt=C(require("fs-extra"));var fr={name:"bun",lock:"bun.lockb"};async function Al(r){let e=Nr.default.join(r.workspaceRoot,fr.lock),t=ut({workspaceRoot:r.workspaceRoot});return Pt.default.existsSync(e)||t===fr.name}async function Nm(r){if(!await Al(r))throw new v("Not a bun project",{type:"package_manager-unexpected"});let t=M(r),{name:s,description:i}=Ue(r),n=cr({workspaces:t.workspaces});return{name:s,description:i,packageManager:fr.name,paths:at({root:r.workspaceRoot,lockFile:fr.lock}),workspaceData:{globs:n,workspaces:ct({workspaceGlobs:n,...r})}}}async function Mm(r){let{project:e,to:t,logger:s,options:i}=r,n=e.workspaceData.globs.length>0;if(!cl({project:e}))throw new v("Unable to convert project to bun - workspace globs unsupported",{type:"bun-workspace_glob_error"});s.mainStep(he({packageManager:fr.name,action:"create",project:e}));let o=M({workspaceRoot:e.paths.root});s.rootHeader(),s.rootStep(`adding "packageManager" field to ${Nr.default.relative(e.paths.root,e.paths.packageJson)}`),o.packageManager=`${t.name}@${t.version}`,n?(s.rootStep(`adding "workspaces" field to ${Nr.default.relative(e.paths.root,e.paths.packageJson)}`),o.workspaces=e.workspaceData.globs,i!=null&&i.dry||Pt.default.writeJSONSync(e.paths.packageJson,o,{spaces:2}),fe({workspace:{name:"root",paths:e.paths},project:e,to:t,logger:s,options:i}),s.workspaceHeader(),e.workspaceData.workspaces.forEach(a=>{fe({workspace:a,project:e,to:t,logger:s,options:i})})):i!=null&&i.dry||Pt.default.writeJSONSync(e.paths.packageJson,o,{spaces:2})}async function Im(r){let{project:e,logger:t,options:s}=r,i=e.workspaceData.globs.length>0;t.mainStep(he({packageManager:fr.name,action:"remove",project:e}));let n=M({workspaceRoot:e.paths.root});if(i&&(t.subStep(`removing "workspaces" field in ${e.name} root "package.json"`),delete n.workspaces),t.subStep(`removing "packageManager" field in ${e.name} root "package.json"`),delete n.packageManager,!(s!=null&&s.dry)){Pt.default.writeJSONSync(e.paths.packageJson,n,{spaces:2});let o=[e.paths.nodeModules,...e.workspaceData.workspaces.map(a=>a.paths.nodeModules)];try{t.subStep('removing "node_modules"'),await Promise.all(o.map(a=>Pt.default.rm(a,{recursive:!0,force:!0})))}catch{throw new v("Failed to remove node_modules",{type:"error_removing_node_modules"})}}}async function jm(r){let{project:e,logger:t,options:s}=r;t.subStep(`removing ${Nr.default.relative(e.paths.root,e.paths.lockfile)}`),s!=null&&s.dry||Pt.default.rmSync(e.paths.lockfile,{force:!0})}async function qm(r){let{project:e,options:t}=r;switch(e.packageManager){case"pnpm":ie({project:e,options:t});break;case"bun":break;case"npm":ie({project:e,options:t});break;case"yarn":ie({project:e,options:t});break}}var Cl={detect:Al,read:Nm,create:Mm,remove:Im,clean:jm,convertLock:qm};var Tt={pnpm:dl,yarn:yl,npm:gl,bun:Cl};async function Ks({root:r}){let{exists:e,absolute:t}=lt({directory:r});if(!e)throw new v(`Could not find directory at ${t}. Ensure the directory exists.`,{type:"invalid_directory"});for(let{detect:s,read:i}of Object.values(Tt))if(await s({workspaceRoot:t}))return i({workspaceRoot:t});throw new v("Could not determine package manager. Add `packageManager` to `package.json` or ensure a lockfile is present.",{type:"package_manager-unable_to_detect"})}async function ao(r){let e=new ot;e.hero();let t=await wl.default.prompt({type:"input",name:"directoryInput",message:"Where is the root of the repo?",when:!r,default:".",validate:p=>{let{exists:E,absolute:w}=lt({directory:p});return E?!0:`Directory ${We.default.dim(`(${w})`)} does not exist`},filter:p=>p.trim()}),{directoryInput:s=r}=t,{exists:i,absolute:n}=lt({directory:s});if(!i)return e.error(`Directory ${We.default.dim(`(${n})`)} does not exist`),process.exit(1);let o=await Ks({root:n}),a=o.workspaceData.workspaces.length,l=a>0,c={};o.workspaceData.workspaces.forEach(p=>{let w=Zs.default.relative(n,p.paths.root).split(Zs.default.sep)[0];w in c||(c[w]=[]),c[w].push(p)});let h=p=>`${p.name} (${We.default.italic(`./${Zs.default.relative(n,p.paths.root)}`)})`,d=({number:p,dir:E,workspaces:w})=>{e.indented(2,`${p}. ${We.default.bold(E)}`),w.forEach((P,S)=>{e.indented(3,`${S+1}. ${h(P)}`)})};e.header("Repository Summary"),e.indented(1,`${We.default.underline(o.name)}:`),e.indented(1,`Package Manager: ${We.default.bold(We.default.italic(o.packageManager))}`),l&&(e.indented(1,`Workspaces (${We.default.bold(a.toString())}):`),Object.keys(c).forEach((p,E)=>{d({number:E+1,workspaces:c[p],dir:p})}),e.blankLine())}u();var co=C(require("inquirer")),lo=C(require("picocolors"));u();var vl=C(require("picocolors"));u();var bl=C(require("execa")),Sl=C(require("ora")),kl=require("semver");var Fl={npm:[{name:"npm",template:"npm",command:"npm",installArgs:["install"],version:"latest",executable:"npx",semver:"*",default:!0}],pnpm:[{name:"pnpm6",template:"pnpm",command:"pnpm",installArgs:["install"],version:"latest-6",executable:"pnpx",semver:"6.x"},{name:"pnpm",template:"pnpm",command:"pnpm",installArgs:["install","--fix-lockfile"],version:"latest",executable:"pnpm dlx",semver:">=7",default:!0}],yarn:[{name:"yarn",template:"yarn",command:"yarn",installArgs:["install"],version:"1.x",executable:"npx",semver:"<2",default:!0},{name:"berry",template:"berry",command:"yarn",installArgs:["install","--no-immutable"],version:"stable",executable:"yarn dlx",semver:">=2"}],bun:[{name:"bun",template:"bun",command:"bun",installArgs:["install"],version:"latest",executable:"bunx",semver:"^1.0.1",default:!0}]};function $m(r){let{version:e,name:t}=r;return e?Fl[t].find(s=>(0,kl.satisfies)(e,s.semver)):Fl[t].find(s=>s.default)}async function _l(r){let{to:e,logger:t,options:s}=r,i=t!=null?t:new ot(s),n=$m(e);if(!n)throw new v("Unsupported package manager version.",{type:"package_manager-unsupported_version"});if(i.subStep(`running "${n.command} ${n.installArgs.join(" ")}"`),!(s!=null&&s.dry)){let o;i.interactive&&(o=(0,Sl.default)({text:"installing dependencies...",spinner:{frames:i.installerFrames()}}).start());try{await(0,bl.default)(n.command,n.installArgs,{cwd:r.project.paths.root}),o&&o.stop(),i.subStep("dependencies installed")}catch(a){throw i.subStepFailure("failed to install dependencies"),a}}}async function Rl({project:r,convertTo:e,logger:t,options:s}){if(t.header(`Converting project from ${r.packageManager} to ${e.name}.`),!(s!=null&&s.ignoreUnchangedPackageManager)){if(r.packageManager===e.name)throw new v("You are already using this package manager",{type:"package_manager-already_in_use"});if(!e.version)throw new v(`${e.name} is not installed, or could not be located`,{type:"package_manager-could_not_be_found"})}let i=e;s!=null&&s.ignoreUnchangedPackageManager||await Tt[r.packageManager].remove({project:r,to:i,logger:t,options:s}),await Tt[i.name].create({project:r,to:i,logger:t,options:s}),t.mainStep("Installing dependencies"),s!=null&&s.skipInstall?t.subStep(vl.default.yellow("Skipping install")):(await Tt[i.name].convertLock({project:r,to:i,logger:t,options:s}),await _l({project:r,to:i,logger:t,options:s})),t.mainStep(`Cleaning up ${r.packageManager} workspaces`),r.packageManager!==e.name&&await Tt[r.packageManager].clean({project:r,logger:t})}function Um({packageManager:r,currentWorkspaceManger:e,availablePackageManagers:t}){return e===r?"already in use":t[r]?!1:"not installed"}async function ho(r,e,t){let s=new ot(t);s.hero(),s.header("Welcome, let's convert your project."),s.blankLine();let i=await co.default.prompt({type:"input",name:"directoryInput",message:"Where is the root of your repo?",when:!r,default:".",validate:p=>{let{exists:E,absolute:w}=lt({directory:p});return E?!0:`Directory ${lo.default.dim(`(${w})`)} does not exist`},filter:p=>p.trim()}),{directoryInput:n=r}=i,{exists:o,absolute:a}=lt({directory:n});if(!o)return s.error(`Directory ${lo.default.dim(`(${a})`)} does not exist`),process.exit(1);let[l,c]=await Promise.all([Ks({root:a}),Ai()]),h=await co.default.prompt({name:"packageManagerInput",type:"list",message:`Convert from ${l.packageManager} to:`,when:!e||!Object.keys(c).includes(e),choices:[{pm:"npm",label:"npm"},{pm:"pnpm",label:"pnpm"},{pm:"yarn",label:"yarn"},{pm:"bun",label:"Bun (beta)"}].map(({pm:p,label:E})=>({name:E,value:p,disabled:Um({packageManager:p,currentWorkspaceManger:l.packageManager,availablePackageManagers:c})}))}),{packageManagerInput:d=e}=h;await Rl({project:l,convertTo:{name:d,version:c[d]},logger:s,options:t})}var Xs=new Bl.Command;Xs.name("@turbo/workspaces").description("Tools for working with package manager workspaces").version(il.version,"-v, --version","output the current version");Xs.command("convert").description("Convert project between workspace managers").argument("[path]","Project root").argument("[package-manager]","Package manager to convert to").option("--skip-install","Do not run a package manager install after conversion",!1).option("--ignore-unchanged-package-manager","Prevent script failure if the package manager is unchanged",!1).option("--dry","Dry run (no changes are made to files)",!1).option("--force","Bypass Git safety checks and forcibly run conversion",!1).action(ho);Xs.command("summary").description("Display a summary of the specified project").argument("[path]","Project root").action(ao);Xs.parseAsync().catch(r=>{ge.log(),r instanceof v?ge.log(fo.default.red(r.message)):(ge.log(fo.default.red("Unexpected error. Please report it as a bug:")),ge.log(r)),ge.log(),process.exit(1)});
/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */
