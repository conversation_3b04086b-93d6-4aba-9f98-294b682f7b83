"use strict";
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.detectResources = exports.detectResourcesSync = exports.serviceInstanceIdDetectorSync = exports.processDetectorSync = exports.processDetector = exports.osDetectorSync = exports.osDetector = exports.hostDetectorSync = exports.hostDetector = exports.envDetectorSync = exports.envDetector = exports.browserDetectorSync = exports.browserDetector = exports.defaultServiceName = exports.Resource = void 0;
var Resource_1 = require("./Resource");
Object.defineProperty(exports, "Resource", { enumerable: true, get: function () { return Resource_1.Resource; } });
var platform_1 = require("./platform");
Object.defineProperty(exports, "defaultServiceName", { enumerable: true, get: function () { return platform_1.defaultServiceName; } });
var detectors_1 = require("./detectors");
Object.defineProperty(exports, "browserDetector", { enumerable: true, get: function () { return detectors_1.browserDetector; } });
Object.defineProperty(exports, "browserDetectorSync", { enumerable: true, get: function () { return detectors_1.browserDetectorSync; } });
Object.defineProperty(exports, "envDetector", { enumerable: true, get: function () { return detectors_1.envDetector; } });
Object.defineProperty(exports, "envDetectorSync", { enumerable: true, get: function () { return detectors_1.envDetectorSync; } });
Object.defineProperty(exports, "hostDetector", { enumerable: true, get: function () { return detectors_1.hostDetector; } });
Object.defineProperty(exports, "hostDetectorSync", { enumerable: true, get: function () { return detectors_1.hostDetectorSync; } });
Object.defineProperty(exports, "osDetector", { enumerable: true, get: function () { return detectors_1.osDetector; } });
Object.defineProperty(exports, "osDetectorSync", { enumerable: true, get: function () { return detectors_1.osDetectorSync; } });
Object.defineProperty(exports, "processDetector", { enumerable: true, get: function () { return detectors_1.processDetector; } });
Object.defineProperty(exports, "processDetectorSync", { enumerable: true, get: function () { return detectors_1.processDetectorSync; } });
Object.defineProperty(exports, "serviceInstanceIdDetectorSync", { enumerable: true, get: function () { return detectors_1.serviceInstanceIdDetectorSync; } });
var detect_resources_1 = require("./detect-resources");
Object.defineProperty(exports, "detectResourcesSync", { enumerable: true, get: function () { return detect_resources_1.detectResourcesSync; } });
Object.defineProperty(exports, "detectResources", { enumerable: true, get: function () { return detect_resources_1.detectResources; } });
//# sourceMappingURL=index.js.map