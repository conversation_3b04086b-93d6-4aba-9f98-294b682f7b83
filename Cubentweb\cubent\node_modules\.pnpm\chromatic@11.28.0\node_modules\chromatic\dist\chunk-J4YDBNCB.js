'use strict';

!function(){try{var e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},n=(new e.Error).stack;n&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[n]="4d50641f-6596-5a59-be2e-ca3023d9d22e")}catch(e){}}();

var chunk7UHX5T7X_js = require('./chunk-7UHX5T7X.js');
var chunkTKGT252T_js = require('./chunk-TKGT252T.js');
var Fe = require('fs');
var D = require('path');
var _ = require('process');
var buffer = require('buffer');
var ei = require('child_process');
var ia = require('url');
var rn = require('os');
var promises = require('timers/promises');
var sa = require('stream');
var util = require('util');
var ua = require('tty');
var la = require('readline');
var ca = require('events');
var sn = require('fs/promises');

function _interopDefault (e) { return e && e.__esModule ? e : { default: e }; }

var Fe__default = /*#__PURE__*/_interopDefault(Fe);
var D__default = /*#__PURE__*/_interopDefault(D);
var ___default = /*#__PURE__*/_interopDefault(_);
var ei__default = /*#__PURE__*/_interopDefault(ei);
var ia__default = /*#__PURE__*/_interopDefault(ia);
var rn__default = /*#__PURE__*/_interopDefault(rn);
var sa__default = /*#__PURE__*/_interopDefault(sa);
var ua__default = /*#__PURE__*/_interopDefault(ua);
var la__default = /*#__PURE__*/_interopDefault(la);
var ca__default = /*#__PURE__*/_interopDefault(ca);
var sn__default = /*#__PURE__*/_interopDefault(sn);

var fs=chunkTKGT252T_js.c(De=>{Object.defineProperty(De,"__esModule",{value:!0});De.isIdentifierChar=hs;De.isIdentifierName=Rf;De.isIdentifierStart=cs;var Er="\xAA\xB5\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0370-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u048A-\u052F\u0531-\u0556\u0559\u0560-\u0588\u05D0-\u05EA\u05EF-\u05F2\u0620-\u064A\u066E\u066F\u0671-\u06D3\u06D5\u06E5\u06E6\u06EE\u06EF\u06FA-\u06FC\u06FF\u0710\u0712-\u072F\u074D-\u07A5\u07B1\u07CA-\u07EA\u07F4\u07F5\u07FA\u0800-\u0815\u081A\u0824\u0828\u0840-\u0858\u0860-\u086A\u0870-\u0887\u0889-\u088E\u08A0-\u08C9\u0904-\u0939\u093D\u0950\u0958-\u0961\u0971-\u0980\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BD\u09CE\u09DC\u09DD\u09DF-\u09E1\u09F0\u09F1\u09FC\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A59-\u0A5C\u0A5E\u0A72-\u0A74\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABD\u0AD0\u0AE0\u0AE1\u0AF9\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3D\u0B5C\u0B5D\u0B5F-\u0B61\u0B71\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BD0\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D\u0C58-\u0C5A\u0C5D\u0C60\u0C61\u0C80\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBD\u0CDD\u0CDE\u0CE0\u0CE1\u0CF1\u0CF2\u0D04-\u0D0C\u0D0E-\u0D10\u0D12-\u0D3A\u0D3D\u0D4E\u0D54-\u0D56\u0D5F-\u0D61\u0D7A-\u0D7F\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0E01-\u0E30\u0E32\u0E33\u0E40-\u0E46\u0E81\u0E82\u0E84\u0E86-\u0E8A\u0E8C-\u0EA3\u0EA5\u0EA7-\u0EB0\u0EB2\u0EB3\u0EBD\u0EC0-\u0EC4\u0EC6\u0EDC-\u0EDF\u0F00\u0F40-\u0F47\u0F49-\u0F6C\u0F88-\u0F8C\u1000-\u102A\u103F\u1050-\u1055\u105A-\u105D\u1061\u1065\u1066\u106E-\u1070\u1075-\u1081\u108E\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16EE-\u16F8\u1700-\u1711\u171F-\u1731\u1740-\u1751\u1760-\u176C\u176E-\u1770\u1780-\u17B3\u17D7\u17DC\u1820-\u1878\u1880-\u18A8\u18AA\u18B0-\u18F5\u1900-\u191E\u1950-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u1A00-\u1A16\u1A20-\u1A54\u1AA7\u1B05-\u1B33\u1B45-\u1B4C\u1B83-\u1BA0\u1BAE\u1BAF\u1BBA-\u1BE5\u1C00-\u1C23\u1C4D-\u1C4F\u1C5A-\u1C7D\u1C80-\u1C8A\u1C90-\u1CBA\u1CBD-\u1CBF\u1CE9-\u1CEC\u1CEE-\u1CF3\u1CF5\u1CF6\u1CFA\u1D00-\u1DBF\u1E00-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u2071\u207F\u2090-\u209C\u2102\u2107\u210A-\u2113\u2115\u2118-\u211D\u2124\u2126\u2128\u212A-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2160-\u2188\u2C00-\u2CE4\u2CEB-\u2CEE\u2CF2\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D80-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u3005-\u3007\u3021-\u3029\u3031-\u3035\u3038-\u303C\u3041-\u3096\u309B-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312F\u3131-\u318E\u31A0-\u31BF\u31F0-\u31FF\u3400-\u4DBF\u4E00-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA61F\uA62A\uA62B\uA640-\uA66E\uA67F-\uA69D\uA6A0-\uA6EF\uA717-\uA71F\uA722-\uA788\uA78B-\uA7CD\uA7D0\uA7D1\uA7D3\uA7D5-\uA7DC\uA7F2-\uA801\uA803-\uA805\uA807-\uA80A\uA80C-\uA822\uA840-\uA873\uA882-\uA8B3\uA8F2-\uA8F7\uA8FB\uA8FD\uA8FE\uA90A-\uA925\uA930-\uA946\uA960-\uA97C\uA984-\uA9B2\uA9CF\uA9E0-\uA9E4\uA9E6-\uA9EF\uA9FA-\uA9FE\uAA00-\uAA28\uAA40-\uAA42\uAA44-\uAA4B\uAA60-\uAA76\uAA7A\uAA7E-\uAAAF\uAAB1\uAAB5\uAAB6\uAAB9-\uAABD\uAAC0\uAAC2\uAADB-\uAADD\uAAE0-\uAAEA\uAAF2-\uAAF4\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB69\uAB70-\uABE2\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D\uFB1F-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE70-\uFE74\uFE76-\uFEFC\uFF21-\uFF3A\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC",us="\xB7\u0300-\u036F\u0387\u0483-\u0487\u0591-\u05BD\u05BF\u05C1\u05C2\u05C4\u05C5\u05C7\u0610-\u061A\u064B-\u0669\u0670\u06D6-\u06DC\u06DF-\u06E4\u06E7\u06E8\u06EA-\u06ED\u06F0-\u06F9\u0711\u0730-\u074A\u07A6-\u07B0\u07C0-\u07C9\u07EB-\u07F3\u07FD\u0816-\u0819\u081B-\u0823\u0825-\u0827\u0829-\u082D\u0859-\u085B\u0897-\u089F\u08CA-\u08E1\u08E3-\u0903\u093A-\u093C\u093E-\u094F\u0951-\u0957\u0962\u0963\u0966-\u096F\u0981-\u0983\u09BC\u09BE-\u09C4\u09C7\u09C8\u09CB-\u09CD\u09D7\u09E2\u09E3\u09E6-\u09EF\u09FE\u0A01-\u0A03\u0A3C\u0A3E-\u0A42\u0A47\u0A48\u0A4B-\u0A4D\u0A51\u0A66-\u0A71\u0A75\u0A81-\u0A83\u0ABC\u0ABE-\u0AC5\u0AC7-\u0AC9\u0ACB-\u0ACD\u0AE2\u0AE3\u0AE6-\u0AEF\u0AFA-\u0AFF\u0B01-\u0B03\u0B3C\u0B3E-\u0B44\u0B47\u0B48\u0B4B-\u0B4D\u0B55-\u0B57\u0B62\u0B63\u0B66-\u0B6F\u0B82\u0BBE-\u0BC2\u0BC6-\u0BC8\u0BCA-\u0BCD\u0BD7\u0BE6-\u0BEF\u0C00-\u0C04\u0C3C\u0C3E-\u0C44\u0C46-\u0C48\u0C4A-\u0C4D\u0C55\u0C56\u0C62\u0C63\u0C66-\u0C6F\u0C81-\u0C83\u0CBC\u0CBE-\u0CC4\u0CC6-\u0CC8\u0CCA-\u0CCD\u0CD5\u0CD6\u0CE2\u0CE3\u0CE6-\u0CEF\u0CF3\u0D00-\u0D03\u0D3B\u0D3C\u0D3E-\u0D44\u0D46-\u0D48\u0D4A-\u0D4D\u0D57\u0D62\u0D63\u0D66-\u0D6F\u0D81-\u0D83\u0DCA\u0DCF-\u0DD4\u0DD6\u0DD8-\u0DDF\u0DE6-\u0DEF\u0DF2\u0DF3\u0E31\u0E34-\u0E3A\u0E47-\u0E4E\u0E50-\u0E59\u0EB1\u0EB4-\u0EBC\u0EC8-\u0ECE\u0ED0-\u0ED9\u0F18\u0F19\u0F20-\u0F29\u0F35\u0F37\u0F39\u0F3E\u0F3F\u0F71-\u0F84\u0F86\u0F87\u0F8D-\u0F97\u0F99-\u0FBC\u0FC6\u102B-\u103E\u1040-\u1049\u1056-\u1059\u105E-\u1060\u1062-\u1064\u1067-\u106D\u1071-\u1074\u1082-\u108D\u108F-\u109D\u135D-\u135F\u1369-\u1371\u1712-\u1715\u1732-\u1734\u1752\u1753\u1772\u1773\u17B4-\u17D3\u17DD\u17E0-\u17E9\u180B-\u180D\u180F-\u1819\u18A9\u1920-\u192B\u1930-\u193B\u1946-\u194F\u19D0-\u19DA\u1A17-\u1A1B\u1A55-\u1A5E\u1A60-\u1A7C\u1A7F-\u1A89\u1A90-\u1A99\u1AB0-\u1ABD\u1ABF-\u1ACE\u1B00-\u1B04\u1B34-\u1B44\u1B50-\u1B59\u1B6B-\u1B73\u1B80-\u1B82\u1BA1-\u1BAD\u1BB0-\u1BB9\u1BE6-\u1BF3\u1C24-\u1C37\u1C40-\u1C49\u1C50-\u1C59\u1CD0-\u1CD2\u1CD4-\u1CE8\u1CED\u1CF4\u1CF7-\u1CF9\u1DC0-\u1DFF\u200C\u200D\u203F\u2040\u2054\u20D0-\u20DC\u20E1\u20E5-\u20F0\u2CEF-\u2CF1\u2D7F\u2DE0-\u2DFF\u302A-\u302F\u3099\u309A\u30FB\uA620-\uA629\uA66F\uA674-\uA67D\uA69E\uA69F\uA6F0\uA6F1\uA802\uA806\uA80B\uA823-\uA827\uA82C\uA880\uA881\uA8B4-\uA8C5\uA8D0-\uA8D9\uA8E0-\uA8F1\uA8FF-\uA909\uA926-\uA92D\uA947-\uA953\uA980-\uA983\uA9B3-\uA9C0\uA9D0-\uA9D9\uA9E5\uA9F0-\uA9F9\uAA29-\uAA36\uAA43\uAA4C\uAA4D\uAA50-\uAA59\uAA7B-\uAA7D\uAAB0\uAAB2-\uAAB4\uAAB7\uAAB8\uAABE\uAABF\uAAC1\uAAEB-\uAAEF\uAAF5\uAAF6\uABE3-\uABEA\uABEC\uABED\uABF0-\uABF9\uFB1E\uFE00-\uFE0F\uFE20-\uFE2F\uFE33\uFE34\uFE4D-\uFE4F\uFF10-\uFF19\uFF3F\uFF65",Cf=new RegExp("["+Er+"]"),Tf=new RegExp("["+Er+us+"]");Er=us=null;var ls=[0,11,2,25,2,18,2,1,2,14,3,13,35,122,70,52,268,28,4,48,48,31,14,29,6,37,11,29,3,35,5,7,2,4,43,157,19,35,5,35,5,39,9,51,13,10,2,14,2,6,2,1,2,10,2,14,2,6,2,1,4,51,13,310,10,21,11,7,25,5,2,41,2,8,70,5,3,0,2,43,2,1,4,0,3,22,11,22,10,30,66,18,2,1,11,21,11,25,71,55,7,1,65,0,16,3,2,2,2,28,43,28,4,28,36,7,2,27,28,53,11,21,11,18,14,17,111,72,56,50,14,50,14,35,39,27,10,22,251,41,7,1,17,2,60,28,11,0,9,21,43,17,47,20,28,22,13,52,58,1,3,0,14,44,33,24,27,35,30,0,3,0,9,34,4,0,13,47,15,3,22,0,2,0,36,17,2,24,20,1,64,6,2,0,2,3,2,14,2,9,8,46,39,7,3,1,3,21,2,6,2,1,2,4,4,0,19,0,13,4,31,9,2,0,3,0,2,37,2,0,26,0,2,0,45,52,19,3,21,2,31,47,21,1,2,0,185,46,42,3,37,47,21,0,60,42,14,0,72,26,38,6,186,43,117,63,32,7,3,0,3,7,2,1,2,23,16,0,2,0,95,7,3,38,17,0,2,0,29,0,11,39,8,0,22,0,12,45,20,0,19,72,200,32,32,8,2,36,18,0,50,29,113,6,2,1,2,37,22,0,26,5,2,1,2,31,15,0,328,18,16,0,2,12,2,33,125,0,80,921,103,110,18,195,2637,96,16,1071,18,5,26,3994,6,582,6842,29,1763,568,8,30,18,78,18,29,19,47,17,3,32,20,6,18,433,44,212,63,129,74,6,0,67,12,65,1,2,0,29,6135,9,1237,42,9,8936,3,2,6,2,1,2,290,16,0,30,2,3,0,15,3,9,395,2309,106,6,12,4,8,8,9,5991,84,2,70,2,1,3,0,3,1,3,3,2,11,2,0,2,6,2,64,2,3,3,7,2,6,2,27,2,3,2,4,2,0,4,6,2,339,3,24,2,24,2,30,2,24,2,30,2,24,2,30,2,24,2,30,2,24,2,7,1845,30,7,5,262,61,147,44,11,6,17,0,322,29,19,43,485,27,229,29,3,0,496,6,2,3,2,1,2,14,2,196,60,67,8,0,1205,3,2,26,2,1,2,0,3,0,2,9,2,3,2,0,2,0,7,0,5,0,2,0,2,0,2,2,2,1,2,0,3,0,2,0,2,0,2,0,2,0,2,1,2,0,3,3,2,6,2,3,2,3,2,0,2,9,2,16,6,2,2,4,2,16,4421,42719,33,4153,7,221,3,5761,15,7472,16,621,2467,541,1507,4938,6,4191],Af=[509,0,227,0,150,4,294,9,1368,2,2,1,6,3,41,2,5,0,166,1,574,3,9,9,7,9,32,4,318,1,80,3,71,10,50,3,123,2,54,14,32,10,3,1,11,3,46,10,8,0,46,9,7,2,37,13,2,9,6,1,45,0,13,2,49,13,9,3,2,11,83,11,7,0,3,0,158,11,6,9,7,3,56,1,2,6,3,1,3,2,10,0,11,1,3,6,4,4,68,8,2,0,3,0,2,3,2,4,2,0,15,1,83,17,10,9,5,0,82,19,13,9,214,6,3,8,28,1,83,16,16,9,82,12,9,9,7,19,58,14,5,9,243,14,166,9,71,5,2,1,3,3,2,0,2,1,13,9,120,6,3,6,4,0,29,9,41,6,2,3,9,0,10,10,47,15,343,9,54,7,2,7,17,9,57,21,2,13,123,5,4,0,2,1,2,6,2,0,9,9,49,4,2,1,2,4,9,9,330,3,10,1,2,0,49,6,4,4,14,10,5350,0,7,14,11465,27,2343,9,87,9,39,4,60,6,26,9,535,9,470,0,2,54,8,3,82,0,12,1,19628,1,4178,9,519,45,3,22,543,4,4,5,9,7,3,6,31,3,149,2,1418,49,513,54,5,49,9,0,15,0,23,4,2,14,1361,6,2,16,3,6,2,1,2,4,101,0,161,6,10,9,357,0,62,13,499,13,245,1,2,9,726,6,110,6,6,9,4759,9,787719,239];function xr(t,e){let r=65536;for(let i=0,n=e.length;i<n;i+=2){if(r+=e[i],r>t)return !1;if(r+=e[i+1],r>=t)return !0}return !1}function cs(t){return t<65?t===36:t<=90?!0:t<97?t===95:t<=122?!0:t<=65535?t>=170&&Cf.test(String.fromCharCode(t)):xr(t,ls)}function hs(t){return t<48?t===36:t<58?!0:t<65?!1:t<=90?!0:t<97?t===95:t<=122?!0:t<=65535?t>=170&&Tf.test(String.fromCharCode(t)):xr(t,ls)||xr(t,Af)}function Rf(t){let e=!0;for(let r=0;r<t.length;r++){let i=t.charCodeAt(r);if((i&64512)===55296&&r+1<t.length){let n=t.charCodeAt(++r);(n&64512)===56320&&(i=65536+((i&1023)<<10)+(n&1023));}if(e){if(e=!1,!cs(i))return !1}else if(!hs(i))return !1}return !e}});var gs=chunkTKGT252T_js.c(ue=>{Object.defineProperty(ue,"__esModule",{value:!0});ue.isKeyword=Mf;ue.isReservedWord=ds;ue.isStrictBindOnlyReservedWord=ms;ue.isStrictBindReservedWord=kf;ue.isStrictReservedWord=ps;var Or={keyword:["break","case","catch","continue","debugger","default","do","else","finally","for","function","if","return","switch","throw","try","var","const","while","with","new","this","super","class","extends","export","import","null","true","false","in","instanceof","typeof","void","delete"],strict:["implements","interface","let","package","private","protected","public","static","yield"],strictBind:["eval","arguments"]},Pf=new Set(Or.keyword),_f=new Set(Or.strict),If=new Set(Or.strictBind);function ds(t,e){return e&&t==="await"||t==="enum"}function ps(t,e){return ds(t,e)||_f.has(t)}function ms(t){return If.has(t)}function kf(t,e){return ps(t,e)||ms(t)}function Mf(t){return Pf.has(t)}});var bs=chunkTKGT252T_js.c(V=>{Object.defineProperty(V,"__esModule",{value:!0});Object.defineProperty(V,"isIdentifierChar",{enumerable:!0,get:function(){return Cr.isIdentifierChar}});Object.defineProperty(V,"isIdentifierName",{enumerable:!0,get:function(){return Cr.isIdentifierName}});Object.defineProperty(V,"isIdentifierStart",{enumerable:!0,get:function(){return Cr.isIdentifierStart}});Object.defineProperty(V,"isKeyword",{enumerable:!0,get:function(){return Le.isKeyword}});Object.defineProperty(V,"isReservedWord",{enumerable:!0,get:function(){return Le.isReservedWord}});Object.defineProperty(V,"isStrictBindOnlyReservedWord",{enumerable:!0,get:function(){return Le.isStrictBindOnlyReservedWord}});Object.defineProperty(V,"isStrictBindReservedWord",{enumerable:!0,get:function(){return Le.isStrictBindReservedWord}});Object.defineProperty(V,"isStrictReservedWord",{enumerable:!0,get:function(){return Le.isStrictReservedWord}});var Cr=fs(),Le=gs();});var vs=chunkTKGT252T_js.c((yp,ys)=>{ys.exports={aliceblue:[240,248,255],antiquewhite:[250,235,215],aqua:[0,255,255],aquamarine:[127,255,212],azure:[240,255,255],beige:[245,245,220],bisque:[255,228,196],black:[0,0,0],blanchedalmond:[255,235,205],blue:[0,0,255],blueviolet:[138,43,226],brown:[165,42,42],burlywood:[222,184,135],cadetblue:[95,158,160],chartreuse:[127,255,0],chocolate:[210,105,30],coral:[255,127,80],cornflowerblue:[100,149,237],cornsilk:[255,248,220],crimson:[220,20,60],cyan:[0,255,255],darkblue:[0,0,139],darkcyan:[0,139,139],darkgoldenrod:[184,134,11],darkgray:[169,169,169],darkgreen:[0,100,0],darkgrey:[169,169,169],darkkhaki:[189,183,107],darkmagenta:[139,0,139],darkolivegreen:[85,107,47],darkorange:[255,140,0],darkorchid:[153,50,204],darkred:[139,0,0],darksalmon:[233,150,122],darkseagreen:[143,188,143],darkslateblue:[72,61,139],darkslategray:[47,79,79],darkslategrey:[47,79,79],darkturquoise:[0,206,209],darkviolet:[148,0,211],deeppink:[255,20,147],deepskyblue:[0,191,255],dimgray:[105,105,105],dimgrey:[105,105,105],dodgerblue:[30,144,255],firebrick:[178,34,34],floralwhite:[255,250,240],forestgreen:[34,139,34],fuchsia:[255,0,255],gainsboro:[220,220,220],ghostwhite:[248,248,255],gold:[255,215,0],goldenrod:[218,165,32],gray:[128,128,128],green:[0,128,0],greenyellow:[173,255,47],grey:[128,128,128],honeydew:[240,255,240],hotpink:[255,105,180],indianred:[205,92,92],indigo:[75,0,130],ivory:[255,255,240],khaki:[240,230,140],lavender:[230,230,250],lavenderblush:[255,240,245],lawngreen:[124,252,0],lemonchiffon:[255,250,205],lightblue:[173,216,230],lightcoral:[240,128,128],lightcyan:[224,255,255],lightgoldenrodyellow:[250,250,210],lightgray:[211,211,211],lightgreen:[144,238,144],lightgrey:[211,211,211],lightpink:[255,182,193],lightsalmon:[255,160,122],lightseagreen:[32,178,170],lightskyblue:[135,206,250],lightslategray:[119,136,153],lightslategrey:[119,136,153],lightsteelblue:[176,196,222],lightyellow:[255,255,224],lime:[0,255,0],limegreen:[50,205,50],linen:[250,240,230],magenta:[255,0,255],maroon:[128,0,0],mediumaquamarine:[102,205,170],mediumblue:[0,0,205],mediumorchid:[186,85,211],mediumpurple:[147,112,219],mediumseagreen:[60,179,113],mediumslateblue:[123,104,238],mediumspringgreen:[0,250,154],mediumturquoise:[72,209,204],mediumvioletred:[199,21,133],midnightblue:[25,25,112],mintcream:[245,255,250],mistyrose:[255,228,225],moccasin:[255,228,181],navajowhite:[255,222,173],navy:[0,0,128],oldlace:[253,245,230],olive:[128,128,0],olivedrab:[107,142,35],orange:[255,165,0],orangered:[255,69,0],orchid:[218,112,214],palegoldenrod:[238,232,170],palegreen:[152,251,152],paleturquoise:[175,238,238],palevioletred:[219,112,147],papayawhip:[255,239,213],peachpuff:[255,218,185],peru:[205,133,63],pink:[255,192,203],plum:[221,160,221],powderblue:[176,224,230],purple:[128,0,128],rebeccapurple:[102,51,153],red:[255,0,0],rosybrown:[188,143,143],royalblue:[65,105,225],saddlebrown:[139,69,19],salmon:[250,128,114],sandybrown:[244,164,96],seagreen:[46,139,87],seashell:[255,245,238],sienna:[160,82,45],silver:[192,192,192],skyblue:[135,206,235],slateblue:[106,90,205],slategray:[112,128,144],slategrey:[112,128,144],snow:[255,250,250],springgreen:[0,255,127],steelblue:[70,130,180],tan:[210,180,140],teal:[0,128,128],thistle:[216,191,216],tomato:[255,99,71],turquoise:[64,224,208],violet:[238,130,238],wheat:[245,222,179],white:[255,255,255],whitesmoke:[245,245,245],yellow:[255,255,0],yellowgreen:[154,205,50]};});var Tr=chunkTKGT252T_js.c((vp,xs)=>{var le=vs(),Ss={};for(ut in le)le.hasOwnProperty(ut)&&(Ss[le[ut]]=ut);var ut,g=xs.exports={rgb:{channels:3,labels:"rgb"},hsl:{channels:3,labels:"hsl"},hsv:{channels:3,labels:"hsv"},hwb:{channels:3,labels:"hwb"},cmyk:{channels:4,labels:"cmyk"},xyz:{channels:3,labels:"xyz"},lab:{channels:3,labels:"lab"},lch:{channels:3,labels:"lch"},hex:{channels:1,labels:["hex"]},keyword:{channels:1,labels:["keyword"]},ansi16:{channels:1,labels:["ansi16"]},ansi256:{channels:1,labels:["ansi256"]},hcg:{channels:3,labels:["h","c","g"]},apple:{channels:3,labels:["r16","g16","b16"]},gray:{channels:1,labels:["gray"]}};for(L in g)if(g.hasOwnProperty(L)){if(!("channels"in g[L]))throw new Error("missing channels property: "+L);if(!("labels"in g[L]))throw new Error("missing channel labels property: "+L);if(g[L].labels.length!==g[L].channels)throw new Error("channel and label counts mismatch: "+L);ws=g[L].channels,$s=g[L].labels,delete g[L].channels,delete g[L].labels,Object.defineProperty(g[L],"channels",{value:ws}),Object.defineProperty(g[L],"labels",{value:$s});}var ws,$s,L;g.rgb.hsl=function(t){var e=t[0]/255,r=t[1]/255,i=t[2]/255,n=Math.min(e,r,i),s=Math.max(e,r,i),o=s-n,a,u,l;return s===n?a=0:e===s?a=(r-i)/o:r===s?a=2+(i-e)/o:i===s&&(a=4+(e-r)/o),a=Math.min(a*60,360),a<0&&(a+=360),l=(n+s)/2,s===n?u=0:l<=.5?u=o/(s+n):u=o/(2-s-n),[a,u*100,l*100]};g.rgb.hsv=function(t){var e,r,i,n,s,o=t[0]/255,a=t[1]/255,u=t[2]/255,l=Math.max(o,a,u),c=l-Math.min(o,a,u),h=function(d){return (l-d)/6/c+1/2};return c===0?n=s=0:(s=c/l,e=h(o),r=h(a),i=h(u),o===l?n=i-r:a===l?n=1/3+e-i:u===l&&(n=2/3+r-e),n<0?n+=1:n>1&&(n-=1)),[n*360,s*100,l*100]};g.rgb.hwb=function(t){var e=t[0],r=t[1],i=t[2],n=g.rgb.hsl(t)[0],s=1/255*Math.min(e,Math.min(r,i));return i=1-1/255*Math.max(e,Math.max(r,i)),[n,s*100,i*100]};g.rgb.cmyk=function(t){var e=t[0]/255,r=t[1]/255,i=t[2]/255,n,s,o,a;return a=Math.min(1-e,1-r,1-i),n=(1-e-a)/(1-a)||0,s=(1-r-a)/(1-a)||0,o=(1-i-a)/(1-a)||0,[n*100,s*100,o*100,a*100]};function jf(t,e){return Math.pow(t[0]-e[0],2)+Math.pow(t[1]-e[1],2)+Math.pow(t[2]-e[2],2)}g.rgb.keyword=function(t){var e=Ss[t];if(e)return e;var r=1/0,i;for(var n in le)if(le.hasOwnProperty(n)){var s=le[n],o=jf(t,s);o<r&&(r=o,i=n);}return i};g.keyword.rgb=function(t){return le[t]};g.rgb.xyz=function(t){var e=t[0]/255,r=t[1]/255,i=t[2]/255;e=e>.04045?Math.pow((e+.055)/1.055,2.4):e/12.92,r=r>.04045?Math.pow((r+.055)/1.055,2.4):r/12.92,i=i>.04045?Math.pow((i+.055)/1.055,2.4):i/12.92;var n=e*.4124+r*.3576+i*.1805,s=e*.2126+r*.7152+i*.0722,o=e*.0193+r*.1192+i*.9505;return [n*100,s*100,o*100]};g.rgb.lab=function(t){var e=g.rgb.xyz(t),r=e[0],i=e[1],n=e[2],s,o,a;return r/=95.047,i/=100,n/=108.883,r=r>.008856?Math.pow(r,1/3):7.787*r+16/116,i=i>.008856?Math.pow(i,1/3):7.787*i+16/116,n=n>.008856?Math.pow(n,1/3):7.787*n+16/116,s=116*i-16,o=500*(r-i),a=200*(i-n),[s,o,a]};g.hsl.rgb=function(t){var e=t[0]/360,r=t[1]/100,i=t[2]/100,n,s,o,a,u;if(r===0)return u=i*255,[u,u,u];i<.5?s=i*(1+r):s=i+r-i*r,n=2*i-s,a=[0,0,0];for(var l=0;l<3;l++)o=e+1/3*-(l-1),o<0&&o++,o>1&&o--,6*o<1?u=n+(s-n)*6*o:2*o<1?u=s:3*o<2?u=n+(s-n)*(2/3-o)*6:u=n,a[l]=u*255;return a};g.hsl.hsv=function(t){var e=t[0],r=t[1]/100,i=t[2]/100,n=r,s=Math.max(i,.01),o,a;return i*=2,r*=i<=1?i:2-i,n*=s<=1?s:2-s,a=(i+r)/2,o=i===0?2*n/(s+n):2*r/(i+r),[e,o*100,a*100]};g.hsv.rgb=function(t){var e=t[0]/60,r=t[1]/100,i=t[2]/100,n=Math.floor(e)%6,s=e-Math.floor(e),o=255*i*(1-r),a=255*i*(1-r*s),u=255*i*(1-r*(1-s));switch(i*=255,n){case 0:return [i,u,o];case 1:return [a,i,o];case 2:return [o,i,u];case 3:return [o,a,i];case 4:return [u,o,i];case 5:return [i,o,a]}};g.hsv.hsl=function(t){var e=t[0],r=t[1]/100,i=t[2]/100,n=Math.max(i,.01),s,o,a;return a=(2-r)*i,s=(2-r)*n,o=r*n,o/=s<=1?s:2-s,o=o||0,a/=2,[e,o*100,a*100]};g.hwb.rgb=function(t){var e=t[0]/360,r=t[1]/100,i=t[2]/100,n=r+i,s,o,a,u;n>1&&(r/=n,i/=n),s=Math.floor(6*e),o=1-i,a=6*e-s,s&1&&(a=1-a),u=r+a*(o-r);var l,c,h;switch(s){default:case 6:case 0:l=o,c=u,h=r;break;case 1:l=u,c=o,h=r;break;case 2:l=r,c=o,h=u;break;case 3:l=r,c=u,h=o;break;case 4:l=u,c=r,h=o;break;case 5:l=o,c=r,h=u;break}return [l*255,c*255,h*255]};g.cmyk.rgb=function(t){var e=t[0]/100,r=t[1]/100,i=t[2]/100,n=t[3]/100,s,o,a;return s=1-Math.min(1,e*(1-n)+n),o=1-Math.min(1,r*(1-n)+n),a=1-Math.min(1,i*(1-n)+n),[s*255,o*255,a*255]};g.xyz.rgb=function(t){var e=t[0]/100,r=t[1]/100,i=t[2]/100,n,s,o;return n=e*3.2406+r*-1.5372+i*-.4986,s=e*-.9689+r*1.8758+i*.0415,o=e*.0557+r*-.204+i*1.057,n=n>.0031308?1.055*Math.pow(n,1/2.4)-.055:n*12.92,s=s>.0031308?1.055*Math.pow(s,1/2.4)-.055:s*12.92,o=o>.0031308?1.055*Math.pow(o,1/2.4)-.055:o*12.92,n=Math.min(Math.max(0,n),1),s=Math.min(Math.max(0,s),1),o=Math.min(Math.max(0,o),1),[n*255,s*255,o*255]};g.xyz.lab=function(t){var e=t[0],r=t[1],i=t[2],n,s,o;return e/=95.047,r/=100,i/=108.883,e=e>.008856?Math.pow(e,1/3):7.787*e+16/116,r=r>.008856?Math.pow(r,1/3):7.787*r+16/116,i=i>.008856?Math.pow(i,1/3):7.787*i+16/116,n=116*r-16,s=500*(e-r),o=200*(r-i),[n,s,o]};g.lab.xyz=function(t){var e=t[0],r=t[1],i=t[2],n,s,o;s=(e+16)/116,n=r/500+s,o=s-i/200;var a=Math.pow(s,3),u=Math.pow(n,3),l=Math.pow(o,3);return s=a>.008856?a:(s-16/116)/7.787,n=u>.008856?u:(n-16/116)/7.787,o=l>.008856?l:(o-16/116)/7.787,n*=95.047,s*=100,o*=108.883,[n,s,o]};g.lab.lch=function(t){var e=t[0],r=t[1],i=t[2],n,s,o;return n=Math.atan2(i,r),s=n*360/2/Math.PI,s<0&&(s+=360),o=Math.sqrt(r*r+i*i),[e,o,s]};g.lch.lab=function(t){var e=t[0],r=t[1],i=t[2],n,s,o;return o=i/360*2*Math.PI,n=r*Math.cos(o),s=r*Math.sin(o),[e,n,s]};g.rgb.ansi16=function(t){var e=t[0],r=t[1],i=t[2],n=1 in arguments?arguments[1]:g.rgb.hsv(t)[2];if(n=Math.round(n/50),n===0)return 30;var s=30+(Math.round(i/255)<<2|Math.round(r/255)<<1|Math.round(e/255));return n===2&&(s+=60),s};g.hsv.ansi16=function(t){return g.rgb.ansi16(g.hsv.rgb(t),t[2])};g.rgb.ansi256=function(t){var e=t[0],r=t[1],i=t[2];if(e===r&&r===i)return e<8?16:e>248?231:Math.round((e-8)/247*24)+232;var n=16+36*Math.round(e/255*5)+6*Math.round(r/255*5)+Math.round(i/255*5);return n};g.ansi16.rgb=function(t){var e=t%10;if(e===0||e===7)return t>50&&(e+=3.5),e=e/10.5*255,[e,e,e];var r=(~~(t>50)+1)*.5,i=(e&1)*r*255,n=(e>>1&1)*r*255,s=(e>>2&1)*r*255;return [i,n,s]};g.ansi256.rgb=function(t){if(t>=232){var e=(t-232)*10+8;return [e,e,e]}t-=16;var r,i=Math.floor(t/36)/5*255,n=Math.floor((r=t%36)/6)/5*255,s=r%6/5*255;return [i,n,s]};g.rgb.hex=function(t){var e=((Math.round(t[0])&255)<<16)+((Math.round(t[1])&255)<<8)+(Math.round(t[2])&255),r=e.toString(16).toUpperCase();return "000000".substring(r.length)+r};g.hex.rgb=function(t){var e=t.toString(16).match(/[a-f0-9]{6}|[a-f0-9]{3}/i);if(!e)return [0,0,0];var r=e[0];e[0].length===3&&(r=r.split("").map(function(a){return a+a}).join(""));var i=parseInt(r,16),n=i>>16&255,s=i>>8&255,o=i&255;return [n,s,o]};g.rgb.hcg=function(t){var e=t[0]/255,r=t[1]/255,i=t[2]/255,n=Math.max(Math.max(e,r),i),s=Math.min(Math.min(e,r),i),o=n-s,a,u;return o<1?a=s/(1-o):a=0,o<=0?u=0:n===e?u=(r-i)/o%6:n===r?u=2+(i-e)/o:u=4+(e-r)/o+4,u/=6,u%=1,[u*360,o*100,a*100]};g.hsl.hcg=function(t){var e=t[1]/100,r=t[2]/100,i=1,n=0;return r<.5?i=2*e*r:i=2*e*(1-r),i<1&&(n=(r-.5*i)/(1-i)),[t[0],i*100,n*100]};g.hsv.hcg=function(t){var e=t[1]/100,r=t[2]/100,i=e*r,n=0;return i<1&&(n=(r-i)/(1-i)),[t[0],i*100,n*100]};g.hcg.rgb=function(t){var e=t[0]/360,r=t[1]/100,i=t[2]/100;if(r===0)return [i*255,i*255,i*255];var n=[0,0,0],s=e%1*6,o=s%1,a=1-o,u=0;switch(Math.floor(s)){case 0:n[0]=1,n[1]=o,n[2]=0;break;case 1:n[0]=a,n[1]=1,n[2]=0;break;case 2:n[0]=0,n[1]=1,n[2]=o;break;case 3:n[0]=0,n[1]=a,n[2]=1;break;case 4:n[0]=o,n[1]=0,n[2]=1;break;default:n[0]=1,n[1]=0,n[2]=a;}return u=(1-r)*i,[(r*n[0]+u)*255,(r*n[1]+u)*255,(r*n[2]+u)*255]};g.hcg.hsv=function(t){var e=t[1]/100,r=t[2]/100,i=e+r*(1-e),n=0;return i>0&&(n=e/i),[t[0],n*100,i*100]};g.hcg.hsl=function(t){var e=t[1]/100,r=t[2]/100,i=r*(1-e)+.5*e,n=0;return i>0&&i<.5?n=e/(2*i):i>=.5&&i<1&&(n=e/(2*(1-i))),[t[0],n*100,i*100]};g.hcg.hwb=function(t){var e=t[1]/100,r=t[2]/100,i=e+r*(1-e);return [t[0],(i-e)*100,(1-i)*100]};g.hwb.hcg=function(t){var e=t[1]/100,r=t[2]/100,i=1-r,n=i-e,s=0;return n<1&&(s=(i-n)/(1-n)),[t[0],n*100,s*100]};g.apple.rgb=function(t){return [t[0]/65535*255,t[1]/65535*255,t[2]/65535*255]};g.rgb.apple=function(t){return [t[0]/255*65535,t[1]/255*65535,t[2]/255*65535]};g.gray.rgb=function(t){return [t[0]/100*255,t[0]/100*255,t[0]/100*255]};g.gray.hsl=g.gray.hsv=function(t){return [0,0,t[0]]};g.gray.hwb=function(t){return [0,100,t[0]]};g.gray.cmyk=function(t){return [0,0,0,t[0]]};g.gray.lab=function(t){return [t[0],0,0]};g.gray.hex=function(t){var e=Math.round(t[0]/100*255)&255,r=(e<<16)+(e<<8)+e,i=r.toString(16).toUpperCase();return "000000".substring(i.length)+i};g.rgb.gray=function(t){var e=(t[0]+t[1]+t[2])/3;return [e/255*100]};});var Os=chunkTKGT252T_js.c((wp,Es)=>{var lt=Tr();function Ff(){for(var t={},e=Object.keys(lt),r=e.length,i=0;i<r;i++)t[e[i]]={distance:-1,parent:null};return t}function Df(t){var e=Ff(),r=[t];for(e[t].distance=0;r.length;)for(var i=r.pop(),n=Object.keys(lt[i]),s=n.length,o=0;o<s;o++){var a=n[o],u=e[a];u.distance===-1&&(u.distance=e[i].distance+1,u.parent=i,r.unshift(a));}return e}function Lf(t,e){return function(r){return e(t(r))}}function Nf(t,e){for(var r=[e[t].parent,t],i=lt[e[t].parent][t],n=e[t].parent;e[n].parent;)r.unshift(e[n].parent),i=Lf(lt[e[n].parent][n],i),n=e[n].parent;return i.conversion=r,i}Es.exports=function(t){for(var e=Df(t),r={},i=Object.keys(e),n=i.length,s=0;s<n;s++){var o=i[s],a=e[o];a.parent!==null&&(r[o]=Nf(o,e));}return r};});var Ts=chunkTKGT252T_js.c(($p,Cs)=>{var Ar=Tr(),Bf=Os(),$e={},Gf=Object.keys(Ar);function Uf(t){var e=function(r){return r==null?r:(arguments.length>1&&(r=Array.prototype.slice.call(arguments)),t(r))};return "conversion"in t&&(e.conversion=t.conversion),e}function Wf(t){var e=function(r){if(r==null)return r;arguments.length>1&&(r=Array.prototype.slice.call(arguments));var i=t(r);if(typeof i=="object")for(var n=i.length,s=0;s<n;s++)i[s]=Math.round(i[s]);return i};return "conversion"in t&&(e.conversion=t.conversion),e}Gf.forEach(function(t){$e[t]={},Object.defineProperty($e[t],"channels",{value:Ar[t].channels}),Object.defineProperty($e[t],"labels",{value:Ar[t].labels});var e=Bf(t),r=Object.keys(e);r.forEach(function(i){var n=e[i];$e[t][i]=Wf(n),$e[t][i].raw=Uf(n);});});Cs.exports=$e;});var Rs=chunkTKGT252T_js.c((Sp,As)=>{var Se=Ts(),ct=(t,e)=>function(){return `\x1B[${t.apply(Se,arguments)+e}m`},ht=(t,e)=>function(){let r=t.apply(Se,arguments);return `\x1B[${38+e};5;${r}m`},ft=(t,e)=>function(){let r=t.apply(Se,arguments);return `\x1B[${38+e};2;${r[0]};${r[1]};${r[2]}m`};function zf(){let t=new Map,e={modifier:{reset:[0,0],bold:[1,22],dim:[2,22],italic:[3,23],underline:[4,24],inverse:[7,27],hidden:[8,28],strikethrough:[9,29]},color:{black:[30,39],red:[31,39],green:[32,39],yellow:[33,39],blue:[34,39],magenta:[35,39],cyan:[36,39],white:[37,39],gray:[90,39],redBright:[91,39],greenBright:[92,39],yellowBright:[93,39],blueBright:[94,39],magentaBright:[95,39],cyanBright:[96,39],whiteBright:[97,39]},bgColor:{bgBlack:[40,49],bgRed:[41,49],bgGreen:[42,49],bgYellow:[43,49],bgBlue:[44,49],bgMagenta:[45,49],bgCyan:[46,49],bgWhite:[47,49],bgBlackBright:[100,49],bgRedBright:[101,49],bgGreenBright:[102,49],bgYellowBright:[103,49],bgBlueBright:[104,49],bgMagentaBright:[105,49],bgCyanBright:[106,49],bgWhiteBright:[107,49]}};e.color.grey=e.color.gray;for(let n of Object.keys(e)){let s=e[n];for(let o of Object.keys(s)){let a=s[o];e[o]={open:`\x1B[${a[0]}m`,close:`\x1B[${a[1]}m`},s[o]=e[o],t.set(a[0],a[1]);}Object.defineProperty(e,n,{value:s,enumerable:!1}),Object.defineProperty(e,"codes",{value:t,enumerable:!1});}let r=n=>n,i=(n,s,o)=>[n,s,o];e.color.close="\x1B[39m",e.bgColor.close="\x1B[49m",e.color.ansi={ansi:ct(r,0)},e.color.ansi256={ansi256:ht(r,0)},e.color.ansi16m={rgb:ft(i,0)},e.bgColor.ansi={ansi:ct(r,10)},e.bgColor.ansi256={ansi256:ht(r,10)},e.bgColor.ansi16m={rgb:ft(i,10)};for(let n of Object.keys(Se)){if(typeof Se[n]!="object")continue;let s=Se[n];n==="ansi16"&&(n="ansi"),"ansi16"in s&&(e.color.ansi[n]=ct(s.ansi16,0),e.bgColor.ansi[n]=ct(s.ansi16,10)),"ansi256"in s&&(e.color.ansi256[n]=ht(s.ansi256,0),e.bgColor.ansi256[n]=ht(s.ansi256,10)),"rgb"in s&&(e.color.ansi16m[n]=ft(s.rgb,0),e.bgColor.ansi16m[n]=ft(s.rgb,10));}return e}Object.defineProperty(As,"exports",{enumerable:!0,get:zf});});var _s=chunkTKGT252T_js.c((xp,Ps)=>{Ps.exports=(t,e)=>{e=e||process.argv;let r=t.startsWith("-")?"":t.length===1?"-":"--",i=e.indexOf(r+t),n=e.indexOf("--");return i!==-1&&(n===-1?!0:i<n)};});var ks=chunkTKGT252T_js.c((Ep,Is)=>{var Hf=chunkTKGT252T_js.a("os"),z=_s(),F=process.env,xe;z("no-color")||z("no-colors")||z("color=false")?xe=!1:(z("color")||z("colors")||z("color=true")||z("color=always"))&&(xe=!0);"FORCE_COLOR"in F&&(xe=F.FORCE_COLOR.length===0||parseInt(F.FORCE_COLOR,10)!==0);function qf(t){return t===0?!1:{level:t,hasBasic:!0,has256:t>=2,has16m:t>=3}}function Vf(t){if(xe===!1)return 0;if(z("color=16m")||z("color=full")||z("color=truecolor"))return 3;if(z("color=256"))return 2;if(t&&!t.isTTY&&xe!==!0)return 0;let e=xe?1:0;if(process.platform==="win32"){let r=Hf.release().split(".");return Number(process.versions.node.split(".")[0])>=8&&Number(r[0])>=10&&Number(r[2])>=10586?Number(r[2])>=14931?3:2:1}if("CI"in F)return ["TRAVIS","CIRCLECI","APPVEYOR","GITLAB_CI"].some(r=>r in F)||F.CI_NAME==="codeship"?1:e;if("TEAMCITY_VERSION"in F)return /^(9\.(0*[1-9]\d*)\.|\d{2,}\.)/.test(F.TEAMCITY_VERSION)?1:0;if(F.COLORTERM==="truecolor")return 3;if("TERM_PROGRAM"in F){let r=parseInt((F.TERM_PROGRAM_VERSION||"").split(".")[0],10);switch(F.TERM_PROGRAM){case"iTerm.app":return r>=3?3:2;case"Apple_Terminal":return 2}}return /-256(color)?$/i.test(F.TERM)?2:/^screen|^xterm|^vt100|^vt220|^rxvt|color|ansi|cygwin|linux/i.test(F.TERM)||"COLORTERM"in F?1:(F.TERM==="dumb",e)}function Rr(t){let e=Vf(t);return qf(e)}Is.exports={supportsColor:Rr,stdout:Rr(process.stdout),stderr:Rr(process.stderr)};});var Ls=chunkTKGT252T_js.c((Op,Ds)=>{var Yf=/(?:\\(u[a-f\d]{4}|x[a-f\d]{2}|.))|(?:\{(~)?(\w+(?:\([^)]*\))?(?:\.\w+(?:\([^)]*\))?)*)(?:[ \t]|(?=\r?\n)))|(\})|((?:.|[\r\n\f])+?)/gi,Ms=/(?:^|\.)(\w+)(?:\(([^)]*)\))?/g,Kf=/^(['"])((?:\\.|(?!\1)[^\\])*)\1$/,Jf=/\\(u[a-f\d]{4}|x[a-f\d]{2}|.)|([^\\])/gi,Xf=new Map([["n",`
`],["r","\r"],["t","	"],["b","\b"],["f","\f"],["v","\v"],["0","\0"],["\\","\\"],["e","\x1B"],["a","\x07"]]);function Fs(t){return t[0]==="u"&&t.length===5||t[0]==="x"&&t.length===3?String.fromCharCode(parseInt(t.slice(1),16)):Xf.get(t)||t}function Zf(t,e){let r=[],i=e.trim().split(/\s*,\s*/g),n;for(let s of i)if(!isNaN(s))r.push(Number(s));else if(n=s.match(Kf))r.push(n[2].replace(Jf,(o,a,u)=>a?Fs(a):u));else throw new Error(`Invalid Chalk template style argument: ${s} (in style '${t}')`);return r}function Qf(t){Ms.lastIndex=0;let e=[],r;for(;(r=Ms.exec(t))!==null;){let i=r[1];if(r[2]){let n=Zf(i,r[2]);e.push([i].concat(n));}else e.push([i]);}return e}function js(t,e){let r={};for(let n of e)for(let s of n.styles)r[s[0]]=n.inverse?null:s.slice(1);let i=t;for(let n of Object.keys(r))if(Array.isArray(r[n])){if(!(n in i))throw new Error(`Unknown Chalk style: ${n}`);r[n].length>0?i=i[n].apply(i,r[n]):i=i[n];}return i}Ds.exports=(t,e)=>{let r=[],i=[],n=[];if(e.replace(Yf,(s,o,a,u,l,c)=>{if(o)n.push(Fs(o));else if(u){let h=n.join("");n=[],i.push(r.length===0?h:js(t,r)(h)),r.push({inverse:a,styles:Qf(u)});}else if(l){if(r.length===0)throw new Error("Found extraneous } in Chalk template literal");i.push(js(t,r)(n.join(""))),n=[],r.pop();}else n.push(c);}),i.push(n.join("")),r.length>0){let s=`Chalk template literal is missing ${r.length} closing bracket${r.length===1?"":"s"} (\`}\`)`;throw new Error(s)}return i.join("")};});var Ws=chunkTKGT252T_js.c((Cp,Be)=>{var _r=chunk7UHX5T7X_js.p(),I=Rs(),Pr=ks().stdout,ed=Ls(),Bs=process.platform==="win32"&&!(process.env.TERM||"").toLowerCase().startsWith("xterm"),Gs=["ansi","ansi","ansi256","ansi16m"],Us=new Set(["gray"]),Ee=Object.create(null);function Ns(t,e){e=e||{};let r=Pr?Pr.level:0;t.level=e.level===void 0?r:e.level,t.enabled="enabled"in e?e.enabled:t.level>0;}function Ne(t){if(!this||!(this instanceof Ne)||this.template){let e={};return Ns(e,t),e.template=function(){let r=[].slice.call(arguments);return id.apply(null,[e.template].concat(r))},Object.setPrototypeOf(e,Ne.prototype),Object.setPrototypeOf(e.template,e),e.template.constructor=Ne,e.template}Ns(this,t);}Bs&&(I.blue.open="\x1B[94m");for(let t of Object.keys(I))I[t].closeRe=new RegExp(_r(I[t].close),"g"),Ee[t]={get(){let e=I[t];return dt.call(this,this._styles?this._styles.concat(e):[e],this._empty,t)}};Ee.visible={get(){return dt.call(this,this._styles||[],!0,"visible")}};I.color.closeRe=new RegExp(_r(I.color.close),"g");for(let t of Object.keys(I.color.ansi))Us.has(t)||(Ee[t]={get(){let e=this.level;return function(){let i={open:I.color[Gs[e]][t].apply(null,arguments),close:I.color.close,closeRe:I.color.closeRe};return dt.call(this,this._styles?this._styles.concat(i):[i],this._empty,t)}}});I.bgColor.closeRe=new RegExp(_r(I.bgColor.close),"g");for(let t of Object.keys(I.bgColor.ansi)){if(Us.has(t))continue;let e="bg"+t[0].toUpperCase()+t.slice(1);Ee[e]={get(){let r=this.level;return function(){let n={open:I.bgColor[Gs[r]][t].apply(null,arguments),close:I.bgColor.close,closeRe:I.bgColor.closeRe};return dt.call(this,this._styles?this._styles.concat(n):[n],this._empty,t)}}};}var td=Object.defineProperties(()=>{},Ee);function dt(t,e,r){let i=function(){return rd.apply(i,arguments)};i._styles=t,i._empty=e;let n=this;return Object.defineProperty(i,"level",{enumerable:!0,get(){return n.level},set(s){n.level=s;}}),Object.defineProperty(i,"enabled",{enumerable:!0,get(){return n.enabled},set(s){n.enabled=s;}}),i.hasGrey=this.hasGrey||r==="gray"||r==="grey",i.__proto__=td,i}function rd(){let t=arguments,e=t.length,r=String(arguments[0]);if(e===0)return "";if(e>1)for(let n=1;n<e;n++)r+=" "+t[n];if(!this.enabled||this.level<=0||!r)return this._empty?"":r;let i=I.dim.open;Bs&&this.hasGrey&&(I.dim.open="");for(let n of this._styles.slice().reverse())r=n.open+r.replace(n.closeRe,n.open)+n.close,r=r.replace(/\r?\n/g,`${n.close}$&${n.open}`);return I.dim.open=i,r}function id(t,e){if(!Array.isArray(e))return [].slice.call(arguments,1).join(" ");let r=[].slice.call(arguments,2),i=[e.raw[0]];for(let n=1;n<e.length;n++)i.push(String(r[n-1]).replace(/[{}\\]/g,"\\$&")),i.push(String(e.raw[n]));return ed(t,i.join(""))}Object.defineProperties(Ne.prototype,Ee);Be.exports=Ne();Be.exports.supportsColor=Pr;Be.exports.default=Be.exports;});var Xs=chunkTKGT252T_js.c(Ge=>{Object.defineProperty(Ge,"__esModule",{value:!0});Ge.default=hd;Ge.shouldHighlight=Js;var zs=chunk7UHX5T7X_js.m(),Hs=bs(),kr=nd(chunk7UHX5T7X_js.o(),!0);function Vs(t){if(typeof WeakMap!="function")return null;var e=new WeakMap,r=new WeakMap;return (Vs=function(i){return i?r:e})(t)}function nd(t,e){if(!e&&t&&t.__esModule)return t;if(t===null||typeof t!="object"&&typeof t!="function")return {default:t};var r=Vs(e);if(r&&r.has(t))return r.get(t);var i={__proto__:null},n=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var s in t)if(s!=="default"&&{}.hasOwnProperty.call(t,s)){var o=n?Object.getOwnPropertyDescriptor(t,s):null;o&&(o.get||o.set)?Object.defineProperty(i,s,o):i[s]=t[s];}return i.default=t,r&&r.set(t,i),i}var Ys=typeof process=="object"&&(process.env.FORCE_COLOR==="0"||process.env.FORCE_COLOR==="false")?(0, kr.createColors)(!1):kr.default,qs=(t,e)=>r=>t(e(r)),sd=new Set(["as","async","from","get","of","set"]);function od(t){return {keyword:t.cyan,capitalized:t.yellow,jsxIdentifier:t.yellow,punctuator:t.yellow,number:t.magenta,string:t.green,regex:t.magenta,comment:t.gray,invalid:qs(qs(t.white,t.bgRed),t.bold)}}var ad=/\r\n|[\n\r\u2028\u2029]/,ud=/^[()[\]{}]$/,Ks;{let t=/^[a-z][\w-]*$/i,e=function(r,i,n){if(r.type==="name"){if((0, Hs.isKeyword)(r.value)||(0, Hs.isStrictReservedWord)(r.value,!0)||sd.has(r.value))return "keyword";if(t.test(r.value)&&(n[i-1]==="<"||n.slice(i-2,i)==="</"))return "jsxIdentifier";if(r.value[0]!==r.value[0].toLowerCase())return "capitalized"}return r.type==="punctuator"&&ud.test(r.value)?"bracket":r.type==="invalid"&&(r.value==="@"||r.value==="#")?"punctuator":r.type};Ks=function*(r){let i;for(;i=zs.default.exec(r);){let n=zs.matchToToken(i);yield {type:e(n,i.index,r),value:n.value};}};}function ld(t,e){let r="";for(let{type:i,value:n}of Ks(e)){let s=t[i];s?r+=n.split(ad).map(o=>s(o)).join(`
`):r+=n;}return r}function Js(t){return Ys.isColorSupported||t.forceColor}var Ir;function cd(t){if(t){return (Ir)!=null||(Ir=(0, kr.createColors)(!0)),Ir}return Ys}function hd(t,e={}){if(t!==""&&Js(e)){let r=od(cd(e.forceColor));return ld(r,t)}else return t}{let t,e;Ge.getChalk=({forceColor:r})=>{if((t)!=null||(t=Ws()),r){return (e)!=null||(e=new t.constructor({enabled:!0,level:1})),e}return t};}});var no=chunkTKGT252T_js.c(pt=>{Object.defineProperty(pt,"__esModule",{value:!0});pt.codeFrameColumns=io;pt.default=bd;var Zs=Xs(),jr=fd(chunk7UHX5T7X_js.o(),!0);function ro(t){if(typeof WeakMap!="function")return null;var e=new WeakMap,r=new WeakMap;return (ro=function(i){return i?r:e})(t)}function fd(t,e){if(!e&&t&&t.__esModule)return t;if(t===null||typeof t!="object"&&typeof t!="function")return {default:t};var r=ro(e);if(r&&r.has(t))return r.get(t);var i={__proto__:null},n=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var s in t)if(s!=="default"&&{}.hasOwnProperty.call(t,s)){var o=n?Object.getOwnPropertyDescriptor(t,s):null;o&&(o.get||o.set)?Object.defineProperty(i,s,o):i[s]=t[s];}return i.default=t,r&&r.set(t,i),i}var dd=typeof process=="object"&&(process.env.FORCE_COLOR==="0"||process.env.FORCE_COLOR==="false")?(0, jr.createColors)(!1):jr.default,Qs=(t,e)=>r=>t(e(r)),Mr;function pd(t){if(t){return (Mr)!=null||(Mr=(0, jr.createColors)(!0)),Mr}return dd}var eo=!1;function md(t){return {gutter:t.gray,marker:Qs(t.red,t.bold),message:Qs(t.red,t.bold)}}var to=/\r\n|[\n\r\u2028\u2029]/;function gd(t,e,r){let i=Object.assign({column:0,line:-1},t.start),n=Object.assign({},i,t.end),{linesAbove:s=2,linesBelow:o=3}=r||{},a=i.line,u=i.column,l=n.line,c=n.column,h=Math.max(a-(s+1),0),d=Math.min(e.length,l+o);a===-1&&(h=0),l===-1&&(d=e.length);let f=l-a,b={};if(f)for(let m=0;m<=f;m++){let y=m+a;if(!u)b[y]=!0;else if(m===0){let p=e[y-1].length;b[y]=[u,p-u+1];}else if(m===f)b[y]=[0,c];else {let p=e[y-m].length;b[y]=[0,p];}}else u===c?u?b[a]=[u,0]:b[a]=!0:b[a]=[u,c-u];return {start:h,end:d,markerLines:b}}function io(t,e,r={}){let i=(r.highlightCode||r.forceColor)&&(0, Zs.shouldHighlight)(r),n=pd(r.forceColor),s=md(n),o=(m,y)=>i?m(y):y,a=t.split(to),{start:u,end:l,markerLines:c}=gd(e,a,r),h=e.start&&typeof e.start.column=="number",d=String(l).length,b=(i?(0, Zs.default)(t,r):t).split(to,l).slice(u,l).map((m,y)=>{let p=u+1+y,O=` ${` ${p}`.slice(-d)} |`,A=c[p],j=!c[p+1];if(A){let N="";if(Array.isArray(A)){let B=m.slice(0,Math.max(A[0]-1,0)).replace(/[^\t]/g," "),wt=A[1]||1;N=[`
 `,o(s.gutter,O.replace(/\d/g," "))," ",B,o(s.marker,"^").repeat(wt)].join(""),j&&r.message&&(N+=" "+o(s.message,r.message));}return [o(s.marker,">"),o(s.gutter,O),m.length>0?` ${m}`:"",N].join("")}else return ` ${o(s.gutter,O)}${m.length>0?` ${m}`:""}`}).join(`
`);return r.message&&!h&&(b=`${" ".repeat(d+1)}${r.message}
${b}`),i?n.reset(b):b}function bd(t,e,r,i={}){if(!eo){eo=!0;let s="Passing lineNumber and colNumber is deprecated to @babel/code-frame. Please use `codeFrameColumns`.";if(process.emitWarning)process.emitWarning(s,"DeprecationWarning");else {let o=new Error(s);o.name="DeprecationWarning",console.warn(new Error(s));}}return r=Math.max(r,0),io(t,{start:{column:r,line:e}},i)}});var po=chunkTKGT252T_js.c(bt=>{Object.defineProperty(bt,"__esModule",{value:!0});bt.LRUCache=void 0;var Oe=typeof performance=="object"&&performance&&typeof performance.now=="function"?performance:Date,co=new Set,Lr=typeof process=="object"&&process?process:{},ho=(t,e,r,i)=>{typeof Lr.emitWarning=="function"?Lr.emitWarning(t,e,r,i):console.error(`[${r}] ${e}: ${t}`);},gt=globalThis.AbortController,uo=globalThis.AbortSignal,lo;if(typeof gt>"u"){uo=class{onabort;_onabort=[];reason;aborted=!1;addEventListener(i,n){this._onabort.push(n);}},gt=class{constructor(){e();}signal=new uo;abort(i){var n,s;if(!this.signal.aborted){this.signal.reason=i,this.signal.aborted=!0;for(let o of this.signal._onabort)o(i);(s=(n=this.signal).onabort)==null||s.call(n,i);}}};let t=((lo=Lr.env)==null?void 0:lo.LRU_CACHE_IGNORE_AC_WARNING)!=="1",e=()=>{t&&(t=!1,ho("AbortController is not defined. If using lru-cache in node 14, load an AbortController polyfill from the `node-abort-controller` package. A minimal polyfill is provided for use by LRUCache.fetch(), but it should not be relied upon in other contexts (eg, passing it to other APIs that use AbortController/AbortSignal might have undesirable effects). You may disable this with LRU_CACHE_IGNORE_AC_WARNING=1 in the env.","NO_ABORT_CONTROLLER","ENOTSUP",e));};}var Sd=t=>!co.has(t),ne=t=>t&&t===Math.floor(t)&&t>0&&isFinite(t),fo=t=>ne(t)?t<=Math.pow(2,8)?Uint8Array:t<=Math.pow(2,16)?Uint16Array:t<=Math.pow(2,32)?Uint32Array:t<=Number.MAX_SAFE_INTEGER?Ce:null:null,Ce=class extends Array{constructor(e){super(e),this.fill(0);}},Nr=class t{heap;length;static#r=!1;static create(e){let r=fo(e);if(!r)return [];t.#r=!0;let i=new t(e,r);return t.#r=!1,i}constructor(e,r){if(!t.#r)throw new TypeError("instantiate Stack using Stack.create(n)");this.heap=new r(e),this.length=0;}push(e){this.heap[this.length++]=e;}pop(){return this.heap[--this.length]}},Br=class t{#r;#i;#e;#f;#S;ttl;ttlResolution;ttlAutopurge;updateAgeOnGet;updateAgeOnHas;allowStale;noDisposeOnSet;noUpdateTTL;maxEntrySize;sizeCalculation;noDeleteOnFetchRejection;noDeleteOnStaleGet;allowStaleOnFetchAbort;allowStaleOnFetchRejection;ignoreFetchAbort;#o;#c;#a;#n;#t;#d;#m;#h;#u;#y;#l;#v;#w;#g;#$;#C;#p;static unsafeExposeInternals(e){return {starts:e.#w,ttls:e.#g,sizes:e.#v,keyMap:e.#a,keyList:e.#n,valList:e.#t,next:e.#d,prev:e.#m,get head(){return e.#h},get tail(){return e.#u},free:e.#y,isBackgroundFetch:r=>e.#s(r),backgroundFetch:(r,i,n,s)=>e.#I(r,i,n,s),moveToTail:r=>e.#R(r),indexes:r=>e.#x(r),rindexes:r=>e.#E(r),isStale:r=>e.#b(r)}}get max(){return this.#r}get maxSize(){return this.#i}get calculatedSize(){return this.#c}get size(){return this.#o}get fetchMethod(){return this.#S}get dispose(){return this.#e}get disposeAfter(){return this.#f}constructor(e){let{max:r=0,ttl:i,ttlResolution:n=1,ttlAutopurge:s,updateAgeOnGet:o,updateAgeOnHas:a,allowStale:u,dispose:l,disposeAfter:c,noDisposeOnSet:h,noUpdateTTL:d,maxSize:f=0,maxEntrySize:b=0,sizeCalculation:m,fetchMethod:y,noDeleteOnFetchRejection:p,noDeleteOnStaleGet:$,allowStaleOnFetchRejection:O,allowStaleOnFetchAbort:A,ignoreFetchAbort:j}=e;if(r!==0&&!ne(r))throw new TypeError("max option must be a nonnegative integer");let N=r?fo(r):Array;if(!N)throw new Error("invalid max value: "+r);if(this.#r=r,this.#i=f,this.maxEntrySize=b||this.#i,this.sizeCalculation=m,this.sizeCalculation){if(!this.#i&&!this.maxEntrySize)throw new TypeError("cannot set sizeCalculation without setting maxSize or maxEntrySize");if(typeof this.sizeCalculation!="function")throw new TypeError("sizeCalculation set to non-function")}if(y!==void 0&&typeof y!="function")throw new TypeError("fetchMethod must be a function if specified");if(this.#S=y,this.#C=!!y,this.#a=new Map,this.#n=new Array(r).fill(void 0),this.#t=new Array(r).fill(void 0),this.#d=new N(r),this.#m=new N(r),this.#h=0,this.#u=0,this.#y=Nr.create(r),this.#o=0,this.#c=0,typeof l=="function"&&(this.#e=l),typeof c=="function"?(this.#f=c,this.#l=[]):(this.#f=void 0,this.#l=void 0),this.#$=!!this.#e,this.#p=!!this.#f,this.noDisposeOnSet=!!h,this.noUpdateTTL=!!d,this.noDeleteOnFetchRejection=!!p,this.allowStaleOnFetchRejection=!!O,this.allowStaleOnFetchAbort=!!A,this.ignoreFetchAbort=!!j,this.maxEntrySize!==0){if(this.#i!==0&&!ne(this.#i))throw new TypeError("maxSize must be a positive integer if specified");if(!ne(this.maxEntrySize))throw new TypeError("maxEntrySize must be a positive integer if specified");this.#L();}if(this.allowStale=!!u,this.noDeleteOnStaleGet=!!$,this.updateAgeOnGet=!!o,this.updateAgeOnHas=!!a,this.ttlResolution=ne(n)||n===0?n:1,this.ttlAutopurge=!!s,this.ttl=i||0,this.ttl){if(!ne(this.ttl))throw new TypeError("ttl must be a positive integer if specified");this.#k();}if(this.#r===0&&this.ttl===0&&this.#i===0)throw new TypeError("At least one of max, maxSize, or ttl is required");if(!this.ttlAutopurge&&!this.#r&&!this.#i){let B="LRU_CACHE_UNBOUNDED";Sd(B)&&(co.add(B),ho("TTL caching without ttlAutopurge, max, or maxSize can result in unbounded memory consumption.","UnboundedCacheWarning",B,t));}}getRemainingTTL(e){return this.#a.has(e)?1/0:0}#k(){let e=new Ce(this.#r),r=new Ce(this.#r);this.#g=e,this.#w=r,this.#M=(s,o,a=Oe.now())=>{if(r[s]=o!==0?a:0,e[s]=o,o!==0&&this.ttlAutopurge){let u=setTimeout(()=>{this.#b(s)&&this.delete(this.#n[s]);},o+1);u.unref&&u.unref();}},this.#T=s=>{r[s]=e[s]!==0?Oe.now():0;},this.#O=(s,o)=>{if(e[o]){let a=e[o],u=r[o];if(!a||!u)return;s.ttl=a,s.start=u,s.now=i||n();let l=s.now-u;s.remainingTTL=a-l;}};let i=0,n=()=>{let s=Oe.now();if(this.ttlResolution>0){i=s;let o=setTimeout(()=>i=0,this.ttlResolution);o.unref&&o.unref();}return s};this.getRemainingTTL=s=>{let o=this.#a.get(s);if(o===void 0)return 0;let a=e[o],u=r[o];if(!a||!u)return 1/0;let l=(i||n())-u;return a-l},this.#b=s=>{let o=r[s],a=e[s];return !!a&&!!o&&(i||n())-o>a};}#T=()=>{};#O=()=>{};#M=()=>{};#b=()=>!1;#L(){let e=new Ce(this.#r);this.#c=0,this.#v=e,this.#A=r=>{this.#c-=e[r],e[r]=0;},this.#j=(r,i,n,s)=>{if(this.#s(i))return 0;if(!ne(n))if(s){if(typeof s!="function")throw new TypeError("sizeCalculation must be a function");if(n=s(i,r),!ne(n))throw new TypeError("sizeCalculation return invalid (expect positive integer)")}else throw new TypeError("invalid size value (must be positive integer). When maxSize or maxEntrySize is used, sizeCalculation or size must be set.");return n},this.#P=(r,i,n)=>{if(e[r]=i,this.#i){let s=this.#i-e[r];for(;this.#c>s;)this.#_(!0);}this.#c+=e[r],n&&(n.entrySize=i,n.totalCalculatedSize=this.#c);};}#A=e=>{};#P=(e,r,i)=>{};#j=(e,r,i,n)=>{if(i||n)throw new TypeError("cannot set size without setting maxSize or maxEntrySize on cache");return 0};*#x({allowStale:e=this.allowStale}={}){if(this.#o)for(let r=this.#u;!(!this.#F(r)||((e||!this.#b(r))&&(yield r),r===this.#h));)r=this.#m[r];}*#E({allowStale:e=this.allowStale}={}){if(this.#o)for(let r=this.#h;!(!this.#F(r)||((e||!this.#b(r))&&(yield r),r===this.#u));)r=this.#d[r];}#F(e){return e!==void 0&&this.#a.get(this.#n[e])===e}*entries(){for(let e of this.#x())this.#t[e]!==void 0&&this.#n[e]!==void 0&&!this.#s(this.#t[e])&&(yield [this.#n[e],this.#t[e]]);}*rentries(){for(let e of this.#E())this.#t[e]!==void 0&&this.#n[e]!==void 0&&!this.#s(this.#t[e])&&(yield [this.#n[e],this.#t[e]]);}*keys(){for(let e of this.#x()){let r=this.#n[e];r!==void 0&&!this.#s(this.#t[e])&&(yield r);}}*rkeys(){for(let e of this.#E()){let r=this.#n[e];r!==void 0&&!this.#s(this.#t[e])&&(yield r);}}*values(){for(let e of this.#x())this.#t[e]!==void 0&&!this.#s(this.#t[e])&&(yield this.#t[e]);}*rvalues(){for(let e of this.#E())this.#t[e]!==void 0&&!this.#s(this.#t[e])&&(yield this.#t[e]);}[Symbol.iterator](){return this.entries()}[Symbol.toStringTag]="LRUCache";find(e,r={}){for(let i of this.#x()){let n=this.#t[i],s=this.#s(n)?n.__staleWhileFetching:n;if(s!==void 0&&e(s,this.#n[i],this))return this.get(this.#n[i],r)}}forEach(e,r=this){for(let i of this.#x()){let n=this.#t[i],s=this.#s(n)?n.__staleWhileFetching:n;s!==void 0&&e.call(r,s,this.#n[i],this);}}rforEach(e,r=this){for(let i of this.#E()){let n=this.#t[i],s=this.#s(n)?n.__staleWhileFetching:n;s!==void 0&&e.call(r,s,this.#n[i],this);}}purgeStale(){let e=!1;for(let r of this.#E({allowStale:!0}))this.#b(r)&&(this.delete(this.#n[r]),e=!0);return e}info(e){let r=this.#a.get(e);if(r===void 0)return;let i=this.#t[r],n=this.#s(i)?i.__staleWhileFetching:i;if(n===void 0)return;let s={value:n};if(this.#g&&this.#w){let o=this.#g[r],a=this.#w[r];if(o&&a){let u=o-(Oe.now()-a);s.ttl=u,s.start=Date.now();}}return this.#v&&(s.size=this.#v[r]),s}dump(){let e=[];for(let r of this.#x({allowStale:!0})){let i=this.#n[r],n=this.#t[r],s=this.#s(n)?n.__staleWhileFetching:n;if(s===void 0||i===void 0)continue;let o={value:s};if(this.#g&&this.#w){o.ttl=this.#g[r];let a=Oe.now()-this.#w[r];o.start=Math.floor(Date.now()-a);}this.#v&&(o.size=this.#v[r]),e.unshift([i,o]);}return e}load(e){this.clear();for(let[r,i]of e){if(i.start){let n=Date.now()-i.start;i.start=Oe.now()-n;}this.set(r,i.value,i);}}set(e,r,i={}){var d,f,b,m,y;if(r===void 0)return this.delete(e),this;let{ttl:n=this.ttl,start:s,noDisposeOnSet:o=this.noDisposeOnSet,sizeCalculation:a=this.sizeCalculation,status:u}=i,{noUpdateTTL:l=this.noUpdateTTL}=i,c=this.#j(e,r,i.size||0,a);if(this.maxEntrySize&&c>this.maxEntrySize)return u&&(u.set="miss",u.maxEntrySizeExceeded=!0),this.delete(e),this;let h=this.#o===0?void 0:this.#a.get(e);if(h===void 0)h=this.#o===0?this.#u:this.#y.length!==0?this.#y.pop():this.#o===this.#r?this.#_(!1):this.#o,this.#n[h]=e,this.#t[h]=r,this.#a.set(e,h),this.#d[this.#u]=h,this.#m[h]=this.#u,this.#u=h,this.#o++,this.#P(h,c,u),u&&(u.set="add"),l=!1;else {this.#R(h);let p=this.#t[h];if(r!==p){if(this.#C&&this.#s(p)){p.__abortController.abort(new Error("replaced"));let{__staleWhileFetching:$}=p;$!==void 0&&!o&&(this.#$&&((d=this.#e)==null||d.call(this,$,e,"set")),this.#p&&((f=this.#l)==null||f.push([$,e,"set"])));}else o||(this.#$&&((b=this.#e)==null||b.call(this,p,e,"set")),this.#p&&((m=this.#l)==null||m.push([p,e,"set"])));if(this.#A(h),this.#P(h,c,u),this.#t[h]=r,u){u.set="replace";let $=p&&this.#s(p)?p.__staleWhileFetching:p;$!==void 0&&(u.oldValue=$);}}else u&&(u.set="update");}if(n!==0&&!this.#g&&this.#k(),this.#g&&(l||this.#M(h,n,s),u&&this.#O(u,h)),!o&&this.#p&&this.#l){let p=this.#l,$;for(;$=p==null?void 0:p.shift();)(y=this.#f)==null||y.call(this,...$);}return this}pop(){var e;try{for(;this.#o;){let r=this.#t[this.#h];if(this.#_(!0),this.#s(r)){if(r.__staleWhileFetching)return r.__staleWhileFetching}else if(r!==void 0)return r}}finally{if(this.#p&&this.#l){let r=this.#l,i;for(;i=r==null?void 0:r.shift();)(e=this.#f)==null||e.call(this,...i);}}}#_(e){var s,o;let r=this.#h,i=this.#n[r],n=this.#t[r];return this.#C&&this.#s(n)?n.__abortController.abort(new Error("evicted")):(this.#$||this.#p)&&(this.#$&&((s=this.#e)==null||s.call(this,n,i,"evict")),this.#p&&((o=this.#l)==null||o.push([n,i,"evict"]))),this.#A(r),e&&(this.#n[r]=void 0,this.#t[r]=void 0,this.#y.push(r)),this.#o===1?(this.#h=this.#u=0,this.#y.length=0):this.#h=this.#d[r],this.#a.delete(i),this.#o--,r}has(e,r={}){let{updateAgeOnHas:i=this.updateAgeOnHas,status:n}=r,s=this.#a.get(e);if(s!==void 0){let o=this.#t[s];if(this.#s(o)&&o.__staleWhileFetching===void 0)return !1;if(this.#b(s))n&&(n.has="stale",this.#O(n,s));else return i&&this.#T(s),n&&(n.has="hit",this.#O(n,s)),!0}else n&&(n.has="miss");return !1}peek(e,r={}){let{allowStale:i=this.allowStale}=r,n=this.#a.get(e);if(n===void 0||!i&&this.#b(n))return;let s=this.#t[n];return this.#s(s)?s.__staleWhileFetching:s}#I(e,r,i,n){let s=r===void 0?void 0:this.#t[r];if(this.#s(s))return s;let o=new gt,{signal:a}=i;a==null||a.addEventListener("abort",()=>o.abort(a.reason),{signal:o.signal});let u={signal:o.signal,options:i,context:n},l=(m,y=!1)=>{let{aborted:p}=o.signal,$=i.ignoreFetchAbort&&m!==void 0;if(i.status&&(p&&!y?(i.status.fetchAborted=!0,i.status.fetchError=o.signal.reason,$&&(i.status.fetchAbortIgnored=!0)):i.status.fetchResolved=!0),p&&!$&&!y)return h(o.signal.reason);let O=f;return this.#t[r]===f&&(m===void 0?O.__staleWhileFetching?this.#t[r]=O.__staleWhileFetching:this.delete(e):(i.status&&(i.status.fetchUpdated=!0),this.set(e,m,u.options))),m},c=m=>(i.status&&(i.status.fetchRejected=!0,i.status.fetchError=m),h(m)),h=m=>{let{aborted:y}=o.signal,p=y&&i.allowStaleOnFetchAbort,$=p||i.allowStaleOnFetchRejection,O=$||i.noDeleteOnFetchRejection,A=f;if(this.#t[r]===f&&(!O||A.__staleWhileFetching===void 0?this.delete(e):p||(this.#t[r]=A.__staleWhileFetching)),$)return i.status&&A.__staleWhileFetching!==void 0&&(i.status.returnedStale=!0),A.__staleWhileFetching;if(A.__returned===A)throw m},d=(m,y)=>{var $;let p=($=this.#S)==null?void 0:$.call(this,e,s,u);p&&p instanceof Promise&&p.then(O=>m(O===void 0?void 0:O),y),o.signal.addEventListener("abort",()=>{(!i.ignoreFetchAbort||i.allowStaleOnFetchAbort)&&(m(void 0),i.allowStaleOnFetchAbort&&(m=O=>l(O,!0)));});};i.status&&(i.status.fetchDispatched=!0);let f=new Promise(d).then(l,c),b=Object.assign(f,{__abortController:o,__staleWhileFetching:s,__returned:void 0});return r===void 0?(this.set(e,b,{...u.options,status:void 0}),r=this.#a.get(e)):this.#t[r]=b,b}#s(e){if(!this.#C)return !1;let r=e;return !!r&&r instanceof Promise&&r.hasOwnProperty("__staleWhileFetching")&&r.__abortController instanceof gt}async fetch(e,r={}){let{allowStale:i=this.allowStale,updateAgeOnGet:n=this.updateAgeOnGet,noDeleteOnStaleGet:s=this.noDeleteOnStaleGet,ttl:o=this.ttl,noDisposeOnSet:a=this.noDisposeOnSet,size:u=0,sizeCalculation:l=this.sizeCalculation,noUpdateTTL:c=this.noUpdateTTL,noDeleteOnFetchRejection:h=this.noDeleteOnFetchRejection,allowStaleOnFetchRejection:d=this.allowStaleOnFetchRejection,ignoreFetchAbort:f=this.ignoreFetchAbort,allowStaleOnFetchAbort:b=this.allowStaleOnFetchAbort,context:m,forceRefresh:y=!1,status:p,signal:$}=r;if(!this.#C)return p&&(p.fetch="get"),this.get(e,{allowStale:i,updateAgeOnGet:n,noDeleteOnStaleGet:s,status:p});let O={allowStale:i,updateAgeOnGet:n,noDeleteOnStaleGet:s,ttl:o,noDisposeOnSet:a,size:u,sizeCalculation:l,noUpdateTTL:c,noDeleteOnFetchRejection:h,allowStaleOnFetchRejection:d,allowStaleOnFetchAbort:b,ignoreFetchAbort:f,status:p,signal:$},A=this.#a.get(e);if(A===void 0){p&&(p.fetch="miss");let j=this.#I(e,A,O,m);return j.__returned=j}else {let j=this.#t[A];if(this.#s(j)){let Jr=i&&j.__staleWhileFetching!==void 0;return p&&(p.fetch="inflight",Jr&&(p.returnedStale=!0)),Jr?j.__staleWhileFetching:j.__returned=j}let N=this.#b(A);if(!y&&!N)return p&&(p.fetch="hit"),this.#R(A),n&&this.#T(A),p&&this.#O(p,A),j;let B=this.#I(e,A,O,m),Kr=B.__staleWhileFetching!==void 0&&i;return p&&(p.fetch=N?"stale":"refresh",Kr&&N&&(p.returnedStale=!0)),Kr?B.__staleWhileFetching:B.__returned=B}}get(e,r={}){let{allowStale:i=this.allowStale,updateAgeOnGet:n=this.updateAgeOnGet,noDeleteOnStaleGet:s=this.noDeleteOnStaleGet,status:o}=r,a=this.#a.get(e);if(a!==void 0){let u=this.#t[a],l=this.#s(u);return o&&this.#O(o,a),this.#b(a)?(o&&(o.get="stale"),l?(o&&i&&u.__staleWhileFetching!==void 0&&(o.returnedStale=!0),i?u.__staleWhileFetching:void 0):(s||this.delete(e),o&&i&&(o.returnedStale=!0),i?u:void 0)):(o&&(o.get="hit"),l?u.__staleWhileFetching:(this.#R(a),n&&this.#T(a),u))}else o&&(o.get="miss");}#D(e,r){this.#m[r]=e,this.#d[e]=r;}#R(e){e!==this.#u&&(e===this.#h?this.#h=this.#d[e]:this.#D(this.#m[e],this.#d[e]),this.#D(this.#u,e),this.#u=e);}delete(e){var i,n,s,o;let r=!1;if(this.#o!==0){let a=this.#a.get(e);if(a!==void 0)if(r=!0,this.#o===1)this.clear();else {this.#A(a);let u=this.#t[a];if(this.#s(u)?u.__abortController.abort(new Error("deleted")):(this.#$||this.#p)&&(this.#$&&((i=this.#e)==null||i.call(this,u,e,"delete")),this.#p&&((n=this.#l)==null||n.push([u,e,"delete"]))),this.#a.delete(e),this.#n[a]=void 0,this.#t[a]=void 0,a===this.#u)this.#u=this.#m[a];else if(a===this.#h)this.#h=this.#d[a];else {let l=this.#m[a];this.#d[l]=this.#d[a];let c=this.#d[a];this.#m[c]=this.#m[a];}this.#o--,this.#y.push(a);}}if(this.#p&&((s=this.#l)!=null&&s.length)){let a=this.#l,u;for(;u=a==null?void 0:a.shift();)(o=this.#f)==null||o.call(this,...u);}return r}clear(){var e,r,i;for(let n of this.#E({allowStale:!0})){let s=this.#t[n];if(this.#s(s))s.__abortController.abort(new Error("deleted"));else {let o=this.#n[n];this.#$&&((e=this.#e)==null||e.call(this,s,o,"delete")),this.#p&&((r=this.#l)==null||r.push([s,o,"delete"]));}}if(this.#a.clear(),this.#t.fill(void 0),this.#n.fill(void 0),this.#g&&this.#w&&(this.#g.fill(0),this.#w.fill(0)),this.#v&&this.#v.fill(0),this.#h=0,this.#u=0,this.#y.length=0,this.#c=0,this.#o=0,this.#p&&this.#l){let n=this.#l,s;for(;s=n==null?void 0:n.shift();)(i=this.#f)==null||i.call(this,...s);}}};bt.LRUCache=Br;});var bo=chunkTKGT252T_js.c((Fp,go)=>{var w=(...t)=>t.every(e=>e)?t.join(""):"",k=t=>t?encodeURIComponent(t):"",mo=t=>t.toLowerCase().replace(/^\W+|\/|\W+$/g,"").replace(/\W+/g,"-"),xd={sshtemplate:({domain:t,user:e,project:r,committish:i})=>`git@${t}:${e}/${r}.git${w("#",i)}`,sshurltemplate:({domain:t,user:e,project:r,committish:i})=>`git+ssh://git@${t}/${e}/${r}.git${w("#",i)}`,edittemplate:({domain:t,user:e,project:r,committish:i,editpath:n,path:s})=>`https://${t}/${e}/${r}${w("/",n,"/",k(i||"HEAD"),"/",s)}`,browsetemplate:({domain:t,user:e,project:r,committish:i,treepath:n})=>`https://${t}/${e}/${r}${w("/",n,"/",k(i))}`,browsetreetemplate:({domain:t,user:e,project:r,committish:i,treepath:n,path:s,fragment:o,hashformat:a})=>`https://${t}/${e}/${r}/${n}/${k(i||"HEAD")}/${s}${w("#",a(o||""))}`,browseblobtemplate:({domain:t,user:e,project:r,committish:i,blobpath:n,path:s,fragment:o,hashformat:a})=>`https://${t}/${e}/${r}/${n}/${k(i||"HEAD")}/${s}${w("#",a(o||""))}`,docstemplate:({domain:t,user:e,project:r,treepath:i,committish:n})=>`https://${t}/${e}/${r}${w("/",i,"/",k(n))}#readme`,httpstemplate:({auth:t,domain:e,user:r,project:i,committish:n})=>`git+https://${w(t,"@")}${e}/${r}/${i}.git${w("#",n)}`,filetemplate:({domain:t,user:e,project:r,committish:i,path:n})=>`https://${t}/${e}/${r}/raw/${k(i||"HEAD")}/${n}`,shortcuttemplate:({type:t,user:e,project:r,committish:i})=>`${t}:${e}/${r}${w("#",i)}`,pathtemplate:({user:t,project:e,committish:r})=>`${t}/${e}${w("#",r)}`,bugstemplate:({domain:t,user:e,project:r})=>`https://${t}/${e}/${r}/issues`,hashformat:mo},se={};se.github={protocols:["git:","http:","git+ssh:","git+https:","ssh:","https:"],domain:"github.com",treepath:"tree",blobpath:"blob",editpath:"edit",filetemplate:({auth:t,user:e,project:r,committish:i,path:n})=>`https://${w(t,"@")}raw.githubusercontent.com/${e}/${r}/${k(i||"HEAD")}/${n}`,gittemplate:({auth:t,domain:e,user:r,project:i,committish:n})=>`git://${w(t,"@")}${e}/${r}/${i}.git${w("#",n)}`,tarballtemplate:({domain:t,user:e,project:r,committish:i})=>`https://codeload.${t}/${e}/${r}/tar.gz/${k(i||"HEAD")}`,extract:t=>{let[,e,r,i,n]=t.pathname.split("/",5);if(!(i&&i!=="tree")&&(i||(n=t.hash.slice(1)),r&&r.endsWith(".git")&&(r=r.slice(0,-4)),!(!e||!r)))return {user:e,project:r,committish:n}}};se.bitbucket={protocols:["git+ssh:","git+https:","ssh:","https:"],domain:"bitbucket.org",treepath:"src",blobpath:"src",editpath:"?mode=edit",edittemplate:({domain:t,user:e,project:r,committish:i,treepath:n,path:s,editpath:o})=>`https://${t}/${e}/${r}${w("/",n,"/",k(i||"HEAD"),"/",s,o)}`,tarballtemplate:({domain:t,user:e,project:r,committish:i})=>`https://${t}/${e}/${r}/get/${k(i||"HEAD")}.tar.gz`,extract:t=>{let[,e,r,i]=t.pathname.split("/",4);if(!["get"].includes(i)&&(r&&r.endsWith(".git")&&(r=r.slice(0,-4)),!(!e||!r)))return {user:e,project:r,committish:t.hash.slice(1)}}};se.gitlab={protocols:["git+ssh:","git+https:","ssh:","https:"],domain:"gitlab.com",treepath:"tree",blobpath:"tree",editpath:"-/edit",httpstemplate:({auth:t,domain:e,user:r,project:i,committish:n})=>`git+https://${w(t,"@")}${e}/${r}/${i}.git${w("#",n)}`,tarballtemplate:({domain:t,user:e,project:r,committish:i})=>`https://${t}/${e}/${r}/repository/archive.tar.gz?ref=${k(i||"HEAD")}`,extract:t=>{let e=t.pathname.slice(1);if(e.includes("/-/")||e.includes("/archive.tar.gz"))return;let r=e.split("/"),i=r.pop();i.endsWith(".git")&&(i=i.slice(0,-4));let n=r.join("/");if(!(!n||!i))return {user:n,project:i,committish:t.hash.slice(1)}}};se.gist={protocols:["git:","git+ssh:","git+https:","ssh:","https:"],domain:"gist.github.com",editpath:"edit",sshtemplate:({domain:t,project:e,committish:r})=>`git@${t}:${e}.git${w("#",r)}`,sshurltemplate:({domain:t,project:e,committish:r})=>`git+ssh://git@${t}/${e}.git${w("#",r)}`,edittemplate:({domain:t,user:e,project:r,committish:i,editpath:n})=>`https://${t}/${e}/${r}${w("/",k(i))}/${n}`,browsetemplate:({domain:t,project:e,committish:r})=>`https://${t}/${e}${w("/",k(r))}`,browsetreetemplate:({domain:t,project:e,committish:r,path:i,hashformat:n})=>`https://${t}/${e}${w("/",k(r))}${w("#",n(i))}`,browseblobtemplate:({domain:t,project:e,committish:r,path:i,hashformat:n})=>`https://${t}/${e}${w("/",k(r))}${w("#",n(i))}`,docstemplate:({domain:t,project:e,committish:r})=>`https://${t}/${e}${w("/",k(r))}`,httpstemplate:({domain:t,project:e,committish:r})=>`git+https://${t}/${e}.git${w("#",r)}`,filetemplate:({user:t,project:e,committish:r,path:i})=>`https://gist.githubusercontent.com/${t}/${e}/raw${w("/",k(r))}/${i}`,shortcuttemplate:({type:t,project:e,committish:r})=>`${t}:${e}${w("#",r)}`,pathtemplate:({project:t,committish:e})=>`${t}${w("#",e)}`,bugstemplate:({domain:t,project:e})=>`https://${t}/${e}`,gittemplate:({domain:t,project:e,committish:r})=>`git://${t}/${e}.git${w("#",r)}`,tarballtemplate:({project:t,committish:e})=>`https://codeload.github.com/gist/${t}/tar.gz/${k(e||"HEAD")}`,extract:t=>{let[,e,r,i]=t.pathname.split("/",4);if(i!=="raw"){if(!r){if(!e)return;r=e,e=null;}return r.endsWith(".git")&&(r=r.slice(0,-4)),{user:e,project:r,committish:t.hash.slice(1)}}},hashformat:function(t){return t&&"file-"+mo(t)}};se.sourcehut={protocols:["git+ssh:","https:"],domain:"git.sr.ht",treepath:"tree",blobpath:"tree",filetemplate:({domain:t,user:e,project:r,committish:i,path:n})=>`https://${t}/${e}/${r}/blob/${k(i)||"HEAD"}/${n}`,httpstemplate:({domain:t,user:e,project:r,committish:i})=>`https://${t}/${e}/${r}.git${w("#",i)}`,tarballtemplate:({domain:t,user:e,project:r,committish:i})=>`https://${t}/${e}/${r}/archive/${k(i)||"HEAD"}.tar.gz`,bugstemplate:()=>null,extract:t=>{let[,e,r,i]=t.pathname.split("/",4);if(!["archive"].includes(i)&&(r&&r.endsWith(".git")&&(r=r.slice(0,-4)),!(!e||!r)))return {user:e,project:r,committish:t.hash.slice(1)}}};for(let[t,e]of Object.entries(se))se[t]=Object.assign({},xd,e);go.exports=se;});var Ur=chunkTKGT252T_js.c((Dp,vo)=>{var Ed=chunkTKGT252T_js.a("url"),Gr=(t,e,r)=>{let i=t.indexOf(r);return t.lastIndexOf(e,i>-1?i:1/0)},yo=t=>{try{return new Ed.URL(t)}catch{}},Od=(t,e)=>{let r=t.indexOf(":"),i=t.slice(0,r+1);if(Object.prototype.hasOwnProperty.call(e,i))return t;let n=t.indexOf("@");return n>-1?n>r?`git+ssh://${t}`:t:t.indexOf("//")===r+1?t:`${t.slice(0,r+1)}//${t.slice(r+1)}`},Cd=t=>{let e=Gr(t,"@","#"),r=Gr(t,":","#");return r>e&&(t=t.slice(0,r)+"/"+t.slice(r+1)),Gr(t,":","#")===-1&&t.indexOf("//")===-1&&(t=`git+ssh://${t}`),t};vo.exports=(t,e)=>{let r=e?Od(t,e):t;return yo(r)||yo(Cd(r))};});var $o=chunkTKGT252T_js.c((Lp,wo)=>{var Td=Ur(),Ad=t=>{let e=t.indexOf("#"),r=t.indexOf("/"),i=t.indexOf("/",r+1),n=t.indexOf(":"),s=/\s/.exec(t),o=t.indexOf("@"),a=!s||e>-1&&s.index>e,u=o===-1||e>-1&&o>e,l=n===-1||e>-1&&n>e,c=i===-1||e>-1&&i>e,h=r>0,d=e>-1?t[e-1]!=="/":!t.endsWith("/"),f=!t.startsWith(".");return a&&h&&d&&f&&u&&l&&c};wo.exports=(t,e,{gitHosts:r,protocols:i})=>{var m,y;if(!t)return;let n=Ad(t)?`github:${t}`:t,s=Td(n,i);if(!s)return;let o=r.byShortcut[s.protocol],a=r.byDomain[s.hostname.startsWith("www.")?s.hostname.slice(4):s.hostname],u=o||a;if(!u)return;let l=r[o||a],c=null;(m=i[s.protocol])!=null&&m.auth&&(s.username||s.password)&&(c=`${s.username}${s.password?":"+s.password:""}`);let h=null,d=null,f=null,b=null;try{if(o){let p=s.pathname.startsWith("/")?s.pathname.slice(1):s.pathname,$=p.indexOf("@");$>-1&&(p=p.slice($+1));let O=p.lastIndexOf("/");O>-1?(d=decodeURIComponent(p.slice(0,O)),d||(d=null),f=decodeURIComponent(p.slice(O+1))):f=decodeURIComponent(p),f.endsWith(".git")&&(f=f.slice(0,-4)),s.hash&&(h=decodeURIComponent(s.hash.slice(1))),b="shortcut";}else {if(!l.protocols.includes(s.protocol))return;let p=l.extract(s);if(!p)return;d=p.user&&decodeURIComponent(p.user),f=decodeURIComponent(p.project),h=decodeURIComponent(p.committish),b=((y=i[s.protocol])==null?void 0:y.name)||s.protocol.slice(0,-1);}}catch(p){if(p instanceof URIError)return;throw p}return [u,d,c,f,h,b,e]};});var xo=chunkTKGT252T_js.c((Np,So)=>{var{LRUCache:Rd}=po(),Pd=bo(),_d=$o(),Id=Ur(),Wr=new Rd({max:1e3}),yt=class t{constructor(e,r,i,n,s,o,a={}){Object.assign(this,t.#r[e],{type:e,user:r,auth:i,project:n,committish:s,default:o,opts:a});}static#r={byShortcut:{},byDomain:{}};static#i={"git+ssh:":{name:"sshurl"},"ssh:":{name:"sshurl"},"git+https:":{name:"https",auth:!0},"git:":{auth:!0},"http:":{auth:!0},"https:":{auth:!0},"git+http:":{auth:!0}};static addHost(e,r){t.#r[e]=r,t.#r.byDomain[r.domain]=e,t.#r.byShortcut[`${e}:`]=e,t.#i[`${e}:`]={name:e};}static fromUrl(e,r){if(typeof e!="string")return;let i=e+JSON.stringify(r||{});if(!Wr.has(i)){let n=_d(e,r,{gitHosts:t.#r,protocols:t.#i});Wr.set(i,n?new t(...n):void 0);}return Wr.get(i)}static parseUrl(e){return Id(e)}#e(e,r){if(typeof e!="function")return null;let i={...this,...this.opts,...r};i.path||(i.path=""),i.path.startsWith("/")&&(i.path=i.path.slice(1)),i.noCommittish&&(i.committish=null);let n=e(i);return i.noGitPlus&&n.startsWith("git+")?n.slice(4):n}hash(){return this.committish?`#${this.committish}`:""}ssh(e){return this.#e(this.sshtemplate,e)}sshurl(e){return this.#e(this.sshurltemplate,e)}browse(e,...r){return typeof e!="string"?this.#e(this.browsetemplate,e):typeof r[0]!="string"?this.#e(this.browsetreetemplate,{...r[0],path:e}):this.#e(this.browsetreetemplate,{...r[1],fragment:r[0],path:e})}browseFile(e,...r){return typeof r[0]!="string"?this.#e(this.browseblobtemplate,{...r[0],path:e}):this.#e(this.browseblobtemplate,{...r[1],fragment:r[0],path:e})}docs(e){return this.#e(this.docstemplate,e)}bugs(e){return this.#e(this.bugstemplate,e)}https(e){return this.#e(this.httpstemplate,e)}git(e){return this.#e(this.gittemplate,e)}shortcut(e){return this.#e(this.shortcuttemplate,e)}path(e){return this.#e(this.pathtemplate,e)}tarball(e){return this.#e(this.tarballtemplate,{...e,noCommittish:!1})}file(e,r){return this.#e(this.filetemplate,{...r,path:e})}edit(e,r){return this.#e(this.edittemplate,{...r,path:e})}getDefaultRepresentation(){return this.default}toString(e){return this.default&&typeof this[this.default]=="function"?this[this.default](e):this.sshurl(e)}};for(let[t,e]of Object.entries(Pd))yt.addHost(t,e);So.exports=yt;});var Oo=chunkTKGT252T_js.c((Bp,Eo)=>{Eo.exports=kd;function kd(t){if(!t||t==="ERROR: No README data found!")return;t=t.trim().split(`
`);let e=0;for(;t[e]&&t[e].trim().match(/^(#|$)/);)e++;let r=t.length,i=e+1;for(;i<r&&t[i].trim();)i++;return t.slice(e,i).join(" ").trim()}});var Co=chunkTKGT252T_js.c((Gp,Md)=>{Md.exports={topLevel:{dependancies:"dependencies",dependecies:"dependencies",depdenencies:"dependencies",devEependencies:"devDependencies",depends:"dependencies","dev-dependencies":"devDependencies",devDependences:"devDependencies",devDepenencies:"devDependencies",devdependencies:"devDependencies",repostitory:"repository",repo:"repository",prefereGlobal:"preferGlobal",hompage:"homepage",hampage:"homepage",autohr:"author",autor:"author",contributers:"contributors",publicationConfig:"publishConfig",script:"scripts"},bugs:{web:"url",name:"url"},script:{server:"start",tests:"test"}};});var Po=chunkTKGT252T_js.c((Up,Ro)=>{var jd=chunk7UHX5T7X_js.z(),Fd=chunk7UHX5T7X_js.A(),Dd=chunk7UHX5T7X_js.q(),vt=xo(),Ld=chunkTKGT252T_js.a("module"),Nd=["dependencies","devDependencies","optionalDependencies"],Bd=Oo(),zr=chunkTKGT252T_js.a("url"),oe=Co(),To=t=>t.includes("@")&&t.indexOf("@")<t.lastIndexOf(".");Ro.exports={warn:function(){},fixRepositoryField:function(t){if(t.repositories&&(this.warn("repositories"),t.repository=t.repositories[0]),!t.repository)return this.warn("missingRepository");typeof t.repository=="string"&&(t.repository={type:"git",url:t.repository});var e=t.repository.url||"";if(e){var r=vt.fromUrl(e);r&&(e=t.repository.url=r.getDefaultRepresentation()==="shortcut"?r.https():r.toString());}e.match(/github.com\/[^/]+\/[^/]+\.git\.git$/)&&this.warn("brokenGitUrl",e);},fixTypos:function(t){Object.keys(oe.topLevel).forEach(function(e){Object.prototype.hasOwnProperty.call(t,e)&&this.warn("typo",e,oe.topLevel[e]);},this);},fixScriptsField:function(t){if(t.scripts){if(typeof t.scripts!="object"){this.warn("nonObjectScripts"),delete t.scripts;return}Object.keys(t.scripts).forEach(function(e){typeof t.scripts[e]!="string"?(this.warn("nonStringScript"),delete t.scripts[e]):oe.script[e]&&!t.scripts[oe.script[e]]&&this.warn("typo",e,oe.script[e],"scripts");},this);}},fixFilesField:function(t){var e=t.files;e&&!Array.isArray(e)?(this.warn("nonArrayFiles"),delete t.files):t.files&&(t.files=t.files.filter(function(r){return !r||typeof r!="string"?(this.warn("invalidFilename",r),!1):!0},this));},fixBinField:function(t){if(t.bin&&typeof t.bin=="string"){var e={},r;(r=t.name.match(/^@[^/]+[/](.*)$/))?e[r[1]]=t.bin:e[t.name]=t.bin,t.bin=e;}},fixManField:function(t){t.man&&typeof t.man=="string"&&(t.man=[t.man]);},fixBundleDependenciesField:function(t){var e="bundledDependencies",r="bundleDependencies";t[e]&&!t[r]&&(t[r]=t[e],delete t[e]),t[r]&&!Array.isArray(t[r])?(this.warn("nonArrayBundleDependencies"),delete t[r]):t[r]&&(t[r]=t[r].filter(function(i){return !i||typeof i!="string"?(this.warn("nonStringBundleDependency",i),!1):(t.dependencies||(t.dependencies={}),Object.prototype.hasOwnProperty.call(t.dependencies,i)||(this.warn("nonDependencyBundleDependency",i),t.dependencies[i]="*"),!0)},this));},fixDependencies:function(t){Yd(t,this.warn),qd(t,this.warn),this.fixBundleDependenciesField(t),["dependencies","devDependencies"].forEach(function(e){if(e in t){if(!t[e]||typeof t[e]!="object"){this.warn("nonObjectDependencies",e),delete t[e];return}Object.keys(t[e]).forEach(function(r){var i=t[e][r];typeof i!="string"&&(this.warn("nonStringDependency",r,JSON.stringify(i)),delete t[e][r]);var n=vt.fromUrl(t[e][r]);n&&(t[e][r]=n.toString());},this);}},this);},fixModulesField:function(t){t.modules&&(this.warn("deprecatedModules"),delete t.modules);},fixKeywordsField:function(t){typeof t.keywords=="string"&&(t.keywords=t.keywords.split(/,\s+/)),t.keywords&&!Array.isArray(t.keywords)?(delete t.keywords,this.warn("nonArrayKeywords")):t.keywords&&(t.keywords=t.keywords.filter(function(e){return typeof e!="string"||!e?(this.warn("nonStringKeyword"),!1):!0},this));},fixVersionField:function(t,e){var r=!e;if(!t.version)return t.version="",!0;if(!jd(t.version,r))throw new Error('Invalid version: "'+t.version+'"');return t.version=Fd(t.version,r),!0},fixPeople:function(t){Ao(t,zd),Ao(t,Hd);},fixNameField:function(t,e){typeof e=="boolean"?e={strict:e}:typeof e>"u"&&(e={});var r=e.strict;if(!t.name&&!r){t.name="";return}if(typeof t.name!="string")throw new Error("name field must be a string.");r||(t.name=t.name.trim()),Wd(t.name,r,e.allowLegacyCase),Ld.builtinModules.includes(t.name)&&this.warn("conflictingName",t.name);},fixDescriptionField:function(t){t.description&&typeof t.description!="string"&&(this.warn("nonStringDescription"),delete t.description),t.readme&&!t.description&&(t.description=Bd(t.readme)),t.description===void 0&&delete t.description,t.description||this.warn("missingDescription");},fixReadmeField:function(t){t.readme||(this.warn("missingReadme"),t.readme="ERROR: No README data found!");},fixBugsField:function(t){if(!t.bugs&&t.repository&&t.repository.url){var e=vt.fromUrl(t.repository.url);e&&e.bugs()&&(t.bugs={url:e.bugs()});}else if(t.bugs){if(typeof t.bugs=="string")To(t.bugs)?t.bugs={email:t.bugs}:zr.parse(t.bugs).protocol?t.bugs={url:t.bugs}:this.warn("nonEmailUrlBugsString");else {Kd(t.bugs,this.warn);var r=t.bugs;t.bugs={},r.url&&(typeof r.url=="string"&&zr.parse(r.url).protocol?t.bugs.url=r.url:this.warn("nonUrlBugsUrlField")),r.email&&(typeof r.email=="string"&&To(r.email)?t.bugs.email=r.email:this.warn("nonEmailBugsEmailField"));}!t.bugs.email&&!t.bugs.url&&(delete t.bugs,this.warn("emptyNormalizedBugs"));}},fixHomepageField:function(t){if(!t.homepage&&t.repository&&t.repository.url){var e=vt.fromUrl(t.repository.url);e&&e.docs()&&(t.homepage=e.docs());}if(t.homepage){if(typeof t.homepage!="string")return this.warn("nonUrlHomepage"),delete t.homepage;zr.parse(t.homepage).protocol||(t.homepage="http://"+t.homepage);}},fixLicenseField:function(t){let e=t.license||t.licence;if(!e)return this.warn("missingLicense");if(typeof e!="string"||e.length<1||e.trim()==="")return this.warn("invalidLicense");if(!Dd(e).validForNewPackages)return this.warn("invalidLicense")}};function Gd(t){if(t.charAt(0)!=="@")return !1;var e=t.slice(1).split("/");return e.length!==2?!1:e[0]&&e[1]&&e[0]===encodeURIComponent(e[0])&&e[1]===encodeURIComponent(e[1])}function Ud(t){return !t.match(/[/@\s+%:]/)&&t===encodeURIComponent(t)}function Wd(t,e,r){if(t.charAt(0)==="."||!(Gd(t)||Ud(t))||e&&!r&&t!==t.toLowerCase()||t.toLowerCase()==="node_modules"||t.toLowerCase()==="favicon.ico")throw new Error("Invalid name: "+JSON.stringify(t))}function Ao(t,e){return t.author&&(t.author=e(t.author)),["maintainers","contributors"].forEach(function(r){Array.isArray(t[r])&&(t[r]=t[r].map(e));}),t}function zd(t){if(typeof t=="string")return t;var e=t.name||"",r=t.url||t.web,i=r?" ("+r+")":"",n=t.email||t.mail,s=n?" <"+n+">":"";return e+s+i}function Hd(t){if(typeof t!="string")return t;var e=t.match(/^([^(<]+)/),r=t.match(/\(([^()]+)\)/),i=t.match(/<([^<>]+)>/),n={};return e&&e[0].trim()&&(n.name=e[0].trim()),i&&(n.email=i[1]),r&&(n.url=r[1]),n}function qd(t){var e=t.optionalDependencies;if(e){var r=t.dependencies||{};Object.keys(e).forEach(function(i){r[i]=e[i];}),t.dependencies=r;}}function Vd(t,e,r){if(!t)return {};if(typeof t=="string"&&(t=t.trim().split(/[\n\r\s\t ,]+/)),!Array.isArray(t))return t;r("deprecatedArrayDependencies",e);var i={};return t.filter(function(n){return typeof n=="string"}).forEach(function(n){n=n.trim().split(/(:?[@\s><=])/);var s=n.shift(),o=n.join("");o=o.trim(),o=o.replace(/^@/,""),i[s]=o;}),i}function Yd(t,e){Nd.forEach(function(r){t[r]&&(t[r]=Vd(t[r],r,e));});}function Kd(t,e){t&&Object.keys(t).forEach(function(r){oe.bugs[r]&&(e("typo",r,oe.bugs[r],"bugs"),t[oe.bugs[r]]=t[r],delete t[r]);});}});var _o=chunkTKGT252T_js.c((Wp,Jd)=>{Jd.exports={repositories:"'repositories' (plural) Not supported. Please pick one as the 'repository' field",missingRepository:"No repository field.",brokenGitUrl:"Probably broken git url: %s",nonObjectScripts:"scripts must be an object",nonStringScript:"script values must be string commands",nonArrayFiles:"Invalid 'files' member",invalidFilename:"Invalid filename in 'files' list: %s",nonArrayBundleDependencies:"Invalid 'bundleDependencies' list. Must be array of package names",nonStringBundleDependency:"Invalid bundleDependencies member: %s",nonDependencyBundleDependency:"Non-dependency in bundleDependencies: %s",nonObjectDependencies:"%s field must be an object",nonStringDependency:"Invalid dependency: %s %s",deprecatedArrayDependencies:"specifying %s as array is deprecated",deprecatedModules:"modules field is deprecated",nonArrayKeywords:"keywords should be an array of strings",nonStringKeyword:"keywords should be an array of strings",conflictingName:"%s is also the name of a node core module.",nonStringDescription:"'description' field should be a string",missingDescription:"No description",missingReadme:"No README data",missingLicense:"No license field.",nonEmailUrlBugsString:"Bug string field must be url, email, or {email,url}",nonUrlBugsUrlField:"bugs.url field must be a string url. Deleted.",nonEmailBugsEmailField:"bugs.email field must be a string email. Deleted.",emptyNormalizedBugs:"Normalized value of bugs field is an empty object. Deleted.",nonUrlHomepage:"homepage field must be a string url. Deleted.",invalidLicense:"license should be a valid SPDX license expression",typo:"%s should probably be %s."};});var Mo=chunkTKGT252T_js.c((zp,ko)=>{var Io=chunkTKGT252T_js.a("util"),Hr=_o();ko.exports=function(){var t=Array.prototype.slice.call(arguments,0),e=t.shift();if(e==="typo")return Xd.apply(null,t);var r=Hr[e]?Hr[e]:e+": '%s'";return t.unshift(r),Io.format.apply(null,t)};function Xd(t,e,r){return r&&(t=r+"['"+t+"']",e=r+"['"+e+"']"),Io.format(Hr.typo,t,e)}});var Lo=chunkTKGT252T_js.c((Hp,Do)=>{Do.exports=jo;var qr=Po();jo.fixer=qr;var Zd=Mo(),Qd=["name","version","description","repository","modules","scripts","files","bin","man","bugs","keywords","readme","homepage","license"],e0=["dependencies","people","typos"],Vr=Qd.map(function(t){return Fo(t)+"Field"});Vr=Vr.concat(e0);function jo(t,e,r){e===!0&&(e=null,r=!0),r||(r=!1),(!e||t.private)&&(e=function(){}),t.scripts&&t.scripts.install==="node-gyp rebuild"&&!t.scripts.preinstall&&(t.gypfile=!0),qr.warn=function(){e(Zd.apply(null,arguments));},Vr.forEach(function(i){qr["fix"+Fo(i)](t,r);}),t._id=t.name+"@"+t.version;}function Fo(t){return t.charAt(0).toUpperCase()+t.slice(1)}});function ti(t){return e=>e.length>1?`${t} run ${e[0]} -- ${e.slice(1).join(" ")}`:`${t} run ${e[0]}`}var ri={agent:"yarn {0}",run:"yarn run {0}",install:"yarn install {0}",frozen:"yarn install --frozen-lockfile",global:"yarn global add {0}",add:"yarn add {0}",upgrade:"yarn upgrade {0}","upgrade-interactive":"yarn upgrade-interactive {0}",execute:"npx {0}",uninstall:"yarn remove {0}",global_uninstall:"yarn global remove {0}"},ii={agent:"pnpm {0}",run:"pnpm run {0}",install:"pnpm i {0}",frozen:"pnpm i --frozen-lockfile",global:"pnpm add -g {0}",add:"pnpm add {0}",upgrade:"pnpm update {0}","upgrade-interactive":"pnpm update -i {0}",execute:"pnpm dlx {0}",uninstall:"pnpm remove {0}",global_uninstall:"pnpm remove --global {0}"},ha={agent:"bun {0}",run:"bun run {0}",install:"bun install {0}",frozen:"bun install --no-save",global:"bun add -g {0}",add:"bun add {0}",upgrade:"bun update {0}","upgrade-interactive":"bun update {0}",execute:"bunx {0}",uninstall:"bun remove {0}",global_uninstall:"bun remove -g {0}"},ke={npm:{agent:"npm {0}",run:ti("npm"),install:"npm i {0}",frozen:"npm ci",global:"npm i -g {0}",add:"npm i {0}",upgrade:"npm update {0}","upgrade-interactive":null,execute:"npx {0}",uninstall:"npm uninstall {0}",global_uninstall:"npm uninstall -g {0}"},yarn:ri,"yarn@berry":{...ri,frozen:"yarn install --immutable",upgrade:"yarn up {0}","upgrade-interactive":"yarn up -i {0}",execute:"yarn dlx {0}",global:"npm i -g {0}",global_uninstall:"npm uninstall -g {0}"},pnpm:ii,"pnpm@6":{...ii,run:ti("pnpm")},bun:ha},on=Object.keys(ke),Nt={"bun.lockb":"bun","pnpm-lock.yaml":"pnpm","yarn.lock":"yarn","package-lock.json":"npm","npm-shrinkwrap.json":"npm"},an={bun:"https://bun.sh",pnpm:"https://pnpm.io/installation","pnpm@6":"https://pnpm.io/6.x/installation",yarn:"https://classic.yarnpkg.com/en/docs/install","yarn@berry":"https://yarnpkg.com/getting-started/install",npm:"https://docs.npmjs.com/cli/v8/configuring-npm/install"},J=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function ye(t){return t&&t.__esModule&&Object.prototype.hasOwnProperty.call(t,"default")?t.default:t}var{hasOwnProperty:$t}=Object.prototype,Bt=(t,e={})=>{typeof e=="string"&&(e={section:e}),e.align=e.align===!0,e.newline=e.newline===!0,e.sort=e.sort===!0,e.whitespace=e.whitespace===!0||e.align===!0,e.platform=e.platform||typeof process<"u"&&process.platform,e.bracketedArray=e.bracketedArray!==!1;let r=e.platform==="win32"?`\r
`:`
`,i=e.whitespace?" = ":"=",n=[],s=e.sort?Object.keys(t).sort():Object.keys(t),o=0;e.align&&(o=X(s.filter(l=>t[l]===null||Array.isArray(t[l])||typeof t[l]!="object").map(l=>Array.isArray(t[l])?`${l}[]`:l).concat([""]).reduce((l,c)=>X(l).length>=X(c).length?l:c)).length);let a="",u=e.bracketedArray?"[]":"";for(let l of s){let c=t[l];if(c&&Array.isArray(c))for(let h of c)a+=X(`${l}${u}`).padEnd(o," ")+i+X(h)+r;else c&&typeof c=="object"?n.push(l):a+=X(l).padEnd(o," ")+i+X(c)+r;}e.section&&a.length&&(a="["+X(e.section)+"]"+(e.newline?r+r:r)+a);for(let l of n){let c=un(l,".").join("\\."),h=(e.section?e.section+".":"")+c,d=Bt(t[l],{...e,section:h});a.length&&d.length&&(a+=r),a+=d;}return a};function un(t,e){var r=0,i=0,n=0,s=[];do if(n=t.indexOf(e,r),n!==-1){if(r=n+e.length,n>0&&t[n-1]==="\\")continue;s.push(t.slice(i,n)),i=n+e.length;}while(n!==-1);return s.push(t.slice(i)),s}var ni=(t,e={})=>{e.bracketedArray=e.bracketedArray!==!1;let r=Object.create(null),i=r,n=null,s=/^\[([^\]]*)\]\s*$|^([^=]+)(=(.*))?$/i,o=t.split(/[\r\n]+/g),a={};for(let l of o){if(!l||l.match(/^\s*[;#]/)||l.match(/^\s*$/))continue;let c=l.match(s);if(!c)continue;if(c[1]!==void 0){if(n=Ze(c[1]),n==="__proto__"){i=Object.create(null);continue}i=r[n]=r[n]||Object.create(null);continue}let h=Ze(c[2]),d;e.bracketedArray?d=h.length>2&&h.slice(-2)==="[]":(a[h]=((a==null?void 0:a[h])||0)+1,d=a[h]>1);let f=d?h.slice(0,-2):h;if(f==="__proto__")continue;let b=c[3]?Ze(c[4]):!0,m=b==="true"||b==="false"||b==="null"?JSON.parse(b):b;d&&($t.call(i,f)?Array.isArray(i[f])||(i[f]=[i[f]]):i[f]=[]),Array.isArray(i[f])?i[f].push(m):i[f]=m;}let u=[];for(let l of Object.keys(r)){if(!$t.call(r,l)||typeof r[l]!="object"||Array.isArray(r[l]))continue;let c=un(l,".");i=r;let h=c.pop(),d=h.replace(/\\\./g,".");for(let f of c)f!=="__proto__"&&((!$t.call(i,f)||typeof i[f]!="object")&&(i[f]=Object.create(null)),i=i[f]);i===r&&d===h||(i[d]=r[l],u.push(l));}for(let l of u)delete r[l];return r},ln=t=>t.startsWith('"')&&t.endsWith('"')||t.startsWith("'")&&t.endsWith("'"),X=t=>typeof t!="string"||t.match(/[=\r\n]/)||t.match(/^\[/)||t.length>1&&ln(t)||t!==t.trim()?JSON.stringify(t):t.split(";").join("\\;").split("#").join("\\#"),Ze=(t,e)=>{if(t=(t||"").trim(),ln(t)){t.charAt(0)==="'"&&(t=t.slice(1,-1));try{t=JSON.parse(t);}catch{}}else {let r=!1,i="";for(let n=0,s=t.length;n<s;n++){let o=t.charAt(n);if(r)"\\;#".indexOf(o)!==-1?i+=o:i+="\\"+o,r=!1;else {if(";#".indexOf(o)!==-1)break;o==="\\"?r=!0:i+=o;}}return r&&(i+="\\"),i.trim()}return t},fa={parse:ni,decode:ni,stringify:Bt,encode:Bt,safe:X,unsafe:Ze},da=ye(fa),ve={exports:{}},St,si;function pa(){if(si)return St;si=1,St=i,i.sync=n;var t=Fe__default.default;function e(s,o){var a=o.pathExt!==void 0?o.pathExt:process.env.PATHEXT;if(!a||(a=a.split(";"),a.indexOf("")!==-1))return !0;for(var u=0;u<a.length;u++){var l=a[u].toLowerCase();if(l&&s.substr(-l.length).toLowerCase()===l)return !0}return !1}function r(s,o,a){return !s.isSymbolicLink()&&!s.isFile()?!1:e(o,a)}function i(s,o,a){t.stat(s,function(u,l){a(u,u?!1:r(l,s,o));});}function n(s,o){return r(t.statSync(s),s,o)}return St}var xt,oi;function ma(){if(oi)return xt;oi=1,xt=e,e.sync=r;var t=Fe__default.default;function e(s,o,a){t.stat(s,function(u,l){a(u,u?!1:i(l,o));});}function r(s,o){return i(t.statSync(s),o)}function i(s,o){return s.isFile()&&n(s,o)}function n(s,o){var a=s.mode,u=s.uid,l=s.gid,c=o.uid!==void 0?o.uid:process.getuid&&process.getuid(),h=o.gid!==void 0?o.gid:process.getgid&&process.getgid(),d=parseInt("100",8),f=parseInt("010",8),b=parseInt("001",8),m=d|f,y=a&b||a&f&&l===h||a&d&&u===c||a&m&&c===0;return y}return xt}var rt;process.platform==="win32"||J.TESTING_WINDOWS?rt=pa():rt=ma();var ga=fr;fr.sync=ba;function fr(t,e,r){if(typeof e=="function"&&(r=e,e={}),!r){if(typeof Promise!="function")throw new TypeError("callback not provided");return new Promise(function(i,n){fr(t,e||{},function(s,o){s?n(s):i(o);});})}rt(t,e||{},function(i,n){i&&(i.code==="EACCES"||e&&e.ignoreErrors)&&(i=null,n=!1),r(i,n);});}function ba(t,e){try{return rt.sync(t,e||{})}catch(r){if(e&&e.ignoreErrors||r.code==="EACCES")return !1;throw r}}var fe=process.platform==="win32"||process.env.OSTYPE==="cygwin"||process.env.OSTYPE==="msys",cn=D__default.default,ya=fe?";":":",hn=ga,fn=t=>Object.assign(new Error(`not found: ${t}`),{code:"ENOENT"}),dn=(t,e)=>{let r=e.colon||ya,i=t.match(/\//)||fe&&t.match(/\\/)?[""]:[...fe?[process.cwd()]:[],...(e.path||process.env.PATH||"").split(r)],n=fe?e.pathExt||process.env.PATHEXT||".EXE;.CMD;.BAT;.COM":"",s=fe?n.split(r):[""];return fe&&t.indexOf(".")!==-1&&s[0]!==""&&s.unshift(""),{pathEnv:i,pathExt:s,pathExtExe:n}},pn=(t,e,r)=>{typeof e=="function"&&(r=e,e={}),e||(e={});let{pathEnv:i,pathExt:n,pathExtExe:s}=dn(t,e),o=[],a=l=>new Promise((c,h)=>{if(l===i.length)return e.all&&o.length?c(o):h(fn(t));let d=i[l],f=/^".*"$/.test(d)?d.slice(1,-1):d,b=cn.join(f,t),m=!f&&/^\.[\\\/]/.test(t)?t.slice(0,2)+b:b;c(u(m,l,0));}),u=(l,c,h)=>new Promise((d,f)=>{if(h===n.length)return d(a(c+1));let b=n[h];hn(l+b,{pathExt:s},(m,y)=>{if(!m&&y)if(e.all)o.push(l+b);else return d(l+b);return d(u(l,c,h+1))});});return r?a(0).then(l=>r(null,l),r):a(0)},va=(t,e)=>{e=e||{};let{pathEnv:r,pathExt:i,pathExtExe:n}=dn(t,e),s=[];for(let o=0;o<r.length;o++){let a=r[o],u=/^".*"$/.test(a)?a.slice(1,-1):a,l=cn.join(u,t),c=!u&&/^\.[\\\/]/.test(t)?t.slice(0,2)+l:l;for(let h=0;h<i.length;h++){let d=c+i[h];try{if(hn.sync(d,{pathExt:n}))if(e.all)s.push(d);else return d}catch{}}}if(e.all&&s.length)return s;if(e.nothrow)return null;throw fn(t)},wa=pn;pn.sync=va;var dr={exports:{}},mn=(t={})=>{let e=t.env||process.env;return (t.platform||process.platform)!=="win32"?"PATH":Object.keys(e).reverse().find(i=>i.toUpperCase()==="PATH")||"Path"};dr.exports=mn;dr.exports.default=mn;var $a=dr.exports,ai=D__default.default,Sa=wa,xa=$a;function ui(t,e){let r=t.options.env||process.env,i=process.cwd(),n=t.options.cwd!=null,s=n&&process.chdir!==void 0&&!process.chdir.disabled;if(s)try{process.chdir(t.options.cwd);}catch{}let o;try{o=Sa.sync(t.command,{path:r[xa({env:r})],pathExt:e?ai.delimiter:void 0});}catch{}finally{s&&process.chdir(i);}return o&&(o=ai.resolve(n?t.options.cwd:"",o)),o}function Ea(t){return ui(t)||ui(t,!0)}var Oa=Ea,pr={},Gt=/([()\][%!^"`<>&|;, *?])/g;function Ca(t){return t=t.replace(Gt,"^$1"),t}function Ta(t,e){return t=`${t}`,t=t.replace(/(\\*)"/g,'$1$1\\"'),t=t.replace(/(\\*)$/,"$1$1"),t=`"${t}"`,t=t.replace(Gt,"^$1"),e&&(t=t.replace(Gt,"^$1")),t}pr.command=Ca;pr.argument=Ta;var Aa=/^#!(.*)/,Ra=Aa,Pa=(t="")=>{let e=t.match(Ra);if(!e)return null;let[r,i]=e[0].replace(/#! ?/,"").split(" "),n=r.split("/").pop();return n==="env"?i:i?`${n} ${i}`:n},Et=Fe__default.default,_a=Pa;function Ia(t){let r=Buffer.alloc(150),i;try{i=Et.openSync(t,"r"),Et.readSync(i,r,0,150,0),Et.closeSync(i);}catch{}return _a(r.toString())}var ka=Ia,Ma=D__default.default,li=Oa,ci=pr,ja=ka,Fa=process.platform==="win32",Da=/\.(?:com|exe)$/i,La=/node_modules[\\/].bin[\\/][^\\/]+\.cmd$/i;function Na(t){t.file=li(t);let e=t.file&&ja(t.file);return e?(t.args.unshift(t.file),t.command=e,li(t)):t.file}function Ba(t){if(!Fa)return t;let e=Na(t),r=!Da.test(e);if(t.options.forceShell||r){let i=La.test(e);t.command=Ma.normalize(t.command),t.command=ci.command(t.command),t.args=t.args.map(s=>ci.argument(s,i));let n=[t.command].concat(t.args).join(" ");t.args=["/d","/s","/c",`"${n}"`],t.command=process.env.comspec||"cmd.exe",t.options.windowsVerbatimArguments=!0;}return t}function Ga(t,e,r){e&&!Array.isArray(e)&&(r=e,e=null),e=e?e.slice(0):[],r=Object.assign({},r);let i={command:t,args:e,options:r,file:void 0,original:{command:t,args:e}};return r.shell?i:Ba(i)}var Ua=Ga,mr=process.platform==="win32";function gr(t,e){return Object.assign(new Error(`${e} ${t.command} ENOENT`),{code:"ENOENT",errno:"ENOENT",syscall:`${e} ${t.command}`,path:t.command,spawnargs:t.args})}function Wa(t,e){if(!mr)return;let r=t.emit;t.emit=function(i,n){if(i==="exit"){let s=gn(n,e);if(s)return r.call(t,"error",s)}return r.apply(t,arguments)};}function gn(t,e){return mr&&t===1&&!e.file?gr(e.original,"spawn"):null}function za(t,e){return mr&&t===1&&!e.file?gr(e.original,"spawnSync"):null}var Ha={hookChildProcess:Wa,verifyENOENT:gn,verifyENOENTSync:za,notFoundError:gr},bn=ei__default.default,br=Ua,yr=Ha;function yn(t,e,r){let i=br(t,e,r),n=bn.spawn(i.command,i.args,i.options);return yr.hookChildProcess(n,i),n}function qa(t,e,r){let i=br(t,e,r),n=bn.spawnSync(i.command,i.args,i.options);return n.error=n.error||yr.verifyENOENTSync(n.status,i),n}ve.exports=yn;ve.exports.spawn=yn;ve.exports.sync=qa;ve.exports._parse=br;ve.exports._enoent=yr;var Va=ve.exports,Ya=ye(Va);function Ka(t){let e=typeof t=="string"?`
`:`
`.charCodeAt(),r=typeof t=="string"?"\r":"\r".charCodeAt();return t[t.length-1]===e&&(t=t.slice(0,-1)),t[t.length-1]===r&&(t=t.slice(0,-1)),t}function vn(t={}){let{env:e=process.env,platform:r=process.platform}=t;return r!=="win32"?"PATH":Object.keys(e).reverse().find(i=>i.toUpperCase()==="PATH")||"Path"}function Ja(t={}){let{cwd:e=___default.default.cwd(),path:r=___default.default.env[vn()],execPath:i=___default.default.execPath}=t,n,s=e instanceof URL?ia__default.default.fileURLToPath(e):e,o=D__default.default.resolve(s),a=[];for(;n!==o;)a.push(D__default.default.join(o,"node_modules/.bin")),n=o,o=D__default.default.resolve(o,"..");return a.push(D__default.default.resolve(s,i,"..")),[...a,r].join(D__default.default.delimiter)}function Xa({env:t=___default.default.env,...e}={}){t={...t};let r=vn({env:t});return e.path=t[r],t[r]=Ja(e),t}var Za=(t,e,r,i)=>{if(r==="length"||r==="prototype"||r==="arguments"||r==="caller")return;let n=Object.getOwnPropertyDescriptor(t,r),s=Object.getOwnPropertyDescriptor(e,r);!Qa(n,s)&&i||Object.defineProperty(t,r,s);},Qa=function(t,e){return t===void 0||t.configurable||t.writable===e.writable&&t.enumerable===e.enumerable&&t.configurable===e.configurable&&(t.writable||t.value===e.value)},eu=(t,e)=>{let r=Object.getPrototypeOf(e);r!==Object.getPrototypeOf(t)&&Object.setPrototypeOf(t,r);},tu=(t,e)=>`/* Wrapped ${t}*/
${e}`,ru=Object.getOwnPropertyDescriptor(Function.prototype,"toString"),iu=Object.getOwnPropertyDescriptor(Function.prototype.toString,"name"),nu=(t,e,r)=>{let i=r===""?"":`with ${r.trim()}() `,n=tu.bind(null,i,e.toString());Object.defineProperty(n,"name",iu),Object.defineProperty(t,"toString",{...ru,value:n});};function su(t,e,{ignoreNonConfigurable:r=!1}={}){let{name:i}=t;for(let n of Reflect.ownKeys(e))Za(t,e,n,r);return eu(t,e),nu(t,e,i),t}var it=new WeakMap,wn=(t,e={})=>{if(typeof t!="function")throw new TypeError("Expected a function");let r,i=0,n=t.displayName||t.name||"<anonymous>",s=function(...o){if(it.set(s,++i),i===1)r=t.apply(this,o),t=null;else if(e.throw===!0)throw new Error(`Function \`${n}\` can only be called once`);return r};return su(s,t),it.set(s,i),s};wn.callCount=t=>{if(!it.has(t))throw new Error(`The given function \`${t.name}\` is not wrapped by the \`onetime\` package`);return it.get(t)};var ou=()=>{let t=Sn-$n+1;return Array.from({length:t},au)},au=(t,e)=>({name:`SIGRT${e+1}`,number:$n+e,action:"terminate",description:"Application-specific signal (realtime)",standard:"posix"}),$n=34,Sn=64,uu=[{name:"SIGHUP",number:1,action:"terminate",description:"Terminal closed",standard:"posix"},{name:"SIGINT",number:2,action:"terminate",description:"User interruption with CTRL-C",standard:"ansi"},{name:"SIGQUIT",number:3,action:"core",description:"User interruption with CTRL-\\",standard:"posix"},{name:"SIGILL",number:4,action:"core",description:"Invalid machine instruction",standard:"ansi"},{name:"SIGTRAP",number:5,action:"core",description:"Debugger breakpoint",standard:"posix"},{name:"SIGABRT",number:6,action:"core",description:"Aborted",standard:"ansi"},{name:"SIGIOT",number:6,action:"core",description:"Aborted",standard:"bsd"},{name:"SIGBUS",number:7,action:"core",description:"Bus error due to misaligned, non-existing address or paging error",standard:"bsd"},{name:"SIGEMT",number:7,action:"terminate",description:"Command should be emulated but is not implemented",standard:"other"},{name:"SIGFPE",number:8,action:"core",description:"Floating point arithmetic error",standard:"ansi"},{name:"SIGKILL",number:9,action:"terminate",description:"Forced termination",standard:"posix",forced:!0},{name:"SIGUSR1",number:10,action:"terminate",description:"Application-specific signal",standard:"posix"},{name:"SIGSEGV",number:11,action:"core",description:"Segmentation fault",standard:"ansi"},{name:"SIGUSR2",number:12,action:"terminate",description:"Application-specific signal",standard:"posix"},{name:"SIGPIPE",number:13,action:"terminate",description:"Broken pipe or socket",standard:"posix"},{name:"SIGALRM",number:14,action:"terminate",description:"Timeout or timer",standard:"posix"},{name:"SIGTERM",number:15,action:"terminate",description:"Termination",standard:"ansi"},{name:"SIGSTKFLT",number:16,action:"terminate",description:"Stack is empty or overflowed",standard:"other"},{name:"SIGCHLD",number:17,action:"ignore",description:"Child process terminated, paused or unpaused",standard:"posix"},{name:"SIGCLD",number:17,action:"ignore",description:"Child process terminated, paused or unpaused",standard:"other"},{name:"SIGCONT",number:18,action:"unpause",description:"Unpaused",standard:"posix",forced:!0},{name:"SIGSTOP",number:19,action:"pause",description:"Paused",standard:"posix",forced:!0},{name:"SIGTSTP",number:20,action:"pause",description:'Paused using CTRL-Z or "suspend"',standard:"posix"},{name:"SIGTTIN",number:21,action:"pause",description:"Background process cannot read terminal input",standard:"posix"},{name:"SIGBREAK",number:21,action:"terminate",description:"User interruption with CTRL-BREAK",standard:"other"},{name:"SIGTTOU",number:22,action:"pause",description:"Background process cannot write to terminal output",standard:"posix"},{name:"SIGURG",number:23,action:"ignore",description:"Socket received out-of-band data",standard:"bsd"},{name:"SIGXCPU",number:24,action:"core",description:"Process timed out",standard:"bsd"},{name:"SIGXFSZ",number:25,action:"core",description:"File too big",standard:"bsd"},{name:"SIGVTALRM",number:26,action:"terminate",description:"Timeout or timer",standard:"bsd"},{name:"SIGPROF",number:27,action:"terminate",description:"Timeout or timer",standard:"bsd"},{name:"SIGWINCH",number:28,action:"ignore",description:"Terminal window size changed",standard:"bsd"},{name:"SIGIO",number:29,action:"terminate",description:"I/O is available",standard:"other"},{name:"SIGPOLL",number:29,action:"terminate",description:"Watched event",standard:"other"},{name:"SIGINFO",number:29,action:"ignore",description:"Request for process information",standard:"other"},{name:"SIGPWR",number:30,action:"terminate",description:"Device running out of power",standard:"systemv"},{name:"SIGSYS",number:31,action:"core",description:"Invalid system call",standard:"other"},{name:"SIGUNUSED",number:31,action:"terminate",description:"Invalid system call",standard:"other"}],xn=()=>{let t=ou();return [...uu,...t].map(lu)},lu=({name:t,number:e,description:r,action:i,forced:n=!1,standard:s})=>{let{signals:{[t]:o}}=rn.constants,a=o!==void 0;return {name:t,number:a?o:e,description:r,supported:a,action:i,forced:n,standard:s}},cu=()=>{let t=xn();return Object.fromEntries(t.map(hu))},hu=({name:t,number:e,description:r,supported:i,action:n,forced:s,standard:o})=>[t,{name:t,number:e,description:r,supported:i,action:n,forced:s,standard:o}],fu=cu(),du=()=>{let t=xn(),e=Sn+1,r=Array.from({length:e},(i,n)=>pu(n,t));return Object.assign({},...r)},pu=(t,e)=>{let r=mu(t,e);if(r===void 0)return {};let{name:i,description:n,supported:s,action:o,forced:a,standard:u}=r;return {[t]:{name:i,number:t,description:n,supported:s,action:o,forced:a,standard:u}}},mu=(t,e)=>{let r=e.find(({name:i})=>rn.constants.signals[i]===t);return r!==void 0?r:e.find(i=>i.number===t)};du();var gu=({timedOut:t,timeout:e,errorCode:r,signal:i,signalDescription:n,exitCode:s,isCanceled:o})=>t?`timed out after ${e} milliseconds`:o?"was canceled":r!==void 0?`failed with ${r}`:i!==void 0?`was killed with ${i} (${n})`:s!==void 0?`failed with exit code ${s}`:"failed",hi=({stdout:t,stderr:e,all:r,error:i,signal:n,exitCode:s,command:o,escapedCommand:a,timedOut:u,isCanceled:l,killed:c,parsed:{options:{timeout:h,cwd:d=___default.default.cwd()}}})=>{s=s===null?void 0:s,n=n===null?void 0:n;let f=n===void 0?void 0:fu[n].description,b=i&&i.code,y=`Command ${gu({timedOut:u,timeout:h,errorCode:b,signal:n,signalDescription:f,exitCode:s,isCanceled:l})}: ${o}`,p=Object.prototype.toString.call(i)==="[object Error]",$=p?`${y}
${i.message}`:y,O=[$,e,t].filter(Boolean).join(`
`);return p?(i.originalMessage=i.message,i.message=O):i=new Error(O),i.shortMessage=$,i.command=o,i.escapedCommand=a,i.exitCode=s,i.signal=n,i.signalDescription=f,i.stdout=t,i.stderr=e,i.cwd=d,r!==void 0&&(i.all=r),"bufferedData"in i&&delete i.bufferedData,i.failed=!0,i.timedOut=!!u,i.isCanceled=l,i.killed=c&&!u,i},Qe=["stdin","stdout","stderr"],bu=t=>Qe.some(e=>t[e]!==void 0),yu=t=>{if(!t)return;let{stdio:e}=t;if(e===void 0)return Qe.map(i=>t[i]);if(bu(t))throw new Error(`It's not possible to provide \`stdio\` in combination with one of ${Qe.map(i=>`\`${i}\``).join(", ")}`);if(typeof e=="string")return e;if(!Array.isArray(e))throw new TypeError(`Expected \`stdio\` to be of type \`string\` or \`Array\`, got \`${typeof e}\``);let r=Math.max(e.length,Qe.length);return Array.from({length:r},(i,n)=>e[n])},pe=[];pe.push("SIGHUP","SIGINT","SIGTERM");process.platform!=="win32"&&pe.push("SIGALRM","SIGABRT","SIGVTALRM","SIGXCPU","SIGXFSZ","SIGUSR2","SIGTRAP","SIGSYS","SIGQUIT","SIGIOT");process.platform==="linux"&&pe.push("SIGIO","SIGPOLL","SIGPWR","SIGSTKFLT");var et=t=>!!t&&typeof t=="object"&&typeof t.removeListener=="function"&&typeof t.emit=="function"&&typeof t.reallyExit=="function"&&typeof t.listeners=="function"&&typeof t.kill=="function"&&typeof t.pid=="number"&&typeof t.on=="function",Ot=Symbol.for("signal-exit emitter"),Ct=globalThis,vu=Object.defineProperty.bind(Object),Ut=class{emitted={afterExit:!1,exit:!1};listeners={afterExit:[],exit:[]};count=0;id=Math.random();constructor(){if(Ct[Ot])return Ct[Ot];vu(Ct,Ot,{value:this,writable:!1,enumerable:!1,configurable:!1});}on(e,r){this.listeners[e].push(r);}removeListener(e,r){let i=this.listeners[e],n=i.indexOf(r);n!==-1&&(n===0&&i.length===1?i.length=0:i.splice(n,1));}emit(e,r,i){if(this.emitted[e])return !1;this.emitted[e]=!0;let n=!1;for(let s of this.listeners[e])n=s(r,i)===!0||n;return e==="exit"&&(n=this.emit("afterExit",r,i)||n),n}},nt=class{},wu=t=>({onExit(e,r){return t.onExit(e,r)},load(){return t.load()},unload(){return t.unload()}}),Wt=class extends nt{onExit(){return ()=>{}}load(){}unload(){}},zt=class extends nt{#r=Ht.platform==="win32"?"SIGINT":"SIGHUP";#i=new Ut;#e;#f;#S;#o={};#c=!1;constructor(e){super(),this.#e=e,this.#o={};for(let r of pe)this.#o[r]=()=>{let i=this.#e.listeners(r),{count:n}=this.#i,s=e;if(typeof s.__signal_exit_emitter__=="object"&&typeof s.__signal_exit_emitter__.count=="number"&&(n+=s.__signal_exit_emitter__.count),i.length===n){this.unload();let o=this.#i.emit("exit",null,r),a=r==="SIGHUP"?this.#r:r;o||e.kill(e.pid,a);}};this.#S=e.reallyExit,this.#f=e.emit;}onExit(e,r){if(!et(this.#e))return ()=>{};this.#c===!1&&this.load();let i=r!=null&&r.alwaysLast?"afterExit":"exit";return this.#i.on(i,e),()=>{this.#i.removeListener(i,e),this.#i.listeners.exit.length===0&&this.#i.listeners.afterExit.length===0&&this.unload();}}load(){if(!this.#c){this.#c=!0,this.#i.count+=1;for(let e of pe)try{let r=this.#o[e];r&&this.#e.on(e,r);}catch{}this.#e.emit=(e,...r)=>this.#n(e,...r),this.#e.reallyExit=e=>this.#a(e);}}unload(){this.#c&&(this.#c=!1,pe.forEach(e=>{let r=this.#o[e];if(!r)throw new Error("Listener not defined for signal: "+e);try{this.#e.removeListener(e,r);}catch{}}),this.#e.emit=this.#f,this.#e.reallyExit=this.#S,this.#i.count-=1);}#a(e){return et(this.#e)?(this.#e.exitCode=e||0,this.#i.emit("exit",this.#e.exitCode,null),this.#S.call(this.#e,this.#e.exitCode)):0}#n(e,...r){let i=this.#f;if(e==="exit"&&et(this.#e)){typeof r[0]=="number"&&(this.#e.exitCode=r[0]);let n=i.call(this.#e,e,...r);return this.#i.emit("exit",this.#e.exitCode,null),n}else return i.call(this.#e,e,...r)}},Ht=globalThis.process,{onExit:$u,load:P0,unload:_0}=wu(et(Ht)?new zt(Ht):new Wt),Su=1e3*5,xu=(t,e="SIGTERM",r={})=>{let i=t(e);return Eu(t,e,r,i),i},Eu=(t,e,r,i)=>{if(!Ou(e,r,i))return;let n=Tu(r),s=setTimeout(()=>{t("SIGKILL");},n);s.unref&&s.unref();},Ou=(t,{forceKillAfterTimeout:e},r)=>Cu(t)&&e!==!1&&r,Cu=t=>t===rn__default.default.constants.signals.SIGTERM||typeof t=="string"&&t.toUpperCase()==="SIGTERM",Tu=({forceKillAfterTimeout:t=!0})=>{if(t===!0)return Su;if(!Number.isFinite(t)||t<0)throw new TypeError(`Expected the \`forceKillAfterTimeout\` option to be a non-negative integer, got \`${t}\` (${typeof t})`);return t},Au=(t,e)=>{t.kill()&&(e.isCanceled=!0);},Ru=(t,e,r)=>{t.kill(e),r(Object.assign(new Error("Timed out"),{timedOut:!0,signal:e}));},Pu=(t,{timeout:e,killSignal:r="SIGTERM"},i)=>{if(e===0||e===void 0)return i;let n,s=new Promise((a,u)=>{n=setTimeout(()=>{Ru(t,r,u);},e);}),o=i.finally(()=>{clearTimeout(n);});return Promise.race([s,o])},_u=({timeout:t})=>{if(t!==void 0&&(!Number.isFinite(t)||t<0))throw new TypeError(`Expected the \`timeout\` option to be a non-negative integer, got \`${t}\` (${typeof t})`)},Iu=async(t,{cleanup:e,detached:r},i)=>{if(!e||r)return i;let n=$u(()=>{t.kill();});return i.finally(()=>{n();})};function En(t){return t!==null&&typeof t=="object"&&typeof t.pipe=="function"}function fi(t){return En(t)&&t.writable!==!1&&typeof t._write=="function"&&typeof t._writableState=="object"}var ku=t=>t instanceof ei.ChildProcess&&typeof t.then=="function",Tt=(t,e,r)=>{if(typeof r=="string")return t[e].pipe(Fe.createWriteStream(r)),t;if(fi(r))return t[e].pipe(r),t;if(!ku(r))throw new TypeError("The second argument must be a string, a stream or an Execa child process.");if(!fi(r.stdin))throw new TypeError("The target child process's stdin must be available.");return t[e].pipe(r.stdin),r},Mu=t=>{t.stdout!==null&&(t.pipeStdout=Tt.bind(void 0,t,"stdout")),t.stderr!==null&&(t.pipeStderr=Tt.bind(void 0,t,"stderr")),t.all!==void 0&&(t.pipeAll=Tt.bind(void 0,t,"all"));},On=async(t,{init:e,convertChunk:r,getSize:i,truncateChunk:n,addChunk:s,getFinalChunk:o,finalize:a},{maxBuffer:u=Number.POSITIVE_INFINITY}={})=>{if(!Fu(t))throw new Error("The first argument must be a Readable, a ReadableStream, or an async iterable.");let l=e();l.length=0;try{for await(let c of t){let h=Du(c),d=r[h](c,l);Cn({convertedChunk:d,state:l,getSize:i,truncateChunk:n,addChunk:s,maxBuffer:u});}return ju({state:l,convertChunk:r,getSize:i,truncateChunk:n,addChunk:s,getFinalChunk:o,maxBuffer:u}),a(l)}catch(c){throw c.bufferedData=a(l),c}},ju=({state:t,getSize:e,truncateChunk:r,addChunk:i,getFinalChunk:n,maxBuffer:s})=>{let o=n(t);o!==void 0&&Cn({convertedChunk:o,state:t,getSize:e,truncateChunk:r,addChunk:i,maxBuffer:s});},Cn=({convertedChunk:t,state:e,getSize:r,truncateChunk:i,addChunk:n,maxBuffer:s})=>{let o=r(t),a=e.length+o;if(a<=s){di(t,e,n,a);return}let u=i(t,s-e.length);throw u!==void 0&&di(u,e,n,s),new qt},di=(t,e,r,i)=>{e.contents=r(t,e,i),e.length=i;},Fu=t=>typeof t=="object"&&t!==null&&typeof t[Symbol.asyncIterator]=="function",Du=t=>{var i;let e=typeof t;if(e==="string")return "string";if(e!=="object"||t===null)return "others";if((i=globalThis.Buffer)!=null&&i.isBuffer(t))return "buffer";let r=pi.call(t);return r==="[object ArrayBuffer]"?"arrayBuffer":r==="[object DataView]"?"dataView":Number.isInteger(t.byteLength)&&Number.isInteger(t.byteOffset)&&pi.call(t.buffer)==="[object ArrayBuffer]"?"typedArray":"others"},{toString:pi}=Object.prototype,qt=class extends Error{name="MaxBufferError";constructor(){super("maxBuffer exceeded");}},Lu=t=>t,Nu=()=>{},Bu=({contents:t})=>t,Tn=t=>{throw new Error(`Streams in object mode are not supported: ${String(t)}`)},An=t=>t.length;async function Gu(t,e){return On(t,Ju,e)}var Uu=()=>({contents:new ArrayBuffer(0)}),Wu=t=>zu.encode(t),zu=new TextEncoder,mi=t=>new Uint8Array(t),gi=t=>new Uint8Array(t.buffer,t.byteOffset,t.byteLength),Hu=(t,e)=>t.slice(0,e),qu=(t,{contents:e,length:r},i)=>{let n=Pn()?Yu(e,i):Vu(e,i);return new Uint8Array(n).set(t,r),n},Vu=(t,e)=>{if(e<=t.byteLength)return t;let r=new ArrayBuffer(Rn(e));return new Uint8Array(r).set(new Uint8Array(t),0),r},Yu=(t,e)=>{if(e<=t.maxByteLength)return t.resize(e),t;let r=new ArrayBuffer(e,{maxByteLength:Rn(e)});return new Uint8Array(r).set(new Uint8Array(t),0),r},Rn=t=>bi**Math.ceil(Math.log(t)/Math.log(bi)),bi=2,Ku=({contents:t,length:e})=>Pn()?t:t.slice(0,e),Pn=()=>"resize"in ArrayBuffer.prototype,Ju={init:Uu,convertChunk:{string:Wu,buffer:mi,arrayBuffer:mi,dataView:gi,typedArray:gi,others:Tn},getSize:An,truncateChunk:Hu,addChunk:qu,getFinalChunk:Nu,finalize:Ku};async function _n(t,e){if(!("Buffer"in globalThis))throw new Error("getStreamAsBuffer() is only supported in Node.js");try{return yi(await Gu(t,e))}catch(r){throw r.bufferedData!==void 0&&(r.bufferedData=yi(r.bufferedData)),r}}var yi=t=>globalThis.Buffer.from(t);async function Xu(t,e){return On(t,rl,e)}var Zu=()=>({contents:"",textDecoder:new TextDecoder}),We=(t,{textDecoder:e})=>e.decode(t,{stream:!0}),Qu=(t,{contents:e})=>e+t,el=(t,e)=>t.slice(0,e),tl=({textDecoder:t})=>{let e=t.decode();return e===""?void 0:e},rl={init:Zu,convertChunk:{string:Lu,buffer:We,arrayBuffer:We,dataView:We,typedArray:We,others:Tn},getSize:An,truncateChunk:el,addChunk:Qu,getFinalChunk:tl,finalize:Bu},{PassThrough:il}=sa__default.default,nl=function(){var t=[],e=new il({objectMode:!0});return e.setMaxListeners(0),e.add=r,e.isEmpty=i,e.on("unpipe",n),Array.prototype.slice.call(arguments).forEach(r),e;function r(s){return Array.isArray(s)?(s.forEach(r),this):(t.push(s),s.once("end",n.bind(null,s)),s.once("error",e.emit.bind(e,"error")),s.pipe(e,{end:!1}),this)}function i(){return t.length==0}function n(s){t=t.filter(function(o){return o!==s}),!t.length&&e.readable&&e.end();}},sl=ye(nl),ol=t=>{if(t!==void 0)throw new TypeError("The `input` and `inputFile` options cannot be both set.")},al=({input:t,inputFile:e})=>typeof e!="string"?t:(ol(t),Fe.createReadStream(e)),ul=(t,e)=>{let r=al(e);r!==void 0&&(En(r)?r.pipe(t.stdin):t.stdin.end(r));},ll=(t,{all:e})=>{if(!e||!t.stdout&&!t.stderr)return;let r=sl();return t.stdout&&r.add(t.stdout),t.stderr&&r.add(t.stderr),r},At=async(t,e)=>{if(!(!t||e===void 0)){await promises.setTimeout(0),t.destroy();try{return await e}catch(r){return r.bufferedData}}},Rt=(t,{encoding:e,buffer:r,maxBuffer:i})=>{if(!(!t||!r))return e==="utf8"||e==="utf-8"?Xu(t,{maxBuffer:i}):e===null||e==="buffer"?_n(t,{maxBuffer:i}):cl(t,i,e)},cl=async(t,e,r)=>(await _n(t,{maxBuffer:e})).toString(r),hl=async({stdout:t,stderr:e,all:r},{encoding:i,buffer:n,maxBuffer:s},o)=>{let a=Rt(t,{encoding:i,buffer:n,maxBuffer:s}),u=Rt(e,{encoding:i,buffer:n,maxBuffer:s}),l=Rt(r,{encoding:i,buffer:n,maxBuffer:s*2});try{return await Promise.all([o,a,u,l])}catch(c){return Promise.all([{error:c,signal:c.signal,timedOut:c.timedOut},At(t,a),At(e,u),At(r,l)])}},fl=(async()=>{})().constructor.prototype,dl=["then","catch","finally"].map(t=>[t,Reflect.getOwnPropertyDescriptor(fl,t)]),vi=(t,e)=>{for(let[r,i]of dl){let n=typeof e=="function"?(...s)=>Reflect.apply(i.value,e(),s):i.value.bind(e);Reflect.defineProperty(t,r,{...i,value:n});}},pl=t=>new Promise((e,r)=>{t.on("exit",(i,n)=>{e({exitCode:i,signal:n});}),t.on("error",i=>{r(i);}),t.stdin&&t.stdin.on("error",i=>{r(i);});}),In=(t,e=[])=>Array.isArray(e)?[t,...e]:[t],ml=/^[\w.-]+$/,gl=t=>typeof t!="string"||ml.test(t)?t:`"${t.replaceAll('"','\\"')}"`,bl=(t,e)=>In(t,e).join(" "),yl=(t,e)=>In(t,e).map(r=>gl(r)).join(" "),vl=/ +/g,wl=t=>{let e=[];for(let r of t.trim().split(vl)){let i=e.at(-1);i&&i.endsWith("\\")?e[e.length-1]=`${i.slice(0,-1)} ${r}`:e.push(r);}return e},$l=util.debuglog("execa").enabled,ze=(t,e)=>String(t).padStart(e,"0"),Sl=()=>{let t=new Date;return `${ze(t.getHours(),2)}:${ze(t.getMinutes(),2)}:${ze(t.getSeconds(),2)}.${ze(t.getMilliseconds(),3)}`},xl=(t,{verbose:e})=>{e&&___default.default.stderr.write(`[${Sl()}] ${t}
`);},El=1e3*1e3*100,Ol=({env:t,extendEnv:e,preferLocal:r,localDir:i,execPath:n})=>{let s=e?{...___default.default.env,...t}:t;return r?Xa({env:s,cwd:i,execPath:n}):s},Cl=(t,e,r={})=>{let i=Ya._parse(t,e,r);return t=i.command,e=i.args,r=i.options,r={maxBuffer:El,buffer:!0,stripFinalNewline:!0,extendEnv:!0,preferLocal:!1,localDir:r.cwd||___default.default.cwd(),execPath:___default.default.execPath,encoding:"utf8",reject:!0,cleanup:!0,all:!1,windowsHide:!0,verbose:$l,...r},r.env=Ol(r),r.stdio=yu(r),___default.default.platform==="win32"&&D__default.default.basename(t,".exe")==="cmd"&&e.unshift("/q"),{file:t,args:e,options:r,parsed:i}},Pt=(t,e,r)=>typeof e!="string"&&!buffer.Buffer.isBuffer(e)?r===void 0?void 0:"":t.stripFinalNewline?Ka(e):e;function Tl(t,e,r){let i=Cl(t,e,r),n=bl(t,e),s=yl(t,e);xl(s,i.options),_u(i.options);let o;try{o=ei__default.default.spawn(i.file,i.args,i.options);}catch(f){let b=new ei__default.default.ChildProcess,m=Promise.reject(hi({error:f,stdout:"",stderr:"",all:"",command:n,escapedCommand:s,parsed:i,timedOut:!1,isCanceled:!1,killed:!1}));return vi(b,m),b}let a=pl(o),u=Pu(o,i.options,a),l=Iu(o,i.options,u),c={isCanceled:!1};o.kill=xu.bind(null,o.kill.bind(o)),o.cancel=Au.bind(null,o,c);let d=wn(async()=>{let[{error:f,exitCode:b,signal:m,timedOut:y},p,$,O]=await hl(o,i.options,l),A=Pt(i.options,p),j=Pt(i.options,$),N=Pt(i.options,O);if(f||b!==0||m!==null){let B=hi({error:f,exitCode:b,signal:m,stdout:A,stderr:j,all:N,command:n,escapedCommand:s,parsed:i,timedOut:y,isCanceled:i.options.signal?i.options.signal.aborted:!1,killed:o.killed});if(!i.options.reject)return B;throw B}return {command:n,escapedCommand:s,exitCode:0,stdout:A,stderr:j,all:N,failed:!1,timedOut:!1,isCanceled:!1,killed:!1}});return ul(o,i.options),o.all=ll(o,i.options),Mu(o),vi(o,d),o}function Al(t,e){let[r,...i]=wl(t);return Tl(r,i,e)}var Vt=class{value;next;constructor(e){this.value=e;}},Yt=class{#r;#i;#e;constructor(){this.clear();}enqueue(e){let r=new Vt(e);this.#r?(this.#i.next=r,this.#i=r):(this.#r=r,this.#i=r),this.#e++;}dequeue(){let e=this.#r;if(e)return this.#r=this.#r.next,this.#e--,e.value}clear(){this.#r=void 0,this.#i=void 0,this.#e=0;}get size(){return this.#e}*[Symbol.iterator](){let e=this.#r;for(;e;)yield e.value,e=e.next;}};function wi(t){if(!((Number.isInteger(t)||t===Number.POSITIVE_INFINITY)&&t>0))throw new TypeError("Expected `concurrency` to be a number from 1 and up");let e=new Yt,r=0,i=()=>{r--,e.size>0&&e.dequeue()();},n=async(a,u,l)=>{r++;let c=(async()=>a(...l))();u(c);try{await c;}catch{}i();},s=(a,u,l)=>{e.enqueue(n.bind(void 0,a,u,l)),(async()=>(await Promise.resolve(),r<t&&e.size>0&&e.dequeue()()))();},o=(a,...u)=>new Promise(l=>{s(a,l,u);});return Object.defineProperties(o,{activeCount:{get:()=>r},pendingCount:{get:()=>e.size},clearQueue:{value:()=>{e.clear();}}}),o}var st=class extends Error{constructor(e){super(),this.value=e;}},Rl=async(t,e)=>e(await t),Pl=async t=>{let e=await Promise.all(t);if(e[1]===!0)throw new st(e[0]);return !1};async function _l(t,e,{concurrency:r=Number.POSITIVE_INFINITY,preserveOrder:i=!0}={}){let n=wi(r),s=[...t].map(a=>[a,n(Rl,a,e)]),o=wi(i?1:Number.POSITIVE_INFINITY);try{await Promise.all(s.map(a=>o(Pl,a)));}catch(a){if(a instanceof st)return a.value;throw a}}var kn={directory:"isDirectory",file:"isFile"};function Il(t){if(!Object.hasOwnProperty.call(kn,t))throw new Error(`Invalid type specified: ${t}`)}var kl=(t,e)=>e[kn[t]](),Ml=t=>t instanceof URL?ia.fileURLToPath(t):t;async function $i(t,{cwd:e=___default.default.cwd(),type:r="file",allowSymlinks:i=!0,concurrency:n,preserveOrder:s}={}){Il(r),e=Ml(e);let o=i?Fe.promises.stat:Fe.promises.lstat;return _l(t,async a=>{try{let u=await o(D__default.default.resolve(e,a));return kl(r,u)}catch{return !1}},{concurrency:n,preserveOrder:s})}var jl=t=>t instanceof URL?ia.fileURLToPath(t):t,Fl=Symbol("findUpStop");async function Dl(t,e={}){let r=D__default.default.resolve(jl(e.cwd)||""),{root:i}=D__default.default.parse(r),n=D__default.default.resolve(r,e.stopAt||i),s=e.limit||Number.POSITIVE_INFINITY,o=[t].flat(),a=async l=>{if(typeof t!="function")return $i(o,l);let c=await t(l.cwd);return typeof c=="string"?$i([c],l):c},u=[];for(;;){let l=await a({...e,cwd:r});if(l===Fl||(l&&u.push(D__default.default.resolve(r,l)),r===n||u.length>=s))break;r=D__default.default.dirname(r);}return u}async function Si(t,e={}){return (await Dl(t,{...e,limit:1}))[0]}var x="\x1B[",Me="\x1B]",me="\x07",He=";",Mn=process.env.TERM_PROGRAM==="Apple_Terminal",v={};v.cursorTo=(t,e)=>{if(typeof t!="number")throw new TypeError("The `x` argument is required");return typeof e!="number"?x+(t+1)+"G":x+(e+1)+";"+(t+1)+"H"};v.cursorMove=(t,e)=>{if(typeof t!="number")throw new TypeError("The `x` argument is required");let r="";return t<0?r+=x+-t+"D":t>0&&(r+=x+t+"C"),e<0?r+=x+-e+"A":e>0&&(r+=x+e+"B"),r};v.cursorUp=(t=1)=>x+t+"A";v.cursorDown=(t=1)=>x+t+"B";v.cursorForward=(t=1)=>x+t+"C";v.cursorBackward=(t=1)=>x+t+"D";v.cursorLeft=x+"G";v.cursorSavePosition=Mn?"\x1B7":x+"s";v.cursorRestorePosition=Mn?"\x1B8":x+"u";v.cursorGetPosition=x+"6n";v.cursorNextLine=x+"E";v.cursorPrevLine=x+"F";v.cursorHide=x+"?25l";v.cursorShow=x+"?25h";v.eraseLines=t=>{let e="";for(let r=0;r<t;r++)e+=v.eraseLine+(r<t-1?v.cursorUp():"");return t&&(e+=v.cursorLeft),e};v.eraseEndLine=x+"K";v.eraseStartLine=x+"1K";v.eraseLine=x+"2K";v.eraseDown=x+"J";v.eraseUp=x+"1J";v.eraseScreen=x+"2J";v.scrollUp=x+"S";v.scrollDown=x+"T";v.clearScreen="\x1Bc";v.clearTerminal=process.platform==="win32"?`${v.eraseScreen}${x}0f`:`${v.eraseScreen}${x}3J${x}H`;v.beep=me;v.link=(t,e)=>[Me,"8",He,He,e,me,t,Me,"8",He,He,me].join("");v.image=(t,e={})=>{let r=`${Me}1337;File=inline=1`;return e.width&&(r+=`;width=${e.width}`),e.height&&(r+=`;height=${e.height}`),e.preserveAspectRatio===!1&&(r+=";preserveAspectRatio=0"),r+":"+t.toString("base64")+me};v.iTerm={setCwd:(t=process.cwd())=>`${Me}50;CurrentDir=${t}${me}`,annotation:(t,e={})=>{let r=`${Me}1337;`,i=typeof e.x<"u",n=typeof e.y<"u";if((i||n)&&!(i&&n&&typeof e.length<"u"))throw new Error("`x`, `y` and `length` must be defined when `x` or `y` is defined");return t=t.replace(/\|/g,""),r+=e.isHidden?"AddHiddenAnnotation=":"AddAnnotation=",e.length>0?r+=(i?[t,e.length,e.x,e.y]:[e.length,t]).join("|"):r+=t,r+me}};var jn=(t,e=process.argv)=>{let r=t.startsWith("-")?"":t.length===1?"-":"--",i=e.indexOf(r+t),n=e.indexOf("--");return i!==-1&&(n===-1||i<n)},Ll=rn__default.default,xi=ua__default.default,G=jn,{env:M}=process,te;G("no-color")||G("no-colors")||G("color=false")||G("color=never")?te=0:(G("color")||G("colors")||G("color=true")||G("color=always"))&&(te=1);"FORCE_COLOR"in M&&(M.FORCE_COLOR==="true"?te=1:M.FORCE_COLOR==="false"?te=0:te=M.FORCE_COLOR.length===0?1:Math.min(parseInt(M.FORCE_COLOR,10),3));function Kt(t){return t===0?!1:{level:t,hasBasic:!0,has256:t>=2,has16m:t>=3}}function Jt(t,e){if(te===0)return 0;if(G("color=16m")||G("color=full")||G("color=truecolor"))return 3;if(G("color=256"))return 2;if(t&&!e&&te===void 0)return 0;let r=te||0;if(M.TERM==="dumb")return r;if(process.platform==="win32"){let i=Ll.release().split(".");return Number(i[0])>=10&&Number(i[2])>=10586?Number(i[2])>=14931?3:2:1}if("CI"in M)return ["TRAVIS","CIRCLECI","APPVEYOR","GITLAB_CI","GITHUB_ACTIONS","BUILDKITE"].some(i=>i in M)||M.CI_NAME==="codeship"?1:r;if("TEAMCITY_VERSION"in M)return /^(9\.(0*[1-9]\d*)\.|\d{2,}\.)/.test(M.TEAMCITY_VERSION)?1:0;if(M.COLORTERM==="truecolor")return 3;if("TERM_PROGRAM"in M){let i=parseInt((M.TERM_PROGRAM_VERSION||"").split(".")[0],10);switch(M.TERM_PROGRAM){case"iTerm.app":return i>=3?3:2;case"Apple_Terminal":return 2}}return /-256(color)?$/i.test(M.TERM)?2:/^screen|^xterm|^vt100|^vt220|^rxvt|color|ansi|cygwin|linux/i.test(M.TERM)||"COLORTERM"in M?1:r}function Nl(t){let e=Jt(t,t&&t.isTTY);return Kt(e)}var Bl={supportsColor:Nl,stdout:Kt(Jt(!0,xi.isatty(1))),stderr:Kt(Jt(!0,xi.isatty(2)))},Gl=Bl,ce=jn;function Ei(t){if(/^\d{3,4}$/.test(t)){let r=/(\d{1,2})(\d{2})/.exec(t);return {major:0,minor:parseInt(r[1],10),patch:parseInt(r[2],10)}}let e=(t||"").split(".").map(r=>parseInt(r,10));return {major:e[0],minor:e[1],patch:e[2]}}function _t(t){let{env:e}=process;if("FORCE_HYPERLINK"in e)return !(e.FORCE_HYPERLINK.length>0&&parseInt(e.FORCE_HYPERLINK,10)===0);if(ce("no-hyperlink")||ce("no-hyperlinks")||ce("hyperlink=false")||ce("hyperlink=never"))return !1;if(ce("hyperlink=true")||ce("hyperlink=always"))return !0;if(!Gl.supportsColor(t)||t&&!t.isTTY||process.platform==="win32")return !1;if("NETLIFY"in e)return !0;if("CI"in e||"TEAMCITY_VERSION"in e)return !1;if("TERM_PROGRAM"in e){let r=Ei(e.TERM_PROGRAM_VERSION);switch(e.TERM_PROGRAM){case"iTerm.app":return r.major===3?r.minor>=1:r.major>3}}if("VTE_VERSION"in e){if(e.VTE_VERSION==="0.50.0")return !1;let r=Ei(e.VTE_VERSION);return r.major>0||r.minor>=50}return !1}var Ul={supportsHyperlink:_t,stdout:_t(process.stdout),stderr:_t(process.stderr)},vr=ye(Ul);function je(t,e,{target:r="stdout",...i}={}){return vr[r]?v.link(t,e):i.fallback===!1?t:typeof i.fallback=="function"?i.fallback(t,e):`${t} (\u200B${e}\u200B)`}je.isSupported=vr.stdout;je.stderr=(t,e,r={})=>je(t,e,{target:"stderr",...r});je.stderr.isSupported=vr.stderr;var Fn={},Xt,Dn,Ln,Nn,Bn=!0;typeof process<"u"&&({FORCE_COLOR:Xt,NODE_DISABLE_COLORS:Dn,NO_COLOR:Ln,TERM:Nn}=process.env||{},Bn=process.stdout&&process.stdout.isTTY);var S={enabled:!Dn&&Ln==null&&Nn!=="dumb"&&(Xt!=null&&Xt!=="0"||Bn),reset:C(0,0),bold:C(1,22),dim:C(2,22),italic:C(3,23),underline:C(4,24),inverse:C(7,27),hidden:C(8,28),strikethrough:C(9,29),black:C(30,39),red:C(31,39),green:C(32,39),yellow:C(33,39),blue:C(34,39),magenta:C(35,39),cyan:C(36,39),white:C(37,39),gray:C(90,39),grey:C(90,39),bgBlack:C(40,49),bgRed:C(41,49),bgGreen:C(42,49),bgYellow:C(43,49),bgBlue:C(44,49),bgMagenta:C(45,49),bgCyan:C(46,49),bgWhite:C(47,49)};function Oi(t,e){let r=0,i,n="",s="";for(;r<t.length;r++)i=t[r],n+=i.open,s+=i.close,~e.indexOf(i.close)&&(e=e.replace(i.rgx,i.close+i.open));return n+e+s}function Wl(t,e){let r={has:t,keys:e};return r.reset=S.reset.bind(r),r.bold=S.bold.bind(r),r.dim=S.dim.bind(r),r.italic=S.italic.bind(r),r.underline=S.underline.bind(r),r.inverse=S.inverse.bind(r),r.hidden=S.hidden.bind(r),r.strikethrough=S.strikethrough.bind(r),r.black=S.black.bind(r),r.red=S.red.bind(r),r.green=S.green.bind(r),r.yellow=S.yellow.bind(r),r.blue=S.blue.bind(r),r.magenta=S.magenta.bind(r),r.cyan=S.cyan.bind(r),r.white=S.white.bind(r),r.gray=S.gray.bind(r),r.grey=S.grey.bind(r),r.bgBlack=S.bgBlack.bind(r),r.bgRed=S.bgRed.bind(r),r.bgGreen=S.bgGreen.bind(r),r.bgYellow=S.bgYellow.bind(r),r.bgBlue=S.bgBlue.bind(r),r.bgMagenta=S.bgMagenta.bind(r),r.bgCyan=S.bgCyan.bind(r),r.bgWhite=S.bgWhite.bind(r),r}function C(t,e){let r={open:`\x1B[${t}m`,close:`\x1B[${e}m`,rgx:new RegExp(`\\x1b\\[${e}m`,"g")};return function(i){return this!==void 0&&this.has!==void 0?(~this.has.indexOf(t)||(this.has.push(t),this.keys.push(r)),i===void 0?this:S.enabled?Oi(this.keys,i+""):i+""):i===void 0?Wl([t],[r]):S.enabled?Oi([r],i+""):i+""}}var U=S,zl=(t,e)=>{if(!(t.meta&&t.name!=="escape")){if(t.ctrl)return t.name==="a"?"first":t.name==="c"||t.name==="d"?"abort":t.name==="e"?"last":t.name==="g"?"reset":t.name==="n"?"down":t.name==="p"?"up":void 0;if(e){if(t.name==="j")return "down";if(t.name==="k")return "up"}return t.name==="return"||t.name==="enter"?"submit":t.name==="backspace"?"delete":t.name==="delete"?"deleteForward":t.name==="abort"?"abort":t.name==="escape"?"exit":t.name==="tab"?"next":t.name==="pagedown"?"nextPage":t.name==="pageup"?"prevPage":t.name==="home"?"home":t.name==="end"?"end":t.name==="up"?"up":t.name==="down"?"down":t.name==="right"?"right":t.name==="left"?"left":!1}},wr=t=>{let e=["[\\u001B\\u009B][[\\]()#;?]*(?:(?:(?:(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]+)*|[a-zA-Z\\d]+(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]*)*)?\\u0007)","(?:(?:\\d{1,4}(?:;\\d{0,4})*)?[\\dA-PRZcf-ntqry=><~]))"].join("|"),r=new RegExp(e,"g");return typeof t=="string"?t.replace(r,""):t},Zt="\x1B",P=`${Zt}[`,Hl="\x07",Qt={to(t,e){return e?`${P}${e+1};${t+1}H`:`${P}${t+1}G`},move(t,e){let r="";return t<0?r+=`${P}${-t}D`:t>0&&(r+=`${P}${t}C`),e<0?r+=`${P}${-e}A`:e>0&&(r+=`${P}${e}B`),r},up:(t=1)=>`${P}${t}A`,down:(t=1)=>`${P}${t}B`,forward:(t=1)=>`${P}${t}C`,backward:(t=1)=>`${P}${t}D`,nextLine:(t=1)=>`${P}E`.repeat(t),prevLine:(t=1)=>`${P}F`.repeat(t),left:`${P}G`,hide:`${P}?25l`,show:`${P}?25h`,save:`${Zt}7`,restore:`${Zt}8`},ql={up:(t=1)=>`${P}S`.repeat(t),down:(t=1)=>`${P}T`.repeat(t)},Vl={screen:`${P}2J`,up:(t=1)=>`${P}1J`.repeat(t),down:(t=1)=>`${P}J`.repeat(t),line:`${P}2K`,lineEnd:`${P}K`,lineStart:`${P}1K`,lines(t){let e="";for(let r=0;r<t;r++)e+=this.line+(r<t-1?Qt.up():"");return t&&(e+=Qt.left),e}},W={cursor:Qt,scroll:ql,erase:Vl,beep:Hl},Yl=wr,{erase:Ci,cursor:Kl}=W,Jl=t=>[...Yl(t)].length,Xl=function(t,e){if(!e)return Ci.line+Kl.to(0);let r=0,i=t.split(/\r?\n/);for(let n of i)r+=1+Math.floor(Math.max(Jl(n)-1,0)/e);return Ci.lines(r)},Pe={arrowUp:"\u2191",arrowDown:"\u2193",arrowLeft:"\u2190",arrowRight:"\u2192",radioOn:"\u25C9",radioOff:"\u25EF",tick:"\u2714",cross:"\u2716",ellipsis:"\u2026",pointerSmall:"\u203A",line:"\u2500",pointer:"\u276F"},Zl={arrowUp:Pe.arrowUp,arrowDown:Pe.arrowDown,arrowLeft:Pe.arrowLeft,arrowRight:Pe.arrowRight,radioOn:"(*)",radioOff:"( )",tick:"\u221A",cross:"\xD7",ellipsis:"...",pointerSmall:"\xBB",line:"\u2500",pointer:">"},Ql=process.platform==="win32"?Zl:Pe,Gn=Ql,de=U,ae=Gn,er=Object.freeze({password:{scale:1,render:t=>"*".repeat(t.length)},emoji:{scale:2,render:t=>"\u{1F603}".repeat(t.length)},invisible:{scale:0,render:t=>""},default:{scale:1,render:t=>`${t}`}}),ec=t=>er[t]||er.default,_e=Object.freeze({aborted:de.red(ae.cross),done:de.green(ae.tick),exited:de.yellow(ae.cross),default:de.cyan("?")}),tc=(t,e,r)=>e?_e.aborted:r?_e.exited:t?_e.done:_e.default,rc=t=>de.gray(t?ae.ellipsis:ae.pointerSmall),ic=(t,e)=>de.gray(t?e?ae.pointerSmall:"+":ae.line),nc={styles:er,render:ec,symbols:_e,symbol:tc,delimiter:rc,item:ic},sc=wr,oc=function(t,e){let r=String(sc(t)||"").split(/\r?\n/);return e?r.map(i=>Math.ceil(i.length/e)).reduce((i,n)=>i+n):r.length},ac=(t,e={})=>{let r=Number.isSafeInteger(parseInt(e.margin))?new Array(parseInt(e.margin)).fill(" ").join(""):e.margin||"",i=e.width;return (t||"").split(/\r?\n/g).map(n=>n.split(/\s+/g).reduce((s,o)=>(o.length+r.length>=i||s[s.length-1].length+o.length+1<i?s[s.length-1]+=` ${o}`:s.push(`${r}${o}`),s),[r]).join(`
`)).join(`
`)},uc=(t,e,r)=>{r=r||e;let i=Math.min(e-r,t-Math.floor(r/2));i<0&&(i=0);let n=Math.min(i+r,e);return {startIndex:i,endIndex:n}},q={action:zl,clear:Xl,style:nc,strip:wr,figures:Gn,lines:oc,wrap:ac,entriesToDisplay:uc},Ti=la__default.default,{action:lc}=q,cc=ca__default.default,{beep:hc,cursor:fc}=W,dc=U,pc=class extends cc{constructor(e={}){super(),this.firstRender=!0,this.in=e.stdin||process.stdin,this.out=e.stdout||process.stdout,this.onRender=(e.onRender||(()=>{})).bind(this);let r=Ti.createInterface({input:this.in,escapeCodeTimeout:50});Ti.emitKeypressEvents(this.in,r),this.in.isTTY&&this.in.setRawMode(!0);let i=["SelectPrompt","MultiselectPrompt"].indexOf(this.constructor.name)>-1,n=(s,o)=>{let a=lc(o,i);a===!1?this._&&this._(s,o):typeof this[a]=="function"?this[a](o):this.bell();};this.close=()=>{this.out.write(fc.show),this.in.removeListener("keypress",n),this.in.isTTY&&this.in.setRawMode(!1),r.close(),this.emit(this.aborted?"abort":this.exited?"exit":"submit",this.value),this.closed=!0;},this.in.on("keypress",n);}fire(){this.emit("state",{value:this.value,aborted:!!this.aborted,exited:!!this.exited});}bell(){this.out.write(hc);}render(){this.onRender(dc),this.firstRender&&(this.firstRender=!1);}},ie=pc,qe=U,mc=ie,{erase:gc,cursor:Ae}=W,{style:It,clear:kt,lines:bc,figures:yc}=q,tr=class extends mc{constructor(e={}){super(e),this.transform=It.render(e.style),this.scale=this.transform.scale,this.msg=e.message,this.initial=e.initial||"",this.validator=e.validate||(()=>!0),this.value="",this.errorMsg=e.error||"Please Enter A Valid Value",this.cursor=+!!this.initial,this.cursorOffset=0,this.clear=kt("",this.out.columns),this.render();}set value(e){!e&&this.initial?(this.placeholder=!0,this.rendered=qe.gray(this.transform.render(this.initial))):(this.placeholder=!1,this.rendered=this.transform.render(e)),this._value=e,this.fire();}get value(){return this._value}reset(){this.value="",this.cursor=+!!this.initial,this.cursorOffset=0,this.fire(),this.render();}exit(){this.abort();}abort(){this.value=this.value||this.initial,this.done=this.aborted=!0,this.error=!1,this.red=!1,this.fire(),this.render(),this.out.write(`
`),this.close();}async validate(){let e=await this.validator(this.value);typeof e=="string"&&(this.errorMsg=e,e=!1),this.error=!e;}async submit(){if(this.value=this.value||this.initial,this.cursorOffset=0,this.cursor=this.rendered.length,await this.validate(),this.error){this.red=!0,this.fire(),this.render();return}this.done=!0,this.aborted=!1,this.fire(),this.render(),this.out.write(`
`),this.close();}next(){if(!this.placeholder)return this.bell();this.value=this.initial,this.cursor=this.rendered.length,this.fire(),this.render();}moveCursor(e){this.placeholder||(this.cursor=this.cursor+e,this.cursorOffset+=e);}_(e,r){let i=this.value.slice(0,this.cursor),n=this.value.slice(this.cursor);this.value=`${i}${e}${n}`,this.red=!1,this.cursor=this.placeholder?0:i.length+1,this.render();}delete(){if(this.isCursorAtStart())return this.bell();let e=this.value.slice(0,this.cursor-1),r=this.value.slice(this.cursor);this.value=`${e}${r}`,this.red=!1,this.isCursorAtStart()?this.cursorOffset=0:(this.cursorOffset++,this.moveCursor(-1)),this.render();}deleteForward(){if(this.cursor*this.scale>=this.rendered.length||this.placeholder)return this.bell();let e=this.value.slice(0,this.cursor),r=this.value.slice(this.cursor+1);this.value=`${e}${r}`,this.red=!1,this.isCursorAtEnd()?this.cursorOffset=0:this.cursorOffset++,this.render();}first(){this.cursor=0,this.render();}last(){this.cursor=this.value.length,this.render();}left(){if(this.cursor<=0||this.placeholder)return this.bell();this.moveCursor(-1),this.render();}right(){if(this.cursor*this.scale>=this.rendered.length||this.placeholder)return this.bell();this.moveCursor(1),this.render();}isCursorAtStart(){return this.cursor===0||this.placeholder&&this.cursor===1}isCursorAtEnd(){return this.cursor===this.rendered.length||this.placeholder&&this.cursor===this.rendered.length+1}render(){this.closed||(this.firstRender||(this.outputError&&this.out.write(Ae.down(bc(this.outputError,this.out.columns)-1)+kt(this.outputError,this.out.columns)),this.out.write(kt(this.outputText,this.out.columns))),super.render(),this.outputError="",this.outputText=[It.symbol(this.done,this.aborted),qe.bold(this.msg),It.delimiter(this.done),this.red?qe.red(this.rendered):this.rendered].join(" "),this.error&&(this.outputError+=this.errorMsg.split(`
`).reduce((e,r,i)=>e+`
${i?" ":yc.pointerSmall} ${qe.red().italic(r)}`,"")),this.out.write(gc.line+Ae.to(0)+this.outputText+Ae.save+this.outputError+Ae.restore+Ae.move(this.cursorOffset,0)));}},vc=tr,Y=U,wc=ie,{style:Ai,clear:Ri,figures:Ve,wrap:$c,entriesToDisplay:Sc}=q,{cursor:xc}=W,rr=class extends wc{constructor(e={}){super(e),this.msg=e.message,this.hint=e.hint||"- Use arrow-keys. Return to submit.",this.warn=e.warn||"- This option is disabled",this.cursor=e.initial||0,this.choices=e.choices.map((r,i)=>(typeof r=="string"&&(r={title:r,value:i}),{title:r&&(r.title||r.value||r),value:r&&(r.value===void 0?i:r.value),description:r&&r.description,selected:r&&r.selected,disabled:r&&r.disabled})),this.optionsPerPage=e.optionsPerPage||10,this.value=(this.choices[this.cursor]||{}).value,this.clear=Ri("",this.out.columns),this.render();}moveCursor(e){this.cursor=e,this.value=this.choices[e].value,this.fire();}reset(){this.moveCursor(0),this.fire(),this.render();}exit(){this.abort();}abort(){this.done=this.aborted=!0,this.fire(),this.render(),this.out.write(`
`),this.close();}submit(){this.selection.disabled?this.bell():(this.done=!0,this.aborted=!1,this.fire(),this.render(),this.out.write(`
`),this.close());}first(){this.moveCursor(0),this.render();}last(){this.moveCursor(this.choices.length-1),this.render();}up(){this.cursor===0?this.moveCursor(this.choices.length-1):this.moveCursor(this.cursor-1),this.render();}down(){this.cursor===this.choices.length-1?this.moveCursor(0):this.moveCursor(this.cursor+1),this.render();}next(){this.moveCursor((this.cursor+1)%this.choices.length),this.render();}_(e,r){if(e===" ")return this.submit()}get selection(){return this.choices[this.cursor]}render(){if(this.closed)return;this.firstRender?this.out.write(xc.hide):this.out.write(Ri(this.outputText,this.out.columns)),super.render();let{startIndex:e,endIndex:r}=Sc(this.cursor,this.choices.length,this.optionsPerPage);if(this.outputText=[Ai.symbol(this.done,this.aborted),Y.bold(this.msg),Ai.delimiter(!1),this.done?this.selection.title:this.selection.disabled?Y.yellow(this.warn):Y.gray(this.hint)].join(" "),!this.done){this.outputText+=`
`;for(let i=e;i<r;i++){let n,s,o="",a=this.choices[i];i===e&&e>0?s=Ve.arrowUp:i===r-1&&r<this.choices.length?s=Ve.arrowDown:s=" ",a.disabled?(n=this.cursor===i?Y.gray().underline(a.title):Y.strikethrough().gray(a.title),s=(this.cursor===i?Y.bold().gray(Ve.pointer)+" ":"  ")+s):(n=this.cursor===i?Y.cyan().underline(a.title):a.title,s=(this.cursor===i?Y.cyan(Ve.pointer)+" ":"  ")+s,a.description&&this.cursor===i&&(o=` - ${a.description}`,(s.length+n.length+o.length>=this.out.columns||a.description.split(/\r?\n/).length>1)&&(o=`
`+$c(a.description,{margin:3,width:this.out.columns})))),this.outputText+=`${s} ${n}${Y.gray(o)}
`;}}this.out.write(this.outputText);}},Ec=rr,Ye=U,Oc=ie,{style:Pi,clear:Cc}=q,{cursor:_i,erase:Tc}=W,ir=class extends Oc{constructor(e={}){super(e),this.msg=e.message,this.value=!!e.initial,this.active=e.active||"on",this.inactive=e.inactive||"off",this.initialValue=this.value,this.render();}reset(){this.value=this.initialValue,this.fire(),this.render();}exit(){this.abort();}abort(){this.done=this.aborted=!0,this.fire(),this.render(),this.out.write(`
`),this.close();}submit(){this.done=!0,this.aborted=!1,this.fire(),this.render(),this.out.write(`
`),this.close();}deactivate(){if(this.value===!1)return this.bell();this.value=!1,this.render();}activate(){if(this.value===!0)return this.bell();this.value=!0,this.render();}delete(){this.deactivate();}left(){this.deactivate();}right(){this.activate();}down(){this.deactivate();}up(){this.activate();}next(){this.value=!this.value,this.fire(),this.render();}_(e,r){if(e===" ")this.value=!this.value;else if(e==="1")this.value=!0;else if(e==="0")this.value=!1;else return this.bell();this.render();}render(){this.closed||(this.firstRender?this.out.write(_i.hide):this.out.write(Cc(this.outputText,this.out.columns)),super.render(),this.outputText=[Pi.symbol(this.done,this.aborted),Ye.bold(this.msg),Pi.delimiter(this.done),this.value?this.inactive:Ye.cyan().underline(this.inactive),Ye.gray("/"),this.value?Ye.cyan().underline(this.active):this.active].join(" "),this.out.write(Tc.line+_i.to(0)+this.outputText));}},Ac=ir,Rc=class nr{constructor({token:e,date:r,parts:i,locales:n}){this.token=e,this.date=r||new Date,this.parts=i||[this],this.locales=n||{};}up(){}down(){}next(){let e=this.parts.indexOf(this);return this.parts.find((r,i)=>i>e&&r instanceof nr)}setTo(e){}prev(){let e=[].concat(this.parts).reverse(),r=e.indexOf(this);return e.find((i,n)=>n>r&&i instanceof nr)}toString(){return String(this.date)}},Z=Rc,Pc=Z,_c=class extends Pc{constructor(e={}){super(e);}up(){this.date.setHours((this.date.getHours()+12)%24);}down(){this.up();}toString(){let e=this.date.getHours()>12?"pm":"am";return /\A/.test(this.token)?e.toUpperCase():e}},Ic=_c,kc=Z,Mc=t=>(t=t%10,t===1?"st":t===2?"nd":t===3?"rd":"th"),jc=class extends kc{constructor(e={}){super(e);}up(){this.date.setDate(this.date.getDate()+1);}down(){this.date.setDate(this.date.getDate()-1);}setTo(e){this.date.setDate(parseInt(e.substr(-2)));}toString(){let e=this.date.getDate(),r=this.date.getDay();return this.token==="DD"?String(e).padStart(2,"0"):this.token==="Do"?e+Mc(e):this.token==="d"?r+1:this.token==="ddd"?this.locales.weekdaysShort[r]:this.token==="dddd"?this.locales.weekdays[r]:e}},Fc=jc,Dc=Z,Lc=class extends Dc{constructor(e={}){super(e);}up(){this.date.setHours(this.date.getHours()+1);}down(){this.date.setHours(this.date.getHours()-1);}setTo(e){this.date.setHours(parseInt(e.substr(-2)));}toString(){let e=this.date.getHours();return /h/.test(this.token)&&(e=e%12||12),this.token.length>1?String(e).padStart(2,"0"):e}},Nc=Lc,Bc=Z,Gc=class extends Bc{constructor(e={}){super(e);}up(){this.date.setMilliseconds(this.date.getMilliseconds()+1);}down(){this.date.setMilliseconds(this.date.getMilliseconds()-1);}setTo(e){this.date.setMilliseconds(parseInt(e.substr(-this.token.length)));}toString(){return String(this.date.getMilliseconds()).padStart(4,"0").substr(0,this.token.length)}},Uc=Gc,Wc=Z,zc=class extends Wc{constructor(e={}){super(e);}up(){this.date.setMinutes(this.date.getMinutes()+1);}down(){this.date.setMinutes(this.date.getMinutes()-1);}setTo(e){this.date.setMinutes(parseInt(e.substr(-2)));}toString(){let e=this.date.getMinutes();return this.token.length>1?String(e).padStart(2,"0"):e}},Hc=zc,qc=Z,Vc=class extends qc{constructor(e={}){super(e);}up(){this.date.setMonth(this.date.getMonth()+1);}down(){this.date.setMonth(this.date.getMonth()-1);}setTo(e){e=parseInt(e.substr(-2))-1,this.date.setMonth(e<0?0:e);}toString(){let e=this.date.getMonth(),r=this.token.length;return r===2?String(e+1).padStart(2,"0"):r===3?this.locales.monthsShort[e]:r===4?this.locales.months[e]:String(e+1)}},Yc=Vc,Kc=Z,Jc=class extends Kc{constructor(e={}){super(e);}up(){this.date.setSeconds(this.date.getSeconds()+1);}down(){this.date.setSeconds(this.date.getSeconds()-1);}setTo(e){this.date.setSeconds(parseInt(e.substr(-2)));}toString(){let e=this.date.getSeconds();return this.token.length>1?String(e).padStart(2,"0"):e}},Xc=Jc,Zc=Z,Qc=class extends Zc{constructor(e={}){super(e);}up(){this.date.setFullYear(this.date.getFullYear()+1);}down(){this.date.setFullYear(this.date.getFullYear()-1);}setTo(e){this.date.setFullYear(e.substr(-4));}toString(){let e=String(this.date.getFullYear()).padStart(4,"0");return this.token.length===2?e.substr(-2):e}},eh=Qc,th={DatePart:Z,Meridiem:Ic,Day:Fc,Hours:Nc,Milliseconds:Uc,Minutes:Hc,Month:Yc,Seconds:Xc,Year:eh},Mt=U,rh=ie,{style:Ii,clear:ki,figures:ih}=q,{erase:nh,cursor:Mi}=W,{DatePart:ji,Meridiem:sh,Day:oh,Hours:ah,Milliseconds:uh,Minutes:lh,Month:ch,Seconds:hh,Year:fh}=th,dh=/\\(.)|"((?:\\["\\]|[^"])+)"|(D[Do]?|d{3,4}|d)|(M{1,4})|(YY(?:YY)?)|([aA])|([Hh]{1,2})|(m{1,2})|(s{1,2})|(S{1,4})|./g,Fi={1:({token:t})=>t.replace(/\\(.)/g,"$1"),2:t=>new oh(t),3:t=>new ch(t),4:t=>new fh(t),5:t=>new sh(t),6:t=>new ah(t),7:t=>new lh(t),8:t=>new hh(t),9:t=>new uh(t)},ph={months:"January,February,March,April,May,June,July,August,September,October,November,December".split(","),monthsShort:"Jan,Feb,Mar,Apr,May,Jun,Jul,Aug,Sep,Oct,Nov,Dec".split(","),weekdays:"Sunday,Monday,Tuesday,Wednesday,Thursday,Friday,Saturday".split(","),weekdaysShort:"Sun,Mon,Tue,Wed,Thu,Fri,Sat".split(",")},sr=class extends rh{constructor(e={}){super(e),this.msg=e.message,this.cursor=0,this.typed="",this.locales=Object.assign(ph,e.locales),this._date=e.initial||new Date,this.errorMsg=e.error||"Please Enter A Valid Value",this.validator=e.validate||(()=>!0),this.mask=e.mask||"YYYY-MM-DD HH:mm:ss",this.clear=ki("",this.out.columns),this.render();}get value(){return this.date}get date(){return this._date}set date(e){e&&this._date.setTime(e.getTime());}set mask(e){let r;for(this.parts=[];r=dh.exec(e);){let n=r.shift(),s=r.findIndex(o=>o!=null);this.parts.push(s in Fi?Fi[s]({token:r[s]||n,date:this.date,parts:this.parts,locales:this.locales}):r[s]||n);}let i=this.parts.reduce((n,s)=>(typeof s=="string"&&typeof n[n.length-1]=="string"?n[n.length-1]+=s:n.push(s),n),[]);this.parts.splice(0),this.parts.push(...i),this.reset();}moveCursor(e){this.typed="",this.cursor=e,this.fire();}reset(){this.moveCursor(this.parts.findIndex(e=>e instanceof ji)),this.fire(),this.render();}exit(){this.abort();}abort(){this.done=this.aborted=!0,this.error=!1,this.fire(),this.render(),this.out.write(`
`),this.close();}async validate(){let e=await this.validator(this.value);typeof e=="string"&&(this.errorMsg=e,e=!1),this.error=!e;}async submit(){if(await this.validate(),this.error){this.color="red",this.fire(),this.render();return}this.done=!0,this.aborted=!1,this.fire(),this.render(),this.out.write(`
`),this.close();}up(){this.typed="",this.parts[this.cursor].up(),this.render();}down(){this.typed="",this.parts[this.cursor].down(),this.render();}left(){let e=this.parts[this.cursor].prev();if(e==null)return this.bell();this.moveCursor(this.parts.indexOf(e)),this.render();}right(){let e=this.parts[this.cursor].next();if(e==null)return this.bell();this.moveCursor(this.parts.indexOf(e)),this.render();}next(){let e=this.parts[this.cursor].next();this.moveCursor(e?this.parts.indexOf(e):this.parts.findIndex(r=>r instanceof ji)),this.render();}_(e){/\d/.test(e)&&(this.typed+=e,this.parts[this.cursor].setTo(this.typed),this.render());}render(){this.closed||(this.firstRender?this.out.write(Mi.hide):this.out.write(ki(this.outputText,this.out.columns)),super.render(),this.outputText=[Ii.symbol(this.done,this.aborted),Mt.bold(this.msg),Ii.delimiter(!1),this.parts.reduce((e,r,i)=>e.concat(i===this.cursor&&!this.done?Mt.cyan().underline(r.toString()):r),[]).join("")].join(" "),this.error&&(this.outputText+=this.errorMsg.split(`
`).reduce((e,r,i)=>e+`
${i?" ":ih.pointerSmall} ${Mt.red().italic(r)}`,"")),this.out.write(nh.line+Mi.to(0)+this.outputText));}},mh=sr,Ke=U,gh=ie,{cursor:Je,erase:bh}=W,{style:jt,figures:yh,clear:Di,lines:vh}=q,wh=/[0-9]/,Ft=t=>t!==void 0,Li=(t,e)=>{let r=Math.pow(10,e);return Math.round(t*r)/r},or=class extends gh{constructor(e={}){super(e),this.transform=jt.render(e.style),this.msg=e.message,this.initial=Ft(e.initial)?e.initial:"",this.float=!!e.float,this.round=e.round||2,this.inc=e.increment||1,this.min=Ft(e.min)?e.min:-1/0,this.max=Ft(e.max)?e.max:1/0,this.errorMsg=e.error||"Please Enter A Valid Value",this.validator=e.validate||(()=>!0),this.color="cyan",this.value="",this.typed="",this.lastHit=0,this.render();}set value(e){!e&&e!==0?(this.placeholder=!0,this.rendered=Ke.gray(this.transform.render(`${this.initial}`)),this._value=""):(this.placeholder=!1,this.rendered=this.transform.render(`${Li(e,this.round)}`),this._value=Li(e,this.round)),this.fire();}get value(){return this._value}parse(e){return this.float?parseFloat(e):parseInt(e)}valid(e){return e==="-"||e==="."&&this.float||wh.test(e)}reset(){this.typed="",this.value="",this.fire(),this.render();}exit(){this.abort();}abort(){let e=this.value;this.value=e!==""?e:this.initial,this.done=this.aborted=!0,this.error=!1,this.fire(),this.render(),this.out.write(`
`),this.close();}async validate(){let e=await this.validator(this.value);typeof e=="string"&&(this.errorMsg=e,e=!1),this.error=!e;}async submit(){if(await this.validate(),this.error){this.color="red",this.fire(),this.render();return}let e=this.value;this.value=e!==""?e:this.initial,this.done=!0,this.aborted=!1,this.error=!1,this.fire(),this.render(),this.out.write(`
`),this.close();}up(){if(this.typed="",this.value===""&&(this.value=this.min-this.inc),this.value>=this.max)return this.bell();this.value+=this.inc,this.color="cyan",this.fire(),this.render();}down(){if(this.typed="",this.value===""&&(this.value=this.min+this.inc),this.value<=this.min)return this.bell();this.value-=this.inc,this.color="cyan",this.fire(),this.render();}delete(){let e=this.value.toString();if(e.length===0)return this.bell();this.value=this.parse(e=e.slice(0,-1))||"",this.value!==""&&this.value<this.min&&(this.value=this.min),this.color="cyan",this.fire(),this.render();}next(){this.value=this.initial,this.fire(),this.render();}_(e,r){if(!this.valid(e))return this.bell();let i=Date.now();if(i-this.lastHit>1e3&&(this.typed=""),this.typed+=e,this.lastHit=i,this.color="cyan",e===".")return this.fire();this.value=Math.min(this.parse(this.typed),this.max),this.value>this.max&&(this.value=this.max),this.value<this.min&&(this.value=this.min),this.fire(),this.render();}render(){this.closed||(this.firstRender||(this.outputError&&this.out.write(Je.down(vh(this.outputError,this.out.columns)-1)+Di(this.outputError,this.out.columns)),this.out.write(Di(this.outputText,this.out.columns))),super.render(),this.outputError="",this.outputText=[jt.symbol(this.done,this.aborted),Ke.bold(this.msg),jt.delimiter(this.done),!this.done||!this.done&&!this.placeholder?Ke[this.color]().underline(this.rendered):this.rendered].join(" "),this.error&&(this.outputError+=this.errorMsg.split(`
`).reduce((e,r,i)=>e+`
${i?" ":yh.pointerSmall} ${Ke.red().italic(r)}`,"")),this.out.write(bh.line+Je.to(0)+this.outputText+Je.save+this.outputError+Je.restore));}},$h=or,H=U,{cursor:Sh}=W,xh=ie,{clear:Ni,figures:Q,style:Bi,wrap:Eh,entriesToDisplay:Oh}=q,Ch=class extends xh{constructor(e={}){super(e),this.msg=e.message,this.cursor=e.cursor||0,this.scrollIndex=e.cursor||0,this.hint=e.hint||"",this.warn=e.warn||"- This option is disabled -",this.minSelected=e.min,this.showMinError=!1,this.maxChoices=e.max,this.instructions=e.instructions,this.optionsPerPage=e.optionsPerPage||10,this.value=e.choices.map((r,i)=>(typeof r=="string"&&(r={title:r,value:i}),{title:r&&(r.title||r.value||r),description:r&&r.description,value:r&&(r.value===void 0?i:r.value),selected:r&&r.selected,disabled:r&&r.disabled})),this.clear=Ni("",this.out.columns),e.overrideRender||this.render();}reset(){this.value.map(e=>!e.selected),this.cursor=0,this.fire(),this.render();}selected(){return this.value.filter(e=>e.selected)}exit(){this.abort();}abort(){this.done=this.aborted=!0,this.fire(),this.render(),this.out.write(`
`),this.close();}submit(){let e=this.value.filter(r=>r.selected);this.minSelected&&e.length<this.minSelected?(this.showMinError=!0,this.render()):(this.done=!0,this.aborted=!1,this.fire(),this.render(),this.out.write(`
`),this.close());}first(){this.cursor=0,this.render();}last(){this.cursor=this.value.length-1,this.render();}next(){this.cursor=(this.cursor+1)%this.value.length,this.render();}up(){this.cursor===0?this.cursor=this.value.length-1:this.cursor--,this.render();}down(){this.cursor===this.value.length-1?this.cursor=0:this.cursor++,this.render();}left(){this.value[this.cursor].selected=!1,this.render();}right(){if(this.value.filter(e=>e.selected).length>=this.maxChoices)return this.bell();this.value[this.cursor].selected=!0,this.render();}handleSpaceToggle(){let e=this.value[this.cursor];if(e.selected)e.selected=!1,this.render();else {if(e.disabled||this.value.filter(r=>r.selected).length>=this.maxChoices)return this.bell();e.selected=!0,this.render();}}toggleAll(){if(this.maxChoices!==void 0||this.value[this.cursor].disabled)return this.bell();let e=!this.value[this.cursor].selected;this.value.filter(r=>!r.disabled).forEach(r=>r.selected=e),this.render();}_(e,r){if(e===" ")this.handleSpaceToggle();else if(e==="a")this.toggleAll();else return this.bell()}renderInstructions(){return this.instructions===void 0||this.instructions?typeof this.instructions=="string"?this.instructions:`
Instructions:
    ${Q.arrowUp}/${Q.arrowDown}: Highlight option
    ${Q.arrowLeft}/${Q.arrowRight}/[space]: Toggle selection
`+(this.maxChoices===void 0?`    a: Toggle all
`:"")+"    enter/return: Complete answer":""}renderOption(e,r,i,n){let s=(r.selected?H.green(Q.radioOn):Q.radioOff)+" "+n+" ",o,a;return r.disabled?o=e===i?H.gray().underline(r.title):H.strikethrough().gray(r.title):(o=e===i?H.cyan().underline(r.title):r.title,e===i&&r.description&&(a=` - ${r.description}`,(s.length+o.length+a.length>=this.out.columns||r.description.split(/\r?\n/).length>1)&&(a=`
`+Eh(r.description,{margin:s.length,width:this.out.columns})))),s+o+H.gray(a||"")}paginateOptions(e){if(e.length===0)return H.red("No matches for this query.");let{startIndex:r,endIndex:i}=Oh(this.cursor,e.length,this.optionsPerPage),n,s=[];for(let o=r;o<i;o++)o===r&&r>0?n=Q.arrowUp:o===i-1&&i<e.length?n=Q.arrowDown:n=" ",s.push(this.renderOption(this.cursor,e[o],o,n));return `
`+s.join(`
`)}renderOptions(e){return this.done?"":this.paginateOptions(e)}renderDoneOrInstructions(){if(this.done)return this.value.filter(r=>r.selected).map(r=>r.title).join(", ");let e=[H.gray(this.hint),this.renderInstructions()];return this.value[this.cursor].disabled&&e.push(H.yellow(this.warn)),e.join(" ")}render(){if(this.closed)return;this.firstRender&&this.out.write(Sh.hide),super.render();let e=[Bi.symbol(this.done,this.aborted),H.bold(this.msg),Bi.delimiter(!1),this.renderDoneOrInstructions()].join(" ");this.showMinError&&(e+=H.red(`You must select a minimum of ${this.minSelected} choices.`),this.showMinError=!1),e+=this.renderOptions(this.value),this.out.write(this.clear+e),this.clear=Ni(e,this.out.columns);}},Un=Ch,Re=U,Th=ie,{erase:Ah,cursor:Gi}=W,{style:Dt,clear:Ui,figures:Lt,wrap:Rh,entriesToDisplay:Ph}=q,Wi=(t,e)=>t[e]&&(t[e].value||t[e].title||t[e]),_h=(t,e)=>t[e]&&(t[e].title||t[e].value||t[e]),Ih=(t,e)=>{let r=t.findIndex(i=>i.value===e||i.title===e);return r>-1?r:void 0},ar=class extends Th{constructor(e={}){super(e),this.msg=e.message,this.suggest=e.suggest,this.choices=e.choices,this.initial=typeof e.initial=="number"?e.initial:Ih(e.choices,e.initial),this.select=this.initial||e.cursor||0,this.i18n={noMatches:e.noMatches||"no matches found"},this.fallback=e.fallback||this.initial,this.clearFirst=e.clearFirst||!1,this.suggestions=[],this.input="",this.limit=e.limit||10,this.cursor=0,this.transform=Dt.render(e.style),this.scale=this.transform.scale,this.render=this.render.bind(this),this.complete=this.complete.bind(this),this.clear=Ui("",this.out.columns),this.complete(this.render),this.render();}set fallback(e){this._fb=Number.isSafeInteger(parseInt(e))?parseInt(e):e;}get fallback(){let e;return typeof this._fb=="number"?e=this.choices[this._fb]:typeof this._fb=="string"&&(e={title:this._fb}),e||this._fb||{title:this.i18n.noMatches}}moveSelect(e){this.select=e,this.suggestions.length>0?this.value=Wi(this.suggestions,e):this.value=this.fallback.value,this.fire();}async complete(e){let r=this.completing=this.suggest(this.input,this.choices),i=await r;if(this.completing!==r)return;this.suggestions=i.map((s,o,a)=>({title:_h(a,o),value:Wi(a,o),description:s.description})),this.completing=!1;let n=Math.max(i.length-1,0);this.moveSelect(Math.min(n,this.select)),e&&e();}reset(){this.input="",this.complete(()=>{this.moveSelect(this.initial!==void 0?this.initial:0),this.render();}),this.render();}exit(){this.clearFirst&&this.input.length>0?this.reset():(this.done=this.exited=!0,this.aborted=!1,this.fire(),this.render(),this.out.write(`
`),this.close());}abort(){this.done=this.aborted=!0,this.exited=!1,this.fire(),this.render(),this.out.write(`
`),this.close();}submit(){this.done=!0,this.aborted=this.exited=!1,this.fire(),this.render(),this.out.write(`
`),this.close();}_(e,r){let i=this.input.slice(0,this.cursor),n=this.input.slice(this.cursor);this.input=`${i}${e}${n}`,this.cursor=i.length+1,this.complete(this.render),this.render();}delete(){if(this.cursor===0)return this.bell();let e=this.input.slice(0,this.cursor-1),r=this.input.slice(this.cursor);this.input=`${e}${r}`,this.complete(this.render),this.cursor=this.cursor-1,this.render();}deleteForward(){if(this.cursor*this.scale>=this.rendered.length)return this.bell();let e=this.input.slice(0,this.cursor),r=this.input.slice(this.cursor+1);this.input=`${e}${r}`,this.complete(this.render),this.render();}first(){this.moveSelect(0),this.render();}last(){this.moveSelect(this.suggestions.length-1),this.render();}up(){this.select===0?this.moveSelect(this.suggestions.length-1):this.moveSelect(this.select-1),this.render();}down(){this.select===this.suggestions.length-1?this.moveSelect(0):this.moveSelect(this.select+1),this.render();}next(){this.select===this.suggestions.length-1?this.moveSelect(0):this.moveSelect(this.select+1),this.render();}nextPage(){this.moveSelect(Math.min(this.select+this.limit,this.suggestions.length-1)),this.render();}prevPage(){this.moveSelect(Math.max(this.select-this.limit,0)),this.render();}left(){if(this.cursor<=0)return this.bell();this.cursor=this.cursor-1,this.render();}right(){if(this.cursor*this.scale>=this.rendered.length)return this.bell();this.cursor=this.cursor+1,this.render();}renderOption(e,r,i,n){let s,o=i?Lt.arrowUp:n?Lt.arrowDown:" ",a=r?Re.cyan().underline(e.title):e.title;return o=(r?Re.cyan(Lt.pointer)+" ":"  ")+o,e.description&&(s=` - ${e.description}`,(o.length+a.length+s.length>=this.out.columns||e.description.split(/\r?\n/).length>1)&&(s=`
`+Rh(e.description,{margin:3,width:this.out.columns}))),o+" "+a+Re.gray(s||"")}render(){if(this.closed)return;this.firstRender?this.out.write(Gi.hide):this.out.write(Ui(this.outputText,this.out.columns)),super.render();let{startIndex:e,endIndex:r}=Ph(this.select,this.choices.length,this.limit);if(this.outputText=[Dt.symbol(this.done,this.aborted,this.exited),Re.bold(this.msg),Dt.delimiter(this.completing),this.done&&this.suggestions[this.select]?this.suggestions[this.select].title:this.rendered=this.transform.render(this.input)].join(" "),!this.done){let i=this.suggestions.slice(e,r).map((n,s)=>this.renderOption(n,this.select===s+e,s===0&&e>0,s+e===r-1&&r<this.choices.length)).join(`
`);this.outputText+=`
`+(i||Re.gray(this.fallback.title));}this.out.write(Ah.line+Gi.to(0)+this.outputText);}},kh=ar,K=U,{cursor:Mh}=W,jh=Un,{clear:zi,style:Hi,figures:he}=q,ur=class extends jh{constructor(e={}){e.overrideRender=!0,super(e),this.inputValue="",this.clear=zi("",this.out.columns),this.filteredOptions=this.value,this.render();}last(){this.cursor=this.filteredOptions.length-1,this.render();}next(){this.cursor=(this.cursor+1)%this.filteredOptions.length,this.render();}up(){this.cursor===0?this.cursor=this.filteredOptions.length-1:this.cursor--,this.render();}down(){this.cursor===this.filteredOptions.length-1?this.cursor=0:this.cursor++,this.render();}left(){this.filteredOptions[this.cursor].selected=!1,this.render();}right(){if(this.value.filter(e=>e.selected).length>=this.maxChoices)return this.bell();this.filteredOptions[this.cursor].selected=!0,this.render();}delete(){this.inputValue.length&&(this.inputValue=this.inputValue.substr(0,this.inputValue.length-1),this.updateFilteredOptions());}updateFilteredOptions(){let e=this.filteredOptions[this.cursor];this.filteredOptions=this.value.filter(i=>this.inputValue?!!(typeof i.title=="string"&&i.title.toLowerCase().includes(this.inputValue.toLowerCase())||typeof i.value=="string"&&i.value.toLowerCase().includes(this.inputValue.toLowerCase())):!0);let r=this.filteredOptions.findIndex(i=>i===e);this.cursor=r<0?0:r,this.render();}handleSpaceToggle(){let e=this.filteredOptions[this.cursor];if(e.selected)e.selected=!1,this.render();else {if(e.disabled||this.value.filter(r=>r.selected).length>=this.maxChoices)return this.bell();e.selected=!0,this.render();}}handleInputChange(e){this.inputValue=this.inputValue+e,this.updateFilteredOptions();}_(e,r){e===" "?this.handleSpaceToggle():this.handleInputChange(e);}renderInstructions(){return this.instructions===void 0||this.instructions?typeof this.instructions=="string"?this.instructions:`
Instructions:
    ${he.arrowUp}/${he.arrowDown}: Highlight option
    ${he.arrowLeft}/${he.arrowRight}/[space]: Toggle selection
    [a,b,c]/delete: Filter choices
    enter/return: Complete answer
`:""}renderCurrentInput(){return `
Filtered results for: ${this.inputValue?this.inputValue:K.gray("Enter something to filter")}
`}renderOption(e,r,i,n){let s=(r.selected?K.green(he.radioOn):he.radioOff)+" "+n+" ",o;return r.disabled?o=e===i?K.gray().underline(r.title):K.strikethrough().gray(r.title):o=e===i?K.cyan().underline(r.title):r.title,s+o}renderDoneOrInstructions(){if(this.done)return this.value.filter(r=>r.selected).map(r=>r.title).join(", ");let e=[K.gray(this.hint),this.renderInstructions(),this.renderCurrentInput()];return this.filteredOptions.length&&this.filteredOptions[this.cursor].disabled&&e.push(K.yellow(this.warn)),e.join(" ")}render(){if(this.closed)return;this.firstRender&&this.out.write(Mh.hide),super.render();let e=[Hi.symbol(this.done,this.aborted),K.bold(this.msg),Hi.delimiter(!1),this.renderDoneOrInstructions()].join(" ");this.showMinError&&(e+=K.red(`You must select a minimum of ${this.minSelected} choices.`),this.showMinError=!1),e+=this.renderOptions(this.filteredOptions),this.out.write(this.clear+e),this.clear=zi(e,this.out.columns);}},Fh=ur,qi=U,Dh=ie,{style:Vi,clear:Lh}=q,{erase:Nh,cursor:Yi}=W,lr=class extends Dh{constructor(e={}){super(e),this.msg=e.message,this.value=e.initial,this.initialValue=!!e.initial,this.yesMsg=e.yes||"yes",this.yesOption=e.yesOption||"(Y/n)",this.noMsg=e.no||"no",this.noOption=e.noOption||"(y/N)",this.render();}reset(){this.value=this.initialValue,this.fire(),this.render();}exit(){this.abort();}abort(){this.done=this.aborted=!0,this.fire(),this.render(),this.out.write(`
`),this.close();}submit(){this.value=this.value||!1,this.done=!0,this.aborted=!1,this.fire(),this.render(),this.out.write(`
`),this.close();}_(e,r){return e.toLowerCase()==="y"?(this.value=!0,this.submit()):e.toLowerCase()==="n"?(this.value=!1,this.submit()):this.bell()}render(){this.closed||(this.firstRender?this.out.write(Yi.hide):this.out.write(Lh(this.outputText,this.out.columns)),super.render(),this.outputText=[Vi.symbol(this.done,this.aborted),qi.bold(this.msg),Vi.delimiter(this.done),this.done?this.value?this.yesMsg:this.noMsg:qi.gray(this.initialValue?this.yesOption:this.noOption)].join(" "),this.out.write(Nh.line+Yi.to(0)+this.outputText));}},Bh=lr,Gh={TextPrompt:vc,SelectPrompt:Ec,TogglePrompt:Ac,DatePrompt:mh,NumberPrompt:$h,MultiselectPrompt:Un,AutocompletePrompt:kh,AutocompleteMultiselectPrompt:Fh,ConfirmPrompt:Bh};(function(t){let e=t,r=Gh,i=o=>o;function n(o,a,u={}){return new Promise((l,c)=>{let h=new r[o](a),d=u.onAbort||i,f=u.onSubmit||i,b=u.onExit||i;h.on("state",a.onState||i),h.on("submit",m=>l(f(m))),h.on("exit",m=>l(b(m))),h.on("abort",m=>c(d(m)));})}e.text=o=>n("TextPrompt",o),e.password=o=>(o.style="password",e.text(o)),e.invisible=o=>(o.style="invisible",e.text(o)),e.number=o=>n("NumberPrompt",o),e.date=o=>n("DatePrompt",o),e.confirm=o=>n("ConfirmPrompt",o),e.list=o=>{let a=o.separator||",";return n("TextPrompt",o,{onSubmit:u=>u.split(a).map(l=>l.trim())})},e.toggle=o=>n("TogglePrompt",o),e.select=o=>n("SelectPrompt",o),e.multiselect=o=>{o.choices=[].concat(o.choices||[]);let a=u=>u.filter(l=>l.selected).map(l=>l.value);return n("MultiselectPrompt",o,{onAbort:a,onSubmit:a})},e.autocompleteMultiselect=o=>{o.choices=[].concat(o.choices||[]);let a=u=>u.filter(l=>l.selected).map(l=>l.value);return n("AutocompleteMultiselectPrompt",o,{onAbort:a,onSubmit:a})};let s=(o,a)=>Promise.resolve(a.filter(u=>u.title.slice(0,o.length).toLowerCase()===o.toLowerCase()));e.autocomplete=o=>(o.suggest=o.suggest||s,o.choices=[].concat(o.choices||[]),n("AutocompletePrompt",o));})(Fn);var cr=Fn,Uh=["suggest","format","onState","validate","onRender","type"],Ki=()=>{};async function re(t=[],{onSubmit:e=Ki,onCancel:r=Ki}={}){let i={},n=re._override||{};t=[].concat(t);let s,o,a,u,l,c,h=async(d,f,b=!1)=>{if(!(!b&&d.validate&&d.validate(f)!==!0))return d.format?await d.format(f,i):f};for(o of t)if({name:u,type:l}=o,typeof l=="function"&&(l=await l(s,{...i},o),o.type=l),!!l){for(let d in o){if(Uh.includes(d))continue;let f=o[d];o[d]=typeof f=="function"?await f(s,{...i},c):f;}if(c=o,typeof o.message!="string")throw new Error("prompt message is required");if({name:u,type:l}=o,cr[l]===void 0)throw new Error(`prompt type (${l}) is not defined`);if(n[o.name]!==void 0&&(s=await h(o,n[o.name]),s!==void 0)){i[u]=s;continue}try{s=re._injected?Wh(re._injected,o.initial):await cr[l](o),i[u]=s=await h(o,s,!0),a=await e(o,s,i);}catch{a=!await r(o,i);}if(a)return i}return i}function Wh(t,e){let r=t.shift();if(r instanceof Error)throw r;return r===void 0?e:r}function zh(t){re._injected=(re._injected||[]).concat(t);}function Hh(t){re._override=Object.assign({},t);}var qh=Object.assign(re,{prompt:re,prompts:cr,inject:zh,override:Hh}),Vh=qh,Wn=ye(Vh),zn={},ge={};Object.defineProperty(ge,"__esModule",{value:!0});ge.sync=ge.isexe=void 0;var Yh=Fe__default.default,Kh=sn__default.default,Jh=async(t,e={})=>{let{ignoreErrors:r=!1}=e;try{return Hn(await(0,Kh.stat)(t),e)}catch(i){let n=i;if(r||n.code==="EACCES")return !1;throw n}};ge.isexe=Jh;var Xh=(t,e={})=>{let{ignoreErrors:r=!1}=e;try{return Hn((0,Yh.statSync)(t),e)}catch(i){let n=i;if(r||n.code==="EACCES")return !1;throw n}};ge.sync=Xh;var Hn=(t,e)=>t.isFile()&&Zh(t,e),Zh=(t,e)=>{var f,b,m;let r=e.uid??((f=process.getuid)==null?void 0:f.call(process)),i=e.groups??((b=process.getgroups)==null?void 0:b.call(process))??[],n=e.gid??((m=process.getgid)==null?void 0:m.call(process))??i[0];if(r===void 0||n===void 0)throw new Error("cannot get uid or gid");let s=new Set([n,...i]),o=t.mode,a=t.uid,u=t.gid,l=parseInt("100",8),c=parseInt("010",8),h=parseInt("001",8),d=l|c;return !!(o&h||o&c&&s.has(u)||o&l&&a===r||o&d&&r===0)},be={};Object.defineProperty(be,"__esModule",{value:!0});be.sync=be.isexe=void 0;var Qh=Fe__default.default,ef=sn__default.default,tf=async(t,e={})=>{let{ignoreErrors:r=!1}=e;try{return qn(await(0,ef.stat)(t),t,e)}catch(i){let n=i;if(r||n.code==="EACCES")return !1;throw n}};be.isexe=tf;var rf=(t,e={})=>{let{ignoreErrors:r=!1}=e;try{return qn((0,Qh.statSync)(t),t,e)}catch(i){let n=i;if(r||n.code==="EACCES")return !1;throw n}};be.sync=rf;var nf=(t,e)=>{let{pathExt:r=process.env.PATHEXT||""}=e,i=r.split(";");if(i.indexOf("")!==-1)return !0;for(let n=0;n<i.length;n++){let s=i[n].toLowerCase(),o=t.substring(t.length-s.length).toLowerCase();if(s&&o===s)return !0}return !1},qn=(t,e,r)=>t.isFile()&&nf(e,r),Vn={};Object.defineProperty(Vn,"__esModule",{value:!0});(function(t){var e=J&&J.__createBinding||(Object.create?function(l,c,h,d){d===void 0&&(d=h);var f=Object.getOwnPropertyDescriptor(c,h);(!f||("get"in f?!c.__esModule:f.writable||f.configurable))&&(f={enumerable:!0,get:function(){return c[h]}}),Object.defineProperty(l,d,f);}:function(l,c,h,d){d===void 0&&(d=h),l[d]=c[h];}),r=J&&J.__setModuleDefault||(Object.create?function(l,c){Object.defineProperty(l,"default",{enumerable:!0,value:c});}:function(l,c){l.default=c;}),i=J&&J.__importStar||function(l){if(l&&l.__esModule)return l;var c={};if(l!=null)for(var h in l)h!=="default"&&Object.prototype.hasOwnProperty.call(l,h)&&e(c,l,h);return r(c,l),c},n=J&&J.__exportStar||function(l,c){for(var h in l)h!=="default"&&!Object.prototype.hasOwnProperty.call(c,h)&&e(c,l,h);};Object.defineProperty(t,"__esModule",{value:!0}),t.sync=t.isexe=t.posix=t.win32=void 0;let s=i(ge);t.posix=s;let o=i(be);t.win32=o,n(Vn,t);let u=(process.env._ISEXE_TEST_PLATFORM_||process.platform)==="win32"?o:s;t.isexe=u.isexe,t.sync=u.sync;})(zn);var{isexe:sf,sync:of}=zn,{join:af,delimiter:uf,sep:Ji,posix:Xi}=D__default.default,Zi=process.platform==="win32",Yn=new RegExp(`[${Xi.sep}${Ji===Xi.sep?"":Ji}]`.replace(/(\\)/g,"\\$1")),lf=new RegExp(`^\\.${Yn.source}`),Kn=t=>Object.assign(new Error(`not found: ${t}`),{code:"ENOENT"}),Jn=(t,{path:e=process.env.PATH,pathExt:r=process.env.PATHEXT,delimiter:i=uf})=>{let n=t.match(Yn)?[""]:[...Zi?[process.cwd()]:[],...(e||"").split(i)];if(Zi){let s=r||[".EXE",".CMD",".BAT",".COM"].join(i),o=s.split(i).flatMap(a=>[a,a.toLowerCase()]);return t.includes(".")&&o[0]!==""&&o.unshift(""),{pathEnv:n,pathExt:o,pathExtExe:s}}return {pathEnv:n,pathExt:[""]}},Xn=(t,e)=>{let r=/^".*"$/.test(t)?t.slice(1,-1):t;return (!r&&lf.test(e)?e.slice(0,2):"")+af(r,e)},Zn=async(t,e={})=>{let{pathEnv:r,pathExt:i,pathExtExe:n}=Jn(t,e),s=[];for(let o of r){let a=Xn(o,t);for(let u of i){let l=a+u;if(await sf(l,{pathExt:n,ignoreErrors:!0})){if(!e.all)return l;s.push(l);}}}if(e.all&&s.length)return s;if(e.nothrow)return null;throw Kn(t)},cf=(t,e={})=>{let{pathEnv:r,pathExt:i,pathExtExe:n}=Jn(t,e),s=[];for(let o of r){let a=Xn(o,t);for(let u of i){let l=a+u;if(of(l,{pathExt:n,ignoreErrors:!0})){if(!e.all)return l;s.push(l);}}}if(e.all&&s.length)return s;if(e.nothrow)return null;throw Kn(t)},hf=Zn;Zn.sync=cf;var ff=ye(hf);D.join(rn__default.default.tmpdir(),"antfu-ni");function Ie(t,e){return t.slice().filter(r=>r!==e)}function Qn(t){return ff.sync(t,{nothrow:!0})!==null}async function $r({autoInstall:t,programmatic:e,cwd:r}={}){let i=null,n=null,s=await Si(Object.keys(Nt),{cwd:r}),o;if(s?o=D__default.default.resolve(s,"../package.json"):o=await Si("package.json",{cwd:r}),o&&Fe__default.default.existsSync(o))try{let a=JSON.parse(Fe__default.default.readFileSync(o,"utf8"));if(typeof a.packageManager=="string"){let[u,l]=a.packageManager.replace(/^\^/,"").split("@");n=l,u==="yarn"&&Number.parseInt(l)>1?(i="yarn@berry",n="berry"):u==="pnpm"&&Number.parseInt(l)<7?i="pnpm@6":u in ke?i=u:e||console.warn("[ni] Unknown packageManager:",a.packageManager);}}catch{}if(!i&&s&&(i=Nt[D__default.default.basename(s)]),i&&!Qn(i.split("@")[0])&&!e){if(!t){console.warn(`[ni] Detected ${i} but it doesn't seem to be installed.
`),___default.default.env.CI&&___default.default.exit(1);let a=je(i,an[i]),{tryInstall:u}=await Wn({name:"tryInstall",type:"confirm",message:`Would you like to globally install ${a}?`});u||___default.default.exit(1);}await Al(`npm i -g ${i.split("@")[0]}${n?`@${n}`:""}`,{stdio:"inherit",cwd:r});}return i}var pf=___default.default.env.NI_CONFIG_FILE,mf=___default.default.platform==="win32"?___default.default.env.USERPROFILE:___default.default.env.HOME,gf=D__default.default.join(mf||"~/",".nirc"),Qi=pf||gf,bf={defaultAgent:"prompt",globalAgent:"npm"},Xe;async function Sr(){if(!Xe){Xe=Object.assign({},bf,Fe__default.default.existsSync(Qi)?da.parse(Fe__default.default.readFileSync(Qi,"utf-8")):null);let t=await $r({programmatic:!0});t&&(Xe.defaultAgent=t);}return Xe}async function es(t){let{defaultAgent:e}=await Sr();return e==="prompt"&&(t||___default.default.env.CI)?"npm":e}async function ts(){let{globalAgent:t}=await Sr();return t}var ot=class extends Error{constructor({agent:e,command:r}){super(`Command "${r}" is not support by agent "${e}"`);}};function ee(t,e,r=[]){if(!(t in ke))throw new Error(`Unsupported agent "${t}"`);let i=ke[t][e];if(typeof i=="function")return i(r);if(!i)throw new ot({agent:t,command:e});let n=s=>!s.startsWith("--")&&s.includes(" ")?JSON.stringify(s):s;return i.replace("{0}",r.map(n).join(" ")).trim()}var yf=(t,e,r)=>(t==="bun"&&(e=e.map(i=>i==="-D"?"-d":i)),e.includes("-g")?ee(t,"global",Ie(e,"-g")):e.includes("--frozen-if-present")?(e=Ie(e,"--frozen-if-present"),ee(t,r!=null&&r.hasLock?"frozen":"install",e)):e.includes("--frozen")?ee(t,"frozen",Ie(e,"--frozen")):e.length===0||e.every(i=>i.startsWith("-"))?ee(t,"install",e):ee(t,"add",e)),vf=(t,e)=>(e.length===0&&e.push("start"),e.includes("--if-present")&&(e=Ie(e,"--if-present"),e[0]=`--if-present ${e[0]}`),ee(t,"run",e));var wf=(t,e)=>ee(t,"agent",e);typeof process<"u"&&(process.env||{},process.stdout&&process.stdout.isTTY);async function Sf(t,e,r={},i=r.cwd??___default.default.cwd()){if(e.includes("-g"))return await t(await ts(),e);let s=await $r({...r,cwd:i})||await es(r.programmatic);if(!(s==="prompt"&&(s=(await Wn({name:"agent",type:"select",message:"Choose the agent",choices:on.filter(o=>!o.includes("@")).map(o=>({title:o,value:o}))})).agent,!s)))return await t(s,e,{programmatic:r.programmatic,hasLock:!!s,cwd:i})}var os=t=>t instanceof URL?ia.fileURLToPath(t):t;async function as(t,{cwd:e=___default.default.cwd(),type:r="file",stopAt:i}={}){let n=D__default.default.resolve(os(e)??""),{root:s}=D__default.default.parse(n);for(i=D__default.default.resolve(n,os(i??s));n&&n!==i&&n!==s;){let o=D__default.default.isAbsolute(t)?t:D__default.default.join(n,t);try{let a=await sn__default.default.stat(o);if(r==="file"&&a.isFile()||r==="directory"&&a.isDirectory())return o}catch{}n=D__default.default.dirname(n);}}var ao=chunkTKGT252T_js.e(no(),1);var so=(t,e,r)=>r<0?-1:t.lastIndexOf(e,r);function yd(t,e){let r=so(t,`
`,e-1),i=e-r-1,n=0;for(let s=r;s>=0;s=so(t,`
`,s-1))n++;return {line:n,column:i}}function mt(t,e,{oneBased:r=!1}={}){if(e<0||e>=t.length&&t.length>0)throw new RangeError("Index out of bounds");let i=yd(t,e);return r?{line:i.line+1,column:i.column+1}:i}var vd=t=>`\\u{${t.codePointAt(0).toString(16)}}`,Fr=class t extends Error{name="JSONError";fileName;codeFrame;rawCodeFrame;#r;constructor(e){var r;super(),this.#r=e,(r=Error.captureStackTrace)==null||r.call(Error,this,t);}get message(){let{fileName:e,codeFrame:r}=this;return `${this.#r}${e?` in ${e}`:""}${r?`

${r}
`:""}`}set message(e){this.#r=e;}},oo=(t,e,r=!0)=>(0, ao.codeFrameColumns)(t,{start:e},{highlightCode:r}),wd=(t,e)=>{let r=e.match(/in JSON at position (?<index>\d+)(?: \(line (?<line>\d+) column (?<column>\d+)\))?$/);if(!r)return;let{index:i,line:n,column:s}=r.groups;if(n&&s)return {line:Number(n),column:Number(s)};if(i=Number(i),i===t.length){let{line:o,column:a}=mt(t,t.length-1,{oneBased:!0});return {line:o,column:a+1}}return mt(t,i,{oneBased:!0})},$d=t=>t.replace(/(?<=^Unexpected token )(?<quote>')?(.)\k<quote>/,(e,r,i)=>`"${i}"(${vd(i)})`);function Dr(t,e,r){typeof e=="string"&&(r=e,e=void 0);let i;try{return JSON.parse(t,e)}catch(o){i=o.message;}let n;t?(n=wd(t,i),i=$d(i)):i+=" while parsing empty string";let s=new Fr(i);throw s.fileName=r,n&&(s.codeFrame=oo(t,n),s.rawCodeFrame=oo(t,n,!1)),s}var Bo=chunkTKGT252T_js.e(Lo(),1);function No(t){return t instanceof URL?ia.fileURLToPath(t):t}var n0=t=>D__default.default.resolve(No(t)??".","package.json"),s0=(t,e)=>{let r=typeof t=="string"?Dr(t):t;return e&&(0, Bo.default)(r),r};async function Go({cwd:t,normalize:e=!0}={}){let r=await sn__default.default.readFile(n0(t),"utf8");return s0(r,e)}async function a1(t){let e=await as("package.json",t);if(e)return {packageJson:await Go({...t,cwd:D__default.default.dirname(e)}),path:e}}var Uo=chunkTKGT252T_js.e(chunk7UHX5T7X_js.F()),Wo=chunkTKGT252T_js.e(chunk7UHX5T7X_js.G());var c1=()=>(0, Wo.dedent)(Uo.default`
    ${chunk7UHX5T7X_js.K} {bold No package.json found}
    Chromatic only works from inside a JavaScript project.
    We expected to find a package.json somewhere up the directory tree.
    Are you sure you're running from your project directory?
  `);function Yr({onlyFirst:t=!1}={}){let e=["[\\u001B\\u009B][[\\]()#;?]*(?:(?:(?:(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]+)*|[a-zA-Z\\d]+(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]*)*)?\\u0007)","(?:(?:\\d{1,4}(?:;\\d{0,4})*)?[\\dA-PR-TZcf-ntqry=><~]))"].join("|");return new RegExp(e,t?void 0:"g")}var a0=Yr();function u0(t){if(typeof t!="string")throw new TypeError(`Expected a \`string\`, got \`${typeof t}\``);return t.replace(a0,"")}

exports.a = yf;
exports.b = vf;
exports.c = wf;
exports.d = Sf;
exports.e = u0;
exports.f = a1;
exports.g = c1;
//# sourceMappingURL=out.js.map
//# sourceMappingURL=chunk-J4YDBNCB.js.map
//# debugId=4d50641f-6596-5a59-be2e-ca3023d9d22e
