{"version": 3, "file": "semconv.js", "sourceRoot": "", "sources": ["../../src/semconv.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH;;;;;;GAMG;AACU,QAAA,mCAAmC,GAC9C,gCAAgC,CAAC;AAEnC;;;;;;GAMG;AACU,QAAA,+BAA+B,GAAG,4BAA4B,CAAC;AAE5E;;;;;;;;;;;;GAYG;AACU,QAAA,iBAAiB,GAAG,cAAc,CAAC;AAEhD;;;;;;;;;;;;;GAaG;AACU,QAAA,sBAAsB,GAAG,mBAAmB,CAAC;AAE1D;;GAEG;AACU,QAAA,qCAAqC,GAAG,MAAM,CAAC;AAE5D;;GAEG;AACU,QAAA,qCAAqC,GAAG,MAAM,CAAC;AAE5D;;;;GAIG;AACU,QAAA,iCAAiC,GAAG,4BAA4B,CAAC;AAE9E;;;;GAIG;AACU,QAAA,4CAA4C,GACvD,uCAAuC,CAAC;AAE1C;;;;;;GAMG;AACU,QAAA,mCAAmC,GAC9C,8BAA8B,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * The name of the connection pool; unique within the instrumented application. In case the connection pool implementation doesn't provide a name, instrumentation **SHOULD** use a combination of parameters that would make the name unique, for example, combining attributes `server.address`, `server.port`, and `db.namespace`, formatted as `server.address:server.port/db.namespace`. Instrumentations that generate connection pool name following different patterns **SHOULD** document it.\n *\n * @example myDataSource\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_DB_CLIENT_CONNECTION_POOL_NAME =\n  'db.client.connection.pool.name';\n\n/**\n * The state of a connection in the pool\n *\n * @example idle\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_DB_CLIENT_CONNECTION_STATE = 'db.client.connection.state';\n\n/**\n * The name of the database, fully qualified within the server address and port.\n *\n * @example customers\n * @example test.users\n *\n * @note If a database system has multiple namespace components, they **SHOULD** be concatenated (potentially using database system specific conventions) from most general to most specific namespace component, and more specific namespaces **SHOULD NOT** be captured without the more general namespaces, to ensure that \"startswith\" queries for the more general namespaces will be valid.\n * Semantic conventions for individual database systems **SHOULD** document what `db.namespace` means in the context of that system.\n * It is **RECOMMENDED** to capture the value as provided by the application without attempting to do any case normalization.\n * This attribute has stability level RELEASE CANDIDATE.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_DB_NAMESPACE = 'db.namespace';\n\n/**\n * The name of the operation or command being executed.\n *\n * @example findAndModify\n * @example HMSET\n * @example SELECT\n *\n * @note It is **RECOMMENDED** to capture the value as provided by the application without attempting to do any case normalization.\n * If the operation name is parsed from the query text, it **SHOULD** be the first operation name found in the query.\n * For batch operations, if the individual operations are known to have the same operation name then that operation name **SHOULD** be used prepended by `BATCH `, otherwise `db.operation.name` **SHOULD** be `BATCH` or some other database system specific term if more applicable.\n * This attribute has stability level RELEASE CANDIDATE.\n *\n * @experimental This attribute is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const ATTR_DB_OPERATION_NAME = 'db.operation.name';\n\n/**\n * Enum value \"used\" for attribute {@link ATTR_DB_CLIENT_CONNECTION_STATE}.\n */\nexport const DB_CLIENT_CONNECTION_STATE_VALUE_USED = 'used';\n\n/**\n * Enum value \"idle\" for attribute {@link ATTR_DB_CLIENT_CONNECTION_STATE}.\n */\nexport const DB_CLIENT_CONNECTION_STATE_VALUE_IDLE = 'idle';\n\n/**\n * The number of connections that are currently in state described by the `state` attribute\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_DB_CLIENT_CONNECTION_COUNT = 'db.client.connection.count';\n\n/**\n * The number of current pending requests for an open connection\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_DB_CLIENT_CONNECTION_PENDING_REQUESTS =\n  'db.client.connection.pending_requests';\n\n/**\n * Duration of database client operations.\n *\n * @note Batch operations **SHOULD** be recorded as a single operation.\n *\n * @experimental This metric is experimental and is subject to breaking changes in minor releases of `@opentelemetry/semantic-conventions`.\n */\nexport const METRIC_DB_CLIENT_OPERATION_DURATION =\n  'db.client.operation.duration';\n"]}