// This file was generated by basehub. Do not edit directly. Read more: https://basehub.com/docs/api-reference/basehub-sdk

/* eslint-disable */
/* eslint-disable eslint-comments/no-restricted-disable */
/* tslint:disable */

// ../../node_modules/.pnpm/basehub@8.2.7_@babel+runtim_3aebfa8e064bb601162c04844d38dd02/node_modules/basehub/src/workflows/primitive.tsx
var authenticateWebhook = async ({
  secret: _secret,
  body,
  signature
}) => {
  try {
    if (signature && typeof signature === "object") {
      signature = signature.get("x-basehub-webhook-signature");
    }
    if (!signature) {
      return { success: false, error: "Signature is required" };
    }
    let secret = _secret;
    if (_secret.startsWith("bshb_workflow")) {
      secret = _secret.split(":")[1];
    }
    if (typeof secret !== "string") {
      return { success: false, error: "Invalid secret" };
    }
    let rawBody;
    let parsedBody;
    if (body instanceof ReadableStream) {
      const reader = body.getReader();
      const chunks = [];
      while (true) {
        const { done, value } = await reader.read();
        if (done)
          break;
        chunks.push(value);
      }
      const bodyText = new TextDecoder().decode(
        new Uint8Array(chunks.flatMap((chunk) => Array.from(chunk)))
      );
      rawBody = bodyText;
      parsedBody = JSON.parse(bodyText);
    } else if (typeof body === "string") {
      rawBody = body;
      parsedBody = JSON.parse(body);
    } else {
      rawBody = JSON.stringify(body);
      parsedBody = body;
    }
    if (typeof parsedBody !== "object" || parsedBody === null) {
      return { success: false, error: "Invalid body" };
    }
    const encoder = new TextEncoder();
    const bodyData = encoder.encode(rawBody);
    const secretData = encoder.encode(secret);
    const key = await crypto.subtle.importKey(
      "raw",
      secretData,
      { name: "HMAC", hash: "SHA-256" },
      false,
      ["sign"]
    );
    const signed = await crypto.subtle.sign("HMAC", key, bodyData);
    const calculatedSignature = Array.from(new Uint8Array(signed)).map((b) => b.toString(16).padStart(2, "0")).join("");
    if (signature.length !== calculatedSignature.length) {
      return { success: false, error: "Invalid signature" };
    }
    let mismatch = 0;
    for (let i = 0; i < signature.length; i++) {
      mismatch |= signature.charCodeAt(i) ^ calculatedSignature.charCodeAt(i);
    }
    if (mismatch !== 0) {
      return { success: false, error: "Invalid signature" };
    }
    return {
      success: true,
      payload: parsedBody
    };
  } catch (error) {
    let message = error instanceof Error ? error.message : "Signature verification failed";
    if (message === "Unexpected end of JSON input") {
      message = "Invalid body";
    }
    return {
      success: false,
      error: message
    };
  }
};
export {
  authenticateWebhook
};
