import noCase = require('no-case')
import dotCase = require('dot-case')
import swapCase = require('swap-case')
import pathCase = require('path-case')
import upperCase = require('upper-case')
import lowerCase = require('lower-case')
import camelCase = require('camel-case')
import snakeCase = require('snake-case')
import titleCase = require('title-case')
import paramCase = require('param-case')
import headerCase = require('header-case')
import pascalCase = require('pascal-case')
import constantCase = require('constant-case')
import sentenceCase = require('sentence-case')
import isUpperCase = require('is-upper-case')
import isLowerCase = require('is-lower-case')
import upperCaseFirst = require('upper-case-first')
import lowerCaseFirst = require('lower-case-first')

export { noCase, noCase as no }
export { dotCase, dotCase as dot }
export { swapCase, swapCase as swap }
export { pathCase, pathCase as path }
export { upperCase, upperCase as upper }
export { lowerCase, lowerCase as lower }
export { camelCase, camelCase as camel }
export { snakeCase, snakeCase as snake }
export { titleCase, titleCase as title }
export { paramCase, paramCase as param }
export { paramCase as kebabCase, paramCase as kebab }
export { paramCase as hyphenCase, paramCase as hyphen }
export { headerCase, headerCase as header }
export { pascalCase, pascalCase as pascal }
export { constantCase, constantCase as constant }
export { sentenceCase, sentenceCase as sentence }
export { isUpperCase, isUpperCase as isUpper }
export { isLowerCase, isLowerCase as isLower }
export { upperCaseFirst, upperCaseFirst as ucFirst }
export { lowerCaseFirst, lowerCaseFirst as lcFirst }
